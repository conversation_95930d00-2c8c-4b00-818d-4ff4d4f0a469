package com.tencent.andata.etl.sql;

public class DwmVisitSubTaskBaseInfoSql {

    public static final String QUERY_DWD_VISIT_SUB_TASK_VAR_SQL = ""
            + "SELECT \n"
            + "  `value_of_primary_key`,\n"
            + "  `proc_inst_id`,\n"
            + "  `parent_visit_id`,\n"
            + "  `ltc_no`,\n"
            + "  `project_name`,\n"
            + "  `plan_visit_time`,\n"
            + "  `visit_order_type`,\n"
            + "  `subtaskid`,\n"
            + "  `subtaskname`,\n"
            + "  `subtasktype`,\n"
            + "  `subtasksource`,\n"
            + "  `level1`,\n"
            + "  `level2`,\n"
            + "  `level3`,\n"
            + "  `level4`,\n"
            + "  `uin`,\n"
            + "  `customername`,\n"
            + "  `customerappid`,\n"
            + "  `priority`,\n"
            + "  `industry`,\n"
            + "  `region`,\n"
            + "  `aftersalesupport`,\n"
            + "  `business`,\n"
            + "  `architect`,\n"
            + "  `creator`,\n"
            + "  `handler`,\n"
            + "  `comment`,\n"
            + "  `createtime`,\n"
            + "  `completetime`,\n"
            + "  `status`,\n"
            + "  `accept`,\n"
            + "  `describe`,\n"
            + "  `task_tag`,\n"
            + "  `to_do_progress`,\n"
            + "  `meeting_location`,\n"
            + "  `meeting_when`,\n"
            + "  `tencent_people`,\n"
            + "  `customer_people`,\n"
            + "  `meeting_title`,\n"
            + "  `meeting_what`,\n"
            + "  `meeting_result`,\n"
            + "  `risk`,\n"
            + "  `logs`,\n"
            + "  handler as `current_assign`,\n"
            + "  `start_time`,\n"
            + "  CASE WHEN deleted IS NULL THEN 0 ELSE deleted END AS deleted,\n"
            + "  `more_progress`,\n"
            + "  `business_id`,\n"
            + "  `url`,\n"
            + "  `report_link`,\n"
            + "  CASE WHEN official_show IS NULL OR official_show = '' THEN '1' ELSE official_show END AS official_show,\n"
            + "  get_action(logs) as actual_end_time\n"
            + "FROM(\n"
                + "SELECT\n"
                + "  concat(proc_inst_id,'_',get_json_object(subTaskList, '$.subTaskId')) as value_of_primary_key,\n"
                + "  proc_inst_id,\n"
                + "  visit_id as parent_visit_id,\n"
                + "  ltc_no,\n"
                + "  project_name,\n"
                + "  plan_visit_time,\n"
                + "  visit_order_type,\n"
                + "  get_json_object(subTaskList, '$.subTaskId') AS  subtaskid, \n"
                + "  get_json_object(subTaskList, '$.subTaskName') AS subtaskname, \n"
                + "  cast(get_json_object(subTaskList, '$.subTaskType') as int) AS subtasktype, \n"
                + "  get_json_object(subTaskList, '$.subTaskSource') AS subtasksource, \n"
                + "  get_json_object(subTaskList, '$.level1') AS level1, \n"
                + "  get_json_object(subTaskList, '$.level2') AS level2, \n"
                + "  get_json_object(subTaskList, '$.level3') AS level3, \n"
                + "  get_json_object(subTaskList, '$.level4') AS level4, \n"
                + "  cast(get_json_object(subTaskList, '$.uin') as bigint) AS uin, \n"
                + "  get_json_object(subTaskList, '$.customerName') AS customername, \n"
                + "  get_json_object(subTaskList, '$.customerAppId') AS customerappid, \n"
                + "  get_json_object(subTaskList, '$.priority') AS priority, \n"
                + "  get_json_object(subTaskList, '$.industry') AS industry, \n"
                + "  get_json_object(subTaskList, '$.region') AS region, \n"
                + "  get_json_object(subTaskList, '$.afterSaleSupport') AS aftersalesupport, \n"
                + "  get_json_object(subTaskList, '$.business') AS business, \n"
                + "  get_json_object(subTaskList, '$.architect') AS architect, \n"
                + "  get_json_object(subTaskList, '$.creator') AS creator, \n"
                + "  get_json_object(subTaskList, '$.handler') AS handler, \n"
                + "  get_json_object(subTaskList, '$.comment') AS `comment`, \n"
                + "  get_json_object(subTaskList, '$.createTime') AS createtime, \n"
                + "  get_json_object(subTaskList, '$.completeTime') AS completetime, \n"
                + "  cast(get_json_object(subTaskList, '$.status') as int) AS status, \n"
                + "  cast(get_json_object(subTaskList, '$.accept') as int) AS accept, \n"
                + "  get_json_object(subTaskList, '$.describe') AS `describe`, \n"
                + "  get_json_object(subTaskList, '$.taskTag') AS task_tag, \n"
                + "  get_json_object(subTaskList, '$.toDoProgress') AS to_do_progress, \n"
                + "  get_json_object(subTaskList, '$.meetingLocation') AS meeting_location, \n"
                + "  get_json_object(subTaskList, '$.meetingWhen') AS meeting_when, \n"
                + "  get_json_object(subTaskList, '$.tencentPeople') AS tencent_people, \n"
                + "  get_json_object(subTaskList, '$.customerPeople') AS customer_people, \n"
                + "  get_json_object(subTaskList, '$.meetingTitle') AS meeting_title, \n"
                + "  get_json_object(subTaskList, '$.meetingWhat') AS meeting_what, \n"
                + "  get_json_object(subTaskList, '$.meetingResult') AS meeting_result, \n"
                + "  get_json_object(subTaskList, '$.risk') AS risk, \n"
                + "  get_json_object(subTaskList, '$.logs') AS logs, \n"
                + "  get_json_object(subTaskList, '$.startTime') AS start_time, \n"
                + "  cast(get_json_object(subTaskList, '$.deleted') as int) AS deleted, \n"
                + "  get_json_object(subTaskList, '$.moreProgress') AS more_progress, \n"
                + "  get_json_object(subTaskList, '$.businessId') AS business_id, \n"
                + "  get_json_object(subTaskList, '$.url') AS url, \n"
                + "  get_json_object(subTaskList, '$.reportLink') AS report_link, \n"
                + "  get_json_object(subTaskList, '$.OfficialShow') AS official_show \n"
            + "FROM dwd_visit_var_info_view, "
                + "LATERAL TABLE (get_string_to_mulcol(`sub_task_list`)) AS t(subTaskList)\n"
                + "WHERE sub_task_list IS NOT NULL AND sub_task_list <>''\n"
            + ") as t";
}