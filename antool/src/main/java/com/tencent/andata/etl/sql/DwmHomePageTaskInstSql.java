package com.tencent.andata.etl.sql;

public class DwmHomePageTaskInstSql {

    public static final String QUERY_DWD_MAINTENANCE_TASK_INST_SQL = ""
            + "SELECT\n"
            + "  id,\n"
            + "  proc_inst_id,\n"
            + "  '维保激活' as data_type,\n"
            + "  task_def_key,\n"
            + "  name as task_name,\n"
            + "  assignee as task_assignee,\n"
            + "  start_time as task_start_time,\n"
            + "  end_time as task_end_time,\n"
            + "  duration as task_duration,\n"
            + "  delete_reason,\n"
            + "  tenant_id,\n"
            + "  0 AS deleted\n"
            + "FROM dwd_maintenance_activation_task_inst_rt_view\n";

    public static final String QUERY_DWD_VISIT_TASK_INST_SQL = ""
            + "SELECT\n"
            + "  id,\n"
            + "  proc_inst_id,\n"
            + "  '服务主任务' as data_type,\n"
            + "  task_def_key,\n"
            + "  name as task_name,\n"
            + "  assignee as task_assignee,\n"
            + "  start_time as task_start_time,\n"
            + "  end_time as task_end_time,\n"
            + "  duration as task_duration,\n"
            + "  delete_reason,\n"
            + "  tenant_id,\n"
            + "  0 AS deleted\n"
            + "FROM dwd_visit_task_inst_rt_view\n";

    public static final String QUERY_DWD_VISIT_SUBTASK_TASK_INST_SQL = ""
            + "SELECT \n"
            + "  concat(value_of_primary_key,'_',action,'_',operator_time) as id,\n"
            + "  proc_inst_id,\n"
            + "  '服务子任务' as data_type,\n"
            + "  '' as task_def_key,\n"
            + "  action as task_name,\n"
            + "  operator as task_assignee,\n"
            + "  cast(operator_time as TIMESTAMP) as task_start_time,\n"
            + "  cast(operator_time as TIMESTAMP) as task_end_time,\n"
            + "  0 as task_duration,\n"
            + "  '' as delete_reason,\n"
            + "  '' as tenant_id,\n"
            + "  CASE WHEN deleted IS NULL THEN 0 ELSE deleted END AS deleted\n"
            + "FROM(\n"
            + "  SELECT \n"
            + "  value_of_primary_key,\n"
            + "  proc_inst_id,\n"
            + "  subtaskid,\n"
            + "  get_json_object(logsList,'$.action') AS  action,\n"
            + "  get_json_object(logsList,'$.comment') AS  `comment`,\n"
            + "  get_json_object(logsList,'$.content') AS  content,\n"
            + "  get_json_object(logsList,'$.operator') AS  operator,\n"
            + "  get_json_object(logsList,'$.operatorTime') AS operator_time,\n"
            + "  deleted\n"
            + "FROM\n"
            + "(SELECT\n"
            + "  concat(proc_inst_id,'_',get_json_object(subTaskList, '$.subTaskId')) as value_of_primary_key,\n"
            + "  proc_inst_id,\n"
            + "  get_json_object(subTaskList, '$.subTaskId') AS  subtaskid, \n"
            + "  get_json_object(subTaskList, '$.logs') AS logs, \n"
            + "  cast(get_json_object(subTaskList, '$.deleted') as int) AS deleted \n"
            + "FROM dwd_visit_var_info_view, "
            + "LATERAL TABLE (get_string_to_mulcol(`sub_task_list`)) AS t(subTaskList)\n"
            + "WHERE sub_task_list IS NOT NULL AND sub_task_list <>''"
            + ") as a, LATERAL TABLE (get_string_to_mulcol(`logs`)) AS t(logsList)\n"
            + ") as t ";
    public static final String QUERY_DWD_CHANGE_TASK_INST_SQL = ""
            + "SELECT\n"
            + "  id,\n"
            + "  proc_inst_id,\n"
            + "  '软件更新' as data_type,\n"
            + "  task_def_key,\n"
            + "  name as task_name,\n"
            + "  assignee as task_assignee,\n"
            + "  start_time as task_start_time,\n"
            + "  end_time as task_end_time,\n"
            + "  duration as task_duration,\n"
            + "  delete_reason,\n"
            + "  tenant_id,\n"
            + "  0 AS deleted\n"
            + "FROM dwd_change_task_inst_rt_view\n";
}
