package com.tencent.andata.etl.sql;

public class DwmChangeBaseInfoSql {

    public static final String QUERY_DWD_CHANGE_INFO_SQL = ""
            + "WITH users as (\n"
            + "    select\n"
            + "        c.name,\n"
            + "        u.user_name,\n"
            + "        u.id\n"
            + "    from dim_antool_t_users_rt_view u\n"
            + "    left join dim_antool_t_companies_rt_view as c on u.company_id = c.id\n"
            + ")\n"
            + "SELECT\n"
            + "  t.proc_inst_id,\n"
            + "  t.start_time as create_time,\n"
            + "  t.end_time,\n"
            + "  t1.change_id,\n"
            + "  t1.uin,\n"
            + "  t1.j_stage,\n"
            + "  t1.is_verified,\n"
            + "  t1.unverified_reason,\n"
            + "  t1.inspect_report_1,\n"
            + "  t1.inspect_report_2,\n"
            + "  t1.inspect_report_1_info,\n"
            + "  t1.inspect_report_2_info,\n"
            + "  t1.product_name,\n"
            + "  t1.product_list_second,\n"
            + "  t1.channel,\n"
            + "  t1.state,\n"
            + "  t1.source,\n"
            + "  t1.title,\n"
            + "  t1.c_type,\n"
            + "  t1.tapd_url,\n"
            + "  t1.materials_url,\n"
            + "  t1.emergency_level,\n"
            + "  t1.priority,\n"
            + "  t1.risk,\n"
            + "  t1.impact,\n"
            + "  t1.key_component,\n"
            + "  t1.impact_scope,\n"
            + "  t1.request_impact,\n"
            + "  t1.net_break,\n"
            + "  t1.business_break,\n"
            + "  t1.description,\n"
            + "  t1.initiator,\n"
            + "  t1.plan_range,\n"
            + "  t1.real_range,\n"
            + "  t1.authorization_credential,\n"
            + "  t1.plan_execute_corp,\n"
            + "  t1.plan_verify_corp,\n"
            + "  t1.plan_start,\n"
            + "  t1.plan_end,\n"
            + "  t1.real_execute_corp,\n"
            + "  t1.real_verify_corp,\n"
            + "  t1.real_start,\n"
            + "  t1.real_end,\n"
            + "  t1.plan_execute_corp_company,\n"
            + "  t1.plan_verify_corp_company,\n"
            + "  t1.real_execute_corp_company,\n"
            + "  t1.real_verify_corp_company,\n"
            + "  t1.deploy_mode,\n"
            + "  CASE WHEN t1.priority = '' THEN NULL ELSE cast(t1.priority as double) END AS priority_num,\n"
            + "  t1.phase,\n"
            + "  CASE WHEN COALESCE(t1.product_list_second, '') = '' "
            + "  OR COALESCE(t1.product_list_second, '[]') = '[]' THEN ''"
            + "  ELSE get_distinct_string(t1.product_list_second,';') end as vertical_product_array,\n"
            + "  t2.current_product_version_array,\n"
            + "  t2.target_small_product_version_array,\n"
            + "  t2.ctrl_vertical_product_array,\n"
            + "  t3.ltc_no,\n"
            + "  t3.ltc_name,\n"
            + "  t3.customer_name,\n"
            + "  t3.customer_cid,\n"
            + "  t3.judian_name,\n"
            + "  t3.jid,\n"
            + "  t3.server_provider,\n"
            + "  t3.region,\n"
            + "  t3.department,\n"
            + "  t3.project_trade,\n"
            + "  t3.j_status,\n"
            + "  t3.product_version,\n"
            + "  t3.product_name as project_product_name,\n"
            + "  t3.groupid,\n"
            + "  t4.current_assign,\n"
            + "  t4.current_node_name,\n"
            + "  t4.current_task_def_key,\n"
            + "  t4.current_assign_rtx,\n"
            + "  get_array_to_string(t1.plan_range,0) AS plan_start_pro,\n"
            + "  get_array_to_string(t1.plan_range,1) AS plan_end_pro,\n"
            + "  CASE\n"
            + "        WHEN COALESCE(u1.user_name, '') <> '' and COALESCE(u1.name, '') <> ''\n"
            + "      THEN CONCAT(u1.user_name, '(', COALESCE(u1.name, ''), ')')\n"
            + "  ELSE t1.plan_execute_corp END AS plan_execute_corp_name,\n"
            + "  CASE\n"
            + "        WHEN COALESCE(u2.user_name, '') <> '' and COALESCE(u2.name, '') <> ''\n"
            + "      THEN CONCAT(u2.user_name, '(', COALESCE(u2.name, ''), ')')\n"
            + "  ELSE t1.plan_verify_corp END AS plan_verify_corp_name,\n"
            + "  t1.trade,\n"
            + "  t1.ticket_url_list\n"
            + "FROM dwd_change_proc_inst_view as t \n"
            + "left join dwd_change_var_info_view as t1 \n"
            + "on t.proc_inst_id=t1.proc_inst_id and t1.is_invalid IS NULL\n"
            + "left join (\n"
            + "  SELECT\n"
            + "    proc_inst_id,\n"
            + "    LISTAGG(DISTINCT current_product_version,';') as current_product_version_array,\n"
            + "    LISTAGG(DISTINCT target_small_product_version,';') as target_small_product_version_array, \n"
            + "    LISTAGG(DISTINCT vertical_product,';') as ctrl_vertical_product_array \n"
            + "  FROM dwd_change_ctrl_table_rt_view\n"
            + "  group by proc_inst_id\n"
            + ") as t2 on t2.proc_inst_id=t.proc_inst_id\n"
            + "left join (\n"
            + "  select *,row_number() over(partition by proc_inst_id order by update_time desc) as rn \n"
            + "  FROM dwd_change_project_info_rt_view\n"
            + ") as t3 on t.proc_inst_id=t3.proc_inst_id and t3.rn=1\n"
            + "left join users as u1 on t1.plan_execute_corp = u1.id\n"
            + "left join users as u2 on t1.plan_verify_corp = u2.id\n"
            + "left join ( \n"
            + "   SELECT \n"
            + "     t.proc_inst_id,\n"
            + "     LISTAGG(DISTINCT CASE WHEN u.user_name is not null and u.name is not null then "
            + "     concat(u.user_name,'(',u.name,')') when u.user_name is not null then "
            + "     u.user_name else t.assignee end,';') AS current_assign,\n"
            + "     max(t.name) as current_node_name, \n"
            + "     max(t.task_def_key) as current_task_def_key, \n"
            + "     LISTAGG(DISTINCT case when u.user_name is not null then u.user_name "
            + "     else t.assignee end,';') AS current_assign_rtx\n"
            + "   FROM dwd_change_task_inst_rt_view as t \n"
            + "   left join users as u on t.assignee = u.id \n"
            + "   WHERE t.end_time is null \n"
            + "   group by t.proc_inst_id\n"
            + " ) as t4 on t.proc_inst_id=t4.proc_inst_id \n"
            + "where t.delete_reason is null";

    public static final String QUERY_DWD_CHANGE_PROC_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id,\n"
            + "  start_time as create_time,\n"
            + "  end_time\n"
            + "FROM pgsql_source_dwd_change_proc_inst\n";

    public static final String QUERY_DWD_CHANGE_VAR_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id,\n"
            + "  change_id,\n"
            + "  uin,\n"
            + "  j_stage,\n"
            + "  is_verified,\n"
            + "  unverified_reason,\n"
            + "  inspect_report_1,\n"
            + "  inspect_report_2,\n"
            + "  inspect_report_1_info,\n"
            + "  inspect_report_2_info,\n"
            + "  product_name,\n"
            + "  product_list_second,\n"
            + "  channel,\n"
            + "  state,\n"
            + "  source,\n"
            + "  title,\n"
            + "  c_type,\n"
            + "  tapd_url,\n"
            + "  materials_url,\n"
            + "  emergency_level,\n"
            + "  priority,\n"
            + "  risk,\n"
            + "  impact,\n"
            + "  key_component,\n"
            + "  impact_scope,\n"
            + "  request_impact,\n"
            + "  net_break,\n"
            + "  business_break,\n"
            + "  description,\n"
            + "  initiator,\n"
            + "  plan_range,\n"
            + "  real_range,\n"
            + "  authorization_credential,\n"
            + "  plan_execute_corp,\n"
            + "  plan_verify_corp,\n"
            + "  plan_start,\n"
            + "  plan_end,\n"
            + "  real_execute_corp,\n"
            + "  real_verify_corp,\n"
            + "  real_start,\n"
            + "  real_end,\n"
            + "  plan_execute_corp_company,\n"
            + "  plan_verify_corp_company,\n"
            + "  real_execute_corp_company,\n"
            + "  real_verify_corp_company,\n"
            + "  deploy_mode,\n"
            + "  CASE WHEN priority = '' THEN NULL ELSE cast(priority as double) END AS priority_num,\n"
            + "  phase,\n"
            + "  CASE WHEN COALESCE(product_list_second, '') = '' OR COALESCE(product_list_second, '[]') = '[]' THEN ''"
            + "  ELSE get_distinct_string(product_list_second,';') end as vertical_product_array\n"
            + "FROM pgsql_source_dwd_change_var_info\n"
            + "WHERE is_invalid IS NULL\n";

    public static final String QUERY_DWD_CHANGE_PROJECT_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id,\n"
            + "  ltc_no,\n"
            + "  ltc_name,\n"
            + "  customer_name,\n"
            + "  customer_cid,\n"
            + "  judian_name,\n"
            + "  jid,\n"
            + "  server_provider,\n"
            + "  region,\n"
            + "  department,\n"
            + "  project_trade,\n"
            + "  j_status,\n"
            + "  product_version,\n"
            + "  product_name as project_product_name,\n"
            + "  groupid\n"
            + "  from (select *,row_number() over(partition by proc_inst_id order by update_time desc) as rn "
            + "  FROM pgsql_source_dwd_change_project) as t\n"
            + "  where rn=1\n";

    public static final String QUERY_DWD_CHANGE_CTRL_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id,\n"
            + "  LISTAGG(DISTINCT current_product_version,';') as current_product_version_array,\n"
            + "  LISTAGG(DISTINCT target_small_product_version,';') as target_small_product_version_array, \n"
            + "  LISTAGG(DISTINCT vertical_product,';') as ctrl_vertical_product_array \n"
            + "FROM pgsql_source_dwd_change_ctrl\n"
            + "group by proc_inst_id\n";

    public static final String QUERY_DWD_CHANGE_TASK_SQL = ""
            + "SELECT \n"
            + "t.proc_inst_id,\n"
            + "LISTAGG(DISTINCT CASE WHEN u.user_name is not null and c.name is not null then "
            + "concat(u.user_name,'(',c.name,')') when u.user_name is not null then "
            + "u.user_name else t.assignee end,';') AS current_assign,\n"
            + "max(t.name) as current_node_name, \n"
            + "max(t.task_def_key) as current_task_def_key, \n"
            + "LISTAGG(DISTINCT case when u.user_name is not null then u.user_name "
            + "else t.assignee end,';') AS current_assign_rtx\n"
            + "FROM pgsql_source_dwd_change_task_inst_rt as t \n"
            + "left join pgsql_source_dim_antool_t_users_rt as u on t.assignee = u.id \n"
            + "left join pgsql_source_dim_antool_t_companies_rt as c on u.company_id = c.id\n"
            + "WHERE t.end_time is null \n"
            + "group by t.proc_inst_id\n";
}
