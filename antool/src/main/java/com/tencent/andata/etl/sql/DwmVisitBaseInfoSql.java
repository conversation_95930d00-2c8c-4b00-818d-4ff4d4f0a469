package com.tencent.andata.etl.sql;

public class DwmVisitBaseInfoSql {

    public static final String QUERY_DWD_VISIT_INFO_SQL = ""
            + "SELECT\n"
            + "  t.proc_inst_id,\n"
            + "  t.start_time as create_time,\n"
            + "  t.end_time,\n"
            + "  case when t.delete_reason is not null then 3 when t.end_time is not null then 2 "
            + "  else 1 end as status,\n"
            + "  t1.proc_inst_id,\n"
            + "  t1.visit_id,\n"
            + "  case when t1.visit_order_type = '2' then t1.uins else t1.customer_uin end as customer_uin,\n"
            + "  t1.customer_name,\n"
            + "  t1.ltc_no,\n"
            + "  t1.project_name,\n"
            + "  t1.plan_visit_time,\n"
            + "  t1.initiator,\n"
            + "  t1.visit_order_type,\n"
            + "  CASE WHEN COALESCE(t1.visit_type,'')='' or t1.visit_type='27' THEN '-1' "
            + "  ELSE t1.visit_type END AS visit_type,\n"
            + "  t1.visit_second_type,\n"
            + "  t1.visit_resion,\n"
            + "  t1.sensitive_reason,\n"
            //+ "  t1.big_title,\n"
            + "  case when COALESCE(t1.big_title, '')<> '' then t1.big_title ELSE t1.title END AS big_title,"
            + "  case when COALESCE(t1.visit_type, '')<> '' and COALESCE(t1.visit_second_type, '')<> '' "
            + "  then CONCAT(t1.visit_type,'-',t1.visit_second_type) "
            + "  ELSE COALESCE(t1.visit_type, '') END AS visit_types,\n"
            + "  t1.visit_category,\n"
            //+ "  t1.plan_participants,\n"
            + "  case when COALESCE(t1.plan_participants, '')<> '' then t1.plan_participants "
            + "  ELSE t1.new_plan_participants END AS plan_participants,"
            + "  t1.custom,\n"
            + "  t1.problem,\n"
            + "  t1.task_desc,\n"
            + "  t1.to_do_list,\n"
            + "  t1.jielun,\n"
            + "  t1.sub_task_list,\n"
            //+ "  t1.tencent_participants,\n"
            + "  case when COALESCE(t1.tencent_participants, '')<> '' then t1.tencent_participants "
            + "  ELSE t1.new_tencent_participants END AS tencent_participants,"
            + "  CASE WHEN COALESCE(t1.log_id, '') <> '' THEN 1 else 0 end AS is_send,\n"
            + "  t1.chat_id,\n"
            + "  t1.submit_action, \n"
            + "  t1.current_activity, \n"
            + "  t1.visit_state, \n"
            + "  t1.creator, \n"
            + "  t1.trade, \n"
            + "  t1.spmo, \n"
            + "  t1.pms_pre_salesa_id, \n"
            + "  t1.sales_manager_id, \n"
            + "  t1.deliver_pm_name, \n"
            + "  t1.visit_product, \n"
            + "  t1.visit_area, \n"
            + "  t1.activity_kubxnu, \n"
            + "  t1.activity_lwcifu, \n"
            + "  t1.activity_t6ha6u, \n"
            + "  t1.activity_lzhkr, \n"
            + "  t1.meeting_topic, \n"
            + "  t1.meeting_agenda, \n"
            + "  t1.meeting_time, \n"
            + "  t1.meeting_address, \n"
            + "  t1.customer_participants, \n"
            + "  t1.table_visit_id, \n"
            + "  t1.email_type, \n"
            + "  t1.formal_email1, \n"
            + "  t1.formal_email3, \n"
            + "  t1.formal_email2, \n"
            + "  t1.formal_email4, \n"
            + "  t1.test_email, \n"
            + "  t1.region, \n"
            + "  t1.server_provider, \n"
            + "  t1.url, \n"
            + "  t1.associated_visits, \n"
            + "  t1.asp_uin, \n"
            + "  t1.app_id, \n"
            + "  t1.what_task, \n"
            + "  t1.meeting_location, \n"
            + "  t1.meeting_when, \n"
            + "  t1.customer_people, \n"
            + "  t1.tencent_people, \n"
            + "  t1.meeting_title, \n"
            + "  t1.meeting_what, \n"
            + "  t1.file_upload, \n"
            + "  t2.current_assign,\n"
            + "  t2.current_node_name,\n"
            + "  t2.current_task_def_key,\n"
            + "  t1.meeting_file, \n"
            + "  t1.asp_meeting_result, \n"
            + "  t1.email_content, \n"

            + "  t1.source_channel, \n"
            + "  t1.opportunity_id, \n"
            + "  t1.task_source, \n"
            + "  t1.plan_visit_end_time, \n"
            + "  CASE WHEN t1.official_show IS NULL OR t1.official_show = '' THEN '1' ELSE t1.official_show END AS official_show,\n"
            + "  CASE WHEN t1.has_send IS NULL OR t1.has_send = '' THEN '2' ELSE t1.has_send END AS has_send\n"
            + "FROM dwd_visit_proc_inst_view as t\n"
            + "left join dwd_visit_var_info_view as t1 on t.proc_inst_id=t1.proc_inst_id\n"
            + "left join ( \n"
            + "  SELECT \n"
            + "    t.proc_inst_id,\n"
            + "    LISTAGG(DISTINCT CASE WHEN u.user_name is not null and c.name is not null then "
            + "    concat(u.user_name,'(',c.name,')') when u.user_name is not null then "
            + "    u.user_name else t.assignee end,';') AS current_assign,\n"
            + "    max(t.name) as current_node_name, \n"
            + "    max(t.task_def_key) as current_task_def_key \n"
            + "  FROM dwd_visit_task_inst_rt_view as t \n"
            + "  left join dim_antool_t_users_rt_view as u on t.assignee = u.id \n"
            + "  left join dim_antool_t_companies_rt_view as c on u.company_id = c.id\n"
            + "  WHERE t.end_time is null \n"
            + "  group by t.proc_inst_id\n"
            + " ) as t2 on t.proc_inst_id=t2.proc_inst_id \n";

    public static final String QUERY_DWD_VISIT_PROC_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id,\n"
            + "  start_time as create_time,\n"
            + "  end_time,\n"
            + "  case when delete_reason is not null then 3 when end_time is not null then 2 else 1 end as status\n"
            + "FROM pgsql_source_dwd_visit_proc_inst\n";

    public static final String QUERY_DWD_VISIT_VAR_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id,\n"
            + "  visit_id,\n"
            + "  case when visit_order_type = '2' then uins else customer_uin end as customer_uin,\n"
            + "  customer_name,\n"
            + "  ltc_no,\n"
            + "  project_name,\n"
            + "  plan_visit_time,\n"
            + "  initiator,\n"
            + "  visit_order_type,\n"
            + "  CASE WHEN COALESCE(visit_type,'')='' or visit_type='27' THEN '-1' ELSE visit_type END AS visit_type,\n"
            + "  visit_second_type,\n"
            + "  visit_resion,\n"
            + "  sensitive_reason,\n"
            + "  big_title,\n"
            + "  case when COALESCE(visit_type, '')<> '' and COALESCE(visit_second_type, '')<> '' "
            + "  then CONCAT(visit_type,'-',visit_second_type) ELSE COALESCE(visit_type, '') END AS visit_types,\n"
            + "  visit_category,\n"
            + "  plan_participants,\n"
            + "  custom,\n"
            + "  problem,\n"
            + "  task_desc,\n"
            + "  to_do_list,\n"
            + "  jielun,\n"
            + "  sub_task_list,\n"
            + "  tencent_participants,\n"
            + "  CASE WHEN COALESCE(log_id, '') <> '' THEN 1 else 0 end AS is_send,\n"
            + "  chat_id,\n"
            + "  submit_action, \n"
            + "  current_activity, \n"
            + "  visit_state, \n"
            + "  creator, \n"
            + "  trade, \n"
            + "  spmo, \n"
            + "  pms_pre_salesa_id, \n"
            + "  sales_manager_id, \n"
            + "  deliver_pm_name, \n"
            + "  visit_product, \n"
            + "  visit_area, \n"
            + "  activity_kubxnu, \n"
            + "  activity_lwcifu, \n"
            + "  activity_t6ha6u, \n"
            + "  activity_lzhkr, \n"
            + "  meeting_topic, \n"
            + "  meeting_agenda, \n"
            + "  meeting_time, \n"
            + "  meeting_address, \n"
            + "  customer_participants, \n"
            + "  table_visit_id, \n"
            + "  email_type, \n"
            + "  formal_email1, \n"
            + "  formal_email3, \n"
            + "  formal_email2, \n"
            + "  formal_email4, \n"
            + "  test_email, \n"
            + "  region, \n"
            + "  server_provider, \n"
            + "  url, \n"
            + "  associated_visits, \n"
            + "  asp_uin, \n"
            + "  app_id, \n"
            + "  what_task, \n"
            + "  meeting_location, \n"
            + "  meeting_when, \n"
            + "  customer_people, \n"
            + "  tencent_people, \n"
            + "  meeting_title, \n"
            + "  meeting_what, \n"
            + "  file_upload \n"
            + "FROM pgsql_source_dwd_visit_var_info\n";

    public static final String QUERY_DWD_VISIT_TASK_SQL = ""
            + "SELECT \n"
            + "t.proc_inst_id,\n"
            + "LISTAGG(DISTINCT CASE WHEN u.user_name is not null and c.name is not null then "
            + "concat(u.user_name,'(',c.name,')') when u.user_name is not null then "
            + "u.user_name else t.assignee end,';') AS current_assign,\n"
            + "max(t.name) as current_node_name, \n"
            + "max(t.task_def_key) as current_task_def_key \n"
            + "FROM pgsql_source_dwd_visit_task_inst_rt as t \n"
            + "left join pgsql_source_dim_antool_t_users_rt as u on t.assignee = u.id \n"
            + "left join pgsql_source_dim_antool_t_companies_rt as c on u.company_id = c.id\n"
            + "WHERE t.end_time is null \n"
            + "group by t.proc_inst_id\n";
}