package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.sql.DwmHomePageTaskLogSql;
import com.tencent.andata.etl.tablemap.DwmHomePageTaskLogMapping;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;


@Builder
public class DwmHomePageTaskLog {
    private static final Logger LOG = LoggerFactory.getLogger(DwmHomePageTaskLog.class);
    private final String icebergDbName;
    private final String pgDbName;

    private static String getInsertStatement(String pgSinkTableName) {
        String insertSql = "";
        switch (pgSinkTableName) {
            case "pg_sink_dwm_home_page_maintenance_task_log":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `data_type`,"
                        + "     `proc_inst_id`,"
                        + "     `task_id`,"
                        + "     `task_def_key`,"
                        + "     `task_name`,"
                        + "     `action`,"
                        + "     `submit_action`,"
                        + "     `operator_time`,"
                        + "     `operator`,"
                        + "     `operator_company_name`,"
                        + "     `comment`"
                        + "FROM %s", "maintenance_task_log_view");
                break;
            case "pg_sink_dwm_home_page_visit_task_log":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `data_type`,"
                        + "     `proc_inst_id`,"
                        + "     `task_id`,"
                        + "     `task_def_key`,"
                        + "     `task_name`,"
                        + "     `action`,"
                        + "     `submit_action`,"
                        + "     `operator_time`,"
                        + "     `operator`,"
                        + "     `operator_company_name`,"
                        + "     `comment`"
                        + "FROM %s", "visit_task_log_view");
                break;
            case "pg_sink_dwm_home_page_sub_task_task_log":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `data_type`,"
                        + "     `proc_inst_id`,"
                        + "     `task_id`,"
                        + "     `task_def_key`,"
                        + "     `task_name`,"
                        + "     `action`,"
                        + "     `submit_action`,"
                        + "     `operator_time`,"
                        + "     `operator`,"
                        + "     `operator_company_name`,"
                        + "     `comment`,"
                        + "     `content`"
                        + "FROM %s", "visit_sub_task_task_log_view");
                break;
            case "pg_sink_dwm_home_page_change_task_log":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `data_type`,"
                        + "     `proc_inst_id`,"
                        + "     `task_id`,"
                        + "     `task_def_key`,"
                        + "     `task_name`,"
                        + "     `action`,"
                        + "     `submit_action`,"
                        + "     `operator_time`,"
                        + "     `operator`,"
                        + "     `operator_company_name`,"
                        + "     `comment`"
                        + "FROM %s", "change_task_log_view");
                break;
            default:
                insertSql = "";
                break;
        }
        return insertSql;
    }

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", pgDbName))
                .build();

        final DatabaseConf cdcPgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // pgsql table mapping to flink table
        ArrayNode pgsqlSourceTable2FlinkTableMap = mapper.readValue(
                DwmHomePageTaskLogMapping.pgsqlSourceTaskLogTable2FlinkTable, ArrayNode.class);
        buildSourceView(cdcPgDBConf, flinkEnv, pgsqlSourceTable2FlinkTableMap, PGSQL, parameterTool);
        // TableUtils.pgdbTable2FlinkTable(cdcPgDBConf, pgsqlSourceTable2FlinkTableMap, PGSQL, tEnv,"source");

        ArrayNode pgsqlSinkTable2FlinkTableMap = mapper.readValue(
                DwmHomePageTaskLogMapping.pgsqlSinkTaskLogTable2FlinkTable, ArrayNode.class);
        TableUtils.pgdbTable2FlinkTable(pgDBConf, pgsqlSinkTable2FlinkTableMap, PGSQL, tEnv,"sink");

        Table maintenanceTab = tEnv.sqlQuery(DwmHomePageTaskLogSql.QUERY_DWD_MAINTENANCE_TASK_LOG_SQL);
        Table visitTab = tEnv.sqlQuery(DwmHomePageTaskLogSql.QUERY_DWD_VISIT_TASK_LOG_SQL);
        Table subTaskTab = tEnv.sqlQuery(DwmHomePageTaskLogSql.QUERY_DWD_VISIT_SUBTASK_TASK_LOG_SQL);
        Table changeTab = tEnv.sqlQuery(DwmHomePageTaskLogSql.QUERY_DWD_CHANGE_TASK_LOG_SQL);

        // 创建view
        tEnv.createTemporaryView("maintenance_task_log_view", maintenanceTab);
        tEnv.createTemporaryView("visit_task_log_view", visitTab);
        tEnv.createTemporaryView("visit_sub_task_task_log_view", subTaskTab);
        tEnv.createTemporaryView("change_task_log_view", changeTab);

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(getInsertStatement("pg_sink_dwm_home_page_maintenance_task_log"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_home_page_visit_task_log"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_home_page_sub_task_task_log"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_home_page_change_task_log"));
    }
}
