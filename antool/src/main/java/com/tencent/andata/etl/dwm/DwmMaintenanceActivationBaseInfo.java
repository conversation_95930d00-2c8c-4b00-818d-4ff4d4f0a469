package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.sql.DwmChangeBaseInfoSql;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import com.tencent.andata.etl.sql.DwmMaintenanceActivationBaseInfoSql;
import com.tencent.andata.etl.tablemap.DwmMaintenanceActivationBaseInfoMapping;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;


@Builder
public class DwmMaintenanceActivationBaseInfo {
    private static final Logger LOG = LoggerFactory.getLogger(DwmMaintenanceActivationBaseInfo.class);
    private final String icebergDbName;
    private final String pgDbName;

    private static String getInsertStatement(String pgSinkTableName) {
        String insertSql = "";
        switch (pgSinkTableName) {
            case "pg_sink_dwm_maintenance_activation_proc":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `proc_inst_id`,"
                        + "     `create_time`, "
                        + "     `end_time`"
                        + "FROM %s", "maintenance_activation_proc_inst_view");
                break;
            case "pg_sink_dwm_maintenance_activation_var":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `proc_inst_id`,"
                        + "     `sid`,"
                        + "     `ltc_no`,"
                        + "     `service_type`,"
                        + "     `service_scene`,"
                        + "     `status`,"
                        + "     `initiator`,"
                        + "     `product`,"
                        + "     `server_time_inner`,"
                        + "     `server_time_outer`,"
                        + "     `poid_list`,"
                        + "     `judian_ids`"
                        + "FROM %s", "maintenance_activation_var_view");
                break;
            case "pg_sink_dwm_maintenance_activation_task":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `proc_inst_id`,"
                        + "     `current_assign`, "
                        + "     `current_node_name`,"
                        + "     `current_task_def_key`"
                        + "FROM %s", "maintenance_activation_task_view");
                break;
            case "pg_sink_dwm_maintenance_activation_judian":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `proc_inst_id`,"
                        + "     `customer_name`,"
                        + "     `customer_uin`,"
                        + "     `trade`,"
                        + "     `region`,"
                        + "     `project_name`"
                        + "FROM %s", "maintenance_activation_judian_view");
                break;
            default:
                insertSql = "";
                break;
        }
        return insertSql;
    }

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", pgDbName))
                .build();

        final DatabaseConf cdcPgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // pgsql table mapping to flink table
        ArrayNode pgsqlSourceTable2FlinkTableMap = mapper.readValue(
                DwmMaintenanceActivationBaseInfoMapping.pgsqlSourceMaintenanceTable2FlinkTable, ArrayNode.class);
        buildSourceView(cdcPgDBConf, flinkEnv, pgsqlSourceTable2FlinkTableMap, PGSQL, parameterTool);
        // TableUtils.pgdbTable2FlinkTable(cdcPgDBConf, pgsqlSourceTable2FlinkTableMap, PGSQL, tEnv,"source");

        ArrayNode pgsqlSinkTable2FlinkTableMap = mapper.readValue(
                DwmMaintenanceActivationBaseInfoMapping.pgsqlSinkMaintenance2FlinkTable, ArrayNode.class);
        //TableUtils.pgdbTable2FlinkTable(pgDBConf, pgsqlSinkTable2FlinkTableMap, PGSQL, tEnv,"sink");
        TableUtils.rdbTable2FlinkTable(pgDBConf, pgsqlSinkTable2FlinkTableMap, PGSQL, tEnv);

        Table changeTable = tEnv.sqlQuery(DwmMaintenanceActivationBaseInfoSql.QUERY_DWD_MAINTENANCE_SQL);
        tEnv.createTemporaryView("dwm_maintenance_activation_view", changeTable);
        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                "dwm_maintenance_activation_view",
                "pg_sink_dwm_maintenance_activation",
                tEnv.from("pg_sink_dwm_maintenance_activation"),
                PGSQL
        ));

        /*Table procTab = tEnv.sqlQuery(DwmMaintenanceActivationBaseInfoSql.QUERY_DWD_MAINTENANCE_PROC_SQL);
        Table varTab = tEnv.sqlQuery(DwmMaintenanceActivationBaseInfoSql.QUERY_DWD_MAINTENANCE_VAR_SQL);
        Table taskTab = tEnv.sqlQuery(DwmMaintenanceActivationBaseInfoSql.QUERY_DWD_MAINTENANCE_TASK_SQL);
        Table judainTab = tEnv.sqlQuery(DwmMaintenanceActivationBaseInfoSql.QUERY_DWD_MAINTENANCE_JUDIAN_SQL);

        // 创建view
        tEnv.createTemporaryView("maintenance_activation_proc_inst_view", procTab);
        tEnv.createTemporaryView("maintenance_activation_var_view", varTab);
        tEnv.createTemporaryView("maintenance_activation_task_view", taskTab);
        tEnv.createTemporaryView("maintenance_activation_judian_view", judainTab);

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(getInsertStatement("pg_sink_dwm_maintenance_activation_proc"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_maintenance_activation_var"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_maintenance_activation_task"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_maintenance_activation_judian"));*/
    }
}
