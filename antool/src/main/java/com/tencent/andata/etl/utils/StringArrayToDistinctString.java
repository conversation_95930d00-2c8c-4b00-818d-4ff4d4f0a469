package com.tencent.andata.etl.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class StringArrayToDistinctString extends ScalarFunction {
    public String eval(String arrStr, String split) {
        if (StringUtils.isEmpty(arrStr)) {
            return "";
        }
        String cleanedInput = arrStr.replace("[", "").replace("]", "").replace("\"", "");
        // 使用 Set 去重
        Set<String> uniqueValues = new HashSet<>(Arrays.asList(cleanedInput.split(",")));
        if (StringUtils.isEmpty(split)) {
            split = ",";
        }
        return String.join(split, uniqueValues);
    }
}


