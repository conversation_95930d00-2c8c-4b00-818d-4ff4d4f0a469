package com.tencent.andata.etl.sql;

public class DwmHomePageTaskLogSql {

    public static final String QUERY_DWD_MAINTENANCE_TASK_LOG_SQL = ""
            + "SELECT\n"
            + "  cast(id as string) as id,\n"
            + "  '维保激活' as data_type,\n"
            + "  proc_inst_id,\n"
            + "  task_id,\n"
            + "  task_def_key,\n"
            + "  task_name,\n"
            + "  action,\n"
            + "  submit_action,\n"
            + "  operator_time,\n"
            + "  operator_id as operator,\n"
            + "  operator_company_name,\n"
            + "  approval_comment as `comment`,\n"
            + "  0 AS deleted\n"
            + "FROM dwd_maintenance_activation_t_task_log_view\n";

    public static final String QUERY_DWD_VISIT_TASK_LOG_SQL = ""
            + "SELECT\n"
            + "  cast(id as string) as id,\n"
            + "  '服务主任务' as data_type,\n"
            + "  proc_inst_id,\n"
            + "  task_id,\n"
            + "  task_def_key,\n"
            + "  task_name,\n"
            + "  action,\n"
            + "  submit_action,\n"
            + "  operator_time,\n"
            + "  operator_id as operator,\n"
            + "  operator_company_name,\n"
            + "  approval_comment as `comment`,\n"
            + "  0 AS deleted\n"
            + "FROM dwd_visit_t_task_log_view\n";

    public static final String QUERY_DWD_VISIT_SUBTASK_TASK_LOG_SQL = ""
            + "SELECT \n"
            + "  concat(value_of_primary_key,'_',action,'_',operator_time) as id,\n"
            + "  '服务子任务' as data_type,\n"
            + "  `proc_inst_id`,\n"
            + "  subtaskid as task_id,\n"
            + "  '' as task_def_key,\n"
            + "  action as task_name,\n"
            + "  action,\n"
            + "  '' as submit_action,\n"
            + "  cast(operator_time as TIMESTAMP) as operator_time,\n"
            + "  operator,\n"
            + "  '' as operator_company_name,\n"
            + "  `comment`,\n"
            + "  `content`,\n"
            + "  CASE WHEN deleted IS NULL THEN 0 ELSE deleted END AS deleted\n"
            + "FROM(\n"
            + "  SELECT \n"
            + "  value_of_primary_key,\n"
            + "  proc_inst_id,\n"
            + "  subtaskid,\n"
            + "  get_json_object(logsList,'$.action') AS  action,\n"
            + "  get_json_object(logsList,'$.comment') AS  `comment`,\n"
            + "  get_json_object(logsList,'$.content') AS  content,\n"
            + "  get_json_object(logsList,'$.operator') AS  operator,\n"
            + "  get_json_object(logsList,'$.operatorTime') AS operator_time,\n"
            + "  deleted\n"
            + "FROM\n"
            + "(SELECT\n"
            + "  concat(proc_inst_id,'_',get_json_object(subTaskList, '$.subTaskId')) as value_of_primary_key,\n"
            + "  proc_inst_id,\n"
            + "  get_json_object(subTaskList, '$.subTaskId') AS  subtaskid, \n"
            + "  get_json_object(subTaskList, '$.logs') AS logs, \n"
            + "  cast(get_json_object(subTaskList, '$.deleted') as int) AS deleted \n"
            + "FROM dwd_visit_var_info_view, "
            + "LATERAL TABLE (get_string_to_mulcol(`sub_task_list`)) AS t(subTaskList)\n"
            + "WHERE sub_task_list IS NOT NULL AND sub_task_list <>''"
            + ") as a, LATERAL TABLE (get_string_to_mulcol(`logs`)) AS t(logsList)\n"
            + ") as t";

    public static final String QUERY_DWD_CHANGE_TASK_LOG_SQL = ""
            + "SELECT\n"
            + "  cast(id as string) as id,\n"
            + "  '软件更新' as data_type,\n"
            + "  proc_inst_id,\n"
            + "  task_id,\n"
            + "  '' as task_def_key,\n"
            + "  node as task_name,\n"
            + "  action,\n"
            + "  '' as submit_action,\n"
            + "  create_time as operator_time,\n"
            + "  operator,\n"
            + "  '' as operator_company_name,\n"
            + "  `comment`,\n"
            + "  0 AS deleted\n"
            + "FROM dwd_change_operate_log_rt_view\n";
}
