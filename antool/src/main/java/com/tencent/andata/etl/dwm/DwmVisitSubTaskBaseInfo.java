package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.sql.DwmMaintenanceActivationBaseInfoSql;
import com.tencent.andata.etl.sql.DwmVisitSubTaskBaseInfoSql;
import com.tencent.andata.etl.tablemap.DwmVisitSubTaskBaseInfoMapping;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;


@Builder
public class DwmVisitSubTaskBaseInfo {
    private static final Logger LOG = LoggerFactory.getLogger(DwmVisitSubTaskBaseInfo.class);
    private final String icebergDbName;
    private final String pgDbName;

    private static String getInsertStatement() {
        return String.format("INSERT INTO " + "pg_sink_dwm_visit_sub_task_var"
                + " SELECT"
                + "  `value_of_primary_key`,"
                + "  `proc_inst_id`,"
                + "  `parent_visit_id`,"
                + "  `ltc_no`,"
                + "  `project_name`,"
                + "  `plan_visit_time`,"
                + "  `visit_order_type`,"
                + "  `subtaskid`,"
                + "  `subtaskname`,"
                + "  `subtasktype`,"
                + "  `subtasksource`,"
                + "  `level1`,"
                + "  `level2`,"
                + "  `level3`,"
                + "  `level4`,"
                + "  `uin`,"
                + "  `customername`,"
                + "  `customerappid`,"
                + "  `priority`,"
                + "  `industry`,"
                + "  `region`,"
                + "  `aftersalesupport`,"
                + "  `business`,"
                + "  `architect`,"
                + "  `creator`,"
                + "  `handler`,"
                + "  `comment`,"
                + "  `createtime`,"
                + "  `completetime`,"
                + "  `status`,"
                + "  `accept`,"
                + "  `describe`,"
                + "  `task_tag`,"
                + "  `to_do_progress`,"
                + "  `meeting_location`,"
                + "  `meeting_when`,"
                + "  `tencent_people`,"
                + "  `customer_people`,"
                + "  `meeting_title`,"
                + "  `meeting_what`,"
                + "  `meeting_result`,"
                + "  `risk`,"
                + "  `logs`,"
                + "  `current_assign`,"
                + "  `start_time`"
                + "FROM %s", "visit_sub_task_var_view");
    }

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        ArrayNode pgsqlSinkTable2FlinkTableMap = mapper.readValue(
                DwmVisitSubTaskBaseInfoMapping.pgsqlSinkSubVisit2FlinkTable, ArrayNode.class);
        //TableUtils.pgdbTable2FlinkTable(pgDBConf, pgsqlSinkTable2FlinkTableMap, PGSQL, tEnv,"sink");
        TableUtils.rdbTable2FlinkTable(pgDBConf, pgsqlSinkTable2FlinkTableMap, PGSQL, tEnv);

        Table varTab = tEnv.sqlQuery(DwmVisitSubTaskBaseInfoSql.QUERY_DWD_VISIT_SUB_TASK_VAR_SQL);

        // 创建view
        tEnv.createTemporaryView("visit_sub_task_var_view", varTab);

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                "visit_sub_task_var_view",
                "pg_sink_dwm_visit_sub_task_base_info",
                tEnv.from("pg_sink_dwm_visit_sub_task_base_info"),
                PGSQL
        ));
        /*StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(getInsertStatement());*/
    }
}