package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.sql.DwmChangeBaseInfoSql;
import com.tencent.andata.etl.sql.DwmVisitBaseInfoSql;
import com.tencent.andata.etl.tablemap.DwmVisitBaseInfoMapping;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;


@Builder
public class DwmVisitBaseInfo {
    private static final Logger LOG = LoggerFactory.getLogger(DwmVisitBaseInfo.class);
    private final String icebergDbName;
    private final String pgDbName;

    private static String getInsertStatement(String pgSinkTableName) {
        String insertSql = "";
        switch (pgSinkTableName) {
            case "pg_sink_dwm_visit_proc":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `proc_inst_id`,"
                        + "     `create_time`, "
                        + "     `end_time`,"
                        + "     `status`"
                        + "FROM %s", "visit_proc_inst_view");
                break;
            case "pg_sink_dwm_visit_var":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "  `proc_inst_id`,"
                        + "  `visit_id`,"
                        + "  `customer_uin`,"
                        + "  `customer_name`,"
                        + "  `ltc_no`,"
                        + "  `project_name`,"
                        + "  `plan_visit_time`,"
                        + "  `initiator`,"
                        + "  `visit_order_type`,"
                        + "  `visit_type`,"
                        + "  `visit_second_type`,"
                        + "  `visit_resion`,"
                        + "  `sensitive_reason`,"
                        + "  `big_title`,"
                        + "  `visit_types`,"
                        + "  `visit_category`,"
                        + "  `plan_participants`,"
                        + "  `custom`,"
                        + "  `problem`,"
                        + "  `task_desc`,"
                        + "  `to_do_list`,"
                        + "  `jielun`,"
                        + "  `sub_task_list`,"
                        + "  `tencent_participants`,"
                        + "  `is_send`,"
                        + "  `chat_id`,"
                        + "  `submit_action`,"
                        + "  `current_activity`,"
                        + "  `visit_state`,"
                        + "  `creator`,"
                        + "  `trade`,"
                        + "  `spmo`,"
                        + "  `pms_pre_salesa_id`,"
                        + "  `sales_manager_id`,"
                        + "  `deliver_pm_name`,"
                        + "  `visit_product`,"
                        + "  `visit_area`,"
                        + "  `activity_kubxnu`,"
                        + "  `activity_lwcifu`,"
                        + "  `activity_t6ha6u`,"
                        + "  `activity_lzhkr`,"
                        + "  `meeting_topic`,"
                        + "  `meeting_agenda`,"
                        + "  `meeting_time`,"
                        + "  `meeting_address`,"
                        + "  `customer_participants`,"
                        + "  `table_visit_id`,"
                        + "  `email_type`,"
                        + "  `formal_email1`,"
                        + "  `formal_email3`,"
                        + "  `formal_email2`,"
                        + "  `formal_email4`,"
                        + "  `test_email`,"
                        + "  `region`,"
                        + "  `server_provider`,"
                        + "  `url`,"
                        + "  `associated_visits`,"
                        + "  `asp_uin`,"
                        + "  `app_id`,"
                        + "  `what_task`,"
                        + "  `meeting_location`,"
                        + "  `meeting_when`,"
                        + "  `customer_people`,"
                        + "  `tencent_people`,"
                        + "  `meeting_title`,"
                        + "  `meeting_what`,"
                        + "  `file_upload`"
                        + "FROM %s", "visit_var_view");
                break;
            case "pg_sink_dwm_visit_task":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `proc_inst_id`,"
                        + "     `current_assign`, "
                        + "     `current_node_name`,"
                        + "     `current_task_def_key`"
                        + "FROM %s", "visit_task_view");
                break;
            default:
                insertSql = "";
                break;
        }
        return insertSql;
    }

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", pgDbName))
                .build();

        final DatabaseConf cdcPgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // pgsql table mapping to flink table
        ArrayNode pgsqlSourceTable2FlinkTableMap = mapper.readValue(
                DwmVisitBaseInfoMapping.pgsqlSourceVisitTable2FlinkTable, ArrayNode.class);
        buildSourceView(cdcPgDBConf, flinkEnv, pgsqlSourceTable2FlinkTableMap, PGSQL, parameterTool);
        // TableUtils.pgdbTable2FlinkTable(cdcPgDBConf, pgsqlSourceTable2FlinkTableMap, PGSQL, tEnv,"source");

        ArrayNode pgsqlSinkTable2FlinkTableMap = mapper.readValue(
                DwmVisitBaseInfoMapping.pgsqlSinkVisit2FlinkTable, ArrayNode.class);
        //TableUtils.pgdbTable2FlinkTable(pgDBConf, pgsqlSinkTable2FlinkTableMap, PGSQL, tEnv,"sink");
        TableUtils.rdbTable2FlinkTable(pgDBConf, pgsqlSinkTable2FlinkTableMap, PGSQL, tEnv);

        Table table = tEnv.sqlQuery(DwmVisitBaseInfoSql.QUERY_DWD_VISIT_INFO_SQL);
        tEnv.createTemporaryView("dwm_visit_base_info_view", table);
        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                "dwm_visit_base_info_view",
                "pg_sink_dwm_visit_base_info",
                tEnv.from("pg_sink_dwm_visit_base_info"),
                PGSQL
        ));

        /*Table procTab = tEnv.sqlQuery(DwmVisitBaseInfoSql.QUERY_DWD_VISIT_PROC_SQL);
        Table varTab = tEnv.sqlQuery(DwmVisitBaseInfoSql.QUERY_DWD_VISIT_VAR_SQL);
        Table taskTab = tEnv.sqlQuery(DwmVisitBaseInfoSql.QUERY_DWD_VISIT_TASK_SQL);

        // 创建view
        tEnv.createTemporaryView("visit_proc_inst_view", procTab);
        tEnv.createTemporaryView("visit_var_view", varTab);
        tEnv.createTemporaryView("visit_task_view", taskTab);

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(getInsertStatement("pg_sink_dwm_visit_proc"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_visit_var"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_visit_task"));*/
    }
}