package com.tencent.andata.etl.sql;

public class DwmMaintenanceActivationBaseInfoSql {

    public static final String QUERY_DWD_MAINTENANCE_SQL = ""
            + "SELECT\n"
            + "  t.proc_inst_id,\n"
            + "  t.start_time as create_time,\n"
            + "  t.end_time,\n"
            + "  t1.sid,\n"
            + "  t1.ltc_no,\n"
            + "  t1.service_type,\n"
            + "  t1.service_scene,\n"
            + "  case when t1.node_name='结束终止' then 3 when t1.node_name = '结束' then 2 else 1 end as status,\n"
            + "  t1.initiator,\n"
            + "  t1.product,\n"
            + "  t1.server_time_inner,\n"
            + "  t1.server_time_outer,\n"
            + "  t1.poid_list,\n"
            + "  t1.judian_id as judian_ids,\n"
            + "  t2.customer_name,\n"
            + "  t2.customer_uin,\n"
            + "  t2.trade,\n"
            + "  t2.region,\n"
            + "  t2.project_name,\n"
            + "  t3.current_assign,\n"
            + "  t3.current_node_name,\n"
            + "  t3.current_task_def_key,\n"
            + "  t1.asp_uin,\n"
            + "  t1.asp_name\n"
            + "FROM dwd_maintenance_activation_proc_inst_view as t\n"
            + "left join dwd_maintenance_activation_var_info_view as t1 on t.proc_inst_id=t1.proc_inst_id \n"
            + "left join dwd_maintenance_activation_judian_info_view as t2 on t.proc_inst_id=t2.proc_inst_id \n"
            + "left join (\n"
            + " SELECT \n"
            + "   t.proc_inst_id,\n"
            + "   LISTAGG(DISTINCT CASE WHEN u.user_name is not null and c.name is not null then "
            + "   concat(u.user_name,'(',c.name,')') when u.user_name is not null then "
            + "   u.user_name else t.assignee end,';') AS current_assign,\n"
            + "   max(t.name) as current_node_name, \n"
            + "   max(t.task_def_key) as current_task_def_key \n"
            + " FROM dwd_maintenance_activation_task_inst_rt_view as t \n"
            + " left join dim_antool_t_users_rt_view as u on t.assignee = u.id \n"
            + " left join dim_antool_t_companies_rt_view as c on u.company_id = c.id\n"
            + " WHERE t.end_time is null \n"
            + " group by t.proc_inst_id\n"
            + ") as t3 on t.proc_inst_id=t3.proc_inst_id \n";

    public static final String QUERY_DWD_MAINTENANCE_PROC_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id,\n"
            + "  start_time as create_time,\n"
            + "  end_time\n"
            + "FROM pgsql_source_dwd_maintenance_activation_proc_inst\n";

    public static final String QUERY_DWD_MAINTENANCE_VAR_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id,\n"
            + "  sid,\n"
            + "  ltc_no,\n"
            + "  service_type,\n"
            + "  service_scene,\n"
            + "  case when node_name='结束终止' then 3 when node_name = '结束' then 2 else 1 end as status,\n"
            + "  initiator,\n"
            + "  product,\n"
            + "  server_time_inner,\n"
            + "  server_time_outer,\n"
            + "  poid_list,\n"
            + "  judian_id as judian_ids\n"
            + "FROM pgsql_source_dwd_maintenance_activation_var_info\n";

    public static final String QUERY_DWD_MAINTENANCE_JUDIAN_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id,\n"
            + "  customer_name,\n"
            + "  customer_uin,\n"
            + "  trade,\n"
            + "  region,\n"
            + "  project_name\n"
            + "FROM pgsql_source_dwd_maintenance_activation_judian_info\n";

    public static final String QUERY_DWD_MAINTENANCE_TASK_SQL = ""
            + "SELECT \n"
            + "t.proc_inst_id,\n"
            + "LISTAGG(DISTINCT CASE WHEN u.user_name is not null and c.name is not null then "
            + "concat(u.user_name,'(',c.name,')') when u.user_name is not null then "
            + "u.user_name else t.assignee end,';') AS current_assign,\n"
            + "max(t.name) as current_node_name, \n"
            + "max(t.task_def_key) as current_task_def_key \n"
            + "FROM pgsql_source_dwd_maintenance_activation_task_inst_rt as t \n"
            + "left join pgsql_source_dim_antool_t_users_rt as u on t.assignee = u.id \n"
            + "left join pgsql_source_dim_antool_t_companies_rt as c on u.company_id = c.id\n"
            + "WHERE t.end_time is null \n"
            + "group by t.proc_inst_id\n";
}
