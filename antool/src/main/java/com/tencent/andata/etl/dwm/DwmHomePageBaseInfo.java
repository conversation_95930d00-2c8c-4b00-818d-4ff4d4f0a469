package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.sql.DwmHomePageBaseInfoSql;
import com.tencent.andata.etl.tablemap.DwmHomePageBaseInfoMapping;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;


@Builder
public class DwmHomePageBaseInfo {
    private static final Logger LOG = LoggerFactory.getLogger(DwmHomePageBaseInfo.class);
    private final String icebergDbName;
    private final String pgDbName;

    private static String getInsertStatement(String pgSinkTableName) {
        String insertSql = "";
        switch (pgSinkTableName) {
            case "pg_sink_dwm_home_page_maintenance_base_info":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `proc_inst_id`,"
                        + "     `data_type`,"
                        + "     `create_time`,"
                        + "     `end_time`,"
                        + "     `service_id`,"
                        + "     `customer_name`,"
                        + "     `current_node_name`,"
                        + "     `current_assign`,"
                        + "     `parent_visit_id`,"
                        + "     `initiator`"
                        + "FROM %s", "maintenance_base_info_view");
                break;
            case "pg_sink_dwm_home_page_visit_base_info":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `proc_inst_id`,"
                        + "     `data_type`,"
                        + "     `create_time`,"
                        + "     `end_time`,"
                        + "     `service_id`,"
                        + "     `customer_name`,"
                        + "     `current_node_name`,"
                        + "     `current_assign`,"
                        + "     `parent_visit_id`,"
                        + "     `initiator`"
                        + "FROM %s", "visit_base_info_view");
                break;
            case "pg_sink_dwm_home_page_sub_task_base_info":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `proc_inst_id`,"
                        + "     `data_type`,"
                        + "     `create_time`,"
                        + "     `service_id`,"
                        + "     `customer_name`,"
                        + "     `current_node_name`,"
                        + "     `current_assign`,"
                        + "     `parent_visit_id`,"
                        + "     `initiator`,"
                        + "     `deleted`"
                        + "FROM %s", "visit_sub_task_base_info_view");
                break;
            case "pg_sink_dwm_home_page_change_base_info":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `proc_inst_id`,"
                        + "     `data_type`,"
                        + "     `create_time`,"
                        + "     `end_time`,"
                        + "     `service_id`,"
                        + "     `customer_name`,"
                        + "     `current_node_name`,"
                        + "     `current_assign`,"
                        + "     `parent_visit_id`,"
                        + "     `initiator`"
                        + "FROM %s", "change_base_info_view");
                break;
            default:
                insertSql = "";
                break;
        }
        return insertSql;
    }

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", pgDbName))
                .build();

        final DatabaseConf cdcPgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // pgsql table mapping to flink table
        /*ArrayNode pgsqlSourceTable2FlinkTableMap = mapper.readValue(
                DwmHomePageBaseInfoMapping.pgsqlSourceBaseInfoTable2FlinkTable, ArrayNode.class);
        TableUtils.pgdbTable2FlinkTable(cdcPgDBConf, pgsqlSourceTable2FlinkTableMap, PGSQL, tEnv,"source");*/

        ArrayNode pgsqlSinkTable2FlinkTableMap = mapper.readValue(
                DwmHomePageBaseInfoMapping.pgsqlSinkBaseInfoTable2FlinkTable, ArrayNode.class);
        TableUtils.pgdbTable2FlinkTable(pgDBConf, pgsqlSinkTable2FlinkTableMap, PGSQL, tEnv,"sink");

        Table maintenanceTab = tEnv.sqlQuery(DwmHomePageBaseInfoSql.QUERY_DWD_MAINTENANCE_BASE_INFO_SQL);
        Table visitTab = tEnv.sqlQuery(DwmHomePageBaseInfoSql.QUERY_DWD_VISIT_BASE_INFO_SQL);
        Table subTaskTab = tEnv.sqlQuery(DwmHomePageBaseInfoSql.QUERY_DWD_VISIT_SUBTASK_BASE_INFO_SQL);
        Table changeTab = tEnv.sqlQuery(DwmHomePageBaseInfoSql.QUERY_DWD_CHANGE_BASE_INFO_SQL);

        // 创建view
        tEnv.createTemporaryView("maintenance_base_info_view", maintenanceTab);
        tEnv.createTemporaryView("visit_base_info_view", visitTab);
        tEnv.createTemporaryView("visit_sub_task_base_info_view", subTaskTab);
        tEnv.createTemporaryView("change_base_info_view", changeTab);

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(getInsertStatement("pg_sink_dwm_home_page_maintenance_base_info"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_home_page_visit_base_info"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_home_page_sub_task_base_info"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_home_page_change_base_info"));
    }
}