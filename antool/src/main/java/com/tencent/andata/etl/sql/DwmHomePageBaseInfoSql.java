package com.tencent.andata.etl.sql;

public class DwmHomePageBaseInfoSql {

    public static final String QUERY_DWD_MAINTENANCE_BASE_INFO_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id as id,\n"
            + "  proc_inst_id,\n"
            + "  '维保激活' as data_type,\n"
            + "  cast(create_time as string) as create_time,\n"
            + "  end_time,\n"
            + "  sid as service_id,\n"
            + "  customer_name,\n"
            + "  current_node_name,\n"
            + "  current_assign,\n"
            + "  '' as parent_visit_id,\n"
            + "  initiator,\n"
            + "  0 AS deleted\n"
            + "FROM dwm_maintenance_activation_view\n";

    public static final String QUERY_DWD_VISIT_BASE_INFO_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id as id,\n"
            + "  proc_inst_id,\n"
            + "  '服务主任务' as data_type,\n"
            + "  cast(create_time as string) as create_time,\n"
            + "  end_time,\n"
            + "  visit_id as service_id,\n"
            + "  customer_name,\n"
            + "  current_node_name,\n"
            + "  current_assign,\n"
            + "  '' as parent_visit_id,\n"
            + "  initiator,\n"
            + "  0 AS deleted\n"
            + "FROM dwm_visit_base_info_view\n";

    public static final String QUERY_DWD_VISIT_SUBTASK_BASE_INFO_SQL = ""
            + "SELECT\n"
            + "  concat(proc_inst_id,'_',subtaskid) as id,\n"
            + "  proc_inst_id,\n"
            + "  '服务子任务' as data_type,\n"
            + "  createtime as create_time,\n"
            + "  subtaskid as service_id,\n"
            + "  customername as customer_name,\n"
            + "  '' as current_node_name,\n"
            + "  current_assign,\n"
            + "  parent_visit_id,\n"
            + "  creator as initiator,\n"
            + "  deleted\n"
            + "FROM visit_sub_task_var_view\n";

    public static final String QUERY_DWD_CHANGE_BASE_INFO_SQL = ""
            + "SELECT\n"
            + "  proc_inst_id as id,\n"
            + "  proc_inst_id,\n"
            + "  '软件更新' as data_type,\n"
            + "  cast(create_time as string) as create_time,\n"
            + "  end_time,\n"
            + "  change_id as service_id,\n"
            + "  customer_name,\n"
            + "  current_node_name,\n"
            + "  current_assign,\n"
            + "  '' as parent_visit_id,\n"
            + "  initiator,\n"
            + "  0 AS deleted\n"
            + "FROM dwm_change_base_info_view\n";
}
