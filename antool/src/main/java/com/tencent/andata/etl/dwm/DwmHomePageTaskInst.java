package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.sql.DwmHomePageTaskInstSql;
import com.tencent.andata.etl.tablemap.DwmHomePageTaskInstMapping;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;


@Builder
public class DwmHomePageTaskInst {
    private static final Logger LOG = LoggerFactory.getLogger(DwmHomePageTaskInst.class);
    private final String icebergDbName;
    private final String pgDbName;

    private static String getInsertStatement(String pgSinkTableName) {
        String insertSql = "";
        switch (pgSinkTableName) {
            case "pg_sink_dwm_home_page_maintenance_task_inst":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `proc_inst_id`,"
                        + "     `data_type`,"
                        + "     `task_def_key`,"
                        + "     `task_name`,"
                        + "     `task_assignee`,"
                        + "     `task_start_time`,"
                        + "     `task_end_time`,"
                        + "     `task_duration`,"
                        + "     `delete_reason`,"
                        + "     `tenant_id`"
                        + "FROM %s", "maintenance_task_inst_view");
                break;
            case "pg_sink_dwm_home_page_visit_task_inst":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `proc_inst_id`,"
                        + "     `data_type`,"
                        + "     `task_def_key`,"
                        + "     `task_name`,"
                        + "     `task_assignee`,"
                        + "     `task_start_time`,"
                        + "     `task_end_time`,"
                        + "     `task_duration`,"
                        + "     `delete_reason`,"
                        + "     `tenant_id`"
                        + "FROM %s", "visit_task_inst_view");
                break;
            case "pg_sink_dwm_home_page_sub_task_task_inst":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `proc_inst_id`,"
                        + "     `data_type`,"
                        + "     `task_def_key`,"
                        + "     `task_name`,"
                        + "     `task_assignee`,"
                        + "     `task_start_time`,"
                        + "     `task_end_time`,"
                        + "     `task_duration`,"
                        + "     `delete_reason`,"
                        + "     `tenant_id`"
                        + "FROM %s", "visit_sub_task_task_inst_view");
                break;
            case "pg_sink_dwm_home_page_change_task_inst":
                insertSql = String.format("INSERT INTO " + pgSinkTableName
                        + " SELECT"
                        + "     `id`,"
                        + "     `proc_inst_id`,"
                        + "     `data_type`,"
                        + "     `task_def_key`,"
                        + "     `task_name`,"
                        + "     `task_assignee`,"
                        + "     `task_start_time`,"
                        + "     `task_end_time`,"
                        + "     `task_duration`,"
                        + "     `delete_reason`,"
                        + "     `tenant_id`"
                        + "FROM %s", "change_task_inst_view");
                break;
            default:
                insertSql = "";
                break;
        }
        return insertSql;
    }

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        ArrayNode pgsqlSinkTable2FlinkTableMap = mapper.readValue(
                DwmHomePageTaskInstMapping.pgsqlSinkTaskInstTable2FlinkTable, ArrayNode.class);
        TableUtils.pgdbTable2FlinkTable(pgDBConf, pgsqlSinkTable2FlinkTableMap, PGSQL, tEnv,"sink");

        Table maintenanceTab = tEnv.sqlQuery(DwmHomePageTaskInstSql.QUERY_DWD_MAINTENANCE_TASK_INST_SQL);
        Table visitTab = tEnv.sqlQuery(DwmHomePageTaskInstSql.QUERY_DWD_VISIT_TASK_INST_SQL);
        Table subTaskTab = tEnv.sqlQuery(DwmHomePageTaskInstSql.QUERY_DWD_VISIT_SUBTASK_TASK_INST_SQL);
        Table changeTab = tEnv.sqlQuery(DwmHomePageTaskInstSql.QUERY_DWD_CHANGE_TASK_INST_SQL);

        // 创建view
        tEnv.createTemporaryView("maintenance_task_inst_view", maintenanceTab);
        tEnv.createTemporaryView("visit_task_inst_view", visitTab);
        tEnv.createTemporaryView("visit_sub_task_task_inst_view", subTaskTab);
        tEnv.createTemporaryView("change_task_inst_view", changeTab);

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(getInsertStatement("pg_sink_dwm_home_page_maintenance_task_inst"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_home_page_visit_task_inst"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_home_page_sub_task_task_inst"))
                .addInsertSql(getInsertStatement("pg_sink_dwm_home_page_change_task_inst"));
    }
}
