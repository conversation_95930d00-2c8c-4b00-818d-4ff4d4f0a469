package com.tencent.andata.dispatcher.cdc.operator;

import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.conf.fieldgetter.BaseFieldGetter;
import com.tencent.andata.utils.operator.map.RowDataCleanMap;
import com.tencent.andata.utils.rowdata.MessageRowData;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;

public class AIORowDataCleanMap implements MapFunction<MessageRowData, MessageRowData> {

    private final Map<TableIdentifier, MapFunction<RowData, RowData>> cleanFuncMap = new HashMap<>();

    public void addCleaner(TableIdentifier table, Schema schema) {
        this.cleanFuncMap.put(table, new RowDataCleanMap(
                        BaseFieldGetter.builder()
                                .setRowType(TableUtils.convertFlinkSchemaToRowType(schema, true))
                                .build()
                )
        );
    }

    @Override
    public MessageRowData map(MessageRowData rowData) throws Exception {
        // rEngine数据不需要进行clean
        if (cleanFuncMap.isEmpty()) {
            return rowData;
        }
        
        return MessageRowData.convertFromGenericRowData(
                (GenericRowData) cleanFuncMap.get(rowData.getAttr().getSrcTable()).map(rowData),
                rowData.getAttr()
        );
    }
}
