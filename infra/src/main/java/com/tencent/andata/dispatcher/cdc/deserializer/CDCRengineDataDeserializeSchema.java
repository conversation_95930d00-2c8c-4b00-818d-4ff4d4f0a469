package com.tencent.andata.dispatcher.cdc.deserializer;

import com.google.common.collect.Maps;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.cdc.constant.DebeziumJsonConstant;
import com.tencent.andata.utils.cdc.deserializer.converter.NornsJsonConverter;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.ververica.cdc.debezium.DebeziumDeserializationSchema;
import io.debezium.data.Envelope;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import lombok.Getter;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.data.StringData;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.kafka.connect.data.Schema;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.json.DecimalFormat;
import org.apache.kafka.connect.json.JsonConverter;
import org.apache.kafka.connect.json.JsonConverterConfig;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.storage.ConverterType;

@Getter
public class CDCRengineDataDeserializeSchema implements DebeziumDeserializationSchema<MessageRowData> {

    private String dbName;
    private final boolean includeSchema;
    private final Map<String, Object> customConverterConfigs = Maps.newHashMap();
    private transient JsonConverter jsonConverter;
    private Map<String, String> tableAliasMap;
    private Map<String, String> taggerMap;

    private static HashMap<RowKind, StringData> rowKindMap = new HashMap<RowKind, StringData>() {{
        put(RowKind.INSERT, StringData.fromString("insert"));
        put(RowKind.DELETE, StringData.fromString("delete"));
        put(RowKind.UPDATE_AFTER, StringData.fromString("update"));
    }};

    public CDCRengineDataDeserializeSchema(Map<String, String> taggerMap, String dbName, Map<String, String> tableAliasMap) {
        this(false);
        this.dbName = dbName;
        this.taggerMap = taggerMap;
        this.tableAliasMap = tableAliasMap;
    }

    public CDCRengineDataDeserializeSchema(boolean includeSchema) {
        this(includeSchema, Collections.emptyMap());
    }

    public CDCRengineDataDeserializeSchema(boolean includeSchema, Map<String, Object> customConverterConfigs) {
        this.includeSchema = includeSchema;
        this.customConverterConfigs.put("converter.type", ConverterType.VALUE.getName());
        this.customConverterConfigs.put(JsonConverterConfig.SCHEMAS_ENABLE_CONFIG, isIncludeSchema());
        this.customConverterConfigs.put(JsonConverterConfig.DECIMAL_FORMAT_CONFIG, DecimalFormat.NUMERIC.name());
        this.customConverterConfigs.putAll(customConverterConfigs);
    }


    protected NornsJsonConverter initializeJsonConverter(Map<String, Object> customConverterConfigs) {
        final NornsJsonConverter jsonConverter = new NornsJsonConverter();
        jsonConverter.configure(customConverterConfigs);
        return jsonConverter;
    }

    @Override
    public void deserialize(SourceRecord sr, Collector<MessageRowData> out) {
        if (this.jsonConverter == null) {
            this.jsonConverter = this.initializeJsonConverter(this.customConverterConfigs);
        }

        Envelope.Operation op = Envelope.operationFor(sr);
        String topic = sr.topic();
        Struct value = (Struct) sr.value();

        final Struct source = value.getStruct(DebeziumJsonConstant.SOURCE);
        String table = source.getString(DebeziumJsonConstant.TABLE);
        String dstTable = table;
        if (this.tableAliasMap.containsKey(table)) {
            dstTable = this.tableAliasMap.get(table);
        }
        String connector = source.getString("connector").toUpperCase();
        DatabaseEnum dbEnum = DatabaseEnum.valueOf(connector.toUpperCase().equals("POSTGRESQL") ? "PGSQL" : connector);
        String schemaName = dbEnum == DatabaseEnum.PGSQL ? source.getString("schema") : "";

        Schema valueSchema = sr.valueSchema();
        Long ts = value.getInt64("ts_ms");
        TableIdentifier srcTblId = new TableIdentifier(dbEnum, this.dbName, schemaName, table);
        TableIdentifier dstTblId = new TableIdentifier(dbEnum, this.dbName, schemaName, dstTable);
        switch (op) {
            case READ:
            case CREATE:
                emit(RowKind.INSERT, "{}", extractAfterRow(topic, value, valueSchema), ts, srcTblId, dstTblId, out);
                break;
            case DELETE:
                emit(RowKind.DELETE, extractBeforeRow(topic, value, valueSchema), "{}", ts, srcTblId, dstTblId, out);
                break;
            case UPDATE:
                emit(RowKind.UPDATE_AFTER, extractBeforeRow(topic, value, valueSchema),
                        extractAfterRow(topic, value, valueSchema), ts, srcTblId, dstTblId, out);
                break;
        }
    }

    private String extractAfterRow(String topic, Struct value, Schema valueSchema) {
        return new String(
                jsonConverter
                        .fromConnectData(topic, valueSchema.field(DebeziumJsonConstant.AFTER).schema(),
                                value.getStruct(DebeziumJsonConstant.AFTER)), StandardCharsets.UTF_8
        );
    }

    private String extractBeforeRow(String topic, Struct value, Schema valueSchema) {
        return new String(
                jsonConverter.fromConnectData(topic, valueSchema.field(DebeziumJsonConstant.BEFORE).schema(),
                        value.getStruct(DebeziumJsonConstant.BEFORE)), StandardCharsets.UTF_8
        );
    }

    private void emit(RowKind rowKind, String beforeValue, String afterValue, Long ts, TableIdentifier srcTblId,
                      TableIdentifier dstTblId, Collector<MessageRowData> out) {
        MessageRowData rowData = new MessageRowData(5);
        rowData.setRowKind(rowKind);
        rowData.setField(0, StringData.fromString("data_change"));
        rowData.setField(1, rowKindMap.get(rowKind));
        rowData.setField(2, StringData.fromString(beforeValue));
        rowData.setField(3, StringData.fromString(afterValue));
        rowData.setField(4, ts);

        MessageRowDataAttr messageRowDataAttr = new MessageRowDataAttr();
        messageRowDataAttr.setSrcTable(srcTblId);
        messageRowDataAttr.setDstTable(dstTblId);
        messageRowDataAttr.setTagger(this.taggerMap.get(srcTblId.getTableName()));
        rowData.setAttr(messageRowDataAttr);
        out.collect(rowData);
    }

    @Override
    public TypeInformation<MessageRowData> getProducedType() {
        return TypeInformation.of(MessageRowData.class);
    }
}
