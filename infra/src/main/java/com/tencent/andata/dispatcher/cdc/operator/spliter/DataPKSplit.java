package com.tencent.andata.dispatcher.cdc.operator.spliter;

import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.data.RowData;

public class DataPKSplit implements KeySelector<MessageRowData, String> {

    List<RowData.FieldGetter> pkGetters;
    private Random rand;

    public DataPKSplit(List<RowData.FieldGetter> pkGetters) {
        this.pkGetters = pkGetters;
        this.rand = ThreadLocalRandom.current();
    }

    @Override
    public String getKey(MessageRowData rowData) {
        MessageRowDataAttr attr = rowData.getAttr();
        StringBuilder builder = new StringBuilder().append(attr.getSrcTable().getFullName());

        Optional.of(pkGetters)
                .filter(pk -> !pk.isEmpty())
                .map(pk -> {
                    pk.forEach(getter -> builder.append(getter.getFieldOrNull(rowData)));
                    return builder;
                })
                .orElse(builder.append(this.rand.nextInt(10000)));

        return builder.toString();
    }

    public static DataPKSplit fromFlinkSchema(Schema schema, List<String> primaryKeys) {
        List<RowData.FieldGetter> getters = TableUtils.getFieldGettersUsingFlinkSchemaAndFieldName(schema, primaryKeys);
        return new DataPKSplit(getters);
    }
}
