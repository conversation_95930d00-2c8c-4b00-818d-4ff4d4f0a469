package com.tencent.andata.dispatcher.cdc.task;

import static com.tencent.andata.singularity.settings.Const.CDC_GROUP_PREFIX;

import com.tencent.andata.dispatcher.cdc.deserializer.CDCDBRowDataDeserializeSchema;
import com.tencent.andata.dispatcher.cdc.deserializer.CDCRengineDataDeserializeSchema;
import com.tencent.andata.dispatcher.cdc.struct.TaskParams;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.cdc.CDCUtils;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.struct.CDCCategoryEnum;
import com.tencent.andata.utils.struct.CDCStartMode;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.mysql.source.MySqlSourceBuilder;
import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import com.ververica.cdc.debezium.DebeziumDeserializationSchema;
import com.ververica.cdc.debezium.DebeziumSourceFunction;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Random;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.connector.source.Source;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Preconditions;
import org.apache.flink.util.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MonitorTask implements Serializable {

    private static final Logger LOG = LoggerFactory.getLogger(MonitorTask.class);
    public String dbName;
    public DatabaseEnum databaseType;
    public DatabaseConf databaseConf;
    private CDCStartMode startMode;
    private CDCCategoryEnum cdcCategory;
    public Map<TableIdentifier, Schema> schemaMap; // TODO: 这里可能不行，之前用Map就没成功过好像。晚点再确认
    public Map<String, String> tableAliasMap;
    public Map<String, String> taggerMap;
    private static Properties debeziumProps = new Properties();

    static {
        debeziumProps.setProperty("converters", "dateConverters");
        debeziumProps.setProperty("decimal.handling.mode", "double");
        debeziumProps.setProperty("bigint.unsigned.handling.mode", "long");
        debeziumProps.setProperty("dateConverters.type", "com.tencent.andata.utils.cdc.CDCDateTimeConverter");
    }

    public MonitorTask(TaskParams param) throws Exception {
        // 这些参数是同一个DB下CDC任务应该保持一致的
        this.dbName = param.srcTableID.getDbName();
        this.databaseType = param.srcTableID.getDatabaseEnum();
        this.cdcCategory = param.cdcCategory;
        this.startMode = param.startMode;

        final String settingPath = String.format(
                "%s.%s.%s",
                CDC_GROUP_PREFIX,
                this.databaseType.toString(),
                this.dbName
        );

        LOG.info(String.format("Loading DB Conf. Path: %s", settingPath));
        this.databaseConf = DatabaseConf.fromSettings(settingPath);
        // 初始化一些配置
        this.schemaMap = new HashMap<>();
        this.tableAliasMap = new HashMap<>();
        this.taggerMap = new HashMap<>();
        // 用来初始化MonitorTask的CDC任务也要过一次AddTable
        this.addTable(param);
    }

    /***
     * addTable 通过task参数添加要同步的表
     * @param param .
     * @throws Exception .
     */
    public void addTable(TaskParams param) throws Exception {
        Preconditions.checkState(
                this.startMode == param.startMode,
                "The starMode of the same database must be the same"
        );
        Preconditions.checkState(
                this.cdcCategory == param.cdcCategory,
                "The cdcCategory of the same database must be the same"
        );
        Preconditions.checkState(
                !(param.cdcCategory == CDCCategoryEnum.RENGINE && StringUtils.isNullOrWhitespaceOnly(param.dbus)),
                "REngine CDC must have dbus"
        );

        if (param.alias != null) {
            this.tableAliasMap.put(param.srcTableID.getTableName(), param.alias);
        }
        if (param.dbus != null) {
            this.taggerMap.put(param.srcTableID.getTableName(), param.dbus);
        }

        TableIdentifier tableIdentifier = param.srcTableID;
        Schema schema = CDCUtils.getRdbmsSchema(
                this.databaseConf,
                tableIdentifier.getTableName(),
                tableIdentifier.getDatabaseEnum(),
                param.jdbcConf
        );
        LOG.info(String.format("RDBMS schema: \r\n"
                        + "-----------------------\r\n"
                        + "%s\r\n"
                        + "----------------------\r\n",
                schema)
        );
        this.schemaMap.put(tableIdentifier, schema);
    }

    public Schema getTableSchema(TableIdentifier tableIdentifier) {
        return schemaMap.get(tableIdentifier);
    }

    public CDCDBRowDataDeserializeSchema buildCDCDeserializerUsingSchemaMap() {
        Map<TableIdentifier, RowType> m = this
                .schemaMap
                .entrySet()
                .stream()
                .map(
                        s -> new Tuple2<>(
                                s.getKey(),
                                TableUtils.convertFlinkSchemaToRowType(s.getValue(), true))
                )
                .collect(Collectors.toMap(t -> t.f0, t -> t.f1));

        return CDCDBRowDataDeserializeSchema
                .newBuilder(this.dbName)
                .setPhysicalRowTypeMap(m)
                .build();
    }

    public DataStream<MessageRowData> monitor(StreamExecutionEnvironment env) {
        DebeziumDeserializationSchema<? extends RowData> deserializer = cdcCategory == CDCCategoryEnum.COMMON ?
                buildCDCDeserializerUsingSchemaMap() : new CDCRengineDataDeserializeSchema(this.taggerMap, this.dbName, this.tableAliasMap);

        DataStream<? extends RowData> retMsg;

        switch (this.databaseType) {
            case PGSQL:
                retMsg = env.addSource(
                                this.getPostgreSQLSource(deserializer),
                                this.toString()
                        )
                        .setParallelism(1)
                        .uid(this.toString());
                break;
            // TODO：这里会导致每次增加 & 删除任务的时候，UID都会改变，导致全量同步数据
            // TODO：但PG这里暂时还不支持 Add New Tbl，所以PG这里就先不改
            //  .uid(dbMonitorTask.getTaskUid());
            case MYSQL:
                retMsg = env.fromSource(
                                getMySQLSource(deserializer),
                                WatermarkStrategy.noWatermarks(),
                                this.toString()
                        )
                        .setParallelism(1)
                        .uid(this.getTaskUid());
                break;
            default:
                throw new RuntimeException(
                        String.format("Input DBType %s cannot be recognized.", this.databaseType));
        }
        LOG.info("monitor task init successes. ");
        return (DataStream<MessageRowData>) retMsg;

    }

    private DebeziumSourceFunction<? extends RowData> getPostgreSQLSource(
            DebeziumDeserializationSchema<? extends RowData> rowDataDebeziumDeserializeSchema) {

        return PostgreSQLSource.<org.apache.flink.table.data.RowData>builder()
                .hostname(databaseConf.dbHost)
                .port(databaseConf.dbPort)
                .database(databaseConf.dbName)
                .username(databaseConf.userName)
                .password(databaseConf.password)
                .slotName(
                        String.format(
                                "flink_cdc_reader_%d",
                                Math.abs(this.toString().hashCode())
                        )
                )
                .debeziumProperties(getProperties())
                .tableList(this.getTables(l -> String.format("%s.%s", l.getSchemaName(), l.getTableName())))
                .deserializer((DebeziumDeserializationSchema<RowData>) rowDataDebeziumDeserializeSchema)
                .build();
    }

    private @NotNull Properties getProperties() {
        Properties debeziumProperties = new Properties();

        // debeziumProperties.setProperty("slot.drop.on.stop", "true"); 这里需要去掉，否则重启任务后会丢数据
        debeziumProperties.setProperty("heartbeat.interval.ms", "60000");
        debeziumProperties.setProperty(
                "heartbeat.action.query",
                "INSERT INTO _cdc_heartbeat_tbl (beats) VALUES (true)"
        );
        if (startMode == CDCStartMode.LAST) {
            debeziumProperties.setProperty("snapshot.mode", "never");
        }

        if (cdcCategory == CDCCategoryEnum.RENGINE) {
            debeziumProperties.putAll(debeziumProps);
        }

        return debeziumProperties;
    }

    /**
     * 生成MySQL的CDC Source
     *
     * @param rowDataDebeziumDeserializeSchema rowdata序列化器
     * @return CDC DataStream
     */
    private Source<RowData, ?, ?> getMySQLSource(
            DebeziumDeserializationSchema<? extends RowData> rowDataDebeziumDeserializeSchema
    ) {
        Random r = new Random();
        DatabaseConf databaseConf = this.databaseConf;
        MySqlSourceBuilder<RowData> builder = MySqlSource.<RowData>builder()
                .hostname(databaseConf.dbHost)
                .port(databaseConf.dbPort)
                .databaseList(databaseConf.dbName) // TODO: 自动添加数据库也可以，只要是一个实例
                .username(databaseConf.userName)
                .password(databaseConf.password)
                // 这个ID需要是全局唯一的，如果不唯一会出现问题, 加上abs，serverID不能为负数
                .serverId(String.valueOf(Math.abs((this.toString()).hashCode())))
                // 可以在这里动态添加表，会自动解析
                .tableList(this.getTables(l -> String.format("%s.%s", databaseConf.dbName, l.getTableName())))
                .deserializer((DebeziumDeserializationSchema<RowData>) rowDataDebeziumDeserializeSchema)
                // TODO：这里需要验证下，添加了新的表是否可以全量的同步过来。之前通过改uid的模式有问题，会导致下面TODO记录的问题
                .scanNewlyAddedTableEnabled(true)
                // 由数据库把timestamp with timezone的转换成UTC
                .serverTimeZone("Asia/Shanghai");
        if (startMode == CDCStartMode.LAST) {
            builder.startupOptions(StartupOptions.latest());
        }

        if (cdcCategory == CDCCategoryEnum.RENGINE) {
            builder.debeziumProperties(debeziumProps);
        }
        return builder.build();
    }

    private String getTables(Function<TableIdentifier, String> fc) {
        return this.schemaMap.keySet()
                .stream()
                .map(fc)
                .collect(Collectors.joining(","));
    }

    public String getTaskUid() {
        // 修改了此处代码，保证生成的uid
        return String.format(
                "%s:%d-%s:%s", databaseConf.dbHost, databaseConf.dbPort, databaseType, dbName
        );
    }

    @Override
    public boolean equals(Object obj) {
        if (obj.getClass() == this.getClass()) {
            return this.dbName.equals(((MonitorTask) obj).dbName);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return this.dbName.hashCode();
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", dbName, this.getTables(TableIdentifier::getTableName));
    }
}
