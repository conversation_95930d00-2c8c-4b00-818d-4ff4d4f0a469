package com.tencent.andata.dispatcher.cdc.operator;

import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.operator.map.RowDataFieldSwapMap;
import com.tencent.andata.utils.rowdata.MessageRowData;
import com.tencent.andata.utils.rowdata.MessageRowDataAttr;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;

public class AIORowDataFieldSwapMap implements MapFunction<MessageRowData, MessageRowData> {

    private final Map<TableIdentifier, RowDataFieldSwapMap> swapperMap = new HashMap<>();

    public void addRowDataFieldSwapMap(TableIdentifier tableIdentifier, RowDataFieldSwapMap swapper) {
        swapperMap.put(tableIdentifier, swapper);
    }

    @Override
    public MessageRowData map(MessageRowData messageRowData) throws Exception {
        // TODO: 这里会有性能问题，涉及到多次对象的生成，后面GC会比较多。
        // TODO: 但这块没空搞了，有机会再说吧
        // rEngine数据不需要进行swap
        if (swapperMap.isEmpty()) {
            return messageRowData;
        }

        MessageRowDataAttr attr = messageRowData.getAttr();
        TableIdentifier srcTableID = messageRowData.getAttr().getSrcTable();
        if (swapperMap.containsKey(srcTableID)) {
            RowData mRowData = swapperMap.get(srcTableID).map(messageRowData);
            if (mRowData instanceof MessageRowData) {
                return ((MessageRowData) mRowData);
            } else if (mRowData instanceof GenericRowData) {
                return MessageRowData.convertFromGenericRowData(
                        (GenericRowData) mRowData,
                        attr
                );
            } else {
                throw new RuntimeException(String.format("Unknown msgRowType %s.", mRowData.getClass()));
            }
        }
        // TODO:异常数据应该找个地方扔，先直接扔出去
        throw new RuntimeException(
                String.format("Unknown src table %s. What we have: %s", srcTableID, swapperMap.keySet())
        );
    }
}
