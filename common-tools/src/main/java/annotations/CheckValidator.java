package annotations;


import com.tencent.andata.utils.cdc.conf.input.InputTaskParams;
import com.tencent.andata.utils.struct.CDCCategoryEnum;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class CheckValidator implements ConstraintValidator<ValidVal, InputTaskParams> {

    @Override
    public void initialize(ValidVal constraintAnnotation) {
    }

    @Override
    public boolean isValid(InputTaskParams inputTaskParams, ConstraintValidatorContext constraintValidatorContext) {
        return CDCCategoryEnum.valueOf(inputTaskParams.cdcCategory) == CDCCategoryEnum.RENGINE
                && StringUtils.isNotEmpty(inputTaskParams.dbus);
    }
}
