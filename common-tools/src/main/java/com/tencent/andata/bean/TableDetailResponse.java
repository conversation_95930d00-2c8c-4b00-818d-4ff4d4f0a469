package com.tencent.andata.bean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

/**
 * API返回的表详情响应Bean
 * 用于解析Iceberg表的元数据信息
 * 
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TableDetailResponse {
    
    @JsonProperty("code")
    private int code;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("data")
    private Data data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("database")
        private String database;
        
        @JsonProperty("uuid")
        private String uuid;
        
        @JsonProperty("format")
        private String format;
        
        @JsonProperty("fileFormat")
        private String fileFormat;
        
        @JsonProperty("location")
        private String location;
        
        @JsonProperty("snapshots")
        private long snapshots;
        
        @JsonProperty("snapshotRetention")
        private long snapshotRetention;
        
        @JsonProperty("properties")
        private Map<String, String> properties;
        
        @JsonProperty("fields")
        private List<Field> fields;
        
        @JsonProperty("nestFields")
        private String nestFields;
        
        // 修复：将partitionDefinitions改为支持对象类型，而不是只支持字符串
        @JsonProperty("partitionDefinitions")
        private List<Object> partitionDefinitions;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDatabase() {
            return database;
        }

        public void setDatabase(String database) {
            this.database = database;
        }

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public String getFormat() {
            return format;
        }

        public void setFormat(String format) {
            this.format = format;
        }

        public String getFileFormat() {
            return fileFormat;
        }

        public void setFileFormat(String fileFormat) {
            this.fileFormat = fileFormat;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public long getSnapshots() {
            return snapshots;
        }

        public void setSnapshots(long snapshots) {
            this.snapshots = snapshots;
        }

        public long getSnapshotRetention() {
            return snapshotRetention;
        }

        public void setSnapshotRetention(long snapshotRetention) {
            this.snapshotRetention = snapshotRetention;
        }

        public Map<String, String> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, String> properties) {
            this.properties = properties;
        }

        public List<Field> getFields() {
            return fields;
        }

        public void setFields(List<Field> fields) {
            this.fields = fields;
        }

        public String getNestFields() {
            return nestFields;
        }

        public void setNestFields(String nestFields) {
            this.nestFields = nestFields;
        }

        public List<Object> getPartitionDefinitions() {
            return partitionDefinitions;
        }

        public void setPartitionDefinitions(List<Object> partitionDefinitions) {
            this.partitionDefinitions = partitionDefinitions;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Field {
            
            @JsonProperty("name")
            private String name;
            
            @JsonProperty("type")
            private String type;
            
            @JsonProperty("required")
            private boolean required;
            
            @JsonProperty("doc")
            private String doc;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public boolean isRequired() {
                return required;
            }

            public void setRequired(boolean required) {
                this.required = required;
            }

            public String getDoc() {
                return doc;
            }

            public void setDoc(String doc) {
                this.doc = doc;
            }
        }
    }
} 