package com.tencent.andata.struct.rowTypes;

import java.util.Arrays;
import org.apache.flink.table.types.logical.BigIntType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.VarCharType;

// REngineRowType 单例模式
public class REngineRowType {

    private REngineRowType() {
    }

    // 静态方法
    public static RowType getREngineRowType() {
        return SingleTonHolder.RENGIE_ROWTYPE;
    }

    //静态内部类
    private static class SingleTonHolder {

        private static final RowType RENGIE_ROWTYPE = new RowType(
                Arrays.asList(
                        new RowType.RowField("type", new VarCharType(false, 20)),
                        new RowType.RowField("change_type", new VarCharType(false, 20)),
                        new RowType.RowField("before_value", new VarCharType(true, Integer.MAX_VALUE)),
                        new RowType.RowField("after_value", new VarCharType(true, Integer.MAX_VALUE)),
                        new RowType.RowField("ts_ms", new BigIntType())
                )
        );
    }
}
