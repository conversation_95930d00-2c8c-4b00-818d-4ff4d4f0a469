package com.tencent.andata.struct.regnine;

import java.util.ArrayList;
import java.util.List;

// 如果这个MessagePack是经过了cep后构造的
// 那么就包含了一个cep规则匹配的数据
// 一个MessageData对应一个cep state匹配的数据
public class MessagePack {

    // 这里弄成private的目的是一旦pack生成就不能在其本身上修改了
    private List<MessageData> messageDatas;

    public MessageData getMessageData(int i) {
        return this.messageDatas.get(i);
    }

    public int getDataSize() {
        return messageDatas.size();
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private List<MessageData> messageDatas;

        public Builder setMessageDatas(List<MessageData> messageDatas) {
            this.messageDatas = messageDatas;
            return this;
        }

        public Builder addMessageData(MessageData messageData) {
            if (this.messageDatas == null) {
                this.messageDatas = new ArrayList<>();
            }
            this.messageDatas.add(messageData);
            return this;
        }

        public MessagePack build() {
            MessagePack messagePack = new MessagePack();
            messagePack.messageDatas = this.messageDatas;
            return messagePack;
        }

    }

    @Override
    public String toString() {
        return "MessagePack{" +
                "messageDatas=" + messageDatas +
                '}';
    }
}