package com.tencent.andata.utils.lookup.jdbc;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.lookup.enums.JDBCDriver;
import com.tencent.andata.utils.lookup.impl.AbstractLookupQueryImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import java.io.Serializable;
import java.sql.Connection;
import java.sql.SQLException;
import org.apache.calcite.tools.ValidationException;
import org.apache.flink.util.Preconditions;

public abstract class AbstractJDBCLookupQuery<IN, OUT> extends AbstractLookupQueryImpl<IN, OUT> implements Serializable {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final int CONNECTION_CHECK_TIMEOUT_SECONDS = 5;

    protected String url;
    protected String userName;
    protected String password;
    protected String driverName;
    protected transient HikariDataSource dataSource;

    public AbstractJDBCLookupQuery(DatabaseEnum databaseEnum, DatabaseConf databaseConf) throws ValidationException {
        switch (databaseEnum) {
            case MYSQL:
                this.driverName = JDBCDriver.MYSQL.value;
                break;
            case PGSQL:
                this.driverName = JDBCDriver.PGSQL.value;
                break;
            default:
                throw new ValidationException("Only support mysql and pgsql!");
        }
        this.url = String.format(
                "jdbc:%s://%s:%s/%s",
                databaseEnum.toString(),
                databaseConf.dbHost,
                databaseConf.dbPort,
                databaseConf.dbName
        );
        this.userName = databaseConf.userName;
        this.password = databaseConf.password;

        if (databaseEnum == DatabaseEnum.MYSQL) {
            this.url += "?characterEncoding=utf-8" +
                    "&useOldAliasMetadataBehavior=true" +
                    "&zeroDateTimeBehavior=convertToNull" +
                    "&tinyInt1isBit=false" +
                    "&useSSL=false";
        }
    }

    abstract protected OUT executeQuery(Connection connection, IN in) throws Exception;

    @Override
    protected OUT doQuery(IN in) throws Exception {
        try (Connection connection = dataSource.getConnection()) {
            return this.executeQuery(connection, in);
        }
    }

    @Override
    protected void onRetryCallback() {
        try {
            if (this.dataSource == null || this.dataSource.isClosed()) {
                this.refresh();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void open() throws SQLException {
        Preconditions.checkNotNull(driverName);
        Preconditions.checkNotNull(url);
        Preconditions.checkNotNull(userName);
        Preconditions.checkNotNull(password);

        // 初始化连接池
        HikariConfig config = new HikariConfig();
        config.setDriverClassName(driverName);
        config.setJdbcUrl(url);
        config.setUsername(userName);
        config.setPassword(password);

        // 连接池配置
        config.setMaximumPoolSize(5);
        config.setMinimumIdle(5);
        config.setIdleTimeout(300000);
        config.setConnectionTimeout(20000);
        config.setMaxLifetime(900000);
        config.setAutoCommit(true);
        config.setPoolName("HikariPool-Lookup");

        // 测试连接的有效性
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(CONNECTION_CHECK_TIMEOUT_SECONDS * 1000L);

        this.dataSource = new HikariDataSource(config);
    }

    public void close() throws SQLException {
        if (this.dataSource != null) {
            try {
                this.dataSource.close();
            } finally {
                this.dataSource = null;
            }
        }
    }

    private void refresh() throws Exception {
        this.close();
        this.open();
    }
}