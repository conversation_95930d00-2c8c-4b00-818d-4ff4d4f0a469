package com.tencent.andata.utils;

import org.apache.commons.lang3.ArrayUtils;

import java.util.List;

import static org.apache.commons.lang.SystemUtils.IS_OS_WINDOWS;

public class CommonUtils {
    /**
     * 获取列表指定索引的元素，没有则返回默认值
     *
     * @param index        指定索引
     * @param defaultValue 默认值
     * @param list         列表
     * @param <E>          列表值类型
     * @return
     */
    public static <E> E listGetOrDefault(int index, E defaultValue, List<E> list) {
        if (index < 0) {
            throw new IllegalArgumentException("index is less than 0: " + index);
        }
        return index <= list.size() - 1 ? list.get(index) : defaultValue;
    }

    public static String getHostName() {
        return IS_OS_WINDOWS ? System.getenv("COMPUTERNAME") : System.getenv("HOSTNAME");
    }

    public static int[] toCodePoints(CharSequence str) {
        if (str == null) {
            return null;
        } else if (str.length() == 0) {
            return ArrayUtils.EMPTY_INT_ARRAY;
        } else {
            String s = str.toString();
            int[] result = new int[s.codePointCount(0, s.length())];
            int index = 0;

            for(int i = 0; i < result.length; ++i) {
                result[i] = s.codePointAt(index);
                index += Character.charCount(result[i]);
            }

            return result;
        }
    }
}