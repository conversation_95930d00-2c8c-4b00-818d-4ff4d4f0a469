package com.tencent.andata.utils;

import com.tencent.tdw.security.authentication.v2.TauthClient;
import com.tencent.tdw.security.exceptions.SecureException;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;

public class AuthorityTDW {

    private static final String AUTH_HEADER = "secure-authentication";
    private static final String SERVICE_NAME = "dla-openapi";

    /**
     * 鉴权相关
     *
     * @param userName tdw用户名
     * @param cmk 对应key
     */
    public static HashMap<String, String> getAuthorityKey(String userName, String cmk) throws SecureException {
        // 指定tdw认证地址
        System.setProperty("tdw.security.url", "http://auth.tdw.oa.com/");
        TauthClient client = new TauthClient(userName, cmk);
        String authenticatedToken = client.getAuthentication(SERVICE_NAME);

        HashMap<String, String> header = new HashMap<>();
        header.put(AUTH_HEADER, authenticatedToken);
        return header;
    }
}