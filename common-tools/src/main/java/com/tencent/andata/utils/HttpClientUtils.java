package com.tencent.andata.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * HTTP客户端工具类
 * 提供GET和POST请求功能，支持自定义超时、请求头、重试机制，并将响应直接转换为指定对象。
 * 此工具类封装了 Apache HttpClient，通过连接池优化了性能。
 * {@code HttpClientUtils} 实例实现了 {@link java.io.Closeable}接口，
 * 强烈建议使用 try-with-resources 语句或在使用完毕后手动调用 {@code close()} 方法以释放资源。
 *
 * <p><b>主要特性:</b></p>
 * <ul>
 *   <li>通过Builder模式进行灵活配置</li>
 *   <li>通过连接复用提升性能</li>
 *   <li>支持GET和POST同步请求</li>
 *   <li>可自定义连接超时、套接字超时和最大重试次数</li>
 *   <li>支持自定义请求头</li>
 *   <li>POST请求时自动将{@code Map<String, Object>}数据序列化为JSON字符串</li>
 *   <li>支持将响应JSON自动反序列化为指定类型的对象</li>
 *   <li>统一的异常处理机制，抛出 {@link HttpClientException}</li>
 * </ul>
 *
 * <p><b>使用Builder进行配置 (推荐):</b></p>
 * <pre>{@code
 * try (HttpClientUtils client = HttpClientUtils.builder()
 *                                     .socketTimeout(15000) // 15秒读取超时
 *                                     .connectTimeout(5000)  // 5秒连接超时
 *                                     .maxRetries(3)         // 最多重试3次
 *                                     .build()) {
 *     String response = client.get("https://api.example.com/data");
 *     System.out.println("GET Response: " + response);
 *
 *     // 获取对象
 *     MyDataObject dataObject = client.getAsObject("https://api.example.com/object", MyDataObject.class);
 *     if (dataObject != null) {
 *         System.out.println("GET Object: " + dataObject.getName());
 *     }
 *
 *     Map<String, Object> postParams = new HashMap<>();
 *     postParams.put("key", "value");
 *     UserResponse userRsp = client.postAsObject("https://api.example.com/user", postParams, UserResponse.class);
 *     if (userRsp != null) {
 *         System.out.println("POST User ID: " + userRsp.getId());
 *     }
 *
 * } catch (HttpClientException e) {
 *     System.err.println("HTTP 请求失败: " + e.getMessage());
 * } catch (IOException e) {
 *     System.err.println("关闭 HttpClientUtils 失败: " + e.getMessage());
 * }
 * }</pre>
 *
 * <AUTHOR> (由AI助手重构和增强)
 * @version 1.3 (性能和可读性优化版本)
 * @since 1.0
 */
public class HttpClientUtils implements Serializable, Closeable {

    private static final long serialVersionUID = 2L;
    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);

    // 共享的ObjectMapper实例，线程安全
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // HTTP相关常量
    public static final String CONTENT_TYPE_JSON = "application/json";
    public static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String CONTENT_ENCODING_UTF8 = "UTF-8";

    // 默认配置常量
    private static final int DEFAULT_SOCKET_TIMEOUT = 30000;
    private static final int DEFAULT_CONNECT_TIMEOUT = 10000;
    private static final int DEFAULT_MAX_RETRIES = 3;
    private static final int HTTP_SUCCESS_MIN = 200;
    private static final int HTTP_SUCCESS_MAX = 299;
    private static final int TRUNCATE_LENGTH = 200;
    private static final int ERROR_MSG_LENGTH = 100;

    // 缓存RequestConfig实例以提高性能
    private static final Map<String, RequestConfig> CONFIG_CACHE = new ConcurrentHashMap<>();

    private final int socketTimeout;
    private final int connectTimeout;
    private final int maxRetries;

    private transient RequestConfig requestConfig;
    private transient CloseableHttpClient httpClient;

    /**
     * 默认构造函数。
     * 使用默认的超时配置 (连接超时10秒，读取超时30秒) 和默认最大重试次数 (3次)。
     */
    public HttpClientUtils() {
        this(DEFAULT_SOCKET_TIMEOUT, DEFAULT_CONNECT_TIMEOUT, DEFAULT_MAX_RETRIES);
    }

    /**
     * 构造函数。
     * 使用指定的超时配置和默认最大重试次数 (3次)。
     */
    public HttpClientUtils(int socketTimeout, int connectTimeout) {
        this(socketTimeout, connectTimeout, DEFAULT_MAX_RETRIES);
    }

    /**
     * 私有构造函数，供Builder和公共构造函数调用。
     */
    private HttpClientUtils(int socketTimeout, int connectTimeout, int maxRetries) {
        validateTimeoutParameters(socketTimeout, connectTimeout);
        validateMaxRetries(maxRetries);

        this.socketTimeout = socketTimeout;
        this.connectTimeout = connectTimeout;
        this.maxRetries = maxRetries;

        initializeHttpComponents();
    }

    /**
     * 创建Builder实例的静态方法
     */
    public static Builder builder() {
        return new Builder();
    }

    // --- Builder Class ---
    public static class Builder {
        private int socketTimeout = DEFAULT_SOCKET_TIMEOUT;
        private int connectTimeout = DEFAULT_CONNECT_TIMEOUT;
        private int maxRetries = DEFAULT_MAX_RETRIES;

        public Builder socketTimeout(int socketTimeout) {
            this.socketTimeout = socketTimeout;
            return this;
        }

        public Builder connectTimeout(int connectTimeout) {
            this.connectTimeout = connectTimeout;
            return this;
        }

        public Builder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }

        public HttpClientUtils build() {
            return new HttpClientUtils(this.socketTimeout, this.connectTimeout, this.maxRetries);
        }
    }

    // --- GET方法 ---

    public String get(String url) throws HttpClientException {
        return get(url, null);
    }

    public String get(String url, Map<String, String> headers) throws HttpClientException {
        validateUrl(url);
        HttpGet httpGet = new HttpGet(url);

        return executeRequest(httpGet, headers, url);
    }

    public <T> T getAsObject(String url, Class<T> responseType) throws HttpClientException {
        return getAsObject(url, null, responseType);
    }

    public <T> T getAsObject(String url, Map<String, String> headers, Class<T> responseType) throws HttpClientException {
        String responseString = get(url, headers);
        return deserializeResponse(responseString, responseType, url);
    }

    // --- POST方法 ---

    public String post(String url, Map<String, Object> data) throws HttpClientException {
        return post(url, data, null);
    }

    public String post(String url, Map<String, Object> data, Map<String, String> headers) throws HttpClientException {
        validateUrl(url);
        validateData(data);

        String jsonData = serializeToJson(data, url);
        return post(url, jsonData, headers);
    }

    public String post(String url, String data, Map<String, String> headers) throws HttpClientException {
        validateUrl(url);
        HttpPost httpPost = new HttpPost(url);

        setupPostEntity(httpPost, data);
        return executeRequest(httpPost, headers, url);
    }

    public <T> T postAsObject(String url, Map<String, Object> jsonData, Class<T> responseType) throws HttpClientException {
        return postAsObject(url, jsonData, null, responseType);
    }

    public <T> T postAsObject(String url, Map<String, Object> jsonData, Map<String, String> headers, Class<T> responseType) throws HttpClientException {
        String responseString = post(url, jsonData, headers);
        return deserializeResponse(responseString, responseType, url);
    }

    public <T> T postAsObject(String url, String stringData, Class<T> responseType) throws HttpClientException {
        return postAsObject(url, stringData, null, responseType);
    }

    public <T> T postAsObject(String url, String stringData, Map<String, String> headers, Class<T> responseType) throws HttpClientException {
        String responseString = post(url, stringData, headers);
        return deserializeResponse(responseString, responseType, url);
    }

    // --- 核心执行方法 ---

    /**
     * 执行HTTP请求的核心方法，统一处理GET和POST请求
     */
    private String executeRequest(HttpRequestBase request, Map<String, String> headers, String url) throws HttpClientException {
        try {
            setupRequest(request, headers);
            logger.debug("Executing {} request to URL: {} with {} retries configuration.",
                    request.getMethod(), url, this.maxRetries);

            try (CloseableHttpResponse response = this.httpClient.execute(request)) {
                return handleResponse(response, url);
            }
        } catch (IOException e) {
            String errorMsg = buildErrorMessage("I/O error during request execution for URL", url, e);
            logger.error(errorMsg, e);
            throw new HttpClientException(errorMsg, e);
        } catch (Exception e) {
            if (e instanceof HttpClientException) {
                throw (HttpClientException) e;
            }
            String errorMsg = buildErrorMessage(request.getMethod() + " request failed for URL", url, e);
            logger.error(errorMsg, e);
            throw new HttpClientException(errorMsg, e);
        }
    }

    /**
     * 处理HTTP响应
     */
    private String handleResponse(CloseableHttpResponse response, String url) throws IOException, HttpClientException {
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = extractResponseBody(response);

        if (isSuccessStatusCode(statusCode)) {
            logger.debug("Request successful for URL: {}, Status: {}, Response length: {}",
                    url, statusCode, responseBody.length());
            return responseBody;
        } else {
            String errorMsg = buildHttpErrorMessage(url, statusCode, responseBody);
            logger.error(errorMsg);
            throw new HttpClientException(errorMsg);
        }
    }

    /**
     * JSON反序列化方法
     */
    private <T> T deserializeResponse(String responseString, Class<T> responseType, String urlContext) throws HttpClientException {
        if (responseString == null) {
            logger.warn("Received null response string for URL [{}] before deserialization to type [{}]. Returning null.",
                    urlContext, responseType.getName());
            return null;
        }

        // 如果期望的类型是String，直接返回
        if (String.class.equals(responseType)) {
            return responseType.cast(responseString);
        }

        // 对于非String类型，如果响应为空，返回null
        if (responseString.trim().isEmpty()) {
            logger.warn("Empty response string for URL [{}] when expecting non-String type [{}]. Returning null.",
                    urlContext, responseType.getName());
            return null;
        }

        // JSON反序列化
        try {
            return OBJECT_MAPPER.readValue(responseString, responseType);
        } catch (JsonProcessingException e) {
            String truncatedResponse = truncateString(responseString, TRUNCATE_LENGTH);
            String errorMsg = String.format("Failed to deserialize JSON response from URL [%s] to type [%s]. Response preview: [%s]",
                    urlContext, responseType.getName(), truncatedResponse);
            logger.error(errorMsg, e);
            throw new HttpClientException(errorMsg, e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    // --- 辅助方法 ---

    /**
     * 初始化HTTP组件
     */
    private void initializeHttpComponents() {
        this.requestConfig = getOrCreateRequestConfig(this.socketTimeout, this.connectTimeout);
        this.httpClient = createHttpClient(this.requestConfig, this.maxRetries);
    }

    /**
     * 获取或创建RequestConfig，使用缓存提高性能
     */
    private static RequestConfig getOrCreateRequestConfig(int socketTimeout, int connectTimeout) {
        String cacheKey = socketTimeout + "_" + connectTimeout;
        return CONFIG_CACHE.computeIfAbsent(cacheKey, k ->
                RequestConfig.custom()
                        .setSocketTimeout(socketTimeout)
                        .setConnectTimeout(connectTimeout)
                        .setConnectionRequestTimeout(connectTimeout)
                        .build()
        );
    }

    /**
     * 创建HttpClient实例
     */
    private static CloseableHttpClient createHttpClient(RequestConfig config, int maxRetries) {
        HttpClientBuilder clientBuilder = HttpClients.custom()
                .setDefaultRequestConfig(config);

        if (maxRetries > 0) {
            clientBuilder.setRetryHandler(new DefaultHttpRequestRetryHandler(maxRetries, true));
        }
        return clientBuilder.build();
    }

    /**
     * 设置请求基本配置
     */
    private void setupRequest(HttpRequestBase request, Map<String, String> headers) {
        request.setConfig(requestConfig);
        if (headers != null) {
            headers.entrySet().stream()
                    .filter(entry -> entry.getKey() != null && entry.getValue() != null)
                    .forEach(entry -> request.setHeader(entry.getKey(), entry.getValue()));
        }
    }

    /**
     * 设置POST请求实体
     */
    private static void setupPostEntity(HttpPost httpPost, String data) {
        if (data != null && !data.isEmpty()) {
            StringEntity entity = new StringEntity(data, StandardCharsets.UTF_8);
            if (httpPost.getFirstHeader(HEADER_CONTENT_TYPE) == null) {
                entity.setContentType(CONTENT_TYPE_JSON);
            }
            httpPost.setEntity(entity);
        }
    }

    /**
     * 提取响应体
     */
    private static String extractResponseBody(CloseableHttpResponse response) throws IOException {
        if (response.getEntity() != null) {
            return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        }
        return "";
    }

    /**
     * 序列化数据为JSON
     */
    private static String serializeToJson(Map<String, Object> data, String url) throws HttpClientException {
        try {
            return OBJECT_MAPPER.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            String errorMsg = buildErrorMessage("Failed to serialize request data for URL", url, e);
            logger.error(errorMsg, e);
            throw new HttpClientException(errorMsg, e);
        }
    }

    /**
     * 判断是否为成功状态码
     */
    private static boolean isSuccessStatusCode(int statusCode) {
        return statusCode >= HTTP_SUCCESS_MIN && statusCode <= HTTP_SUCCESS_MAX;
    }

    /**
     * 构建错误消息 - 优化StringBuilder使用
     */
    private static String buildErrorMessage(String message, String url, Throwable cause) {
        int capacity = message.length() + url.length() + 50;
        if (cause != null && cause.getMessage() != null) {
            capacity += Math.min(cause.getMessage().length(), ERROR_MSG_LENGTH);
        }

        StringBuilder sb = new StringBuilder(capacity);
        sb.append(message).append(": ").append(url);
        if (cause != null && cause.getMessage() != null) {
            sb.append(", Cause: ").append(truncateString(cause.getMessage(), ERROR_MSG_LENGTH));
        }
        return sb.toString();
    }

    /**
     * 构建HTTP错误消息
     */
    private static String buildHttpErrorMessage(String url, int statusCode, String responseBody) {
        int capacity = 150 + url.length();
        if (responseBody != null) {
            capacity += Math.min(responseBody.length(), TRUNCATE_LENGTH);
        }

        StringBuilder sb = new StringBuilder(capacity);
        sb.append("HTTP request failed for URL: ").append(url)
                .append(", Status: ").append(statusCode);
        if (responseBody != null && !responseBody.isEmpty()) {
            sb.append(", Response: ").append(truncateString(responseBody, TRUNCATE_LENGTH));
        }
        return sb.toString();
    }

    /**
     * 字符串截断工具方法
     */
    private static String truncateString(String str, int maxLength) {
        if (str == null || str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength) + "...";
    }

    // --- 验证方法 ---

    private static void validateUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL cannot be null or empty");
        }
        String trimmedUrl = url.trim();
        if (!trimmedUrl.startsWith("http://") && !trimmedUrl.startsWith("https://")) {
            throw new IllegalArgumentException("URL must start with http:// or https://");
        }
    }

    private static void validateData(Map<String, Object> data) {
        if (data == null) {
            throw new IllegalArgumentException("Request data map cannot be null");
        }
    }

    private static void validateTimeoutParameters(int socketTimeout, int connectTimeout) {
        if (socketTimeout <= 0) {
            throw new IllegalArgumentException("Socket timeout must be greater than 0");
        }
        if (connectTimeout <= 0) {
            throw new IllegalArgumentException("Connect timeout must be greater than 0");
        }
    }

    private static void validateMaxRetries(int maxRetries) {
        if (maxRetries < 0) {
            throw new IllegalArgumentException("Max retries cannot be negative");
        }
    }

    // --- 配置和工具方法 ---

    public HttpClientConfig getConfig() {
        return new HttpClientConfig(socketTimeout, connectTimeout, maxRetries);
    }

    /**
     * 配置类
     */
    public static class HttpClientConfig {
        private final int socketTimeout;
        private final int connectTimeout;
        private final int maxRetries;

        public HttpClientConfig(int socketTimeout, int connectTimeout, int maxRetries) {
            this.socketTimeout = socketTimeout;
            this.connectTimeout = connectTimeout;
            this.maxRetries = maxRetries;
        }

        public int getSocketTimeout() { return socketTimeout; }
        public int getConnectTimeout() { return connectTimeout; }
        public int getMaxRetries() { return maxRetries; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            HttpClientConfig that = (HttpClientConfig) o;
            return socketTimeout == that.socketTimeout &&
                    connectTimeout == that.connectTimeout &&
                    maxRetries == that.maxRetries;
        }

        @Override
        public int hashCode() {
            return Objects.hash(socketTimeout, connectTimeout, maxRetries);
        }

        @Override
        public String toString() {
            return "HttpClientConfig{" +
                    "socketTimeout=" + socketTimeout +
                    ", connectTimeout=" + connectTimeout +
                    ", maxRetries=" + maxRetries +
                    '}';
        }
    }

    /**
     * 自定义异常类
     */
    public static class HttpClientException extends Exception {
        private static final long serialVersionUID = 1L;

        public HttpClientException(String message) {
            super(message);
        }

        public HttpClientException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    @Override
    public void close() throws IOException {
        if (this.httpClient != null) {
            try {
                this.httpClient.close();
                logger.info("HttpClientUtils's underlying CloseableHttpClient has been closed.");
            } catch (IOException e) {
                logger.error("Error closing CloseableHttpClient.", e);
                throw e;
            }
        }
    }

    /**
     * 序列化后重建对象时的处理
     */
    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
        initializeHttpComponents();
        logger.info("HttpClientUtils instance deserialized and re-initialized with maxRetries={}", this.maxRetries);
    }
}