package com.tencent.andata.utils;

import com.google.common.base.CaseFormat;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * KV 分组的Builder，外部注入对应的Struct来生成对应的
 */
public class KVConfBuilder<T> {

    private final Constructor<? extends T> ctor;
    private final Class<? extends T> impl;
    private RainbowUtils rainbowUtils;
    private String groupName;

    public KVConfBuilder(Class<? extends T> impl) throws NoSuchMethodException {
        this.impl = impl;
        ctor = impl.getConstructor();
    }

    public KVConfBuilder<T> setRainbowUtils(RainbowUtils rainbowUtils) {
        this.rainbowUtils = rainbowUtils;
        return this;
    }

    public KVConfBuilder<T> setGroupName(String groupName) {
        this.groupName = groupName;
        return this;
    }

    /**
     * 构建对应的产物，通过反射捞出来对应的属性名，从kvData里取出来，并根据是否是Integer来确定是否转换
     *
     * @return T
     */
    public T build()
            throws InvocationTargetException, InstantiationException, IllegalAccessException, NoSuchMethodException {
        // 将Rainbow数据转换成我们内部的数据
        Map<String, String> reflectMap =
                rainbowUtils
                        .getKVGroupMap(groupName)
                        .entrySet()
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, entry.getKey()),
                                        entry -> entry.getValue().getValue()
                                )
                        );

        return new Map2ObjectReflector<>(this.impl).build(reflectMap);
    }
}