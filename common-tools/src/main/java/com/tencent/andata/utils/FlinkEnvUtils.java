package com.tencent.andata.utils;

import static org.apache.flink.configuration.NettyShuffleEnvironmentOptions.PARTITION_SCORE_BASED_CREDIT;
import static org.apache.flink.streaming.api.environment.CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION;

import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.TableEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class FlinkEnvUtils {

    /**
     * Checkpoint 参数相关配置，but 不设置 StateBackend，即：读取 flink-conf.yaml 文件的配置
     *
     * @param env env
     */
    public static void setCheckpointConfig(StreamExecutionEnvironment env) {
        // ck 设置
        CheckpointConfig ckConfig = env.getCheckpointConfig();
        ckConfig.setCheckpointInterval(10 * 60 * 1000L);
        ckConfig.setMinPauseBetweenCheckpoints(60 * 1000L);
        ckConfig.setMaxConcurrentCheckpoints(1);
        ckConfig.setCheckpointTimeout(4 * 60 * 60 * 1000L);
        ckConfig.enableUnalignedCheckpoints(); // 启用非对齐 Checkpoint
        ckConfig.setTolerableCheckpointFailureNumber(2000);

        // ck 恢复相关配置
        Configuration configuration = new Configuration();
        configuration.setString("state.backend.incremental", "true");
        configuration.setString("state.checkpoints.num-retained", "10");
        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                15, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));
        env.configure(configuration, Thread.currentThread().getContextClassLoader());

        env.getCheckpointConfig().setExternalizedCheckpointCleanup(RETAIN_ON_CANCELLATION);
    }

    /**
     * 获取flink运行环境
     *
     * @param args 参数列表
     * @return FlinkEnv
     */
    public static FlinkEnv getStreamTableEnv(String[] args, boolean setCk) {

        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        Configuration configuration = Configuration.fromMap(parameterTool.toMap());
        configuration.setBoolean(PARTITION_SCORE_BASED_CREDIT, true);
        configuration.setString("rest.flamegraph.enabled", "true");

        StreamExecutionEnvironment env =
                StreamExecutionEnvironment.getExecutionEnvironment(configuration);

        if (setCk) {
            setCheckpointConfig(env);
        }

        env.setRestartStrategy(RestartStrategies.failureRateRestart(6,
                Time.of(10L, TimeUnit.MINUTES),
                Time.of(5L, TimeUnit.SECONDS)));

        env.getConfig().setGlobalJobParameters(parameterTool);

        EnvironmentSettings settings = EnvironmentSettings
                .newInstance()
                .inStreamingMode()
                .build();

        StreamTableEnvironment tEnv = StreamTableEnvironment.create(env, settings);

        FlinkEnv flinkEnv = FlinkEnv
                .builder()
                .streamExecutionEnvironment(env)
                .statementSet(tEnv.createStatementSet())
                .streamTableEnvironment(tEnv)
                .build();

        loadHiveModule(flinkEnv, parameterTool);

        return flinkEnv;
    }

    /**
     * 获取flink运行环境
     *
     * @param args 参数列表
     * @return FlinkEnv
     */
    public static FlinkEnv getStreamTableEnv(String[] args) {
        return getStreamTableEnv(args, true);
    }

    /**
     * 通过加载 Hive 模块，来使用 Hive 的函数
     *
     * @param flinkEnv flinkEnv
     * @param parameterTool parameterTool
     */
    private static void loadHiveModule(FlinkEnv flinkEnv, ParameterTool parameterTool) {

        String version = "2.3.6";

        HiveModuleV2 hiveModuleV2 = new HiveModuleV2(version);

        final boolean enableHiveModuleLoadFirst = parameterTool.getBoolean("enable.hive.module.load-first", false);

        Optional.ofNullable(flinkEnv.streamTEnv())
                .ifPresent(s -> {
                    if (enableHiveModuleLoadFirst) {
                        s.loadModule("default", hiveModuleV2);
                    }
                });

        Optional.ofNullable(flinkEnv.batchTEnv())
                .ifPresent(s -> {
                    if (enableHiveModuleLoadFirst) {
                        s.loadModule("default", hiveModuleV2);
                    }
                });

        flinkEnv.setHiveModuleV2(hiveModuleV2);
    }

    /**
     * 获取批处理环境
     */
    public static FlinkEnv getBatchTableEnv(String[] args) {

        StreamExecutionEnvironment env =
                StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(new Configuration());

        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        env.setRestartStrategy(RestartStrategies.failureRateRestart(6, org.apache.flink.api.common.time.Time
                .of(10L, TimeUnit.MINUTES), org.apache.flink.api.common.time.Time.of(5L, TimeUnit.SECONDS)));
        env.getConfig().setGlobalJobParameters(parameterTool);
        env.setParallelism(1);

        // ck 设置
        env.enableCheckpointing(30 * 1000L, CheckpointingMode.AT_LEAST_ONCE);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(3L);
        env.getCheckpointConfig().setExternalizedCheckpointCleanup(RETAIN_ON_CANCELLATION);

        EnvironmentSettings settings = EnvironmentSettings
                .newInstance()
                .inBatchMode()
                .build();

        TableEnvironment tEnv = TableEnvironment.create(settings);

        FlinkEnv flinkEnv = FlinkEnv
                .builder()
                .streamExecutionEnvironment(env)
                .statementSet(tEnv.createStatementSet())
                .tableEnvironment(tEnv)
                .build();

        loadHiveModule(flinkEnv, parameterTool);

        return flinkEnv;
    }

    @Builder
    @Data
    public static class FlinkEnv {

        private StreamExecutionEnvironment streamExecutionEnvironment;
        private StreamTableEnvironment streamTableEnvironment;
        private TableEnvironment tableEnvironment;
        private HiveModuleV2 hiveModuleV2;

        private StatementSet statementSet;

        public StreamTableEnvironment streamTEnv() {
            return this.streamTableEnvironment;
        }

        public StatementSet stmtSet() {
            return this.statementSet;
        }

        public TableEnvironment batchTEnv() {
            return this.tableEnvironment;
        }

        public StreamExecutionEnvironment env() {
            return this.streamExecutionEnvironment;
        }

        public HiveModuleV2 hiveModuleV2() {
            return this.hiveModuleV2;
        }
    }
}