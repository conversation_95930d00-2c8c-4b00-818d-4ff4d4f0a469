package com.tencent.andata.utils;

import java.io.IOException;
import java.util.List;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Admin;
import org.apache.hadoop.hbase.client.ColumnFamilyDescriptor;
import org.apache.hadoop.hbase.client.ColumnFamilyDescriptorBuilder;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.RegionInfo;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.client.TableDescriptor;
import org.apache.hadoop.hbase.client.TableDescriptorBuilder;
import org.apache.hadoop.hbase.util.Bytes;

public class HbaseUtil {

    private static ThreadLocal<Connection> connHolder = new ThreadLocal<>();
    private static String ZOOKEEPER_ZNODE_PARENT = "/hbase_vip_performance_yz_v24";
    private static String ZOOKEEPER_QUORUM = "*************,*************,*************,*************,*************";

    /**
     * 创建connection
     *
     * @throws IOException
     */
    public static void makeHbaseConnection() throws IOException {
        Connection connection = connHolder.get();
        if (connection == null) {
            Configuration conf = HBaseConfiguration.create();
            conf.set("hbase.security.authorization", "false");
            conf.set("hbase.zookeeper.quorum", ZOOKEEPER_QUORUM);
            conf.set("hbase.zookeeper.property.clientPort", "2181");
            conf.set("zookeeper.znode.parent", ZOOKEEPER_ZNODE_PARENT);
            connection = ConnectionFactory.createConnection(conf);
            connHolder.set(connection);
        }
    }

    /**
     * 关闭连接
     *
     * @throws IOException
     */
    public static void closeHbaseConn() throws IOException {
        Connection connection = connHolder.get();
        if (connection != null) {
            connection.close();
            connHolder.remove();
        }
    }

    /**
     * 添加一行数据
     *
     * @param tableName 表名
     * @param rowKey 行号
     * @param family 列族
     * @param column 列名
     * @param value
     * @throws IOException
     */
    public static void insertData(String tableName, String rowKey, String family, String column, String value)
            throws IOException {
        //获取连接
        Connection connection = connHolder.get();
        //获取表对象
        Table table = connection.getTable(TableName.valueOf(tableName));
        //获取添加对象
        Put put = new Put(Bytes.toBytes(rowKey));
        //添加一列
        put.addColumn(Bytes.toBytes(family), Bytes.toBytes(column), Bytes.toBytes(value));
        //添加
        table.put(put);
        //关闭
        table.close();

    }

    /**
     * 创建表
     *
     * @param tableName
     * @param family
     * @throws IOException
     */
    public static void createTable(String tableName, String family) throws IOException {
        Connection connection = connHolder.get();
        //获取admin
        Admin admin = connection.getAdmin();

        //列族描述对象建造者
        ColumnFamilyDescriptorBuilder columnFamilyDescriptorBuilder = ColumnFamilyDescriptorBuilder
                .newBuilder(Bytes.toBytes(family));
        //设置最大版本号
        columnFamilyDescriptorBuilder.setMaxVersions(3);
        //列族描述对象
        ColumnFamilyDescriptor columnFamilyDescriptor = columnFamilyDescriptorBuilder.build();

        //表描述对象建造者
        TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName));
        //将列族对象添加进表描述
        tableDescriptorBuilder.setColumnFamily(columnFamilyDescriptor);
        //创建表描述对象
        TableDescriptor tableDescriptor = tableDescriptorBuilder.build();

        //创建表
        admin.createTable(tableDescriptor);
        admin.close();
    }


    /**
     * 创建表(多列族)
     *
     * @param tableName
     * @param familys
     * @throws IOException
     */
    public static void createTable(String tableName, String[] familys) throws IOException {
        Connection connection = connHolder.get();
        //获取admin
        Admin admin = connection.getAdmin();
        //表描述对象建造者
        TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName));

        for (String family : familys) {
            //列族描述对象建造者
            ColumnFamilyDescriptorBuilder columnFamilyDescriptorBuilder = ColumnFamilyDescriptorBuilder.newBuilder(
                    Bytes.toBytes(family));
            //设置最大版本号
            columnFamilyDescriptorBuilder.setMaxVersions(3);
            //将列族对象添加进表描述
            tableDescriptorBuilder.setColumnFamily(columnFamilyDescriptorBuilder.build());
        }

        //创建表描述对象
        TableDescriptor tableDescriptor = tableDescriptorBuilder.build();

        //创建表
        admin.createTable(tableDescriptor);
        admin.close();
    }

    /**
     * 根据行号查询数据
     *
     * @param tableName
     * @param rowKey
     * @return
     */
    public static Result selectDataByRowkey(String tableName, String rowKey) throws IOException {
        Connection connection = connHolder.get();
        //获取表
        Table table = connection.getTable(TableName.valueOf(tableName));
        //获取表描述对象
        Get get = new Get(Bytes.toBytes(rowKey));
        Result result = table.get(get);
        return result;
    }

    /**
     * 获取某一列族中某一列的某一行数据
     *
     * @param tableName
     * @param rowKey
     * @param family
     * @param column
     * @return
     */
    public static Result selectDataByCol(String tableName, String rowKey, String family, String column)
            throws IOException {
        Connection connection = connHolder.get();
        //获取表
        Table table = connection.getTable(TableName.valueOf(tableName));
        //获取表描述对象
        Get get = new Get(Bytes.toBytes(rowKey));
        get.addColumn(Bytes.toBytes(family), Bytes.toBytes(column));
        Result result = table.get(get);
        return result;

    }


    /**
     * 打印result
     *
     * @param result
     */
    public static void showResult(Result result) {
        Cell[] cells = result.rawCells();
        for (Cell cell : cells) {
            System.out.println("family:" + Bytes.toString(CellUtil.cloneFamily(cell)));
            System.out.println("qualifier:" + Bytes.toString(CellUtil.cloneQualifier(cell)));
            System.out.println("row:" + Bytes.toString(CellUtil.cloneRow(cell)));
            System.out.println("value:" + Bytes.toString(CellUtil.cloneValue(cell)));
        }
    }

    /**
     * 删除表
     *
     * @param tableName
     * @throws IOException
     */
    public static void deleteTable(String tableName) throws IOException {
        Connection connection = connHolder.get();
        Admin admin = connection.getAdmin();
        TableName name = TableName.valueOf(tableName);
        if (admin.tableExists(name)) {
            admin.deleteTable(name);
        }
    }

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    public static boolean tableExists(String tableName) throws IOException {
        try {

            Connection connection = connHolder.get();
            Admin admin = connection.getAdmin();
            return admin.tableExists(TableName.valueOf(tableName));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static void mergeRegion(String tableName) throws IOException {
        Connection connection = connHolder.get();
        Admin admin = connection.getAdmin();
        List<RegionInfo> regions = admin.getRegions(TableName.valueOf(tableName));
        regions.sort((r1, r2) -> Bytes.compareTo(r1.getStartKey(), r2.getStartKey()));

        RegionInfo preRegion = null;
        for (RegionInfo r : regions) {
            int index = regions.indexOf(r);
            if (index % 2 == 0) {
                preRegion = r;
            } else {
                // preRegion & r 组成二维数组
                byte[][] regionsToMerge = {preRegion.getEncodedNameAsBytes(), r.getEncodedNameAsBytes()};
                admin.mergeRegionsAsync(regionsToMerge, true);
            }
        }
    }
}