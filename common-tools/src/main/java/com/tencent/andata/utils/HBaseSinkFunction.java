package com.tencent.andata.utils;

import static com.tencent.andata.utils.HBaseSinkFunction.OperationType.DELETE;
import static com.tencent.andata.utils.HBaseSinkFunction.OperationType.INSERT;
import static org.apache.flink.connector.hbase.util.HBaseConfigurationUtil.deserializeConfiguration;
import static org.apache.flink.connector.hbase.util.HBaseConfigurationUtil.getHBaseConfiguration;
import static org.apache.flink.connector.hbase.util.HBaseConfigurationUtil.serializeConfiguration;

import com.tencent.andata.utils.HBaseSinkFunction.HbaseRow;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.util.StringUtils;
import org.apache.flink.util.concurrent.ExecutorThreadFactory;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.BufferedMutator;
import org.apache.hadoop.hbase.client.BufferedMutatorParams;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Delete;
import org.apache.hadoop.hbase.client.Mutation;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.RetriesExhaustedWithDetailsException;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HBaseSinkFunction extends RichSinkFunction<HbaseRow>
        implements CheckpointedFunction, BufferedMutator.ExceptionListener {

    private static final Logger LOG = LoggerFactory.getLogger(HBaseSinkFunction.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    private String hTableName;
    private final long bufferFlushMaxSizeInBytes;
    private final long bufferFlushMaxMutations;
    private final long bufferFlushIntervalMillis;

    private transient Connection connection;
    private transient BufferedMutator mutator;

    private transient ScheduledExecutorService executor;
    private transient ScheduledFuture<?> scheduledFuture;
    private transient AtomicLong numPendingRequests;
    private final byte[] serializedConfig;
    private transient volatile boolean closed = false;

    private final AtomicReference<Throwable> failureThrowable = new AtomicReference<>();
    public HBaseSinkFunction(
            String hTableName,
            Configuration conf,
            long bufferFlushMaxSizeInBytes,
            long bufferFlushMaxMutations,
            long bufferFlushIntervalMillis) {
        this.hTableName = hTableName;
        this.bufferFlushMaxSizeInBytes = bufferFlushMaxSizeInBytes;
        this.bufferFlushMaxMutations = bufferFlushMaxMutations;
        this.bufferFlushIntervalMillis = bufferFlushIntervalMillis;
        this.serializedConfig = serializeConfiguration(conf);
    }

    private Configuration prepareRuntimeConfiguration() throws IOException {

        Configuration runtimeConfig =
                deserializeConfiguration(serializedConfig, getHBaseConfiguration());

        if (StringUtils.isNullOrWhitespaceOnly(runtimeConfig.get(HConstants.ZOOKEEPER_QUORUM))) {
            LOG.error(
                    "Can not connect to HBase without {} configuration",
                    HConstants.ZOOKEEPER_QUORUM);
            throw new IOException(
                    "Check HBase configuration failed, lost: '"
                            + HConstants.ZOOKEEPER_QUORUM
                            + "'!");
        }

        return runtimeConfig;
    }

    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        LOG.info("start open ...");
        org.apache.hadoop.conf.Configuration config = prepareRuntimeConfiguration();
        try {
            this.numPendingRequests = new AtomicLong(0);

            if (null == connection) {
                this.connection = ConnectionFactory.createConnection(config);
            }
            // create a parameter instance, set the table name and custom listener reference.
            BufferedMutatorParams params =
                    new BufferedMutatorParams(TableName.valueOf(hTableName)).listener(this);
            if (bufferFlushMaxSizeInBytes > 0) {
                params.writeBufferSize(bufferFlushMaxSizeInBytes);
            }
            this.mutator = connection.getBufferedMutator(params);

            if (bufferFlushIntervalMillis > 0 && bufferFlushMaxMutations != 1) {
                this.executor =
                        Executors.newScheduledThreadPool(
                                1, new ExecutorThreadFactory("hbase-upsert-sink-flusher"));
                this.scheduledFuture =
                        this.executor.scheduleWithFixedDelay(
                                () -> {
                                    if (closed) {
                                        return;
                                    }
                                    try {
                                        flush();
                                    } catch (Exception e) {
                                        // fail the sink and skip the rest of the items
                                        // if the failure handler decides to throw an exception
                                        failureThrowable.compareAndSet(null, e);
                                    }
                                },
                                bufferFlushIntervalMillis,
                                bufferFlushIntervalMillis,
                                TimeUnit.MILLISECONDS);
            }
        } catch (TableNotFoundException tnfe) {
            LOG.error("The table {} not found ", hTableName, tnfe);
            throw new RuntimeException("HBase table '" + hTableName + "' not found.", tnfe);
        } catch (IOException ioe) {
            LOG.error("Exception while creating connection to HBase.", ioe);
            throw new RuntimeException("Cannot create connection to HBase.", ioe);
        }
        LOG.info("end open.");
    }


    @Override
    public void invoke(HbaseRow value, Context context) throws Exception {

        mutator.mutate(convertToMutation(value));

        // flush when the buffer number of mutations greater than the configured max size.
        long bufferFlushMaxMutations = 1000;
        if (numPendingRequests.incrementAndGet() >= bufferFlushMaxMutations) {
            flush();
        }
    }


    public Mutation convertToMutation(HbaseRow hbaseRow) throws Exception {

        OperationType type = hbaseRow.getType();
        if (type == INSERT) {
            return createPutMutation(hbaseRow);
        } else if (type == DELETE) {
            return createDeleteMutation(hbaseRow);
        } else {
            throw new Exception("Unsupported message kind: " + hbaseRow.getType());
        }
    }

    private void flush() throws IOException {
        mutator.flush();
        numPendingRequests.set(0);
    }

    @Override
    public void close() throws Exception {
        closed = true;

        if (mutator != null) {
            try {
                mutator.close();
            } catch (IOException e) {
                LOG.warn("Exception occurs while closing HBase BufferedMutator.", e);
            }
            this.mutator = null;
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (IOException e) {
                LOG.warn("Exception occurs while closing HBase Connection.", e);
            }
            this.connection = null;
        }

        if (scheduledFuture != null) {
            scheduledFuture.cancel(false);
            if (executor != null) {
                executor.shutdownNow();
            }
        }
    }


    public Put createPutMutation(HbaseRow rowData) {

        byte[] rowkey = Bytes.toBytes(rowData.getRowKey());
        if (rowkey.length == 0) {
            return null;
        }

        Put put = new Put(rowkey);
        byte[] familyBytes = Bytes.toBytes(rowData.getFamily());

        Map<String, Object> map = mapper.convertValue(rowData.getData(), Map.class);

        for (Map.Entry<String, Object> kv : map.entrySet()) {
            put.addColumn(familyBytes, Bytes.toBytes(kv.getKey()), Bytes.toBytes(String.valueOf(kv.getValue())));
        }
        return put;
    }


    public Delete createDeleteMutation(HbaseRow rowData) {
        byte[] rowkey = Bytes.toBytes(rowData.getRowKey());
        if (rowkey.length == 0) {
            return null;
        }
        // delete
        Delete delete = new Delete(rowkey);
        byte[] familyBytes = Bytes.toBytes(rowData.getFamily());

        Map<String, Object> map = mapper.convertValue(rowData.getData(), Map.class);

        for (Map.Entry<String, Object> kv : map.entrySet()) {
            delete.addColumn(familyBytes, Bytes.toBytes(kv.getKey()));
        }
        return delete;
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        while (numPendingRequests.get() != 0) {
            flush();
        }
    }

    @Override
    public void initializeState(FunctionInitializationContext functionInitializationContext) {

    }

    @Override
    public void onException(RetriesExhaustedWithDetailsException exception, BufferedMutator mutator) {
        failureThrowable.compareAndSet(null, exception);
    }

    public enum OperationType {
        INSERT, DELETE
    }

    public static class HbaseRow {

        private OperationType type;
        private String rowKey;
        private String family;
        private JsonNode data;

        public HbaseRow(OperationType type, String rowKey, String family, JsonNode data) {
            this.type = type;
            this.rowKey = rowKey;
            this.family = family;
            this.data = data;
        }

        public OperationType getType() {
            return type;
        }

        public void setType(OperationType type) {
            this.type = type;
        }

        public String getRowKey() {
            return rowKey;
        }

        public void setRowKey(String rowKey) {
            this.rowKey = rowKey;
        }

        public String getFamily() {
            return family;
        }

        public void setFamily(String family) {
            this.family = family;
        }

        public JsonNode getData() {
            return data;
        }

        public void setData(JsonNode data) {
            this.data = data;
        }
    }
}