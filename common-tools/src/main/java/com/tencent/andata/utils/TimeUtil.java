package com.tencent.andata.utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class TimeUtil {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    // 新增：支持常见的时间格式变体
    private static final DateTimeFormatter[] FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),           // 原有格式
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"),         // 毫秒格式（1位）
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SS"),        // 毫秒格式（2位）
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),       // 毫秒格式（3位）
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSS"),      // 毫秒格式（4位）
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSS"),     // 毫秒格式（5位）
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS"),    // 毫秒格式（6位）
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),         // ISO格式无毫秒
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),     // ISO格式含毫秒
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),           // 斜杠分隔
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss.SSS")        // 斜杠分隔含毫秒
    };

    // Instant表示的一定是utc时间
    public static Instant convert2Instant(LocalDateTime localDateTime) {
        return localDateTime.toInstant(ZoneOffset.of("+8"));
    }

    public static Instant convert2Instant(LocalDate localDate) {
        return localDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant();
    }

    public static ZonedDateTime convert2ZonedDateTime(Instant instant) {
        return instant.atZone(ZoneId.of("Asia/Shanghai"));
    }

    public static LocalDateTime convert2LocalDateTime(Instant instant) {
        return LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Shanghai"));
    }

    public static LocalDate convert2LocalDate(Instant instant) {
        return instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();
    }

    public static long getEpochMilli(Object time) {
        if (time instanceof Number) {
            return ((Number) time).longValue();
        }

        if (time instanceof String) {
            String timeStr = (String) time;
            if (isLong(timeStr)) {
                return Long.parseLong(timeStr);
            }

            return parseTimeString(timeStr);
        }

        throw new IllegalArgumentException("Unsupported time type: " + time.getClass().getName());
    }


    /**
     * 解析时间字符串，支持多种格式
     * 保持原有的预处理逻辑：替换T和+08:00
     *
     * @param timeStr 时间字符串
     * @return 时间戳（毫秒）
     */
    private static long parseTimeString(String timeStr) {
        // 保持原有的预处理逻辑
        String normalizedTimeStr = timeStr.replace("T", " ").replace("+08:00", "");

        // 尝试使用原有的格式器（保持向后兼容）
        try {
            return LocalDateTime.parse(normalizedTimeStr, FORMATTER)
                    .atZone(ZoneId.of("Asia/Shanghai"))
                    .toInstant()
                    .toEpochMilli();
        } catch (DateTimeParseException originalException) {
            // 原有格式失败时，尝试其他格式
            return parseWithMultipleFormats(normalizedTimeStr, originalException);
        }
    }

    /**
     * 使用多种格式尝试解析时间字符串
     *
     * @param timeStr 已预处理的时间字符串
     * @param originalException 原始解析异常
     * @return 时间戳（毫秒）
     */
    private static long parseWithMultipleFormats(String timeStr, DateTimeParseException originalException) {
        // 逐个尝试其他格式器
        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                return LocalDateTime.parse(timeStr, formatter)
                        .atZone(ZoneId.of("Asia/Shanghai"))
                        .toInstant()
                        .toEpochMilli();
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式器
            }
        }

        // 所有格式都失败时，抛出原始异常（保持原有错误信息）
        throw new IllegalArgumentException("Invalid date time format: " + timeStr, originalException);
    }

    private static boolean isLong(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        try {
            Long.parseLong(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 将LocalDateTime格式化为"yyyy-MM-dd HH:mm:ss"格式的字符串
     *
     * @param localDateTime 要格式化的时间对象
     * @return 格式化后的时间字符串
     */
    public static String formatDateTime(LocalDateTime localDateTime) {
        return localDateTime.format(FORMATTER);
    }
}