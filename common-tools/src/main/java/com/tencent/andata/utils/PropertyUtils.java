package com.tencent.andata.utils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import org.apache.avro.Schema;

public class PropertyUtils {

    public static Properties loadProperties(String name) {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();

        try (InputStream is = classLoader.getResourceAsStream(name)) {
            Properties properties = new Properties();
            properties.load(is);
            return properties;
        } catch (Exception e) {
            throw new RuntimeException("Failed to load properties file: " + name, e);
        }
    }

    public static Schema loadAvroSchema(String name) throws IOException {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();

        try (InputStream in = classLoader.getResourceAsStream(name)) {
            return new Schema.Parser().parse(in);
        }
    }
}