package com.tencent.andata.utils.speedlayer;

import com.tencent.andata.utils.AuthorityTDW;
import com.tencent.tdw.security.exceptions.SecureException;
import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * API Schema配置管理
 */
public class ApiSchemaConfig {

    private String userName = "cesarezhang";
    private String cmk = "NGY2N2JjYmUwYTY0NTI4ZjMwZTg0NTE4MTA1ZWRjOGYwMzFjY2Q3NTM0OThlZTA0"; // 请替换成待认证用户smk;
    private String apiBaseUrl = "http://api.dla.woa.com/formation/v2/metadata/getTableDetail";
    private long cacheExpireTimeMs = 3600000; // 1小时
    private int maxRetryAttempts = 3;

    public ApiSchemaConfig() {
        this.userName = "cesarezhang";
        this.cmk = "NGY2N2JjYmUwYTY0NTI4ZjMwZTg0NTE4MTA1ZWRjOGYwMzFjY2Q3NTM0OThlZTA0";
    }

    /**
     * 获取认证头部
     */
    public Map<String, String> getAuthHeaders() throws UnsupportedEncodingException, SecureException {
        return AuthorityTDW.getAuthorityKey(userName, cmk);
    }

    // Getters and Setters
    public String getUserName() {return userName;}

    public void setUserName(String userName) {this.userName = userName;}

    public String getCmk() {return cmk;}

    public void setCmk(String cmk) {this.cmk = cmk;}

    public String getApiBaseUrl() {return apiBaseUrl;}

    public void setApiBaseUrl(String apiBaseUrl) {this.apiBaseUrl = apiBaseUrl;}

    public long getCacheExpireTimeMs() {return cacheExpireTimeMs;}

    public void setCacheExpireTimeMs(long cacheExpireTimeMs) {this.cacheExpireTimeMs = cacheExpireTimeMs;}

    public int getMaxRetryAttempts() {return maxRetryAttempts;}

    public void setMaxRetryAttempts(int maxRetryAttempts) {this.maxRetryAttempts = maxRetryAttempts;}
} 