package com.tencent.andata.utils.rowdata;

import com.tencent.andata.utils.TableIdentifier;
import java.io.Serializable;
import lombok.Getter;

/**
 * 嵌入在RowData里的配置，可自定义添加，随着RowData流动
 */
@Getter
public class MessageRowDataAttr implements Serializable {

    // TODO：这里后面集成PK信息进来
    private TableIdentifier srcTable;
    private TableIdentifier dstTable;

    private String tagger;

    public void setTagger(String tagger) {
        this.tagger = tagger;
    }

    public String getTagger() {
        return tagger;
    }

    public MessageRowDataAttr() {
    }

    public MessageRowDataAttr(TableIdentifier src) {
        this.srcTable = src;
    }

    public TableIdentifier getSrcTable() {
        return srcTable;
    }

    public TableIdentifier getDstTable() {
        return dstTable;
    }

    public void setDstTable(TableIdentifier dstTable) {
        this.dstTable = dstTable;
    }

    public void setSrcTable(TableIdentifier src) {
        this.srcTable = src;
    }

    public void setSrcAndTagger(TableIdentifier src) {
        this.srcTable = src;
        this.tagger = String.format("%s.%s.%s", src.getDbName(), src.getSchemaName(), src.getTableName());
    }

    @Override
    public String toString() {
        return "MessageRowDataAttr{"
                + "srcTable=" + srcTable
                + ", tagger=" + tagger
                + '}';
    }
}
