package com.tencent.andata.utils.jsonRepair;

import io.vavr.collection.List;
import io.vavr.control.Option;
import lombok.Data;

@Data
public class JsonContext {
    private List<ContextValues> context;
    private Option<ContextValues> current;
    private boolean empty;

    public JsonContext() {
        this.context = List.empty();
        this.current = Option.none();
        this.empty = true;
    }

    public void set(ContextValues value) {
        this.context = this.context.append(value);
        this.current = Option.of(value);
        this.empty = false;
    }

    public void reset() {
        if (!this.context.isEmpty()) {
            this.context = this.context.dropRight(1);
            this.current = this.context.lastOption();
        } else {
            this.current = Option.none();
            this.empty = true;
        }
    }

    public boolean isEmpty() {
        return this.empty;
    }

    public ContextValues getCurrent() {
        return this.current.getOrElse(() -> null);
    }

    public enum ContextValues {
        OBJECT_KEY,
        OBJECT_VALUE,
        ARRAY
    }
}