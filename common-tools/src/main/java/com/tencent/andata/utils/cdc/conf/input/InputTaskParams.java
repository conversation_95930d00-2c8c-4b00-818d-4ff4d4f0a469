package com.tencent.andata.utils.cdc.conf.input;

import annotations.ValidVal;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.utils.struct.DatabaseEnum;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class InputTaskParams {

    @JsonAlias({"dbName", "srcDBName"})
    @NotNull(message = "dbName should not be null")
    public String srcDBName;
    @JsonAlias({"dbSchemaName", "srcDBSchemaName"})
    public String srcDBSchemaName;
    @JsonAlias({"dbTableName", "srcDBTableName"})
    @NotNull(message = "dbTableName should not be null")
    public String srcDBTableName;
    @JsonAlias({"dbType", "srcDBType"})
    @NotNull(message = "dbType should not be null")
    public DatabaseEnum srcDBType;
    @JsonAlias({"hiveName", "dstDBName"})
    //@NotNull(message = "hiveName should not be null")
    public String dstDBName;
    @JsonAlias({"hiveTableName", "dstTableName"})
    //@NotNull(message = "hiveTableName should not be null")
    public String dstTableName;
    @JsonAlias({"sinkType", "dstDBType"})
    @NotNull(message = "sinkType should not be null")
    public DatabaseEnum dstDBType;
    @JsonInclude(Include.NON_NULL)
    @JsonAlias({"cdcCategory", "cdcCategory"})
    public String cdcCategory = "COMMON";
    @JsonAlias({"startMode", "startMode"})
    // LAST:只同步增量 ALL:全量扫描后同步增量（默认）
    public String startMode;
    public String[] primaryKeys;
    public String[] jdbcConf;
    public String isChangeLog;
    @JsonProperty("partitionArgs")
    public InputPartitionArgs[] inputPartitionArgs;
    @ValidVal
    public String dbus;
    public String alias;

    public InputPartitionArgs[] getPartitionSpec() {
        return inputPartitionArgs;
    }

    public void setPartitionSpec(InputPartitionArgs[] inputPartitionArgs) {
        this.inputPartitionArgs = inputPartitionArgs;
    }

    /***
     * fromJson .
     * @param json .
     * @return .
     * @throws IOException .
     * @throws IllegalAccessException .
     * @throws IllegalArgumentException .
     */
    public static InputTaskParams[] fromJson(String json)
            throws IOException, IllegalAccessException, IllegalArgumentException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 解析Json数据生成InputTaskParams
        // TODO：这里对数据必填进行校验，后面最好使用bean validate api来做这个事情，先这样
        // From xxx (required) with xxx(optional) To xxx (required) with xxx(optional) IN xxx mode (optional)
        final List<String> notNullField = Arrays.asList(
                "dbName", "dbTableName", "dbType", "hiveName", "hiveTableName", "sinkType"
        );
        return objectMapper.readValue(json, InputTaskParams[].class);
    }

    @Override
    public String toString() {
        return "InputTaskParams{"
                + "dbName='" + srcDBName + '\''
                + ", dbSchemaName='" + srcDBSchemaName + '\''
                + ", dbTableName='" + srcDBTableName + '\''
                + ", dbType='" + srcDBType + '\''
                + ", hiveName='" + dstDBName + '\''
                + ", hiveTableName='" + dstTableName + '\''
                + ", primaryKeys=" + Arrays.toString(primaryKeys)
                + ", jdbcConf=" + Arrays.toString(jdbcConf)
                + ", isChangeLog='" + isChangeLog + '\''
                + ", sinkType='" + dstDBType + '\''
                + ", inputPartitionArgs=" + Arrays.toString(inputPartitionArgs)
                + '}';
    }
}