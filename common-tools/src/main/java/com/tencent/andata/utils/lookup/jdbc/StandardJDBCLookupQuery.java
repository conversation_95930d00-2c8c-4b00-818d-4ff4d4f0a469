package com.tencent.andata.utils.lookup.jdbc;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.calcite.tools.ValidationException;

public class StandardJDBCLookupQuery<IN, OUT> extends AbstractJDBCLookupQuery<IN, OUT> {
    private static final FlinkLog logger = FlinkLog.getInstance();
    private final JDBCQueryParser<IN, OUT> jdbcQueryParser;
    private final JDBCQuerySqlBuilder jdbcQuerySqlBuilder;

    public StandardJDBCLookupQuery(
            DatabaseEnum databaseEnum,
            DatabaseConf databaseConf,
            JDBCQueryParser jdbcQueryParser,
            JDBCQuerySqlBuilder jdbcQuerySqlBuilder
    ) throws ValidationException {
        super(databaseEnum, databaseConf);
        this.jdbcQueryParser = jdbcQueryParser;
        this.jdbcQuerySqlBuilder = jdbcQuerySqlBuilder;
    }

    @Override
    public void open() throws SQLException {
        super.open();
    }

    @Override
    protected OUT executeQuery(Connection connection, IN in) throws SQLException, ValidationException {
        String sql = this.jdbcQuerySqlBuilder.build();
        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            // 根据入参构建参数
            this.jdbcQueryParser.setStatement(
                    in,
                    statement,
                    this.jdbcQuerySqlBuilder.conditionKeyIndexMap()
            );
            // jdbc查询
            try (ResultSet rs = statement.executeQuery()) {
                // 解析返回
                return this.jdbcQueryParser.parseResultSet(rs);
            }
        } catch (SQLException e) {
            logger.error(String.format("Query error: %s, SQL: %s", e, sql));
            throw e;
        }
    }

    @Override
    public String toString() {
        return "StandardJDBCLookupQuery{" +
                "sql=" + jdbcQuerySqlBuilder.build() +
                '}';
    }
}