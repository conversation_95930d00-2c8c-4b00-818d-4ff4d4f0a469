package com.tencent.andata.utils;

import io.vavr.control.Try;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 异步HTTP客户端
 * 使用单例模式确保全局唯一实例
 * 使用建造者模式配置客户端参数
 * 使用函数式编程处理异常
 */
public final class AsyncHttpClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncHttpClient.class);
    private static final AtomicReference<HttpClientConfig> CONFIG = new AtomicReference<>();
    private static final AtomicReference<CloseableHttpAsyncClient> CLIENT = new AtomicReference<>();


    /**
     * HTTP客户端配置类
     * 使用建造者模式创建配置对象
     */
    public static final class HttpClientConfig {

        private final int connectTimeout;
        private final int socketTimeout;
        private final int maxConnTotal;
        private final int maxPerRoute;
        private final int ioThreadCount;

        private HttpClientConfig(Builder builder) {
            this.connectTimeout = builder.connectTimeout;
            this.socketTimeout = builder.socketTimeout;
            this.maxConnTotal = builder.maxConnTotal;
            this.maxPerRoute = builder.maxPerRoute;
            this.ioThreadCount = builder.ioThreadCount;
        }

        public static class Builder {

            private int connectTimeout = 6000000;
            private int socketTimeout = 6000000;
            private int maxConnTotal = 1000;
            private int maxPerRoute = 200;
            private int ioThreadCount = Runtime.getRuntime().availableProcessors();

            public Builder connectTimeout(int connectTimeout) {
                this.connectTimeout = connectTimeout;
                return this;
            }

            public Builder socketTimeout(int socketTimeout) {
                this.socketTimeout = socketTimeout;
                return this;
            }

            public Builder maxConnTotal(int maxConnTotal) {
                this.maxConnTotal = maxConnTotal;
                return this;
            }

            public Builder maxPerRoute(int maxPerRoute) {
                this.maxPerRoute = maxPerRoute;
                return this;
            }

            public Builder ioThreadCount(int ioThreadCount) {
                this.ioThreadCount = ioThreadCount;
                return this;
            }

            public HttpClientConfig build() {
                return new HttpClientConfig(this);
            }
        }
    }

    /**
     * 初始化HTTP客户端
     *
     * @param config 客户端配置
     * @throws IllegalStateException 如果初始化失败
     */
    public static void init(HttpClientConfig config) {
        Objects.requireNonNull(config, "HttpClientConfig cannot be null");

        if (CLIENT.get() != null) {
            throw new IllegalStateException("AsyncHttpClient already initialized");
        }

        Try.of(() -> {
            IOReactorConfig ioReactorConfig = IOReactorConfig.custom()
                    .setIoThreadCount(config.ioThreadCount)
                    .setSoKeepAlive(true)
                    .build();

            ConnectingIOReactor ioReactor = new DefaultConnectingIOReactor(ioReactorConfig);
            PoolingNHttpClientConnectionManager connManager = new PoolingNHttpClientConnectionManager(ioReactor);
            connManager.setMaxTotal(config.maxConnTotal);
            connManager.setDefaultMaxPerRoute(config.maxPerRoute);

            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(config.connectTimeout)
                    .setSocketTimeout(config.socketTimeout)
                    .setConnectionRequestTimeout(200000)
                    .build();

            CloseableHttpAsyncClient newClient = HttpAsyncClients.custom()
                    .setConnectionManager(connManager)
                    .setDefaultRequestConfig(requestConfig)
                    .build();

            newClient.start();
            return newClient;
        }).onSuccess(client -> {
            CLIENT.set(client);
            CONFIG.set(config);
            LOGGER.info("AsyncHttpClient initialized successfully");
        }).onFailure(e -> {
            LOGGER.error("Failed to initialize AsyncHttpClient", e);
            throw new IllegalStateException("Failed to initialize AsyncHttpClient", e);
        });
    }

    /**
     * 获取HTTP客户端实例
     *
     * @return CloseableHttpAsyncClient实例
     * @throws IllegalStateException 如果客户端未初始化
     */
    public static CloseableHttpAsyncClient getInstance() {
        CloseableHttpAsyncClient client = CLIENT.get();
        if (client == null) {
            synchronized (AsyncHttpClient.class) {
                client = CLIENT.get();
                if (client == null) {
                    init(new HttpClientConfig.Builder().build());
                    client = CLIENT.get();
                }
            }
        }
        return client;
    }

    /**
     * 获取异步HTTP客户端，手动设置超时时间
     *
     * @param connectTimeout 连接超时时间（毫秒）
     * @param socketTimeout 套接字超时时间（毫秒）
     * @param maxConnTotal 最大连接总数
     * @param maxPerRoute 每个路由的最大连接数
     * @return CloseableHttpAsyncClient实例
     * @throws IllegalStateException 如果初始化失败
     */
    public static CloseableHttpAsyncClient getHttpClient(int connectTimeout,
            int socketTimeout,
            int maxConnTotal,
            int maxPerRoute) {
        CloseableHttpAsyncClient client = CLIENT.get();
        if (client == null) {
            synchronized (AsyncHttpClient.class) {
                client = CLIENT.get();
                if (client == null) {
                    init(new HttpClientConfig.Builder()
                            .connectTimeout(connectTimeout)
                            .socketTimeout(socketTimeout)
                            .maxConnTotal(maxConnTotal)
                            .maxPerRoute(maxPerRoute)
                            .build());
                    client = CLIENT.get();
                }
            }
        }
        return client;
    }

    /**
     * 创建HTTP POST请求
     *
     * @param api 请求URL
     * @param params 请求参数
     * @param contentType 内容类型
     * @return HttpPost实例
     */
    public static HttpPost doHttpPost(String api, String params, ContentType contentType) {
        Objects.requireNonNull(api, "API URL cannot be null");
        Objects.requireNonNull(params, "Request params cannot be null");
        Objects.requireNonNull(contentType, "Content type cannot be null");

        HttpPost post = new HttpPost(api);
        StringEntity entity = new StringEntity(params, contentType);
        post.setEntity(entity);
        return post;
    }

    /**
     * 关闭HTTP客户端
     */
    public static void close() {
        Try.run(() -> {
            CloseableHttpAsyncClient client = CLIENT.getAndSet(null);
            if (client != null) {
                client.close();
                LOGGER.info("AsyncHttpClient closed successfully");
            }
        }).onFailure(e -> LOGGER.error("Failed to close AsyncHttpClient", e));
    }

    private AsyncHttpClient() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}