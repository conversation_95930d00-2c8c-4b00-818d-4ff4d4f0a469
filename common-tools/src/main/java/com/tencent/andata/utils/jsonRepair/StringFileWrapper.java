package com.tencent.andata.utils.jsonRepair;

import java.io.IOException;
import java.io.RandomAccessFile;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.IntStream;
import org.jetbrains.annotations.NotNull;

public class StringFileWrapper implements Serializable, CharSequence {
    private RandomAccessFile fd;
    private int length;
    private Map<Integer, String> buffers;
    private int bufferLength;

    public StringFileWrapper(RandomAccessFile fd, int CHUNK_LENGTH) throws IOException {
        this.fd = fd;
        this.length = 0;
        this.buffers = new HashMap<>();
        if (CHUNK_LENGTH < 2) {
            CHUNK_LENGTH = 1_000_000;
        }
        this.bufferLength = CHUNK_LENGTH;
    }

    private String getBuffer(int index) throws IOException {
        if (!buffers.containsKey(index)) {
            fd.seek((long) index * bufferLength);
            byte[] buffer = new byte[bufferLength];
            int bytesRead = fd.read(buffer);
            String bufferString = new String(buffer, 0, bytesRead);
            buffers.put(index, bufferString);

            // Save memory by keeping max 2MB buffer chunks and min 2 chunks
            if (buffers.size() > Math.max(2, 2_000_000 / bufferLength)) {
                Integer oldestKey = buffers.keySet().iterator().next();
                if (!oldestKey.equals(index)) {
                    buffers.remove(oldestKey);
                }
            }
        }
        return buffers.get(index);
    }

    public String getItem(int index) throws IOException {
        int bufferIndex = index / bufferLength;
        return getBuffer(bufferIndex).substring(index % bufferLength, (index % bufferLength) + 1);
    }

    public String getItem(int start, int stop) throws IOException {
        int bufferIndex = start / bufferLength;
        int bufferEnd = stop / bufferLength;
        if (bufferIndex == bufferEnd) {
            return getBuffer(bufferIndex).substring(start % bufferLength, stop % bufferLength);
        } else {
            String startSlice = getBuffer(bufferIndex).substring(start % bufferLength);
            String endSlice = getBuffer(bufferEnd).substring(0, stop % bufferLength);
            StringBuilder middleSlices = new StringBuilder();
            for (int i = bufferIndex + 1; i < bufferEnd; i++) {
                middleSlices.append(getBuffer(i));
            }
            return startSlice + middleSlices.toString() + endSlice;
        }
    }

    public int getLength() throws IOException {
        if (length < 1) {
            long currentPosition = fd.getFilePointer();
            fd.seek(0);
            length = (int) fd.length();
            fd.seek(currentPosition);
        }
        return length;
    }

    @Override
    public int length() {
        try {
            return getLength();
        } catch (IOException e) {
            return 0;
        }
    }

    @Override
    public char charAt(int index) {
        try {
            return index < length() ? getItem(index).charAt(index) : '\0';
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @NotNull
    @Override
    public CharSequence subSequence(int start, int end) {
        return null;
    }

    @NotNull
    @Override
    public IntStream chars() {
        return CharSequence.super.chars();
    }

    @NotNull
    @Override
    public IntStream codePoints() {
        return CharSequence.super.codePoints();
    }
}