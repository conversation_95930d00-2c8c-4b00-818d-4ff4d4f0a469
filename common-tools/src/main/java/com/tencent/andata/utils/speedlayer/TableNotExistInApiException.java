package com.tencent.andata.utils.speedlayer;

/**
 * API中表不存在的特殊异常
 * 用于标识需要降级到HiveCatalog的情况
 * 
 * <AUTHOR>
 */
public class TableNotExistInApiException extends RuntimeException {
    
    private final String dbName;
    private final String tableName;
    
    public TableNotExistInApiException(String dbName, String tableName, String message) {
        super(String.format("Table %s.%s not found in API: %s", dbName, tableName, message));
        this.dbName = dbName;
        this.tableName = tableName;
    }
    
    public String getDbName() {
        return dbName;
    }
    
    public String getTableName() {
        return tableName;
    }
} 