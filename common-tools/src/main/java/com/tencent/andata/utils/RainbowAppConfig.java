package com.tencent.andata.utils;

import java.util.Properties;

public class RainbowAppConfig {
    private static volatile RainbowUtils instance;
    private static final Object LOCK = new Object();

    // Configuration properties
    private final static Properties configValue = PropertyUtils.loadProperties("env.properties");

    // Private constructor
    private RainbowAppConfig(Properties properties) {
        if (properties != null) {
            configValue.putAll(properties);
        }
    }

    /**
     * Gets or creates a RainbowUtils instance with the specified properties
     * @param properties Configuration properties
     * @return RainbowUtils instance
     * @throws IllegalArgumentException if properties is null
     */
    public static RainbowUtils getInstance(Properties properties) {
        if (properties == null) {
            PropertyUtils.loadProperties("env.properties");
        }

        RainbowUtils result = instance;
        if (result == null) {
            synchronized (LOCK) {
                result = instance;
                if (result == null) {
                    instance = result = new RainbowUtils(properties);
                }
            }
        }
        return result;
    }

    /**
     * Gets the existing RainbowUtils instance
     * @return RainbowUtils instance
     * @throws IllegalStateException if instance hasn't been initialized
     */
    public static RainbowUtils getInstance() {
        RainbowUtils result = instance;
        if (result == null) {
            synchronized (LOCK) {
                result = instance = new RainbowUtils(getDefaultConfigValue());
                // double check
                if (result == null) {
                    throw new IllegalStateException(
                            "RainbowUtils is not initialized. Please call getInstance(Properties properties) first."
                    );
                }
            }
        }
        return result;
    }

    /**
     * Gets a defensive copy of the configuration properties
     * @return Copy of configuration properties
     */
    public static Properties getDefaultConfigValue() {
        Properties copy = new Properties();
        copy.putAll(configValue);
        return copy;
    }
}