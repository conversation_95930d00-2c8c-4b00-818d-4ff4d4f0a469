package com.tencent.andata.utils;

import com.tencent.andata.utils.struct.DatabaseEnum;
import java.io.Serializable;
import java.util.Objects;
import lombok.Getter;

@Getter
public class TableIdentifier implements Serializable {

    private final DatabaseEnum databaseEnum;
    private final String dbName;
    private final String schemaName;
    private final String tableName;

    public TableIdentifier(DatabaseEnum databaseEnum, String dbName, String schemaName, String tableName) {
        this.schemaName = schemaName == null ? "" : schemaName;
        this.databaseEnum = databaseEnum;
        this.tableName = tableName;
        this.dbName = dbName;
    }

    public TableIdentifier(DatabaseEnum databaseEnum, String dbName, String tableName) {
        this(databaseEnum, dbName, "", tableName);
    }

    public String getFullName() {
        return String.format("%s.%s.%s", dbName, schemaName, tableName);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TableIdentifier that = (TableIdentifier) o;
        return databaseEnum.equals(that.databaseEnum)
                && dbName.equals(that.dbName)
                && schemaName.equals(that.schemaName)
                && tableName.equals(that.tableName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(databaseEnum, dbName, schemaName, tableName);
    }

    @Override
    public String toString() {
        return String.format("%s(%s.%s.%s)", databaseEnum, dbName, schemaName, tableName);
    }

}
