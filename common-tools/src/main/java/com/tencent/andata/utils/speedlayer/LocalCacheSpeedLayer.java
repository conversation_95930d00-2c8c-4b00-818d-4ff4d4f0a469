package com.tencent.andata.utils.speedlayer;

import com.tencent.andata.utils.meta.IcebergTableMeta;
import org.apache.iceberg.catalog.TableIdentifier;

import java.util.HashMap;

public class LocalCacheSpeedLayer extends SpeedLayer {

    private static HashMap<TableIdentifier, IcebergTableMeta> metaMap;

    LocalCacheSpeedLayer() {
        if (metaMap == null) {
            metaMap = new HashMap<>();
        }
    }

    private static final class InstanceHolder {

        static final LocalCacheSpeedLayer instance = new LocalCacheSpeedLayer();
    }

    public static LocalCacheSpeedLayer getInstance() {
        return InstanceHolder.instance;
    }

    public static class Builder {

        public LocalCacheSpeedLayer build() {
            return LocalCacheSpeedLayer.getInstance();
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    @Override
    public IcebergTableMeta getIcebergTableMeta(TableIdentifier tableIdentifier) {
        return metaMap.get(tableIdentifier);
    }

    @Override
    public void setIcebergTableMeta(TableIdentifier tableIdentifier, IcebergTableMeta icebergTableMeta) {
        metaMap.put(tableIdentifier, icebergTableMeta);
    }
}