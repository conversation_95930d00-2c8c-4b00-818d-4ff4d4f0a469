package com.tencent.andata.utils.speedlayer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.bean.TableDetailResponse;
import com.tencent.tdw.security.exceptions.SecureException;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import java.io.UnsupportedEncodingException;
import org.apache.flink.table.types.logical.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * API方式的Schema加载器 - 简化版本
 * 通过HTTP API获取Iceberg表的Schema信息，替代Hive Metastore方式
 *
 * <AUTHOR>
 */
public class ApiSchemaLoaderV2 {

    private static final Logger log = LoggerFactory.getLogger(ApiSchemaLoaderV2.class);

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 配置信息
    private ApiSchemaConfig config;

    // 请求间隔控制，避免API限流 (最少间隔20ms，相当于每秒最多50次请求)
    private static volatile long lastRequestTime = 0;
    private static final long MIN_REQUEST_INTERVAL_MS = 20;

    /**
     * 初始化配置
     */
    public void init() {
        this.config = new ApiSchemaConfig();
        log.info("ApiSchemaLoaderV2 initialized");
    }

    /**
     * 获取表的RowType Schema
     */
    public RowType getTableSchema(String dbName, String tableName)
            throws UnsupportedEncodingException, SecureException {

        // 缓存不存在，从API加载
        log.info("Loading schema from API for table: {}", dbName + "." + tableName);
        return loadSchemaFromApi(dbName, tableName);
    }

    /**
     * 从API加载Schema
     */
    private RowType loadSchemaFromApi(String dbName, String tableName)
            throws UnsupportedEncodingException, SecureException {
        Map<String, String> headers = config.getAuthHeaders();
        String url = String.format("%s?dbName=%s&table=%s", config.getApiBaseUrl(), dbName, tableName);

        // 请求间隔控制，避免API限流
        synchronized (ApiSchemaLoaderV2.class) {
            long currentTime = System.currentTimeMillis();
            long timeSinceLastRequest = currentTime - lastRequestTime;
            if (timeSinceLastRequest < MIN_REQUEST_INTERVAL_MS) {
                try {
                    Thread.sleep(MIN_REQUEST_INTERVAL_MS - timeSinceLastRequest);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for rate limit", e);
                }
            }
            lastRequestTime = System.currentTimeMillis();
        }

        try (HttpClientUtils client = HttpClientUtils.builder().build()) {
            TableDetailResponse response = client.getAsObject(url, headers, TableDetailResponse.class);

            if (response.getCode() != 0) {
                // 检查是否是表不存在的错误
                if (isTableNotExistError(response)) {
                    // 抛出特殊异常，由上层InfraConfManagerWithApiSchema处理降级
                    throw new TableNotExistInApiException(dbName, tableName, response.getMessage());
                } else if (isRateLimitError(response)) {
                    // 检查是否是限流错误，也需要降级处理
                    log.warn("API rate limit exceeded for table {}.{}, will fallback to HiveCatalog", dbName, tableName);
                    throw new TableNotExistInApiException(dbName, tableName, "Rate limit exceeded: " + response.getMessage());
                } else {
                    throw new RuntimeException(
                            String.format("API returned error code %d: %s", response.getCode(), response.getMessage()));
                }
            }

            // 根据API文档：如果表中含有复杂类型，从nestFields获取；否则从fields获取
            TableDetailResponse.Data data = response.getData();
            String nestFields = data.getNestFields();

            if (nestFields != null && !nestFields.equals("null") && !nestFields.trim().isEmpty()) {
                // 含有复杂类型，从nestFields获取schema
                log.debug("Using nestFields for complex types in table: {}.{}", dbName, tableName);
                return convertNestFieldsToRowType(nestFields);
            } else {
                // 不含复杂类型，从fields获取schema
                log.debug("Using fields for simple types in table: {}.{}", dbName, tableName);
                return convertFieldsToRowType(data.getFields());
            }

        } catch (Exception e) {
            log.error("Failed to load schema from API for {}.{}", dbName, tableName, e);
            throw new RuntimeException("Schema loading failed", e);
        }
    }

    /**
     * 判断API响应是否表示表不存在
     */
    private boolean isTableNotExistError(TableDetailResponse response) {
        // 根据API的错误码和消息判断是否为表不存在错误
        return response.getCode() == 20005;
    }

    /**
     * 判断API响应是否表示限流错误
     */
    private boolean isRateLimitError(TableDetailResponse response) {
        // 根据错误码和消息判断是否为限流错误
        return response.getCode() == 10000 &&
                response.getMessage() != null &&
                response.getMessage().contains("RateLimiter");
    }


    /**
     * 从nestFields转换为RowType（处理复杂类型）
     */
    private RowType convertNestFieldsToRowType(String nestFields) {
        try {
            // nestFields通常是JSON格式的复杂结构描述
            JsonNode nestNode = objectMapper.readTree(nestFields);

            // 如果nestFields是一个包含fields的JSON对象
            if (!nestNode.has("fields")) {
                // 如果nestFields是其他格式，可能需要特殊处理
                log.warn("Unexpected nestFields format, falling back to string parsing: {}", nestFields);
            }
            return convertIcebergSchemaToRowType(nestFields);

        } catch (Exception e) {
            log.error("Failed to parse nestFields: {}", nestFields, e);
            throw new RuntimeException("NestFields parsing failed", e);
        }
    }

    /**
     * 从fields列表转换为RowType（处理简单类型）
     */
    private RowType convertFieldsToRowType(List<TableDetailResponse.Data.Field> fields) {
        try {
            List<RowType.RowField> rowFields = new ArrayList<>();

            for (TableDetailResponse.Data.Field field : fields) {
                String fieldName = field.getName();
                String fieldType = field.getType();

                LogicalType logicalType = convertIcebergTypeToLogicalType(fieldType);

                // 默认设置为可空
                logicalType = logicalType.copy(true);

                rowFields.add(new RowType.RowField(fieldName, logicalType));
            }

            return new RowType(rowFields);

        } catch (Exception e) {
            log.error("Failed to convert fields to RowType: {}", fields, e);
            throw new RuntimeException("Fields conversion failed", e);
        }
    }

    /**
     * 将Iceberg Schema JSON转换为Flink RowType
     */
    private RowType convertIcebergSchemaToRowType(String schemaJson) {
        try {
            JsonNode schemaNode = objectMapper.readTree(schemaJson);
            JsonNode fieldsNode = schemaNode.get("fields");

            if (fieldsNode == null || !fieldsNode.isArray()) {
                throw new IllegalArgumentException("Invalid schema JSON: missing or invalid 'fields' array");
            }

            List<RowType.RowField> rowFields = new ArrayList<>();

            for (JsonNode fieldNode : fieldsNode) {
                String fieldName = fieldNode.get("name").asText();
                String fieldType = fieldNode.get("type").asText();
                boolean required = fieldNode.has("required") && fieldNode.get("required").asBoolean();

                LogicalType logicalType = convertIcebergTypeToLogicalType(fieldType);

                // Flink中，required=false表示可空
                if (!required) {
                    logicalType = logicalType.copy(true); // 设置为nullable
                }

                rowFields.add(new RowType.RowField(fieldName, logicalType));
            }

            return new RowType(rowFields);

        } catch (Exception e) {
            log.error("Failed to convert Iceberg schema to RowType: {}", schemaJson, e);
            throw new RuntimeException("Schema conversion failed", e);
        }
    }

    /**
     * 将Iceberg类型转换为Flink LogicalType
     */
    private LogicalType convertIcebergTypeToLogicalType(String icebergType) {
        switch (icebergType.toLowerCase()) {
            case "boolean":
                return new BooleanType();
            case "int":
            case "integer":
                return new IntType();
            case "long":
                return new BigIntType();
            case "float":
                return new FloatType();
            case "double":
                return new DoubleType();
            case "string":
                return new VarCharType(VarCharType.MAX_LENGTH);
            case "binary":
                return new VarBinaryType(VarBinaryType.MAX_LENGTH);
            case "date":
                return new DateType();
            case "time":
                return new TimeType();
            case "timestamp":
                return new TimestampType();
            case "timestamptz":
                return new LocalZonedTimestampType();
            default:
                // 处理复杂类型，如decimal(10,2)
                if (icebergType.startsWith("decimal")) {
                    return parseDecimalType(icebergType);
                }

                log.warn("Unknown Iceberg type: {}, defaulting to STRING", icebergType);
                return new VarCharType(VarCharType.MAX_LENGTH);
        }
    }

    /**
     * 解析Decimal类型
     */
    private LogicalType parseDecimalType(String decimalType) {
        // 解析 decimal(precision, scale) 格式
        if (decimalType.contains("(") && decimalType.contains(")")) {
            String params = decimalType.substring(decimalType.indexOf("(") + 1, decimalType.indexOf(")"));
            String[] parts = params.split(",");

            if (parts.length == 2) {
                try {
                    int precision = Integer.parseInt(parts[0].trim());
                    int scale = Integer.parseInt(parts[1].trim());
                    return new DecimalType(precision, scale);
                } catch (NumberFormatException e) {
                    log.warn("Failed to parse decimal type: {}", decimalType, e);
                }
            }
        }

        // 默认decimal类型
        return new DecimalType(10, 0);
    }
} 