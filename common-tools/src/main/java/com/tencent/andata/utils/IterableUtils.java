package com.tencent.andata.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * 工具类，用于处理可迭代对象中元素的上下文关系
 * 提供多种方法处理元素与其前后元素的关系
 */
public class IterableUtils {

    /**
     * 获取可迭代对象中每个元素及其上下文（前一个元素和后一个元素）
     *
     * @param <T> 元素类型
     * @param iterable 输入的可迭代对象
     * @return 包含元素上下文(前一个值, 当前值, 后一个值)的列表
     */
    public static <T> List<ElementContext<T>> getElementsWithContext(Iterable<T> iterable) {
        Objects.requireNonNull(iterable, "输入的可迭代对象不能为null");

        // 优化：如果已经是List类型，直接使用，避免不必要的复制
        List<T> items;
        if (iterable instanceof List) {
            items = (List<T>) iterable;
        } else {
            items = new ArrayList<>();
            for (T item : iterable) {
                items.add(item);
            }
        }

        List<ElementContext<T>> result = new ArrayList<>(items.size());

        // 遍历列表，获取每个元素的前一个和后一个值
        for (int i = 0; i < items.size(); i++) {
            T previous = (i > 0) ? items.get(i - 1) : null;
            T current = items.get(i);
            T next = (i < items.size() - 1) ? items.get(i + 1) : null;

            result.add(new ElementContext<>(previous, current, next));
        }

        return result;
    }

    /**
     * 获取可迭代对象中每个元素及其上下文的Stream
     * 适用于大数据集，避免一次性加载全部数据到内存
     *
     * @param <T> 元素类型
     * @param iterable 输入的可迭代对象
     * @return 包含元素上下文的Stream
     */
    public static <T> Stream<ElementContext<T>> streamElementsWithContext(Iterable<T> iterable) {
        Objects.requireNonNull(iterable, "输入的可迭代对象不能为null");

        List<T> items = StreamSupport.stream(iterable.spliterator(), false)
                                     .collect(Collectors.toList());

        return IntStream.range(0, items.size())
                        .mapToObj(i -> {
                            T previous = (i > 0) ? items.get(i - 1) : null;
                            T current = items.get(i);
                            T next = (i < items.size() - 1) ? items.get(i + 1) : null;
                            return new ElementContext<>(previous, current, next);
                        });
    }

    /**
     * 使用并行流获取可迭代对象中每个元素及其上下文
     * 适用于大数据集且处理逻辑复杂的场景
     *
     * @param <T> 元素类型
     * @param iterable 输入的可迭代对象
     * @return 包含元素上下文的列表
     */
    public static <T> List<ElementContext<T>> getElementsWithContextParallel(Iterable<T> iterable) {
        Objects.requireNonNull(iterable, "输入的可迭代对象不能为null");

        List<T> items = StreamSupport.stream(iterable.spliterator(), false)
                                     .collect(Collectors.toList());

        return IntStream.range(0, items.size())
                        .parallel()
                        .mapToObj(i -> {
                            T previous = (i > 0) ? items.get(i - 1) : null;
                            T current = items.get(i);
                            T next = (i < items.size() - 1) ? items.get(i + 1) : null;
                            return new ElementContext<>(previous, current, next);
                        })
                        .collect(Collectors.toList());
    }

    /**
     * 惰性迭代处理带上下文的元素，适用于大数据集
     * 无需一次性加载全部数据到内存
     *
     * @param <T> 元素类型
     * @param iterable 输入的可迭代对象
     * @param consumer 处理每个上下文的函数
     */
    public static <T> void forEachWithContext(Iterable<T> iterable, Consumer<ElementContext<T>> consumer) {
        Objects.requireNonNull(iterable, "输入的可迭代对象不能为null");
        Objects.requireNonNull(consumer, "消费者函数不能为null");

        Iterator<T> iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            return;
        }

        T previous = null;
        T current = iterator.next();
        T next;

        while (iterator.hasNext()) {
            next = iterator.next();
            consumer.accept(new ElementContext<>(previous, current, next));
            previous = current;
            current = next;
        }

        // 处理最后一个元素
        consumer.accept(new ElementContext<>(previous, current, null));
    }

    /**
     * 对相邻元素应用操作，生成新的结果列表
     *
     * @param <T> 元素类型
     * @param <R> 结果类型
     * @param iterable 输入的可迭代对象
     * @param operation 应用于相邻元素的操作
     * @return 应用操作后的结果列表
     */
    public static <T, R> List<R> zipWithNeighbors(Iterable<T> iterable, BiFunction<T, T, R> operation) {
        Objects.requireNonNull(iterable, "输入的可迭代对象不能为null");
        Objects.requireNonNull(operation, "操作函数不能为null");

        List<T> items;
        if (iterable instanceof List) {
            items = (List<T>) iterable;
        } else {
            items = new ArrayList<>();
            iterable.forEach(items::add);
        }

        if (items.size() < 2) {
            return new ArrayList<>();
        }

        List<R> result = new ArrayList<>(items.size() - 1);
        for (int i = 0; i < items.size() - 1; i++) {
            result.add(operation.apply(items.get(i), items.get(i + 1)));
        }

        return result;
    }

    /**
     * 使用滑动窗口方式获取元素上下文
     * 可指定窗口前后范围
     *
     * @param <T> 元素类型
     * @param iterable 输入的可迭代对象
     * @param before 前方元素数量
     * @param after 后方元素数量
     * @return 包含滑动窗口上下文的列表
     */
    public static <T> List<WindowContext<T>> getElementsWithWindowContext(Iterable<T> iterable, int before, int after) {
        Objects.requireNonNull(iterable, "输入的可迭代对象不能为null");
        if (before < 0 || after < 0) {
            throw new IllegalArgumentException("前后元素数量不能为负数");
        }

        List<T> items;
        if (iterable instanceof List) {
            items = (List<T>) iterable;
        } else {
            items = new ArrayList<>();
            iterable.forEach(items::add);
        }

        List<WindowContext<T>> result = new ArrayList<>(items.size());

        for (int i = 0; i < items.size(); i++) {
            // 计算窗口起始和结束索引
            int startIndex = Math.max(0, i - before);
            int endIndex = Math.min(items.size() - 1, i + after);

            List<T> previousItems = new ArrayList<>(i - startIndex);
            for (int j = startIndex; j < i; j++) {
                previousItems.add(items.get(j));
            }

            List<T> nextItems = new ArrayList<>(endIndex - i);
            for (int j = i + 1; j <= endIndex; j++) {
                nextItems.add(items.get(j));
            }

            result.add(new WindowContext<>(previousItems, items.get(i), nextItems));
        }

        return result;
    }

    /**
     * 表示元素及其上下文（前一个元素和后一个元素）
     * 不可变类，一旦创建不可修改
     */
    public static class ElementContext<T> {

        private final T previous;
        private final T current;
        private final T next;

        public ElementContext(T previous, T current, T next) {
            this.previous = previous;
            this.current = current;
            this.next = next;
        }

        public T getPrevious() {
            return previous;
        }

        public T getCurrent() {
            return current;
        }

        public T getNext() {
            return next;
        }

        /**
         * 判断是否有前一个元素
         */
        public boolean hasPrevious() {
            return previous != null;
        }

        /**
         * 判断是否有后一个元素
         */
        public boolean hasNext() {
            return next != null;
        }

        /**
         * 判断是否为首个元素
         */
        public boolean isFirst() {
            return previous == null && next != null;
        }

        /**
         * 判断是否为最后一个元素
         */
        public boolean isLast() {
            return previous != null && next == null;
        }

        /**
         * 判断是否为唯一元素
         */
        public boolean isOnly() {
            return previous == null && next == null;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (o == null || getClass() != o.getClass()) {return false;}
            ElementContext<?> that = (ElementContext<?>) o;
            return Objects.equals(previous, that.previous) &&
                    Objects.equals(current, that.current) &&
                    Objects.equals(next, that.next);
        }

        @Override
        public int hashCode() {
            return Objects.hash(previous, current, next);
        }

        @Override
        public String toString() {
            return "(" + previous + ", " + current + ", " + next + ")";
        }
    }

    /**
     * 表示元素及其窗口上下文（多个前置元素和多个后置元素）
     * 不可变类，一旦创建不可修改
     */
    public static class WindowContext<T> {

        private final List<T> previousItems;
        private final T current;
        private final List<T> nextItems;

        public WindowContext(List<T> previousItems, T current, List<T> nextItems) {
            // 使用Java 1.8兼容的方式创建不可变列表
            this.previousItems = previousItems != null ?
                                 Collections.unmodifiableList(new ArrayList<>(previousItems)) :
                                 Collections.emptyList();
            this.current = current;
            this.nextItems = nextItems != null ?
                             Collections.unmodifiableList(new ArrayList<>(nextItems)) :
                             Collections.emptyList();
        }

        public List<T> getPreviousItems() {
            return previousItems;
        }

        public T getCurrent() {
            return current;
        }

        public List<T> getNextItems() {
            return nextItems;
        }

        public int getPreviousCount() {
            return previousItems.size();
        }

        public int getNextCount() {
            return nextItems.size();
        }

        public boolean hasPrevious() {
            return !previousItems.isEmpty();
        }

        public boolean hasNext() {
            return !nextItems.isEmpty();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (o == null || getClass() != o.getClass()) {return false;}
            WindowContext<?> that = (WindowContext<?>) o;
            return Objects.equals(previousItems, that.previousItems) &&
                    Objects.equals(current, that.current) &&
                    Objects.equals(nextItems, that.nextItems);
        }

        @Override
        public int hashCode() {
            return Objects.hash(previousItems, current, nextItems);
        }

        @Override
        public String toString() {
            return "{previous=" + previousItems + ", current=" + current + ", next=" + nextItems + "}";
        }
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) {
        List<String> words = new ArrayList<>();
        words.add("first");
        words.add("second");
        words.add("third");
        words.add("fourth");

        // 示例1：基本用法
        System.out.println("基本用法示例：");
        List<ElementContext<String>> result = getElementsWithContext(words);
        System.out.println("ResultSize: " + result.size());
        for (ElementContext<String> context : result) {
            System.out.println("Previous: " + context.getPrevious() +
                    ", Current: " + context.getCurrent() +
                    ", Next: " + context.getNext());
        }

        // 示例2：流式API
        System.out.println("\n流式API示例：");
        streamElementsWithContext(words)
                .filter(ctx -> ctx.hasPrevious() && ctx.hasNext())
                .forEach(ctx -> System.out.println("中间元素: " + ctx.getCurrent()));

        // 示例3：并行处理
        System.out.println("\n并行处理示例：");
        List<ElementContext<String>> parallelResult = getElementsWithContextParallel(words);
        System.out.println("Parallel ResultSize: " + parallelResult.size());

        // 示例4：惰性迭代
        System.out.println("\n惰性迭代示例：");
        forEachWithContext(words, ctx -> {
            if (ctx.isFirst()) {
                System.out.println("首个元素: " + ctx.getCurrent());
            } else if (ctx.isLast()) {
                System.out.println("最后元素: " + ctx.getCurrent());
            } else {
                System.out.println("中间元素: " + ctx.getCurrent());
            }
        });

        // 示例5：相邻元素操作
        System.out.println("\n相邻元素操作示例：");
        List<String> pairs = zipWithNeighbors(words, (a, b) -> a + "->" + b);
        pairs.forEach(System.out::println);

        // 示例6：窗口上下文
        System.out.println("\n窗口上下文示例：");
        List<WindowContext<String>> windowResult = getElementsWithWindowContext(words, 1, 1);
        for (WindowContext<String> ctx : windowResult) {
            System.out.println("Current: " + ctx.getCurrent() +
                    ", Previous: " + ctx.getPreviousItems() +
                    ", Next: " + ctx.getNextItems());
        }
    }
}