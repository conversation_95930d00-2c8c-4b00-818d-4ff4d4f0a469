package com.tencent.andata.utils.jsonRepair;

import com.tencent.andata.utils.jsonRepair.JsonContext.ContextValues;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JsonParser {

    private Object jsonStr;
    private int index;
    private JsonContext context;

    private Logger logger = LoggerFactory.getLogger(JsonParser.class);

    public JsonParser(Object jsonStr, RandomAccessFile jsonFd, int jsonFdChunkLength) throws IOException {
        this.jsonStr = jsonStr;
        if (jsonFd != null) {
            this.jsonStr = new StringFileWrapper(jsonFd, jsonFdChunkLength);
        }
        this.index = 0;
        this.context = new JsonContext();
    }

    public Object parse() {
        Object json = parseJson();
        if (this.index < length(this.jsonStr)) {
            logger.error("The parser returned early, checking if there's more json elements");
            List<Object> jsonList = new ArrayList<>();
            jsonList.add(json);
            int lastIndex = this.index;
            while (this.index < length(this.jsonStr)) {
                Object j = parseJson();
                if (!j.equals("")) {
                    jsonList.add(j);
                }
                if (this.index == lastIndex) {
                    this.index++;
                }
                lastIndex = this.index;
            }
            //  If nothing extra was found, don't return an array
            if (jsonList.size() == 1) {
                logger.warn("There were no more elements, returning the element without the array");
                json = jsonList.get(0);
            } else {
                json = jsonList;
            }
        }
        return json;
    }

    private Object parseJson() {
        while (true) {
            char charAt = getCharAt(0);
            //   means that we are at the end of the string provided
            if (charAt == '\0') {
                return "";
                // <object> starts with '{'
            } else if (charAt == '{') {
                this.index++;
                return parseObject();
                // <array> starts with '['
            } else if (charAt == '[') {
                this.index++;
                return parseArray();
                // there can be an edge case in which a key is empty and at the end of an object
                // like "key": }. We return an empty string here to close the object properly
            } else if (this.context.getCurrent() == ContextValues.OBJECT_VALUE && charAt == '}') {
                logger.warn("At the end of an object we found a key with missing value, skipping");
                return "";
                // <string> starts with a quote
            } else if (!this.context.isEmpty() && (charAt == '"' || charAt == '\'' || charAt == '“'
                    || Character.isAlphabetic(charAt))) {
                return parseString();
                // <number> starts with [0-9] or minus
            } else if (!this.context.isEmpty() && (Character.isDigit(charAt) || charAt == '-' || charAt == '.')) {
                return parseNumber();
                // If everything else fails, we just ignore and move on
            } else {
                this.index++;
            }
        }
    }

    private Map<String, Object> parseObject() {
        // <object> ::= '{' [ <member> *(', ' <member>) ] '}' ; A sequence of 'members'
        Map<String, Object> obj = new HashMap<>();
        // Stop when you either find the closing parentheses or you have iterated over the entire string
        while (getCharAt(0) != '}') {
            // This is what we expect to find:
            // <member> ::= <string> ': ' <json>
            // Skip filler whitespaces
            skipWhitespacesAt(0, true);
            // Sometimes LLMs do weird things, if we find a ":" so early, we'll change it to "," and move on
            if (getCharAt(0) == ':') {
                logger.warn("While parsing an object we found a : before a key, ignoring");
                this.index++;
            }
            // We are now searching for they string key
            // Context is used in the string parser to manage the lack of quotes
            this.context.set(ContextValues.OBJECT_KEY);
            skipWhitespacesAt(0, true);

            // <member> starts with a <string>
            String key = "";
            while (!charIsEmpty(getCharAt(0))) {
                key = parseString().toString();
                if (StringUtils.isNotEmpty(key) || (StringUtils.isEmpty(key) && getCharAt(0) == ':')) {
                    // If the string is empty but there is a object divider, we are done here
                    break;
                }
            }

            skipWhitespacesAt(0, true);

            if (getCharAt(0) == '}') {
                continue;
            }

            skipWhitespacesAt(0, true);

            // An extreme case of missing ":" after a key
            if (getCharAt(0) != ':') {
                logger.warn("While parsing an object we missed a : after a key");
            }

            this.index++;
            this.context.reset();
            this.context.set(ContextValues.OBJECT_VALUE);
            // The value can be any valid json
            Object value = parseJson();
            // Reset context since our job is done
            this.context.reset();
            obj.put(key, value);

            if (getCharAt(0) == ',' || getCharAt(0) == '\'' || getCharAt(0) == '"') {
                this.index++;
            }

            // Remove trailing spaces
            skipWhitespacesAt(0, true);
        }

        this.index++;
        return obj;
    }

    private List<Object> parseArray() {
        // <array> ::= '[' [ <json> *(', ' <json>) ] ']' ; A sequence of JSON values separated by commas
        List<Object> arr = new ArrayList<>();
        this.context.set(ContextValues.ARRAY);
        // Stop when you either find the closing parentheses or you have iterated over the entire string
        char charAt = getCharAt(0);
        while (charAt != ']' && charAt != '}') {
            skipWhitespacesAt(0, true);
            Object value = parseJson();
            // It is possible that parse_json() returns nothing valid, so we stop
            if (value.equals("")) {
                break;
            }

            if (value.equals("...") && getCharAt(index - 1) == '.') {
                logger.warn("While parsing an array, found a stray '...'; ignoring it");
            } else {
                arr.add(value);
            }

            // skip over whitespace after a value but before closing ]
            charAt = getCharAt(0);
            while (Character.isSpaceChar(charAt) || charAt == ',') {
                index++;
                charAt = getCharAt(0);
            }
        }

        // Especially at the end of an LLM generated json you might miss the last "]"
        charAt = getCharAt(0);
        if (charAt != ']') {
            logger.warn("While parsing an array we missed the closing ], adding it back");
            this.index--;
        }

        this.index++;
        this.context.reset();
        return arr;
    }

    private Object parseString() {
        // <string> is a string of valid characters enclosed in quotes
        // i.e. { name: "John" }
        // Somehow all weird cases in an invalid JSON happen to be resolved in this function, so be careful here

        // Flag to manage corner cases related to missing starting quote
        boolean missingQuotes = false;
        boolean doubledQuotes = false;
        char lstringDelimiter = '"';
        char rstringDelimiter = '"';
        char charAt = getCharAt(0);
        // A valid string can only start with a valid quote or, in our case, with a literal
        while (charAt != '"' && charAt != '\'' && charAt != '“' && !Character.isAlphabetic(charAt)) {
            index++;
            charAt = getCharAt(0);
        }

        if (charIsEmpty(charAt)) {
            // This is an empty string
            return "";
        }

        // Ensuring we use the right delimiter
        if (charAt == '\'') {
            lstringDelimiter = rstringDelimiter = '\'';
        } else if (charAt == '“') {
            lstringDelimiter = '“';
            rstringDelimiter = '”';
        } else if (Character.isAlphabetic(charAt)) {
            // This could be a <boolean> and not a string. Because (T)rue or (F)alse or (N)ull are valid
            // But remember, object keys are only of type string
            char lowerChar = Character.toLowerCase(charAt);
            if ((lowerChar == 't' || lowerChar == 'f' || lowerChar == 'n')
                    && this.context.getCurrent() != ContextValues.OBJECT_KEY) {
                Object value = parseBooleanOrNull();
                if (value != null && value != "") {
                    return value.toString();
                }
            }
            logger.warn("While parsing a string, we found a literal instead of a quote");
            logger.warn("While parsing a string, we found no starting quote. Will add the quote back");
            missingQuotes = true;
        }
        if (!missingQuotes) {
            this.index++;
        }

        // There is sometimes a weird case of doubled quotes, we manage this also later in the while loop
        if (getCharAt(0) == lstringDelimiter) {
            // If it's an empty key, this was easy
            if (this.context.getCurrent() == ContextValues.OBJECT_KEY && getCharAt(1) == ':') {
                this.index++;
                return "";
            }

            // Find the next delimiter
            int i = skipToCharacter(rstringDelimiter, 1);
            char nextC = getCharAt(i);

            // Now check that the next character is also a delimiter to ensure that we have "".....""
            // In that case we ignore this rstring delimiter
            if (!charIsEmpty(nextC) && getCharAt(i + 1) == rstringDelimiter) {
                logger.warn("While parsing a string, we found a valid starting doubled quote, ignoring it");
                doubledQuotes = true;
                this.index++;
            } else {
                // Ok this is not a doubled quote, check if this is an empty string or not
                i = skipWhitespacesAt(1, false);
                nextC = getCharAt(i);
                if (!charIsEmpty(nextC) && nextC != ',' && nextC != ']' && nextC != '}') {
                    logger.warn(
                            "While parsing a string, we found a doubled quote but it was a mistake, removing one quote");
                    this.index++;
                }
            }
        }

        // Initialize our return value
        StringBuilder stringAcc = new StringBuilder();

        //  Here things get a bit hairy because a string missing the final quote can also be a key or a value in an object
        //  In that case we need to use the ":|,|}" characters as terminators of the string
        //  So this will stop if:
        //  * It finds a closing quote
        //  * It iterated over the entire sequence
        //  * If we are fixing missing quotes in an object, when it finds the special terminators
        charAt = getCharAt(0);
        boolean unmatchedDelimiter = false;
        while (!charIsEmpty(charAt) && charAt != rstringDelimiter) {
            if (missingQuotes && this.context.getCurrent() == ContextValues.OBJECT_KEY && (charAt == ':'
                    || Character.isSpaceChar(charAt))) {
                logger.warn(
                        "While parsing a string missing the left delimiter in object key context, we found a :, stopping here");
                break;
            }

            if (this.context.getCurrent() == ContextValues.OBJECT_VALUE && (charAt == ',' || charAt == '}')) {
                boolean rstringDelimiterMissing = true;
                // check if this is a case in which the closing comma is NOT missing instead
                int i = skipToCharacter(rstringDelimiter, 1);
                char nextC = getCharAt(i);
                if (!charIsEmpty(nextC)) {
                    i++;
                    // found a delimiter, now we need to check that is followed strictly by a comma or brace
                    // or the string ended
                    i = skipWhitespacesAt(i, false);
                    nextC = getCharAt(i);
                    if (charIsEmpty(nextC) || nextC == ',' || nextC == '}') {
                        rstringDelimiterMissing = false;
                    } else {
                        // OK but this could still be some garbage at the end of the string
                        // So we need to check if we find a new lstring_delimiter afterwards
                        // If we do, maybe this is a missing delimiter
                        i = skipToCharacter(lstringDelimiter, i);
                        if (doubledQuotes) {
                            i = skipToCharacter(lstringDelimiter, i);
                        }
                        nextC = getCharAt(i);
                        if (charIsEmpty(nextC)) {
                            rstringDelimiterMissing = false;
                        } else {
                            // But again, this could just be something a bit stupid like "lorem, "ipsum" sic"
                            // Check if we find a : afterwards (skipping space)
                            i = skipWhitespacesAt(i + 1, false);
                            nextC = getCharAt(i);
                            if (!charIsEmpty(nextC) && nextC != ':') {
                                rstringDelimiterMissing = false;
                            }
                        }
                    }
                } else {
                    // There could be a case in which even the next key:value is missing delimeters
                    // because it might be a systemic issue with the output
                    // So let's check if we can find a : in the string instead
                    i = skipToCharacter(':', 1);
                    nextC = getCharAt(i);
                    if (!charIsEmpty(nextC)) {
                        // OK then this is a systemic issue with the output
                        break;
                    } else {
                        // skip any whitespace first
                        i = skipWhitespacesAt(1, false);
                        // We couldn't find any rstring_delimeter before the end of the string
                        // check if this is the last string of an object and therefore we can keep going
                        // make an exception if this is the last char before the closing brace
                        int j = skipToCharacter('}', i);
                        if (j - i > 1) {
                            // Ok it's not right after the comma
                            // Let's ignore
                            rstringDelimiterMissing = false;

                            // Check that j was not out of bound
                        } else if (!charIsEmpty(getCharAt(j))) {
                            for (char c : stringAcc.reverse().toString().toCharArray()) {
                                if (c == '{') {
                                    // Ok then this is part of the string
                                    rstringDelimiterMissing = false;
                                    break;
                                } else if (c == '}') {
                                    break;
                                }
                            }
                        }
                    }
                }
                if (rstringDelimiterMissing) {
                    logger.warn(
                            "While parsing a string missing the left delimiter in object value context, we found a , or } and we couldn't determine that a right delimiter was present. Stopping here");
                    break;
                }
            }

            if (charAt == ']' && this.context.getContext().contains(ContextValues.ARRAY)) {
                // We found the end of an array and we are in array context
                // So let's check if we find a rstring_delimiter forward otherwise end early
                int i = skipToCharacter(rstringDelimiter, 0);
                if (charIsEmpty(getCharAt(i))) {
                    // No delimiter found
                    break;
                }
            }

            stringAcc.append(charAt);
            this.index++;
            charAt = getCharAt(0);
            if (!charIsEmpty(charAt) && stringAcc.length() > 0 && stringAcc.charAt(stringAcc.length() - 1) == '\\') {
                // This is a special case, if people use real strings this might happen
                logger.warn("Found a stray escape sequence, normalizing it");
                Set<Character> escapeChars = new HashSet<>(Arrays.asList('t', 'n', 'r', 'b', '\\'));
                Map<String, Character> escapeMap = new HashMap<>();
                escapeMap.put("t", '\t');
                escapeMap.put("n", '\n');
                escapeMap.put("r", '\r');
                escapeMap.put("b", '\b');

                if (escapeChars.contains(charAt)) {
                    stringAcc = new StringBuilder(stringAcc.substring(0, stringAcc.length() - 1));
                    stringAcc.append(escapeMap.getOrDefault(String.valueOf(charAt), charAt));
                    this.index++;
                    charAt = getCharAt(0);
                }

                // ChatGPT sometimes forget to quote stuff in html tags or markdown, so we do this whole thing here
                if (charAt == rstringDelimiter) {
                    // Special case here, in case of double quotes one after another
                    if (doubledQuotes && getCharAt(1) == rstringDelimiter) {
                        logger.warn("While parsing a string, we found a doubled quote, ignoring it");
                        this.index++;
                    } else if (missingQuotes && this.context.getCurrent() == ContextValues.OBJECT_VALUE) {
                        // In case of missing starting quote I need to check if the delimeter is the end or the beginning of a key
                        int i = 1;
                        char nextC = getCharAt(i);
                        while (!charIsEmpty(nextC) && nextC != rstringDelimiter) {
                            i++;
                            nextC = getCharAt(i);
                        }

                        if (!charIsEmpty(nextC)) {
                            // We found a quote, now let's make sure there's a ":" following
                            i++;
                            // found a delimiter, now we need to check that is followed strictly by a comma or brace
                            i = skipWhitespacesAt(i, false);
                            nextC = getCharAt(i);
                            if (nextC == ':') {
                                // Reset the cursor
                                this.index--;
                                charAt = getCharAt(0);
                                logger.warn(
                                        "In a string with missing quotes and object value context, I found a delimeter but it turns out it was the beginning on the next key. Stopping here.");
                                break;
                            }
                        }
                    }
                } else if (unmatchedDelimiter) {
                    unmatchedDelimiter = false;
                    stringAcc.append(charAt);
                    this.index++;
                    charAt = getCharAt(0);
                } else {
                    // Check if eventually there is a rstring delimiter, otherwise we bail
                    int i = 1;
                    char nextC = getCharAt(i);
                    boolean checkCommaInObjectValue = true;
                    while (!charIsEmpty(nextC) && nextC != rstringDelimiter) {
                        if (checkCommaInObjectValue && Character.isAlphabetic(nextC)) {
                            checkCommaInObjectValue = false;
                        }

                        // If we are in an object context, let's check for the right delimiters
                        if ((this.context.getContext().contains(ContextValues.OBJECT_KEY) && (nextC == ':'
                                || nextC == ','))
                                || (this.context.getContext().contains(ContextValues.OBJECT_VALUE) && nextC == '}')
                                || (this.context.getContext().contains(ContextValues.ARRAY) && (nextC == ']'
                                || nextC == ','))
                                || (checkCommaInObjectValue && this.context.getCurrent() == ContextValues.OBJECT_VALUE
                                && nextC == ',')) {
                            break;

                        }

                        i++;
                        nextC = getCharAt(i);
                        // If we stopped for a comma in object_value context, let's check if find a "} at the end of the string
                        if (nextC == ',' && this.context.getCurrent() == ContextValues.OBJECT_VALUE) {
                            i++;
                            i = skipToCharacter(rstringDelimiter, i);
                            nextC = getCharAt(i);
                            // Ok now I found a delimiter, let's skip whitespaces and see if next we find a }
                            i++;
                            i = skipWhitespacesAt(i, false);
                            if (nextC == '}') {
                                logger.warn(
                                        "While parsing a string, we misplaced a quote that would have closed the string but has a different meaning here since this is the last element of the object, ignoring it");
                                unmatchedDelimiter = !unmatchedDelimiter;
                                stringAcc.append(charAt);
                                this.index++;
                                charAt = getCharAt(0);
                            }
                        } else if (nextC == rstringDelimiter && getCharAt(i - 1) != '\\') {
                            if (this.context.getCurrent() == ContextValues.OBJECT_VALUE) {
                                // But this might not be it! This could be just a missing comma
                                // We found a delimiter and we need to check if this is a key
                                // so find a rstring_delimiter and a colon after
                                i++;
                                i = skipToCharacter(rstringDelimiter, i);
                                i++;
                                while (!charIsEmpty(nextC)) {
                                    if (getCharAt(i - 1) != '\\') {
                                        break;
                                    }
                                    i++;
                                    nextC = getCharAt(i);
                                }
                                // Only if we fail to find a ':' then we know this is misplaced quote
                                if (nextC != ':') {
                                    logger.warn(
                                            "While parsing a string, we a misplaced quote that would have closed the string but has a different meaning here, ignoring it");
                                    unmatchedDelimiter = !unmatchedDelimiter;
                                    stringAcc.append(charAt);
                                    this.index++;
                                    charAt = getCharAt(0);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (!charIsEmpty(charAt) && missingQuotes && this.context.getCurrent() == ContextValues.OBJECT_KEY
                && Character.isSpaceChar(charAt)) {
            logger.warn(
                    "While parsing a string, handling an extreme corner case in which the LLM added a comment instead of valid string, invalidate the string and return an empty value");
            skipWhitespacesAt(0, true);
            if (!(getCharAt(0) == ':' || getCharAt(0) == ',')) {
                return "";
            }
        }

        // A fallout of the previous special case in the while loop,
        // we need to update the index only if we had a closing quote
        if (charAt == rstringDelimiter) {
            logger.warn("While parsing a string, we missed the closing quote, ignoring");
        } else {
            this.index++;
        }
        return rstrip(stringAcc.toString());
    }

    private Object parseNumber() {
        // <number> is a valid real number expressed in one of a number of given formats
        char charAt = getCharAt(0);
        StringBuilder numberStr = new StringBuilder();
        boolean isArray = this.context.getCurrent() == ContextValues.ARRAY;
        Set<Character> needChar = new HashSet<>(Arrays.asList('-', 'e', 'E', '/', ','));
        Set<Character> numberChars = new HashSet<>(
                Arrays.asList('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '-', '.', 'e', 'E', ',', '/'));

        while (charAt != '\0' && numberChars.contains(charAt) && (charAt != ',' || !isArray)) {
            numberStr.append(charAt);
            this.index++;
            charAt = getCharAt(0);
        }

        char lastChar = numberStr.charAt(numberStr.length() - 1);

        if (numberStr.length() > 1 && needChar.contains(lastChar)) {
            // The number ends with a non valid character for a number/currency, rolling back one
            numberStr.deleteCharAt(numberStr.length() - 1);
            this.index--;
        }

        try {
            if (numberStr.toString().contains(",")) {
                return numberStr.toString();
            }
            if (numberStr.toString().contains(".") || numberStr.toString().contains("e") || numberStr.toString()
                    .contains("E")) {
                return Double.parseDouble(numberStr.toString());
            } else if (numberStr.toString().equals("-")) {
                // If there is a stray "-" this will throw an exception, throw away this character
                return parseJson();
            } else {
                return Integer.parseInt(numberStr.toString());
            }
        } catch (NumberFormatException e) {
            return numberStr.toString();
        }
    }

    private Object parseBooleanOrNull() {
        // <boolean> is one of the literal strings 'true', 'false', or 'null' (unquoted)
        String value = null;
        Boolean result = null;
        int startingIndex = index;
        char charAt = Character.toLowerCase(getCharAt(0));
        switch (charAt) {
            case 't':
                value = "true";
                result = true;
                break;
            case 'f':
                value = "false";
                result = false;
                break;
            case 'n':
                value = "null";
                result = null;
                break;
        }

        if (value != null) {
            int i = 0;
            while (!charIsEmpty(charAt) && i < value.length() && charAt == value.charAt(i)) {
                i++;
                this.index++;
                charAt = Character.toLowerCase(getCharAt(0));
            }
            if (i == value.length()) {
                return result;
            }
        }

        // If nothing works reset the index before returning
        this.index = startingIndex;
        return "";
    }

    private char getCharAt(int offset) {
        try {
            return ((CharSequence) this.jsonStr).charAt(this.index + offset);
        } catch (StringIndexOutOfBoundsException e) {
            return  '\0';
        }
    }

    private boolean charIsEmpty(char charAt) {
        return charAt == '\0';
    }

    private int length(Object jsonStr) {
        if (jsonStr instanceof String) {
            return ((String) jsonStr).length();
        } else if (jsonStr instanceof StringFileWrapper) {
            return ((StringFileWrapper) jsonStr).length();
        }
        return 0;
    }


    private int skipWhitespacesAt(int idx, boolean moveMainIndex) {
        char charAt;
        try {
            charAt = getCharAt(this.index + idx);
            while (Character.isSpaceChar(charAt)) {
                if (moveMainIndex) {
                    this.index++;
                } else {
                    idx++;
                }
                charAt = getCharAt(this.index + idx);
            }
        } catch (Exception e) {
            return idx;
        }
        return idx;
    }

    private int skipToCharacter(char character, int idx) {
        char charAt;
        try {
            charAt = getCharAt(this.index + idx);
            while (charAt != character) {
                idx++;
                charAt = getCharAt(this.index + idx);
            }
            if (this.index + idx > 0 && getCharAt(this.index + idx - 1) == '\\') {
                return skipToCharacter(character, idx + 1);
            }
        } catch (Exception e) {
            return idx;
        }
        return idx;
    }

    private static String rstrip(String str) {
        int endIndex = str.length();
        while (endIndex > 0 && Character.isWhitespace(str.charAt(endIndex - 1))) {
            endIndex--; // 找到最后一个非空白字符的索引
        }
        return str.substring(0, endIndex); // 返回去除右侧空白后的字符串
    }
}