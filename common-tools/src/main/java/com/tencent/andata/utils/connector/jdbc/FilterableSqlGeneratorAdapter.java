package com.tencent.andata.utils.connector.jdbc;

import com.tencent.andata.utils.sink.pg.SqlGenerator;
import com.tencent.andata.utils.sink.pg.TableDefinition;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 可过滤SQL生成器的适配器实现
 * 将普通SQL生成器适配为可过滤的SQL生成器
 */
public class FilterableSqlGeneratorAdapter implements FilterableSqlGenerator {

    private static final long serialVersionUID = 1L;

    private final SqlGenerator sqlGenerator;

    /**
     * 构造函数
     *
     * @param sqlGenerator SQL生成器
     */
    public FilterableSqlGeneratorAdapter(SqlGenerator sqlGenerator) {
        this.sqlGenerator = sqlGenerator;
    }

    @Override
    public String generateInsertSql(TableDefinition tableDefinition, ColumnFilter columnFilter) {
        // 使用列过滤器过滤表列
        List<String> filteredColumnNames = tableDefinition.getColumns().stream()
                .filter(columnFilter::accept)
                .map(column -> column.getName())
                .collect(Collectors.toList());

        // 调用原始SQL生成器生成SQL，但只包含过滤后的列
        return sqlGenerator.generateInsertSql(tableDefinition, filteredColumnNames);
    }
}