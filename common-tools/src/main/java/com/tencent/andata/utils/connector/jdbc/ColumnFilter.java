package com.tencent.andata.utils.connector.jdbc;

import com.tencent.andata.utils.sink.pg.ColumnDefinition;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 列过滤器接口
 * 用于在数据库操作中过滤需要处理的列
 */
public interface ColumnFilter extends Serializable {

    /**
     * 过滤列定义列表
     *
     * @param columns 原始列定义列表
     * @return 过滤后的列定义列表
     */
    default List<ColumnDefinition> filterColumns(List<ColumnDefinition> columns) {
        return columns.stream()
                .filter(this::accept)
                .collect(Collectors.toList());
    }

    /**
     * 判断给定列定义是否应该被包含
     *
     * @param column 列定义
     * @return 如果列应该被包含则返回true，否则返回false
     */
    default boolean accept(ColumnDefinition column) {
        return shouldInclude(column.getName());
    }

    /**
     * 判断给定列名是否应该被包含
     *
     * @param columnName 列名
     * @return 如果列应该被包含则返回true，否则返回false
     */
    boolean shouldInclude(String columnName);
}