package com.tencent.andata.utils;

import static com.tencent.andata.utils.ExceptionWrapperUtil.function;
import static org.apache.flink.util.concurrent.Executors.directExecutor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.log.FlinkLog;
import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.util.EntityUtils;

public class AsyncHttpClientUtils extends HttpClientUtils implements Serializable {

    private static CloseableHttpAsyncClient httpAsyncClient;

    private static ObjectMapper MAPPER = new ObjectMapper();

    private static final FlinkLog logger = FlinkLog.getInstance();


    public AsyncHttpClientUtils(int socketTimeout, int connectTimeout) {
        super(socketTimeout, connectTimeout);

        // 创建异步HttpClient
        httpAsyncClient = AsyncHttpClient.getInstance();
    }


    /**
     * 异步执行HTTP POST请求
     *
     * @param api 请求URL
     * @param params 请求参数
     * @param headers 请求头
     * @return 包含响应字符串的CompletableFuture
     */
    public CompletableFuture<String> doHttpPost(
            String api,
            Map<String, Object> params,
            Map<String, String> headers) throws Exception {

        HttpPost post = new HttpPost(api);
        headers.forEach(post::setHeader);
        String paramStr = MAPPER.writeValueAsString(params);
        post.setEntity(new StringEntity(paramStr, ContentType.APPLICATION_JSON));

        /****************************************************************************************************************************
         * <p>
         * 这里调用httpAsyncClient.execute(post, null).get()会阻塞异步IO
         * 但是使用完全的异步FutureCallback方式，需要注意上游flink算子并发问题
         * 如果并发太高会导致Connection lease request time out
         * 参考：https://juejin.cn/post/7367659849101918223
         * <p>
         *
         // 使用CompletableFuture来链式处理HTTP请求和响应
         return CompletableFuture
         .supplyAsync(supplier(() -> httpAsyncClient.execute(post, null).get()), directExecutor())
         .thenApplyAsync(function((response) -> EntityUtils.toString(response.getEntity(), "UTF-8")), directExecutor());
         *******************************************************************************************************************************/

        return executeRequestAsync(post);
    }

    /**
     * 异步执行HTTP GET请求
     *
     * @param url 请求URL
     * @param headers 请求头
     * @return 包含HTTP响应的Future
     */
    public CompletableFuture<String> doHttpGet(String url, Map<String, String> headers) {
        HttpGet httpGet = new HttpGet(url);
        headers.forEach(httpGet::setHeader);
        return executeRequestAsync(httpGet);
    }

    /**
     * 执行异步HTTP请求并处理响应
     *
     * @param request HTTP请求
     * @return 包含响应字符串的CompletableFuture
     */
    private <T extends HttpUriRequest> CompletableFuture<String> executeRequestAsync(T request) {
        CompletableFuture<HttpResponse> responseFuture = new CompletableFuture<>();

        httpAsyncClient.execute(request, createResponseCallback(responseFuture));

        return responseFuture.thenApplyAsync(
                function(response -> EntityUtils.toString(response.getEntity(), "UTF-8")), directExecutor()
        );
    }

    /**
     * 创建HTTP响应回调处理器
     *
     * @param responseFuture 用于接收结果的CompletableFuture
     * @return 配置好的FutureCallback实例
     */
    private FutureCallback<HttpResponse> createResponseCallback(CompletableFuture<HttpResponse> responseFuture) {
        return new FutureCallback<HttpResponse>() {
            @Override
            public void completed(HttpResponse result) {
                responseFuture.complete(result);
            }

            @Override
            public void failed(Exception ex) {
                logger.error("HTTP request failed with exception: " + ex);
                responseFuture.completeExceptionally(ex);
            }

            @Override
            public void cancelled() {
                logger.warn("HTTP request was cancelled");
                responseFuture.cancel(true);
            }
        };
    }
}