package com.tencent.andata.utils;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HttpClientUtils 的测试类。
 * 使用 JSONPlaceholder (http://jsonplaceholder.typicode.com/) 作为测试API。
 * 参考: https://blog.csdn.net/qq_36259143/article/details/120761674
 *
 * 注意: 这些测试依赖于外部API的可用性。
 * 如果在无法访问外部网络的环境中运行，这些测试可能会失败。
 * 可以使用 @DisabledIfSystemProperty 来控制是否在特定环境下禁用测试。
 */
// 示例：如果系统属性 "offlineTests" 设置为 "true"，则禁用这些测试
// @DisabledIfSystemProperty(named = "offlineTests", matches = "true", disabledReason = "Tests require internet access to JSONPlaceholder")
public class HttpClientUtilsTest {

    private static final String BASE_URL = "https://jsonplaceholder.typicode.com"; // 使用HTTPS

    @BeforeAll
    static void setUp() {
        // 可以在此处进行一次性的设置，例如检查网络连接等
        System.out.println("开始 HttpClientUtils 测试，目标API: " + BASE_URL);
    }

    @Test
    void testGetPosts() {
        String url = BASE_URL + "/posts";
        try (HttpClientUtils client = new HttpClientUtils.Builder().maxRetries(1).build()) {
            String response = client.get(url);
            System.out.println("testGetPosts 响应: " + response.substring(0, Math.min(response.length(), 300)) + "..."); // 打印部分响应
            assertNotNull(response, "响应不应为null");
            assertTrue(!response.isEmpty(), "响应不应为空");
            assertTrue(response.startsWith("["), "响应应为JSON数组");
        } catch (HttpClientUtils.HttpClientException e) {
            fail("GET请求失败: " + e.getMessage(), e);
        } catch (IOException e) {
            fail("关闭HttpClientUtils失败: " + e.getMessage(), e);
        }
    }

    @Test
    void testGetPostsForUser() {
        String url = BASE_URL + "/posts?userId=1";
        try (HttpClientUtils client = new HttpClientUtils()) { // 使用默认配置
            String response = client.get(url);
            System.out.println("testGetPostsForUser 响应 (userId=1): " + response.substring(0, Math.min(response.length(), 300)) + "...");
            assertNotNull(response);
            assertTrue(response.contains("\"userId\": 1"), "响应应包含userId 1 的帖子");
        } catch (HttpClientUtils.HttpClientException e) {
            fail("GET请求 (带参数) 失败: " + e.getMessage(), e);
        } catch (IOException e) {
            fail("关闭HttpClientUtils失败: " + e.getMessage(), e);
        }
    }

    @Test
    void testGetSinglePostAsObject() {
        String url = BASE_URL + "/posts/1";
        try (HttpClientUtils client = new HttpClientUtils.Builder()
                .connectTimeout(5000)
                .socketTimeout(10000)
                .maxRetries(2)
                .build()) {
            Post post = client.getAsObject(url, Post.class);
            System.out.println("testGetSinglePostAsObject 响应: " + post);
            assertNotNull(post, "反序列化的Post对象不应为null");
            assertEquals(1, post.getId(), "Post ID 应为 1");
            assertNotNull(post.getTitle(), "Post 标题不应为 null");
        } catch (HttpClientUtils.HttpClientException e) {
            fail("getAsObject请求失败: " + e.getMessage(), e);
        } catch (IOException e) {
            fail("关闭HttpClientUtils失败: " + e.getMessage(), e);
        }
    }

    @Test
    void testCreatePostAsString() {
        String url = BASE_URL + "/posts";
        String jsonInputString = "{\"title\": \"foo bar\", \"body\": \"baz qux\", \"userId\": 99}";
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpClientUtils.HEADER_CONTENT_TYPE, HttpClientUtils.CONTENT_TYPE_JSON); // 明确指定Content-Type

        try (HttpClientUtils client = new HttpClientUtils()) {
            String response = client.post(url, jsonInputString, headers);
            System.out.println("testCreatePostAsString 响应: " + response);
            assertNotNull(response);
            assertTrue(response.contains("\"title\": \"foo bar\""), "响应应包含发送的title");
            assertTrue(response.contains("\"userId\": 99"), "响应应包含发送的userId");
            assertTrue(response.contains("\"id\":"), "响应应包含一个id字段 (JSONPlaceholder通常会分配一个)");
        } catch (HttpClientUtils.HttpClientException e) {
            fail("POST请求 (String) 失败: " + e.getMessage(), e);
        } catch (IOException e) {
            fail("关闭HttpClientUtils失败: " + e.getMessage(), e);
        }
    }

    @Test
    void testCreatePostWithMapAsObject() {
        String url = BASE_URL + "/posts";
        Map<String, Object> postData = new HashMap<>();
        postData.put("title", "My Test Post");
        postData.put("body", "This is the body of my test post.");
        postData.put("userId", 77);

        try (HttpClientUtils client = new HttpClientUtils.Builder().maxRetries(0).build()) { // 禁用重试以更快失败（如果API行为意外）
            Post createdPost = client.postAsObject(url, postData, Post.class);
            System.out.println("testCreatePostWithMapAsObject 响应: " + createdPost);
            assertNotNull(createdPost, "创建的Post对象不应为null");
            assertEquals("My Test Post", createdPost.getTitle(), "Title不匹配");
            assertEquals(77, createdPost.getUserId(), "UserId不匹配");
            assertTrue(createdPost.getId() > 0, "应分配一个正数ID"); // JSONPlaceholder通常返回一个ID，如101
        } catch (HttpClientUtils.HttpClientException e) {
            fail("postAsObject (Map) 请求失败: " + e.getMessage(), e);
        } catch (IOException e) {
            fail("关闭HttpClientUtils失败: " + e.getMessage(), e);
        }
    }

    @Test
    void testGetCommentsForPost() {
        String url = BASE_URL + "/posts/1/comments";
        try (HttpClientUtils client = new HttpClientUtils()) {
            String response = client.get(url);
            System.out.println("testGetCommentsForPost 响应: " + response.substring(0, Math.min(response.length(), 300)) + "...");
            assertNotNull(response);
            assertTrue(response.startsWith("[") && response.endsWith("]"), "响应应为JSON数组");
            assertTrue(response.contains("\"postId\": 1"), "所有评论的postId应为1");
        } catch (HttpClientUtils.HttpClientException e) {
            fail("GET请求 (comments) 失败: " + e.getMessage(), e);
        } catch (IOException e) {
            fail("关闭HttpClientUtils失败: " + e.getMessage(), e);
        }
    }

    @Test
    void testGetNonExistentResource() {
        String url = BASE_URL + "/nonexistentresource/999";
        try (HttpClientUtils client = new HttpClientUtils.Builder().maxRetries(0).build()) { // 禁用重试
            // JSONPlaceholder 对于不存在的资源通常返回 {} 和 404
            // HttpClientUtils 会将非2xx状态码包装为HttpClientException
            HttpClientUtils.HttpClientException exception = assertThrows(
                    HttpClientUtils.HttpClientException.class,
                    () -> client.get(url),
                    "对于不存在的资源，应抛出 HttpClientException"
            );
            System.out.println("testGetNonExistentResource 捕获到的异常: " + exception.getMessage());
            assertTrue(exception.getMessage().contains("Status: 404"), "异常消息应包含状态码404");
        } catch (IOException e) {
            fail("关闭HttpClientUtils失败: " + e.getMessage(), e);
        }
    }

    @Test
    void testGetAsObjectWithEmptyResponse() {
        // 模拟一个返回空JSON对象的场景 (JSONPlaceholder本身不易构造此场景，此处主要测试工具类的处理)
        // 假设有个API /emptyObject 返回 "{}" 或 ""
        // 此处我们用一个已知API但期望一个无法转换的类型，或让其返回空
        // 更好的测试方法是配合Mock服务器，这里我们尝试用一个不太可能返回有效 Post 的端点
        String url = BASE_URL + "/users/1/albums"; // 通常返回数组，如果期望单个Post会出问题
        // 或者如果API返回空字符串 ""
        try (HttpClientUtils client = new HttpClientUtils.Builder().build()) {
            // 如果API /users/1/albums 返回 "[]" (空数组) 或 "" (空字符串)
            // deserializeResponse 方法对空字符串返回 null (非String类型时)
            // ObjectMapper 对 "[]" 转换到 Post.class 通常也会失败或返回null (取决于配置)

            // 测试点：当响应体是空字符串时，getAsObject 返回 null
            // 实际中JSONPlaceholder /albums 不会返回单个空字符串，但可以测试逻辑
            // 为了模拟这个，我们修改测试，假设 get() 方法返回了空字符串

            // 这里我们直接测试 deserializeResponse 的行为，如果get返回空字符串
            HttpClientUtils.HttpClientException ex = assertThrows(HttpClientUtils.HttpClientException.class, () -> {
                Post post = client.getAsObject(url, Post.class); // /albums 返回数组，不是单个Post对象
                // 如果 get() 成功返回了JSON数组字符串，那么readValue(arrayString, Post.class)会失败
                // 如果我们想测试的是 get() 返回空字符串，然后getAsObject收到空字符串的情况：
                // 在deserializeResponse中，空字符串对非String类型会返回null。
                // 如果返回的是"{}", Jackson可能创建一个所有字段为null的Post对象。
            });

            System.out.println("testGetAsObjectWithEmptyResponse 异常信息: " + ex.getMessage());
            assertTrue(ex.getMessage().contains("Failed to deserialize JSON response"), "应为反序列化失败");

        } catch (IOException e) {
            fail("关闭HttpClientUtils失败: " + e.getMessage(), e);
        }
    }

    public static class Post {
        private int userId;
        private int id;
        private String title;
        private String body;

        // Jackson需要一个无参构造函数
        public Post() {
        }

        public Post(int userId, String title, String body) {
            this.userId = userId;
            this.title = title;
            this.body = body;
        }

        // Getters and Setters
        public int getUserId() {
            return userId;
        }

        public void setUserId(int userId) {
            this.userId = userId;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getBody() {
            return body;
        }

        public void setBody(String body) {
            this.body = body;
        }

        @Override
        public String toString() {
            return "Post{" +
                    "userId=" + userId +
                    ", id=" + id +
                    ", title='" + title + '\'' +
                    ", body='" + body + '\'' +
                    '}';
        }
    }
}