import com.tencent.andata.utils.TableUtils;

/**
 * 测试MySQL server ID范围生成功能
 * 验证生成的server ID范围是否唯一且支持并行度
 */
public class TestServerIdGeneration {

    public static void main(String[] args) {
        System.out.println("=== MySQL Server ID 范围生成测试 ===");

        // 测试100次生成
        String result = TableUtils.testServerIdGeneration(100);
        System.out.println(result);

        // 测试1000次生成（压力测试）
        System.out.println("\n=== 压力测试 ===");
        String stressResult = TableUtils.testServerIdGeneration(1000);
        System.out.println(stressResult);

        System.out.println("\n=== 功能说明 ===");
        System.out.println("1. 每个表分配一个1000个server ID的范围");
        System.out.println("2. 支持最大1000个并行度的CDC连接");
        System.out.println("3. 同一个表每次部署获得相同的范围（基于表名hash）");
        System.out.println("4. 不同表获得不同的范围，避免冲突");

        System.out.println("\n=== 测试完成 ===");
    }
}