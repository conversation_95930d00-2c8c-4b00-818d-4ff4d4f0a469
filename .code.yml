source:
  # 提供产品代码库中编写的测试代码文件路径。
  # 此处配置的路径，在后续统计代码环节会被排除，若不需要排除，该项配置标识可为空。
  test_source:
    # 填写相对路径的正则表达式(相对于代码库根目录)，要求匹配到文件层级，例如：["test/.*", "src/.*_test.go"]
    filepath_regex: [
      "infra/src/test/.*"
    ]

  # 提供产品代码库中工具或框架自动生成的且在代码库中的文件路径。
  # 此处配置的路径，在后续统计代码环节会被排除，若不需要排除，该项配置标识可为空。
  auto_generate_source:
    # 填写相对路径的正则表达式(相对于代码库根目录)，要求匹配到文件层级，例如：["auto_generate/.*"]
    filepath_regex: [
      "common-tools/src/main/java/com/tencent/andata/struct/avro/.*",
      "common-tools/src/main/java/com/tencent/andata/struct/protobuf/.*",

    ]

  # 提供产品代码库中直接以源码形式存在的第三方代码文件路径。
  # 此处配置的路径，在后续统计代码环节会被排除，若不需要排除，该项配置标识可为空。
  third_party_source:
    # 填写相对路径的正则表达式(相对于代码库根目录)，要求匹配到文件层级，例如：["third_party/.*"]
    filepath_regex: [
      "cprb/pom.xml"
    ]