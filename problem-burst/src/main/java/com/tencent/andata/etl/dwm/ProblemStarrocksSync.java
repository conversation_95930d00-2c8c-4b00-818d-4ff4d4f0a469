package com.tencent.andata.etl.dwm;

import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Properties;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

@Builder
public class ProblemStarrocksSync {

    private final String icebergDbName;
    private final String srRainbowConf;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        String starRocksTable2FlinkTable = rainbowUtils.getStringValue(
                srRainbowConf, "starRocksTable2FlinkTable");
        String icebergTable2FlinkTable = rainbowUtils.getStringValue(
                srRainbowConf, "icebergTable2FlinkTable");
        String icebergTable2StarrocksTable = rainbowUtils.getStringValue(
                srRainbowConf, "icebergTable2StarrocksTable");

        ObjectMapper mapper = new ObjectMapper();

        // starrocks table mapping to flink table
        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(starRocksTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(rocksDbConf, starRocksTable2FlinkTableMap, ROCKS, tEnv);
        ArrayNode icebergTable2StarrocksTableMap = mapper.readValue(icebergTable2StarrocksTable, ArrayNode.class);

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable,
                        ArrayNode.class), tEnv, catalog
        );

        StatementSet stmtSet = flinkEnv.stmtSet();

        for (JsonNode node : icebergTable2StarrocksTableMap) {
            String icebergTable = node.get("icebergTable").asText();
            String sTable = node.get("sTable").asText();
            String fView = node.get("fView").asText();
            String columns = node.get("columns").asText();

            String hints = "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
                    + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/";
            tEnv.createTemporaryView(
                    fView,
                    tEnv.sqlQuery(String.format("SELECT %s FROM %s %s", columns, icebergTable, hints)));

            stmtSet.addInsertSql(insertIntoSql(fView, sTable, tEnv.from(sTable), ROCKS));
        }
    }
}
