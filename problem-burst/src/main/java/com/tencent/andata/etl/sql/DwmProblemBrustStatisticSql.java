package com.tencent.andata.etl.sql;

public class DwmProblemBrustStatisticSql {

    public static String DwmFaultManagementStatisticSql = ""
            + "SELECT\n"
            + "    now() AS record_update_time,\n"
            + "    CAST(t.problem_id AS BIGINT) AS value_of_primary_key,\n"
            + "    CAST(t.problem_id AS BIGINT) AS problem_id,\n"
            + "    t1.create_time,\n"
            + "    t1.status,\n"
            + "    t1.priority,\n"
            + "    t1.description,\n"
            + "    t1.manager,\n"
            + "    t1.title,\n"
            + "    t1.current_operator,\n"
            + "    t1.reason_category,\n"
            + "    t1.is_fault,\n"
            + "    t1.reason,\n"
            + "    t1.reason_category_supplement,\n"
            + "    t1.is_lone_problem,\n"
            + "    t1.service_scene_display,\n"
            + "    t1.is_affect_public_cloud,\n"
            + "    t1.is_affect_private_cloud,\n"
            + "    t1.is_public_cloud_problem_solved,\n"
            + "    t1.is_private_cloud_problem_solved,\n"
            + "    t1.problem_change_production_href,\n"
            + "    t.burst_status,\n"
            + "    t.burst_title,\n"
            + "    t.fault_grade,\n"
            + "    t.is_related_with_testing,\n"
            + "    t.is_without_testing,\n"
            + "    t.fault_influenced_customer,\n"
            + "    t.delivery_mode,\n"
            + "    t.is_supplement,\n"
            + "    t.is_yanxi,\n"
            + "    t.is_sensitive_accident,\n"
            + "    t.is_major_accident,\n"
            + "    unicode_to_string(t.affected_bussiness_display) AS affected_bussiness_display,\n"
            + "    t.relative_owners,\n"
            + "    t.affected_range,\n"
            + "    t.fault_category,\n"
            + "    t.affect_availability,\n"
            + "    t.data_miss_desc,\n"
            + "    t.is_regional_delivery,\n"
            + "    t.region_id,\n"
            + "    t.trade_id,\n"
            + "    t.is_breaking_prohibited_rules,\n"
            + "    t.burst_time_end,\n"
            + "    t.is_mechanism_common_problem,\n"
            + "    t.common_problem_description,\n"
            + "    t.is_change_production_happen,\n"
            + "    t.change_production_happen,\n"
            + "    t.ltc_id,\n"
            + "    t.customer_cid,\n"
            + "    t.judian_name,\n"
            + "    t.judian_id,\n"
            + "    t.project_information,\n"
            + "    t.final_customer,\n"
            + "    t.is_involve_regional_business,\n"
            + "    t.is_violate_sla,\n"
            + "    t.qa_summary,\n"
            + "    t.burst_time_start,\n"
            + "    t.breaking_standard_rules,\n"
            + "    t.industry_department,\n"
            + "    t.feedback_customer,\n"
            + "    t.is_transfer_operation,\n"
            + "    t.affected_department,\n"
            + "    unicode_to_string(t.affected_department_display) AS affected_department_display,\n"
            + "    t.qa,\n"
            + "    t.no_private_cloud_type,\n"
            + "    t.involve_responsibility_attribution,\n"
            + "    t.process_operator,\n"
            + "    t.process_operator_time,\n"
            + "    t.judian_status,\n"
            + "    t.manager_or_second_intervention_time,\n"
            + "    t.one_button_push,\n"
            + "    t.one_button_call,\n"
            + "    t.one_button_call_object,\n"
            + "    t.one_button_call_persons,\n"
            + "    t.burst_push,\n"
            + "    t.maintenance_or_research_in_time,\n"
            + "    t.backend_leader,\n"
            + "    t.process,\n"
            + "    t.duty_leader_list,\n"
            + "    t.duty_gm_list,\n"
            + "    t.is_breaking_safe_rules,\n"
            + "    t.locate_reason_time,\n"
            + "    t.regional_business_person,\n"
            + "    t.research_in_time,\n"
            + "    t2.plan_finish_time,\n"
            + "    t2.responsible,\n"
            + "    t2.validate_method,\n"
            + "    t2.validate_person,\n"
            + "    t2.improvement_status,\n"
            + "    t3.first_burst_time,\n"
            + "    t3.third_burst_time,\n"
            + "    t3.first_transfer_special_time,\n"
            + "    t3.first_transfer_operation_or_research_time,\n"
            + "    t3.burst_create_time,\n"
            + "    t3.monitor_time,\n"
            + "    t3.recover_time,\n"
            + "    t4.influence_customer_counts,\n"
            + "    t4.influence_uin_list,\n"
            + "    t4.influence_customer_name_list,\n"
            + "    t5.affected_business,\n"
            + "    t5.affected_module,\n"
            + "    t5.affected_minutes,\n"
            + "    t5.responsible_business,\n"
            + "    t5.responsible_module,\n"
            + "    t5.responsible_minutes,\n"
            + "    t5.responsible_time,\n"
            + "    t5.affected_time,\n"
            + "    t6.discovery_time,\n"
            + "    t6.discovery_way,\n"
            + "    t6.duty_delimitation,\n"
            + "    t6.duty_group_display,\n"
            + "    t6.is_break_change_rule,\n"
            + "    t6.monitor_alarm_situation,\n"
            + "    t6.is_push_customer,\n"
            + "    t6.is_monitor_confirm,\n"
            + "    t6.monitor_belong_to,\n"
            + "    t6.monitor_improve_remark,\n"
            + "    t6.break_change_rules,\n"
            + "    t6.change_duty_group,\n"
            + "    t6.change_duty_group_display,\n"
            + "    t7.first_report_time,\n"
            + "    t.l_one_hidden,\n"
            + "    get_category_name(t1.new_reason_category,t8.category_id_map) as reason_category_name,\n"
            + "    case when t1.priority in (-1,100) and t1.is_fault = 1 and t1.is_lone_problem in (-1, 0) "
            + "    then t4.all_num when t1.is_lone_problem = 1 then t4.all_num else 0 end as all_num,\n"
            + "    case when t1.priority in (-1,100) and t1.is_fault = 1 and t1.is_lone_problem in (-1, 0) "
            + "    then t4.p0_num when t1.is_lone_problem = 1 then t4.p0_num else 0 end as p0_num,\n"
            + "    case when t1.priority in (-1,100) and t1.is_fault = 1 and t1.is_lone_problem in (-1, 0) "
            + "    then t4.p1_num when t1.is_lone_problem = 1 then t4.p1_num else 0 end as p1_num,\n"
            + "    case when t1.priority in (-1,100) and t1.is_fault = 1 and t1.is_lone_problem in (-1, 0) "
            + "    then t4.p2_num when t1.is_lone_problem = 1 then  t4.p2_num else 0 end as p2_num,\n"
            + "    case when t1.priority in (-1,100) and t1.is_fault = 1 and t1.is_lone_problem in (-1, 0) "
            + "    then t4.p3_num when t1.is_lone_problem = 1 then t4.p3_num else 0 end as p3_num,\n"
            + "    case when t1.priority in (-1,100) and t1.is_fault = 1 and t1.is_lone_problem in (-1, 0) "
            + "    then t4.p4_num when t1.is_lone_problem = 1 then t4.p4_num else 0 end as p4_num,\n"
            + "    t.is_affect_multi,\n"
            + "    t.customer_impact_time,\n"
            + "    t1.service_scene_id\n"
            + "FROM iceberg_source_dwd_burst_base_info "
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ AS t\n"
            + "LEFT JOIN (select *,row_number() over(partition by problem_id order by update_time desc) as rn \n"
            + "from iceberg_source_dwd_problem_info /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/"
            + " )AS t1 ON t.problem_id = t1.problem_id and t1.rn=1\n"
            + "LEFT JOIN  (\n"
            + "  SELECT\n"
            + "    problem_id,\n"
            + "    plan_finish_time,\n"
            + "    responsible,\n"
            + "    validate_method,\n"
            + "    validate_person,\n"
            + "    status AS improvement_status,\n"
            + "    ROW_NUMBER() OVER (PARTITION BY problem_id ORDER BY update_time DESC) rn\n"
            + "  FROM iceberg_source_dwd_problem_measure /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + "  WHERE is_deleted = 0\n"
            + ") AS t2 ON t.problem_id = t2.problem_id AND t2.rn=1\n"
            + "LEFT JOIN  (\n"
            + "  SELECT\n"
            + "    problem_id,\n"
            + "    first_burst_time,\n"
            + "    third_burst_time,\n"
            + "    first_transfer_special_time,\n"
            + "    first_transfer_operation_or_research_time,\n"
            + "    burst_create_time,\n"
            + "    monitor_time,\n"
            + "    recover_time,\n"
            + "    ROW_NUMBER() OVER (PARTITION BY problem_id ORDER BY create_time DESC) rn\n"
            + "  FROM iceberg_source_dwd_burst_timeline /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + ") AS t3 ON t.problem_id = t3.problem_id AND t3.rn=1\n"
            + "LEFT JOIN  (\n"
            + "  SELECT\n"
            + "    problem_id,\n"
            + "    count(problem_id) AS influence_customer_counts,\n"
            + "    LISTAGG(customer_uin,',') AS influence_uin_list,\n"
            + "    LISTAGG(customer_name,',') AS influence_customer_name_list,\n"
            + "    count(distinct case when is_big_customer=1 and grade in (1,2,3,4,5) "
            + "    then customer_uin else null end) as all_num,\n"
            + "    count(distinct case when is_big_customer=1 and grade=1 then customer_uin else null end) as p0_num,\n"
            + "    count(distinct case when is_big_customer=1 and grade=2 then customer_uin else null end) as p1_num,\n"
            + "    count(distinct case when is_big_customer=1 and grade=3 then customer_uin else null end) as p2_num,\n"
            + "    count(distinct case when is_big_customer=1 and grade=4 then customer_uin else null end) as p3_num,\n"
            + "    count(distinct case when is_big_customer=1 and grade=5 then customer_uin else null end) as p4_num\n"
            + "  FROM iceberg_source_dwd_burst_related_customer "
            + "  /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ "
            + "  WHERE is_deleted = 0\n"
            + "  GROUP BY problem_id\n"
            + ") AS t4 ON t.problem_id = t4.problem_id\n"
            + "LEFT JOIN  (\n"
            + "  SELECT\n"
            + "    problem_id,\n"
            + "    LISTAGG(CAST(affected_business AS string),',') AS affected_business,\n"
            + "    LISTAGG(CAST(affected_module AS string),',') AS affected_module,\n"
            + "    LISTAGG(CAST(affected_minutes AS string),',') AS affected_minutes,\n"
            + "    LISTAGG(CAST(responsible_business AS string),',') AS responsible_business,\n"
            + "    LISTAGG(CAST(responsible_module AS string),',') AS responsible_module,\n"
            + "    LISTAGG(CAST(responsible_minutes AS string),',') AS responsible_minutes,\n"
            + "    LISTAGG(responsible_time,',') AS responsible_time,\n"
            + "    LISTAGG(affected_time,',') AS affected_time\n"
            + "  FROM (\n"
            + "     SELECT\n"
            + "         problem_id,\n"
            + "         affected_business,\n"
            + "         affected_module,\n"
            + "         get_min(affected_time) AS affected_minutes,\n"
            + "         responsible_business,\n"
            + "         responsible_module,\n"
            + "         get_min(responsible_time) AS responsible_minutes,\n"
            + "         responsible_time,\n"
            + "         affected_time\n"
            + "     FROM (select *,ROW_NUMBER() OVER (PARTITION BY problem_id ORDER BY update_time DESC) rn"
            + "           from iceberg_source_dwd_burst_effect_assessment "
            + "     /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + "     WHERE is_deleted = 0) as k where rn=1\n"
            + "  )\n"
            + "  GROUP BY problem_id\n"
            + ") AS t5 ON t.problem_id = t5.problem_id\n"
            + "LEFT JOIN (select *,row_number() over(partition by problem_id order by update_time desc) as rn \n"
            + "from iceberg_source_dwd_problem_extra /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/"
            + " )AS  t6 ON t.problem_id = t6.problem_id and t6.rn=1\n"
            + "LEFT JOIN (\n"
            + "        SELECT\n"
            + "            problem_id,\n"
            + "            happen_time AS first_report_time,\n"
            + "            ROW_NUMBER() OVER (PARTITION BY problem_id ORDER BY happen_time DESC) rn\n"
            + "        FROM iceberg_source_dwd_burst_event_log "
            + "        /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + ") AS t7 ON t.problem_id = t7.problem_id AND t7.rn=1\n"
            + "left join (\n"
            + "          select LISTAGG(CONCAT(CAST(reason_category_id AS string),'|',"
            + "                  reason_category_name),',') as category_id_map from iceberg_sink_dim_reason_category "
            + "        /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n"
            + ") as t8 on 1=1\n";

    public static String DwmProblemOperationStatisticSql = ""
            + "SELECT\n"
            + "  t.problem_id,\n"
            + "  case when t4.json_list is not null then get_problem_first_time(t4.json_list,"
            + "  cast(t.problem_delivery_mode as string)) else '' end AS first_time_json,\n"
            + "  case when t4.json_list is not null then get_problem_duration(t4.json_list,"
            + "  concat(cast(t.problem_delivery_mode as string),'|',b.holiday_day_list)) "
            + "  else '' end  AS problem_duration_json,\n"
            + "  case when t4.json_list is not null then get_problem_reserse_duration(t4.json_list, b.holiday_day_list)"
            + "  else '' end AS problem_reserse_duration_json,\n"
            + "  case when t4.json_list is not null then get_problem_deal_num(t4.json_list) "
            + "  else '' end AS problem_deal_num_json\n"
            + "FROM (select *,row_number() over(partition by problem_id order by update_time desc) as rn "
            + "      from iceberg_source_dwd_problem_info /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL') */) as t \n"
            + "LEFT JOIN (\n"
            + "    SELECT problem_id, \n"
            + "    concat('[',LISTAGG(concat('{ \"operation_id\": \"', CAST(operation_id as string), "
            + "    '\", \"operation_type\": \"', CAST(operation_type as string), '\", \"field_type\": \"', "
            + "    COALESCE(CAST(field_type as string),''), '\", \"operate_time\": \"', CAST(operate_time as string),"
            + "    '\", \"operator\": \"', CAST(operator as string), '\", \"target_status\": \"', "
            + "    CAST(target_status as string), '\", \"next_operator\": \"', CAST(next_operator as string),"
            + "    '\"  }') ,','),']') as json_list \n"
            + "   from iceberg_source_dwd_problem_operation /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s',"
            + "   'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + "    GROUP BY problem_id\n"
            + ") AS t4 on t.problem_id=t4.problem_id \n"
            + "left join (select LISTAGG(cast(holiday_day AS string),',') as holiday_day_list "
            + "from iceberg_source_dim_holiday_days /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/"
            + ") as b on 1=1\n"
            + "where t.rn=1\n";

    public static String DwmProblemBaseStatisticSql = ""
            + "SELECT\n"
            + "    now() AS record_update_time,\n"
            + "    CAST(t.problem_id AS BIGINT) AS value_of_primary_key,\n"
            + "    CAST(t.problem_id AS BIGINT) AS problem_id,\n"
            + "    t.title,\n"
            + "    REGEXP_REPLACE(t.description, '[\\n\\t]', '') as description,\n"
            + "    case when t.service_scene_display like '%}]' then REGEXP_REPLACE(get_scene_name(t.service_scene_display,0), '[\\n\\t]', '') "
            + "    else '' end as service_scene_display,\n"
            + "    case when t.service_scene_display like '%}]' then REGEXP_REPLACE(get_scene_name(t.service_scene_display,1), '[\\n\\t]', '') "
            + "    else '' end as service_scene_level1_name,\n"
            + "    case when t.service_scene_display like '%}]' then REGEXP_REPLACE(get_scene_name(t.service_scene_display,2), '[\\n\\t]', '') "
            + "    else '' end as service_scene_level2_name,\n"
            + "    case when t.service_scene_display like '%}]' then REGEXP_REPLACE(get_scene_name(t.service_scene_display,3), '[\\n\\t]', '') "
            + "    else '' end as service_scene_display3_name,\n"
            + "    REGEXP_REPLACE(t.reason, '[\\n\\t]', '') as reason,\n"
            + "    t.new_reason_category AS reason_category,\n"
            + "    t.is_known_problem,\n"
            + "    t.responsibility_attribution,\n"
            + "    t.temporary_solution,\n"
            + "    t.status,\n"
            + "    t.creator,\n"
            + "    t.current_operator,\n"
            + "    t.manager,\n"
            + "    t.responsible,\n"
            + "    t.create_time,\n"
            + "    t.first_should_assign,\n"
            + "    t.first_fact_assign,\n"
            + "    t.should_assign,\n"
            + "    t.fact_assign,\n"
            + "    t.priority,\n"
            + "    t.is_priority_modified,\n"
            + "    t.priority_score,\n"
            + "    t.problem_delivery_mode,\n"
            + "    t.project_stage,\n"
            + "    t.reason_category_supplement,\n"
            + "    t.product_short_name,\n"
            + "    t.child_product_name,\n"
            + "    t.discover_version,\n"
            + "    t.history_burst_operator,\n"
            + "    t.is_tech_plan_wrong,\n"
            + "    t.is_no_test_related,\n"
            + "    t.delete_person,\n"
            + "    t.problem_change_production_happen,\n"
            + "    t.problem_is_change_production_happen,\n"
            + "    t.is_fault,\n"
            + "    t.is_lone_problem,\n"
            + "    t.no_private_cloud_type,\n"
            + "    t.is_affect_public_cloud,\n"
            + "    t.is_affect_private_cloud,\n"
            + "    t.is_public_cloud_problem_solved,\n"
            + "    t.is_private_cloud_problem_solved,\n"
            + "    t.problem_change_production_href,\n"
            + "    t1.measures_amount,\n"
            + "    t1.measures_finished_on_time,\n"
            + "    t1.measures_finished_not_on_time,\n"
            + "    t1.measures_finished_plan_time,\n"
            + "    t1.measures_improve_plan_time,\n"
            + "    COALESCE(t1.measure_day_num,0) as measure_day_num,\n"
            + "    t2.related_ticket_id,\n"
            + "    t2.related_ticket_id_amount,\n"
            + "    t3.monitor_alarm_situation,\n"
            + "    t3.discovery_way,\n"
            + "    t3.is_push_customer,\n"
            + "    t3.is_introduce_without_test,\n"
            + "    t3.is_leak_test,\n"
            + "    t3.level_of_leak_test,\n"
            + "    t3.category_of_leak_test,\n"
            + "    t3.reason_of_leak_test,\n"
            + "    t3.category_of_defect,\n"
            + "    t3.tester,\n"
            + "    t3.tester_comment,\n"
            + "    t3.is_monitor_confirm,\n"
            + "    t3.monitor_belong_to,\n"
            + "    t3.monitor_improve_remark,\n"
            + "    t3.duty_group,\n"
            + "    t3.duty_group_display,\n"
            + "    t3.change_duty_group,\n"
            + "    t3.change_duty_group_display,\n"
            + "    t3.is_preview_env,\n"
            + "    t3.is_customer_disaster_recovery,\n"
            + "    t3.is_consultant_identify_dangers,\n"
            + "    t3.break_change_rules,\n"
            + "    t3.breaking_standard_rules,\n"
            + "    t3.is_break_change_rule,\n"
            + "    t3.is_breaking_prohibited_rules,\n"
            + "  case when t4.first_time_json is not null and t4.first_time_json<>'' then "
            + "  get_json_object(t4.first_time_json, '$.first_processing_time') "
            + "  else '1970-01-01 00:00:00' end AS first_processing_time,\n"
            + "  case when t4.first_time_json is not null and t4.first_time_json<>'' then "
            + "  get_json_object(t4.first_time_json, '$.first_approve_time') "
            + "  else '1970-01-01 00:00:00' end AS first_approve_time,\n"
            + "  case when t4.first_time_json is not null and t4.first_time_json<>'' then "
            + "  get_json_object(t4.first_time_json, '$.first_confirm_close_time') "
            + "  else '1970-01-01 00:00:00' end AS first_confirm_close_time,\n"
            + "  case when t4.first_time_json is not null and t4.first_time_json<>'' then "
            + "  get_json_object(t4.first_time_json, '$.close_time') "
            + "  else '1970-01-01 00:00:00' end AS close_time,\n"
            + "  case when t4.first_time_json is not null and t4.first_time_json<>'' then "
            + "  get_json_object(t4.first_time_json, '$.first_deal_time') "
            + "  else '1970-01-01 00:00:00' end AS first_deal_time,\n"
            + "  COALESCE(get_json_object(t4.problem_duration_json,"
            + "  '$.first_approver'),'') AS first_approver,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_duration_json,"
            + "  '$.processing_duration') as bigint),0) AS processing_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_duration_json,"
            + "  '$.resolved_duration') as bigint),0) AS resolved_duration,\n"
            + "  COALESCE(get_json_object(t4.problem_duration_json,"
            + "  '$.second_third_line_handler'),'') AS second_third_line_handler,\n"
            + "  case when t4.first_time_json is not null and t4.first_time_json<>'' then "
            + "  get_json_object(t4.first_time_json, '$.first_improve_time') "
            + "  else '1970-01-01 00:00:00' end AS first_improve_time,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.check_duration') as int),0) AS check_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.deny_duration') as int),0) AS deny_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.handle_duration') as int),0) AS handle_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.second_third_line_deny_duration') as int),0) AS second_third_line_deny_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.manager_confirm_close_duration') as int),0) AS manager_confirm_close_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.manager_confirm_deny_duration') as int),0) AS manager_confirm_deny_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.problem_deal_duration') as int),0) AS problem_deal_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.is_approved_by_leader') as int),0) AS is_approved_by_leader,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.approved_duration_by_leader') as int),0) AS approved_duration_by_leader,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.rejected_duration_by_leader') as int),0) AS rejected_duration_by_leader,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.manager_duration') as int),0) AS manager_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.operator_duration') as int),0) AS operator_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_reserse_duration_json,"
            + "  '$.processing_solved_duration') as int),0) AS processing_solved_duration,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_deal_num_json,"
            + "  '$.manager_deny_num') as int),0) AS manager_deny_num,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_deal_num_json,"
            + "  '$.second_third_line_deny_num') as int),0) AS second_third_line_deny_num,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_deal_num_json,"
            + "  '$.manager_deny_close_num') as int),0) AS manager_deny_close_num,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_deal_num_json,"
            + "  '$.fact_reopen_num') as int),0) AS fact_reopen_num,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_deal_num_json,"
            + "  '$.reopen_num') as int),0) AS reopen_num,\n"
            + "  COALESCE(get_json_object(t4.problem_deal_num_json,"
            + "  '$.final_handler'),'') AS final_handler,\n"
            + "  COALESCE(get_json_object(t4.problem_deal_num_json,"
            + "  '$.final_approver'),'') AS final_approver,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_deal_num_json,"
            + "  '$.is_manager_check') as int),0) AS is_manager_check,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_deal_num_json,"
            + "  '$.is_second_third_line_handle') as int),0) AS is_second_third_line_handle,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_deal_num_json,"
            + "  '$.is_manager_confirm_situation') as int),0) AS is_manager_confirm_situation,\n"
            + "  COALESCE(cast(get_json_object(t4.problem_duration_json,"
            + "  '$.improve_duration') as bigint),0) AS improve_duration,\n"
            + "  get_category_name(t.new_reason_category,t5.category_id_map) as reason_category_name,\n"
            + "  t.update_time as problem_update_time,\n"
            + "  case when t4.first_time_json is not null and t4.first_time_json<>'' then "
            + "  get_json_object(t4.first_time_json, '$.first_inflow_approving_time') "
            + "  else '1970-01-01 00:00:00' end AS first_inflow_approving_time,\n"
            + "  COALESCE(get_json_object(t4.problem_duration_json,"
            + "  '$.processing_max_duration_operator'),'') AS processing_max_duration_operator,\n"
            + "  t.service_scene_id,\n"
            + "  t.problem_stage\n"
            + "FROM (select *,row_number() over(partition by problem_id order by update_time desc) as rn "
            + "      from iceberg_source_dwd_problem_info /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + ", 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL') */) as t \n"
            + "LEFT JOIN (\n"
            + "    SELECT \n"
            + "        problem_id,\n"
            + "        COUNT(*) AS measures_amount,\n"
            + "        SUM(CASE WHEN status = 2 AND CAST(actual_finish_time as string) > '1970-01-01 08:00:00' "
            + "        AND CAST(plan_finish_time as string) > '1970-01-01 08:00:00' "
            + "        AND actual_finish_time <= plan_finish_time THEN 1 ELSE 0 END) AS measures_finished_on_time,\n"
            + "        SUM(CASE WHEN status = 2 AND CAST(actual_finish_time as string) > '1970-01-01 08:00:00' "
            + "        AND CAST(plan_finish_time as string) > '1970-01-01 08:00:00' "
            + "        AND actual_finish_time > plan_finish_time THEN 1 ELSE 0 END) AS measures_finished_not_on_time,\n"
            + "        MAX(CASE WHEN status = 1 THEN plan_finish_time "
            + "        ELSE cast('1970-01-01 00:00:00' as timestamp) END) AS measures_improve_plan_time,\n"
            + "        MAX(CASE WHEN status = 2 THEN plan_finish_time "
            + "        ELSE cast('1970-01-01 00:00:00' as timestamp) END) AS measures_finished_plan_time,\n"
            + "        SUM(case when plan_finish_time>=create_time then get_duration_time(CAST(create_time as string),"
            + "        CAST(plan_finish_time as string),'') else 0 end)/86400 as measure_day_num\n"
            + "    FROM (select *,row_number() over(partition by measure_id order by update_time desc) as rn "
            + "         from iceberg_source_dwd_problem_measure /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s'"
            + " , 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/) as k\n"
            + "    WHERE is_deleted = 0 and k.rn=1\n"
            + "    GROUP BY problem_id\n"
            + ") AS t1 on t.problem_id=t1.problem_id \n"
            + "LEFT JOIN (\n"
            + "    SELECT\n"
            + "            problem_id,\n"
            + "            COUNT(relation_id) AS related_ticket_id_amount,\n"
            + "            LISTAGG(CAST(ticket_id AS string),',') AS related_ticket_id\n"
            + "    FROM iceberg_source_dwd_problem_related_ticket /*+ OPTIONS('streaming'='true',"
            + " 'monitor-interval'='1s', 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + "    WHERE is_deleted = 0\n"
            + "    GROUP BY problem_id\n"
            + ") AS t2 ON t.problem_id = t2.problem_id\n"
            + "LEFT JOIN (select *,row_number() over(partition by problem_id order by update_time desc) as rn \n"
            + "from iceberg_source_dwd_problem_extra /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/"
            + " )AS  t3 ON t.problem_id = t3.problem_id and t3.rn=1\n"
            + "LEFT JOIN dwm_problem_operation_view AS t4 on t.problem_id=t4.problem_id \n"
            + "left join (\n"
            + "          select LISTAGG(CONCAT(CAST(reason_category_id AS string),'|',"
            + "                  reason_category_name),',') as category_id_map from iceberg_sink_dim_reason_category "
            + "        /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s')*/\n"
            + ") as t5 on 1=1\n"
            + "where t.rn=1\n";
}
