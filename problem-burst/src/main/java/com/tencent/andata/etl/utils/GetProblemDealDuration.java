package com.tencent.andata.etl.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.utils.WorkDurationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetProblemDealDuration extends ScalarFunction {
    /**
     * 计算问题单处理时长
     * @param operationJson 操作流水 json
     * @param param 返回参数列名
     * @return 计算结果
     * @throws Exception 异常
     */
    public  String eval(String operationJson, String param) throws Exception {
        if (StringUtils.isBlank(operationJson) || StringUtils.isBlank(param)) {
            return "";
        }
        int processingDuration = 0; // 处理时长
        int resolvedDuration = 0; // 问题解决时长
        int improveDuration = 0; // 改进时长
        String firstApprover = ""; // 首次审批人
        String secondThirdLineHandler = ""; // 二三线处理人
        String processingMaxDurationOperator = ""; //处理中最长处理人
        // 辅助字段
        boolean processingDurationStartFlag = false;
        String firstApproveTime = "1970-01-01 00:00:00";
        String firstProcessingTime = "1970-01-01 00:00:00"; // 问题单首次进入处理中时间
        String firstConfirmCloseTime = "1970-01-01 00:00:00"; // 问题单首次进入待确认结单时间
        String firstDealTime = "1970-01-01 00:00:00"; // 问题单首次处理完成时间
        String firstImproveTime = "1970-01-01 00:00:00"; // 问题单首次进入改进中时间
        String processingStartTime = "";
        String processingEndTime = "";
        String resolvedStartTime = "";
        String resolvedEndTime = "";
        String problemDeliveryMode = "";
        String holidayList = "";
        String tmpProcessingMaxDurationTime1 = "";
        String tmpProcessingMaxDurationTime2 = "";
        Map<String, Double> processingMaxDurationDict = new HashMap<>();

        try {
            String[] paramList = param.split("\\|");
            problemDeliveryMode = paramList[0];
            holidayList = paramList[1];
        } catch (Exception e) {
            return "0";
        }

        GetOperationList opList = new GetOperationList();
        List<Map<String, Object>> operationList = opList.operationJsonToList(operationJson);
        for (Map<String, Object> map : operationList) {
            String operator = map.get("operator").toString();
            String targetStatus = map.get("target_status").toString();
            String operationType = map.get("operation_type").toString(); // 操作类型
            String operateTime = map.get("operate_time").toString(); // 操作时间
            String nextOperator = map.get("next_operator").toString(); // 下次操作人

            if (operationType.equals("17") && firstProcessingTime.equals("1970-01-01 00:00:00")) {
                firstProcessingTime = operateTime; // 问题单首次进入处理中时间
            } else if (operationType.equals("46") && firstApproveTime.equals("1970-01-01 00:00:00")) {
                firstApproveTime = operateTime; // 问题单首次进入改进中时间
                firstApprover = operator;
            } else if (operationType.equals("13") && firstConfirmCloseTime.equals("1970-01-01 00:00:00")) {
                firstConfirmCloseTime = operateTime; // 问题单首次进入待确认结单时间
            } else if (operationType.equals("20") && firstDealTime.equals("1970-01-01 00:00:00")) {
                firstDealTime = operateTime; // 问题单首次处理完成时间
            }
            // 处理时长计算逻辑
            if (targetStatus.equals("3") && StringUtils.isEmpty(processingStartTime) && !processingDurationStartFlag) {
                processingStartTime = operateTime;
                processingDurationStartFlag = true;
            }

            String fieldType = map.get("field_type").toString(); // 操作字段类型

            if (StringUtils.isEmpty(tmpProcessingMaxDurationTime1)
                    && processingDurationStartFlag && fieldType.equals("1")) {
                tmpProcessingMaxDurationTime1 = operateTime;
            }
            else if (StringUtils.isEmpty(tmpProcessingMaxDurationTime2)
                    && processingDurationStartFlag && fieldType.equals("1")) {
                tmpProcessingMaxDurationTime2 = operateTime;
                if (!operator.equals("SYSTEM") && StringUtils.isNotEmpty(tmpProcessingMaxDurationTime1)
                        && StringUtils.isNotEmpty(tmpProcessingMaxDurationTime2)) {
                    double workDuration = WorkDurationUtils.getWorkDuration(
                            tmpProcessingMaxDurationTime1, tmpProcessingMaxDurationTime2, holidayList
                    );
                    workDuration = workDuration < 0 ? 0 : workDuration;
                    if (processingMaxDurationDict.containsKey(operator)) {
                        processingMaxDurationDict.put(operator, processingMaxDurationDict.get(operator) + workDuration);
                    } else {
                        processingMaxDurationDict.put(operator, workDuration);
                    }

                    tmpProcessingMaxDurationTime1 = tmpProcessingMaxDurationTime2;
                    tmpProcessingMaxDurationTime2 = "";

                }
            }

            if (processingDurationStartFlag && ((operationType.equals("20") && targetStatus.equals("12"))
                    || (operationType.equals("21") && targetStatus.equals("2"))
                    || (operationType.equals("20") && targetStatus.equals("4")))) {
                processingEndTime = operateTime;
                processingDurationStartFlag = false;
            }

            if (StringUtils.isNotEmpty(processingStartTime) && StringUtils.isNotEmpty(processingEndTime)) {
                double workDuration = WorkDurationUtils.getWorkDuration(
                        processingStartTime, processingEndTime, holidayList
                );
                processingDuration += (int) workDuration;
                processingStartTime = "";
                processingEndTime = "";
            }
            // 问题解决时长
            if (targetStatus.equals("3") && StringUtils.isEmpty(resolvedStartTime)) {
                resolvedStartTime = operateTime;
            }

            if (targetStatus.equals("5")) {
                resolvedEndTime = operateTime;
            }

            if (StringUtils.isNotEmpty(resolvedStartTime) && StringUtils.isNotEmpty(resolvedEndTime)) {
                double workDuration = WorkDurationUtils.getWorkDuration(
                        resolvedStartTime, resolvedEndTime, holidayList
                );
                resolvedDuration = (int) workDuration;
            }
            // 二三线处理人
            if (operationType.equals("20")) {
                secondThirdLineHandler = operator;
            } else if (targetStatus.equals("3")) {
                secondThirdLineHandler = nextOperator;
            }
        }
        if (!firstApproveTime.equals("1970-01-01 00:00:00")) {
            firstImproveTime = firstApproveTime;
        } else if (!firstDealTime.equals("1970-01-01 00:00:00")
                && (firstDealTime.compareTo("2021-07-08 16:05:00") <= 0 || !problemDeliveryMode.equals("1"))) {
            firstImproveTime = firstDealTime;
        }

        if (!firstImproveTime.equals("1970-01-01 00:00:00") && !firstConfirmCloseTime.equals("1970-01-01 00:00:00")) {
            double workDuration = WorkDurationUtils.getWorkDuration(
                    firstImproveTime, firstConfirmCloseTime, ""
            );
            improveDuration += (int) workDuration;
        }

        if (processingMaxDurationDict.size() > 0) {
            processingMaxDurationOperator = Collections.max(
                    processingMaxDurationDict.entrySet(), Map.Entry.comparingByValue()).getKey();
        }
        // 创建一个Map来存储返回值
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("first_approver", firstApprover);
        resultMap.put("processing_duration", String.valueOf(processingDuration));
        resultMap.put("resolved_duration", String.valueOf(resolvedDuration));
        resultMap.put("second_third_line_handler", secondThirdLineHandler);
        resultMap.put("improve_duration", String.valueOf(improveDuration));
        resultMap.put("processing_max_duration_operator", processingMaxDurationOperator);
        // 创建ObjectMapper对象
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(resultMap);
    }
}
