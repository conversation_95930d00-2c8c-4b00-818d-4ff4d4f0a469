package com.tencent.andata.etl.dwm;

import com.tencent.andata.etl.sql.S360ProblemBaseStatisticSql;
import com.tencent.andata.etl.tablemap.S360ProblemBaseStatisticMapping;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import java.util.Properties;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

@Builder
public class S360ProblemBaseStatistic {

    private final String icebergDbName;
    private final String pgDbName;


    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        // 出库PG表注册
        DatabaseConf dataWareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(S360ProblemBaseStatisticMapping.icebergTable2FlinkTable,
                        ArrayNode.class), tEnv, catalog
        );

        // pgTable mapping to flinkTable
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(S360ProblemBaseStatisticMapping.PG_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                PGSQL,
                tEnv
        );

        // create temporary view for source table
        flinkEnv.streamTEnv().createTemporaryView("s360_problem_base_view",
                flinkEnv.streamTEnv().sqlQuery(S360ProblemBaseStatisticSql.S360ProblemBaseStatisticSql));

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(
                insertIntoSql(
                        "s360_problem_base_view",
                        "iceberg_sink_dwm_s360_problem_base_rt",
                        tEnv.from("iceberg_sink_dwm_s360_problem_base_rt"),
                        ICEBERG
                ))
                .addInsertSql(insertIntoSql(
                "s360_problem_base_view",
                "pg_dwm_s360_problem_base_rt",
                tEnv.from("pg_dwm_s360_problem_base_rt"),
                PGSQL
                ));
    }
}
