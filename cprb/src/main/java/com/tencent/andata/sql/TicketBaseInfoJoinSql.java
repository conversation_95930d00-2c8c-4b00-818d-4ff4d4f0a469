package com.tencent.andata.sql;

import com.tencent.andata.filter.TimeFilter;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;

import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.table.types.DataType;
import org.apache.flink.types.Row;
import org.apache.iceberg.flink.FlinkSchemaUtil;

import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.stream.Collectors;

import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

public class TicketBaseInfoJoinSql {

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public long startTime;
    public String startTimeStr;

    public TicketBaseInfoJoinSql(long startTime) {
        this.startTime = startTime;
        this.startTimeStr = sdf.format(startTime);
    }

    /***
     * replenishTicketBaseInfo .
     * @param tEnv .
     * @param groupStream .
     * @return .
     */
    public DataStream<Row> replenishTicketBaseInfo(StreamTableEnvironment tEnv, DataStream<Row> groupStream) {
        // 注册工单基础表
        Table ticketBaseInfo = tEnv.sqlQuery(String.format(CREATE_TICKET_BASE_INFO_SQL, startTimeStr));
        tEnv.createTemporaryView("dim_ticket_base_info", tEnv.fromDataStream(tEnv.toDataStream(ticketBaseInfo)
                .filter(new TimeFilter("create_time", startTime, startTime, startTime))));
        // 聚合流转表
        Table groupTable = tEnv.fromChangelogStream(
                groupStream,
                Schema.newBuilder()
                        .columnByExpression("process_time", "PROCTIME()")
                        .build(), ChangelogMode.insertOnly()
        );
        tEnv.createTemporaryView("group_flow_data", groupTable);
        // interval join
        Table table = tEnv.sqlQuery(INTERVAL_JOIN_SQL);
        // 表转流
        return tEnv.toChangelogStream(table, Schema.newBuilder()
                .primaryKey("id")
                .build(), ChangelogMode.insertOnly());
    }


    /****
     * richDataByLookupJoinPG .
     * @param tEnv .
     * @param groupStream .
     * @param rainbowUtils .
     * @return .
     * @throws Exception .
     */
    public DataStream<Row> richDataByLookupJoinPG(
            StreamTableEnvironment tEnv,
            DataStream<Row> groupStream,
            RainbowUtils rainbowUtils
    ) throws Exception {
        return this.richDataByLookupJoinPG(tEnv, groupStream, rainbowUtils, "cdc.database.pgsql.dataware_r");
    }

    /***
     * richDataByLookupJoinPG .
     * @param tEnv .
     * @param groupStream .
     * @param rainbowUtils .
     * @param groupName .
     * @return .
     * @throws Exception .
     */
    public DataStream<Row> richDataByLookupJoinPG(
            StreamTableEnvironment tEnv,
            DataStream<Row> groupStream,
            RainbowUtils rainbowUtils,
            String groupName
    ) throws Exception {
        // PG 表注册
        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);
        DatabaseConf readPgDb = kvConfBuilder
                // 只读库
                .setGroupName(groupName)
                .build();
        String flinkTableName = "f_dwm_ticket_statistic";
        tEnv.executeSql(
                String.format(
                        LOOKUP_JOIN_TABLE_DDL,
                        flinkTableName,
                        readPgDb.password,
                        "dwm_ticket_statistic",
                        readPgDb.dbHost,
                        readPgDb.dbPort,
                        readPgDb.dbName,
                        readPgDb.userName
                )
        );
        // 聚合流转表
        Table groupTable = tEnv.fromChangelogStream(
                groupStream,
                Schema.newBuilder()
                        .columnByExpression("process_time", "PROCTIME()")
                        .build(), ChangelogMode.insertOnly()
        );
        tEnv.createTemporaryView("group_flow_data", groupTable);
        List<DataType> columnDataTypes = groupTable.getResolvedSchema().getColumnDataTypes();
        List<String> columnNames = groupTable.getResolvedSchema().getColumnNames();
        for (int i = 0; i < columnNames.size(); i++) {
            System.out.println("groupTable :" + columnNames.get(i) + " type: " + columnDataTypes.get(i).toString());
        }
        // lookup join
        Table table = tEnv.sqlQuery(String.format(LOOKUP_JOIN_SQL, flinkTableName));
        DataStream<Row> stream = tEnv.toDataStream(table);

        return stream.map(new MapFunction<Row, Row>() {
            private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            private final SimpleDateFormat sdfUtc = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss'Z'");

            @Override
            public Row map(Row row) throws Exception {
                // 这里要处理pg时间格式不统一问题
                Object createTime = row.getField("t2_create_time");
                if (createTime != null) {
                    long epochSecond = ((LocalDateTime) createTime).toEpochSecond(ZoneOffset.of("+8"));
                    row.setField("ticket_create_time", Instant.ofEpochSecond(epochSecond));
                } else {
                    row.setField("ticket_create_time", Instant.ofEpochSecond(0));
                }
                Object updateTime = row.getField("t2_update_time");
                if (updateTime != null) {
                    long epochSecond = ((LocalDateTime) updateTime).toEpochSecond(ZoneOffset.of("+8"));
                    row.setField("update_time", Instant.ofEpochSecond(epochSecond));
                } else {
                    row.setField("update_time", Instant.ofEpochSecond(0));
                }
                return row;
            }
        }).returns(stream.getType());
    }


    private static final String LOOKUP_JOIN_TABLE_DDL = ""
            + "create table %s (\n"
            + "     status string,\n"
            + "     ticket_id bigint,\n"
            + "     url string,\n"
            + "     short_url string,\n"
            + "     title string,\n"
            + "     question string,\n"
            + "     service_scene_checked int,\n"
            + "     service_scene int,\n"
            + "     create_time timestamp(6),\n"
            + "     record_update_time timestamp(6)\n"
            + ") with (\n"
            + "'connector'='jdbc',\n"
            + "'lookup.cache.max-rows'='1000',\n"
            + "'lookup.cache.ttl'='1h',\n"
            + "'password'='%s',\n"
            + "'table-name'='%s',\n"
            + "'url'='***************************************************************************&"
            + "serverTimezone=Asia/Shanghai',\n"
            + "'username'='%s'"
            + ")";

    private static final String LOOKUP_JOIN_SQL = ""
            + "select\n"
            + "    t1.id,\n"
            + "    t1.ticket_id,\n"
            + "    t1.reply,\n"
            + "    t1.c_reply,\n"
            + "    t1.msg,\n"
            + "    t1.source,\n"
            + "    t2.short_url as url,\n"
            + "    CASE t2.title WHEN '' THEN t2.question ELSE t2.title END AS question,\n"
            + "    if(t2.service_scene_checked > 0, t2.service_scene_checked, t2.service_scene) as service_scene,\n"
            + "    t2.create_time as t2_create_time,\n"
            + "    t2.record_update_time as t2_update_time,\n"
            + "    t1.close_time as ticket_create_time,\n"
            + "    t1.close_time as update_time,\n"
            + "    t1.close_time,\n"
            + "    t2.status as status\n"
            + "from group_flow_data as t1\n"
            + "LEFT JOIN %s FOR SYSTEM_TIME AS OF t1.`process_time` AS t2 "
            + "on t1.ticket_id = t2.ticket_id"
            + "";


    // iceberg interval join
    private static final String INTERVAL_JOIN_SQL = ""
            + "select\n"
            + "    t1.id,\n"
            + "    CAST(t1.ticket_id as BIGINT) as ticket_id,\n"
            + "    t1.reply,\n"
            + "    t1.c_reply,\n"
            + "    t1.msg,\n"
            + "    t1.source,\n"
            + "    t2.url,\n"
            + "    CASE t2.title WHEN '' THEN t2.question ELSE t2.title END AS question,\n"
            + "    if(t2.service_scene_checked > 0, t2.service_scene_checked, t2.service_scene) as service_scene,\n"
            + "    t2.create_time as ticket_create_time,\n"
            + "    t2.update_time,\n"
            + "    t1.close_time\n"
            + "from group_flow_data t1\n"
            + "inner join dim_ticket_base_info t2\n"
            + "on CAST(t1.ticket_id as BIGINT) = t2.ticket_id"
            + "";


    private static final String CREATE_TICKET_BASE_INFO_SQL = ""
            + "SELECT \n"
            + "    ticket_id,\n"
            + "    url,\n"
            + "    title,\n"
            + "    question,\n"
            + "    service_scene_checked,\n"
            + "    service_scene,\n"
            + "    create_time,\n"
            + "    status,\n"
            + "    create_time,\n"
            + "    update_time,\n"
            + "    process_time\n"
            + "FROM iceberg_incident_ticket_base_info\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s',"
            + " 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + "where (create_time > '%s')\n"
            + "";

}
