package com.tencent.andata.sql;

public class CprbInProgressSql {
    public static String IN_PROGRESS_TICKET_DATA_SQL = "select \n"
            + " ticket_id,\n"
            + " reply,\n"
            + " msg,\n"
            + " c_reply,\n"
            + "     case\n"
            + "         when source = 'ticket_extern' then 'ticket_external_reply'\n"
            + "         when source = 'ticket_inner' then 'ticket_internal_reply'\n"
            + "         when source like '%%webim%%' then 'webim'\n"
            + "         else source\n"
            + "     end as source_type,\n"
            + " url,\n"
            + " question,\n"
            + " cast(service_scene as int) as service_scene,\n"
            + " cast(close_time as timestamp) as close_time,\n"
            + " DATE_FORMAT(cast(ticket_create_time as timestamp), 'yyyy-MM-dd HH:mm:ss') as ticket_create_time,\n"
            + " DATE_FORMAT(cast(update_time as timestamp), 'yyyy-MM-dd HH:mm:ss') as update_time,\n"
            + " service_scene_level1_id,\n"
            + " service_scene_level2_id,\n"
            + " service_scene_level3_id,\n"
            + " service_scene_level4_id,\n"
            + " proc_time\n"
            + "from %s";

    public static String PUSH_DATA_ADD_PROC_TIME_SQL = "create view %s as(\n"
            + "select\n"
            + " *,\n"
            + " PROCTIME() as proc_time\n"
            + "from %s\n"
            + "%s\n"
            + ")";

    public static String RICH_DATA_SQL = "create view %s as(\n"
            + "  with t as (\n"
            + "    select\n"
            + "         t1.ticket_id,\n"
            + "         t2.name,\n"
            + "         t3.service_scene_level1_id,\n"
            + "         t3.service_scene_level1_name,\n"
            + "         '' as push_url\n"
            + "     from %s t1 \n"
            + "     left join %s FOR SYSTEM_TIME AS OF t1.proc_time as t2\n"
            + "     on t1.tag_id = t2.id\n"
            + "     left join %s FOR SYSTEM_TIME AS OF t1.proc_time as t3\n"
            + "     on t1.service_scene = t3.dim_id\n"
            + " )\n"
            + " select * from t\n"
            + ")";

    public static String YUNGUWEN_INSERT_KAFKA_SQL = "insert into %s \n"
            + "select "
            + " ticket_id as TicketId,\n"
            + " name as TagName,\n"
            + " push_url as Ticket360Url\n"
            + "from %s";

    public static String INTERNAL_INSERT_KAFKA_SQL = "insert into %s \n"
            + "select "
            + " ticket_id as ticket_id,\n"
            + " cast(tag_id as bigint) as tag_id,\n"
            + " service_scene as service_scene\n"
            + "from %s";


    /**
     * 云顾问过滤没有产生过共性的工单
     */
    public static String YUNGUWEN_PUSH_DATA_FILTER_SQL = "create view %s as(\n"
            + "  with t as (\n"
            + "  select\n"
            + "   t1.*,\n"
            + "   row_number() over(partition by t1.ticket_id order by t1.proc_time) as rn\n"
            + "  from %s t1\n"
            + "  left join %s FOR SYSTEM_TIME AS OF t1.proc_time as t2\n"
            + "  on t1.ticket_id = t2.ticket_id and t2.prompt_result = '是'\n"
            + "  where t2.ticket_id is null\n"
            + "  )\n"
            + "  select * from t where rn = 1\n"
            + ")";

    /**
     * 内部标签群告警过滤没有产生过共性的工单
     */
    public static String INTERNAL_PUSH_DATA_FILTER_SQL = "create view %s as(\n"
            + "  with t as (\n"
            + "  select\n"
            + "   t1.*,\n"
            + "   row_number() over(partition by t1.ticket_id,t1.tag_id order by t1.proc_time) as rn\n"
            + "  from %s t1\n"
            + "  left join %s FOR SYSTEM_TIME AS OF t1.proc_time as t2\n"
            + "  on t1.ticket_id = t2.ticket_id and t1.prompt_id = t2.prompt_id and t2.prompt_result = '是'\n"
            + "  where t2.ticket_id is null\n"
            + "  )\n"
            + "  select * from t where rn = 1\n"
            + ")";

    public static String CPRB_DATA_INSERT_SQL_SR = "insert into %s \n"
            + "select \n"
            + " ticket_id,\n"
            + " prompt_id,\n"
            + " source_type,\n"
            + " url,\n"
            + " service_scene,\n"
            + " question,\n"
            + " prompt_result,\n"
            + " reasoning,\n"
            + " TO_TIMESTAMP(ticket_create_time),\n"
            + " TO_TIMESTAMP(update_time),\n"
            + " service_scene_level1_id,\n"
            + " service_scene_level2_id,\n"
            + " service_scene_level3_id,\n"
            + " service_scene_level4_id\n"
            + "from %s";
    public static String CPRB_DATA_INSERT_SQL_PG = "insert into %s \n" // PG的列顺序没法动
            + "select \n"
            + " ticket_id,\n"
            + " prompt_id,\n"
            + " source_type,\n"
            + " url,\n"
            + " service_scene,\n"
            + " question,\n"
            + " prompt_result,\n"
            + " TO_TIMESTAMP(ticket_create_time),\n"
            + " TO_TIMESTAMP(update_time),\n"
            + " service_scene_level1_id,\n"
            + " service_scene_level2_id,\n"
            + " service_scene_level3_id,\n"
            + " service_scene_level4_id,\n"
            + " reasoning\n"
            + "from %s";
    public static String PUBLISHED_TAG_VIEW_DDL = ""
            + "create view %s as \n"
            + "select\n"
            + "     config_id,\n"
            + "     tag_id,\n"
            + "     prompt_group_id,\n"
            + "     status,\n"
            + "     create_time,\n"
            + "     update_time,\n"
            + "     start_run_time,\n"
            + "     end_run_time,\n"
            + "     forward_filter,\n"
            + "     reverse_filter,\n"
            + "     ticket_service_scene_filter,\n"
            + "     ticket_service_scene_filter,\n"
            + "     source_type,\n"
            + "     increment_type,\n"
            + "     deleted,\n"
            + "     model_type,\n"
            + "     cast(function_call_active_status as bigint)as function_call_active_status\n"
            + "from %s \n"
            // 未删除的标签
            + "where deleted = 0\n"
            // 上线状态的标签
            + "     and status = 4\n"
            // 增量状态的标签
            + "     and increment_type = 1\n"
            // 指定标签不进行处理
            + "     and config_id not in (1,4,17,12,13)\n";

}
