package com.tencent.andata.sql;

public class CprbClusterSql {

    public static final String LOOK_UP_JOIN_TABLE_PG_CREATE_SQL = "CREATE TABLE %s (\n"
            + "ticket_id bigint,\n"
            + "service_channel string,\n"
            + "service_scene_level1_id bigint,\n"
            + "service_scene_level2_id bigint,\n"
            + "service_scene_level3_id bigint,\n"
            + "service_scene_level4_id bigint,\n"
            + "ticket_close_time timestamp,\n"
            + "update_time timestamp,\n"
            + "title string,\n"
            + "ai_title string,\n"
            + "ai_topic string,\n"
            + "ai_solution string,\n"
            + "ai_description string,\n"
            + "solution string,\n"
            + "reason string,\n"
            + "description string,\n"
            + "`process_time` AS PROCTIME(),\n"
            + " PRIMARY KEY (ticket_id) NOT ENFORCED\n"
            + " )WITH (\n"
            + "  'table-name' = '%s',\n"
            + "  'password' = '%s',\n"
            + "  'connector' = 'jdbc',\n"
            + "  'url' = 'jdbc:postgresql://%s:%s/%s?"
            + "reWriteBatchedInserts=true&?currentSchema=public&serverTimezone=Asia/Shanghai&stringtype=unspecified',\n"
            + "  'username' = '%s',\n"
            + "  'lookup.cache.max-rows' = '200000',\n"
            + "  'lookup.cache.ttl' = '1h'\n"
            + ")";

    public static final String READ_CLUSTER_DATA_FROM_PG_SQL = ""
            + "SELECT \n"
            + "*,\n"
            + "cast(0 as bigint) as ticket_id_a,\n"
            + "cast(0 as bigint) as ticket_id_b,\n"
            + "cast(0 as double) as score,\n"
            + "'' as calibration_type\n"
            + "FROM pg_cprb_aggregated_cluster\n"
            + "";

    public static String REPLENISH_SQL = "select\n"
            + "t1.*,\n"
            // 归档相关信息
            + "t2.service_scene_level1_id as service_scene_level1_id,\n"
            + "t2.service_scene_level2_id as service_scene_level2_id,\n"
            // ai 生成的解决方案和问题描述
            + "t2.ai_solution as ticket_a_ai_solution,\n"
            + "t3.ai_solution as ticket_b_ai_solution,\n"
            + "t2.ai_description as ticket_a_ai_description,\n"
            + "t3.ai_description as ticket_b_ai_description,\n"
            // 摘要的数据
            + "t2.solution as ticket_a_solution,\n"
            + "t3.solution as ticket_b_solution,\n"
            + "t2.description as ticket_a_description,\n"
            + "t3.description as ticket_b_description\n"
            + "from tickets_flat_tb t1\n"
            + "left join pg_cprb_summary_embedding FOR SYSTEM_TIME AS OF t1.`process_time_ticket` AS t2 on (t1.ticket_id_a = t2.ticket_id)\n"
            + "left join pg_cprb_summary_embedding FOR SYSTEM_TIME AS OF t1.`process_time_ticket` AS t3 on (t1.ticket_id_b = t3.ticket_id)\n";

    public static String CLUSTER_INSERT_SQL = ""
            + "insert into %s (\n"
            + " title,\n"
            + " base_cluster_id,\n"
            + " status,\n"
            + " service_scene_level1_id,\n"
            + " service_scene_level2_id,\n"
            + " aggregate_type,\n"
            + " aggregate_level,\n"
            + " ticket_list\n"
            + ")\n"
            + "select * from %s";

    public static String CLUSTER_UPDATE_SQL = ""
            + "insert into %s \n"
            + "select * from %s ";

    public static String CPRB_TICKET_SINK_SQL = ""
            + "insert into %s \n"
            + "select\n"
            + " standard_cluster_id,\n"
            + " ticket_id ,\n"
            + " ticket_close_time,\n"
            + " status,\n"
            + " service_scene_level1_id ,\n"
            + " service_scene_level2_id ,\n"
            + " service_scene_level3_id ,\n"
            + " service_scene_level4_id ,\n"
            + " title,\n"
            + " ai_description,\n"
            + " ai_solution,\n"
            + " ai_title\n"
            + "from %s\n";

}
