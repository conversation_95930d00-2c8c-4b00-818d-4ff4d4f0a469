delete from cprb_standard_cluster_related_ticket;
with t1 as (
    SELECT value::text::bigint as ticket_id,id
    FROM cprb_standard_aggregated_cluster,
         LATERAL jsonb_array_elements(ticket_list) AS value
    where status = 1
), res as (
    select
        t1.id,
        t1.ticket_id,
        t2.ticket_close_time,
        1 as status,
        t2.service_scene_level1_id,
        t2.service_scene_level2_id,
        t2.service_scene_level3_id,
        t2.service_scene_level4_id,
        t2.title,
        t2.ai_description,
        t2.ai_solution,
        t2.ai_title
    from t1
    left join cprb_summary_embedding t2
    on t1.ticket_id = t2.ticket_id
)
insert into cprb_standard_cluster_related_ticket
    (
    standard_cluster_id,
    ticket_id,
    ticket_close_time,
    status,
    service_scene_level1_id,
    service_scene_level2_id,
    service_scene_level3_id,
    service_scene_level4_id,
    title,
    ai_description,
    ai_solution,
    ai_title
)
SELECT * FROM res;


备案 11597
防火墙 16571
会议 263044


update cprb_standard_aggregated_cluster set title = SUBSTRING(title FROM 2 FOR LENGTH(title) - 2);


with t1 as (
    SELECT value::text::bigint as ticket_id,base_cluster_id
    FROM cprb_base_cluster_ticket_list,
         LATERAL jsonb_array_elements(ticket_list) AS value
),res as (
    select t1.ticket_id,t1.base_cluster_id
    from t1
    left join cprb_summary_embedding t2
    on t1.ticket_id = t2.ticket_id
        where t2.service_scene_level2_id is null
)select * from res

SELECT value::text::bigint as ticket_id
FROM cprb_base_cluster_ticket_list,
     LATERAL jsonb_array_elements(ticket_list) AS value
INTERSECT
select ticket_id
from cprb_summary_embedding
where (ticket_close_time between '2023-12-01' and '2024-02-01')
  and ai_description_embedding is not null
  and service_scene_level2_id in (11597, 16571, 263044);

select count(*)
from cprb_summary_embedding
where (ticket_close_time between '2023-12-01' and '2024-02-01')
  and ai_description_embedding is not null
  and service_scene_level2_id in (11597, 16571, 263044);
