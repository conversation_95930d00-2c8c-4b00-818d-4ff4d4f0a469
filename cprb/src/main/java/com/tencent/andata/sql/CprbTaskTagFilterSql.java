package com.tencent.andata.sql;

public class CprbTaskTagFilterSql {
    public static String TICKET_DATA_TRANS_SQL =""
            + "select\n"
            + "     *,\n"
            + "     close_time as partition_time,\n"
            + "     case\n"
            + "         when source = 'ticket_extern' then 'ticket_external_reply'\n"
            + "         when source = 'ticket_inner' then 'ticket_internal_reply'\n"
            + "         when source like '%%webim%%' then 'webim'\n"
            + "         else source \n"
            + "     end as partition_source\n"
            + "from %s\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s',\n"
            + " 'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
//            + "Where close_time > '2023-01-01'\n"
            //INCREMENTAL_FROM_LATEST_SNAPSHOT
            ;
    public static String RUNNING_TASK_SQL = "SELECT "
            + "*,\n"
            + "'' as partition_source,\n"
            + "start_run_time as partition_time\n"
            + " FROM %s";
}
