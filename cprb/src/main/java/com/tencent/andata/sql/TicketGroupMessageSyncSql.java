package com.tencent.andata.sql;

public class TicketGroupMessageSyncSql {
    public static String GROUP_MESSAGE_SYNC_SQL = "select\n"
            + " *\n"
            + "from %s\n"
            // 只同步文本数据
            + "where msg_type = 'text'";

    public static String EXTERNAL1_GROUP_SYNC_SQL = "select\n"
            + " ticket_id,\n"
            + " group_id\n"
            + "from %s\n"
            // 只同步未解散的群
            + "where group_dismissed!=0";

    public static String EXTERNAL2_GROUP_SYNC_SQL = "select\n"
            + " unique_value as ticket_id,\n"
            + " group_id\n"
            + "from %s\n"
            // type = 10为 一事两群的外部群
            // 取未解散的数据
            + "where group_type = 10 and (dismiss_at!='0000-00-00 00:00:00' or dismiss_at is null)";
}
