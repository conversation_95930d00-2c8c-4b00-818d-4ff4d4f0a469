package com.tencent.andata.source;

import com.tencent.andata.utils.FlinkEnvUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.stream.Collectors;

public class CommonMqSource {

    private final String topic;
    private final List<String> columnNames;
    private final List<String> columnTypes;
    private final String groupId;
    private final List<String> extraFields;
    private final String primaryKey;

    public CommonMqSource(String topic, Table table, String groupId,
                          List<String> extraFields, String primaryKey) {
        // 定义 Kafka 表的连接器和格式
        this.columnNames = table.getResolvedSchema().getColumnNames();
        this.columnTypes = table.getResolvedSchema().getColumnDataTypes().stream().map(type -> {
            return type.getLogicalType().toString();
        }).collect(Collectors.toList());
        this.topic = topic;
        this.groupId = groupId;
        this.extraFields = extraFields;
        this.primaryKey = primaryKey;
    }

    public static final String KAFKA_SOURCE_SQL = "CREATE TABLE kafka_source_tb (\n"
            + "%s"
            + ") WITH (\n"
            + "  'connector' = 'kafka',\n"
            + "  'topic' = '%s',\n"
            + "  'properties.bootstrap.servers' = '30.181.143.184:9092',\n"
            + "  'properties.group.id' = '%s',\n"
            + "  'properties.max.request.size' = '31457280',\n"
            + "  'scan.startup.mode' = 'earliest-offset',\n"
            + "  'key.format' = 'json',\n"
            + "  'key.fields' = '%s',\n"
            + "  'value.format' = 'json',\n"
            + "  'value.json.fail-on-missing-field' = 'false'"
            + ");";

    public static final String READ_KAFKA_SQL = ""
            + "SELECT\n"
            + "%s"
            + "\nFROM kafka_source_tb";

    public String getCreateKafkaTbSql() {
        StringBuilder fieldsBuilder = new StringBuilder();
        for (int i = 0; i < this.columnNames.size(); i++) {
            String columnName = columnNames.get(i);
            String columnType = columnTypes.get(i);
            fieldsBuilder.append(columnName);
            fieldsBuilder.append(" ");
            fieldsBuilder.append(columnType);
            fieldsBuilder.append(",\n");
        }
        return String.format(KAFKA_SOURCE_SQL,
                StringUtils.stripEnd(fieldsBuilder.toString(), ",\n").replaceAll(" \\*PROCTIME\\*", ""),
                topic, groupId, primaryKey);
    }

    public String getReadKafkaTbSql() {
        StringBuilder builder = new StringBuilder();
        builder.append("*,\n");
        for (String extraField : extraFields) {
            builder.append("'' as ");
            builder.append(extraField);
            builder.append(",\n");
        }
        return String.format(READ_KAFKA_SQL,
                StringUtils.stripEnd(builder.toString(), ",\n"));
    }

    public DataStream<Row> read(FlinkEnvUtils.FlinkEnv flinkEnv) {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        String createKafkaTbSql = this.getCreateKafkaTbSql();
        System.out.println(createKafkaTbSql);
        tEnv.executeSql(createKafkaTbSql);
        Table startTable = tEnv.sqlQuery(this.getReadKafkaTbSql());
        return tEnv.toDataStream(startTable);
    }
}
