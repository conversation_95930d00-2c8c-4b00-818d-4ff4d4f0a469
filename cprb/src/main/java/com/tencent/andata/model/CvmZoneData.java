package com.tencent.andata.model;

import lombok.Builder;

import java.io.Serializable;
import java.util.Objects;

@Builder
public class CvmZoneData implements Serializable {
    public String regionType;
    public String zoneName;
    public String zone;
    public String areaName;
    public String regionName;
    public String type;
    public String typeName;

    @Override
    public String toString() {
        return "CvmZoneData{" +
                "regionType='" + regionType + '\'' +
                ", zoneName='" + zoneName + '\'' +
                ", zone='" + zone + '\'' +
                ", areaName='" + areaName + '\'' +
                ", regionName='" + regionName + '\'' +
                ", type='" + type + '\'' +
                ", typeName='" + typeName + '\'' +
                '}';
    }

    public boolean isMainZone() {
        return Objects.equals(this.typeName, "主力可用区");
    }
}
