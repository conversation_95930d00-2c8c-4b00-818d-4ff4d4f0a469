package com.tencent.andata.model.cluster;

import lombok.Builder;
import lombok.ToString;

import java.io.Serializable;
import java.sql.Timestamp;

@Builder
@ToString
public class ClusterRelatedTicket implements Serializable {
    public Long standardClusterId;
    public Long ticketId;
    public Timestamp ticketCloseTime;
    public Integer status;
    public Long serviceSceneLevel1Id;
    public Long serviceSceneLevel2Id;
    public Long serviceSceneLevel3Id;
    public Long serviceSceneLevel4Id;
    public String title;
    public String aiDesc;
    public String aiSolution;
    public String aiTitle;
}
