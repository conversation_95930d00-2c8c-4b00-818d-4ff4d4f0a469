package com.tencent.andata.model;

import org.apache.flink.types.Row;

public class TagConfig {
    public long configId;
    public long tagId;
    public long promptGroupId;
    public String forwardFilter;
    public String reverseFilter;
    public String ticketServiceSceneFilter;
    public String sourceType;
    public String modelType;
    public long functionCallActiveStatus;


    public void setConfigId(long configId) {
        this.configId = configId;
    }

    public void setTagId(long tagId) {
        this.tagId = tagId;
    }

    public void setPromptGroupId(long promptGroupId) {
        this.promptGroupId = promptGroupId;
    }

    public void setForwardFilter(String forwardFilter) {
        this.forwardFilter = forwardFilter;
    }

    public void setReverseFilter(String reverseFilter) {
        this.reverseFilter = reverseFilter;
    }

    public void setTicketServiceSceneFilter(String ticketServiceSceneFilter) {
        this.ticketServiceSceneFilter = ticketServiceSceneFilter;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public long getConfigId() {
        return configId;
    }

    public long getTagId() {
        return tagId;
    }

    public long getPromptGroupId() {
        return promptGroupId;
    }

    public String getForwardFilter() {
        return forwardFilter;
    }

    public String getReverseFilter() {
        return reverseFilter;
    }

    public String getTicketServiceSceneFilter() {
        return ticketServiceSceneFilter;
    }

    public String getSourceType() {
        return sourceType;
    }

    @Override
    public String toString() {
        return "TagConfig{"
                + "configId=" + configId
                + ", tagId=" + tagId
                + ", promptGroupId=" + promptGroupId
                + ", forwardFilter='" + forwardFilter + '\''
                + ", reverseFilter='" + reverseFilter + '\''
                + ", ticketServiceSceneFilter='" + ticketServiceSceneFilter + '\''
                + ", sourceType='" + sourceType + '\''
                + ", modelType='" + modelType + '\''
                + ", functionCallActiveStatus=" + functionCallActiveStatus
                + '}';
    }

    public static TagConfig buildFromRow(Row row) {
        TagConfig res = new TagConfig();
        res.configId = (long) (row.getField("config_id"));
        res.tagId = (long) (row.getField("tag_id"));
        res.promptGroupId = (long) (row.getField("prompt_group_id"));
        res.forwardFilter = (String) (row.getField("forward_filter"));
        res.reverseFilter = (String) (row.getField("reverse_filter"));
        res.ticketServiceSceneFilter = (String) (row.getField("ticket_service_scene_filter"));
        res.sourceType = (String) (row.getField("source_type"));
        long modelTypeInt = Long.parseLong(row.getField("model_type").toString());
        if (modelTypeInt == 4) {
            res.modelType = "gpt4";
        } else {
            res.modelType = "chatgpt";
        }
        res.functionCallActiveStatus = Long.parseLong(row.getField("function_call_active_status").toString());
        return res;
    }
}
