package com.tencent.andata.model.cluster;

import lombok.Builder;
import lombok.ToString;

import java.io.Serializable;

@Builder
@ToString
public class TicketTupleScore implements Serializable {
    public Long ticketId1;
    public Long ticketId2;
    public Long baseClusterId;
    public double score;
    public CalibrationType calibrationType;
    public int serviceSceneLevel1Id;
    public int serviceSceneLevel2Id;

}
