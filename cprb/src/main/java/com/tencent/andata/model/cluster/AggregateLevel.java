package com.tencent.andata.model.cluster;

public enum AggregateLevel {

    HIGH(3), MIDDLE(2),

    LOW(1), ELSE(0);

    public int value;

    AggregateLevel(int value) {
        this.value = value;
    }

    public static AggregateLevel of(int i) {
        switch (i) {
            case 1:
                return LOW;
            case 2:
                return MIDDLE;
            case 3:
                return HIGH;
            default:
                return ELSE;
        }
    }
}
