package com.tencent.andata.model;

import lombok.Builder;

import java.io.Serializable;
import java.util.Objects;

@Builder
public class CvmInstanceData implements Serializable {
    public String regionType;
    public String instanceType;
    public String type1;
    public String type1Name;
    public String type2;
    public String type2Name;

    @Override
    public String toString() {
        return "CvmInstanceData{" +
                "regionType='" + regionType + '\'' +
                ", instanceType='" + instanceType + '\'' +
                ", type1='" + type1 + '\'' +
                ", type1Name='" + type1Name + '\'' +
                ", type2='" + type2 + '\'' +
                ", type2Name='" + type2Name + '\'' +
                '}';
    }

    public boolean isMainInstance() {
        return Objects.equals(this.type1Name, "主力机型");
    }
}
