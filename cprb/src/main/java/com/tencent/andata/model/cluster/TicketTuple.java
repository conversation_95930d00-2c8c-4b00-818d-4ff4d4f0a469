package com.tencent.andata.model.cluster;

import lombok.Builder;
import lombok.ToString;

import java.io.Serializable;

@Builder
@ToString
public class TicketTuple implements Serializable {
    public Long ticketId1;
    public Long ticketId2;
    public Long baseClusterId;
    public int serviceSceneLevel1Id;
    public int serviceSceneLevel2Id;

    public Double extract_solution_score;
    public Double extract_desc_score;
    public Double model_solution_score;
    public Double model_desc_score;



    public boolean readyToMerge() {
        return this.extract_solution_score != null &&
                this.extract_desc_score != null &&
                this.model_solution_score != null &&
                this.model_desc_score != null;
    }
}