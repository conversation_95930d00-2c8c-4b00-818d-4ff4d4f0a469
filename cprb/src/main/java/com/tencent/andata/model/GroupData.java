package com.tencent.andata.model;

import lombok.Builder;

import java.io.Serializable;

/**
 * 群实体Model
 */
@Builder
public class GroupData implements Serializable {
    public enum GroupType {
        // 内部群，全是OA账号
        OA,
        // GTS 内部群：没有客户
        GTS_INTERNAL,
        // GTS 外部群：有客户
        GTS_EXTERNAL
    }

    // 群ID
    public String groupId;
    // 工单ID
    public long ticketId;
    // 群类型
    public GroupType groupType;

    @Override
    public String toString() {
        return "GroupData{" +
                "groupId='" + groupId + '\'' +
                ", ticketId=" + ticketId +
                ", groupType=" + groupType +
                '}';
    }
}
