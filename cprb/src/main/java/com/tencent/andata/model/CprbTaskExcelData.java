package com.tencent.andata.model;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@EqualsAndHashCode
public class CprbTaskExcelData implements Serializable {
    @ExcelProperty("ticket_id")
    private Long ticketId;
    @ExcelProperty("url")
    private String url;
    @ExcelProperty("source_type")
    private String sourceType;
    @ExcelProperty("question")
    private String question;
    @ExcelProperty("source_scene")
    private Long serviceScene;
    @ExcelProperty("ticket_create_time")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Timestamp ticketCreateTime;
    @ExcelProperty("update_time")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Timestamp updateTime;
    @ExcelProperty("prompt_id")
    private Long promptId;
    @ExcelProperty("prompt_result")
    private String promptResult;
    @ExcelProperty("reply")
    private String reply;
    @ExcelProperty("msg")
    private String msg;
    @ExcelProperty("reasoning")
    private String reasoning;
    @ExcelProperty("version")
    private Long version;
}
