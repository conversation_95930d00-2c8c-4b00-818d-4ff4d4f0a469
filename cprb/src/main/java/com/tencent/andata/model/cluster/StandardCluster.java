package com.tencent.andata.model.cluster;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.util.PromptUtils;
import com.tencent.andata.util.lookup.StandardClusterLookupQuery;
import com.tencent.andata.util.lookup.TicketListAiTitleLookupQuery;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import lombok.Builder;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Properties;


@Builder
@ToString
public class StandardCluster implements Serializable {
    // 如果是新增的Cluster的话，这里ID为空
    public Long id;
    public String title;
    public Long baseClusterId;
    public int status;
    public int serviceSceneLevel1Id;
    public int serviceSceneLevel2Id;
    public String aggregateType;
    public int aggregateLevel;
    public List<Long> ticketList;

    public void setId(Long id) {
        this.id = id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setBaseClusterId(Long baseClusterId) {
        this.baseClusterId = baseClusterId;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setServiceSceneLevel1Id(int serviceSceneLevel1Id) {
        this.serviceSceneLevel1Id = serviceSceneLevel1Id;
    }

    public void setServiceSceneLevel2Id(int serviceSceneLevel2Id) {
        this.serviceSceneLevel2Id = serviceSceneLevel2Id;
    }

    public void setAggregateType(String aggregateType) {
        this.aggregateType = aggregateType;
    }

    public void setAggregateLevel(int aggregateLevel) {
        this.aggregateLevel = aggregateLevel;
    }

    public void setTicketList(List<Long> ticketList) {
        this.ticketList = ticketList;
    }

    public Long getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public Long getBaseClusterId() {
        return baseClusterId;
    }

    public int getStatus() {
        return status;
    }

    public int getServiceSceneLevel1Id() {
        return serviceSceneLevel1Id;
    }

    public int getServiceSceneLevel2Id() {
        return serviceSceneLevel2Id;
    }

    public String getAggregateType() {
        return aggregateType;
    }

    public int getAggregateLevel() {
        return aggregateLevel;
    }

    public List<Long> getTicketList() {
        return ticketList;
    }

    /**
     * 获取校准Cluster的子图
     *
     * @return 子图
     */
    public SubGraph getSubGraph() {
        return new SubGraph(
                AggregateLevel.of(this.aggregateLevel),
                this.ticketList
        );
    }

    /**
     * 根据工单列表，生成CLuster 标题
     */
    public void initTitle() throws Exception {
        // 查询数据库工单的AI标题，再过模型成成Cluster标题
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        final DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cprb.database", "pgsql", "aigc"))
                .build();
        final TicketListAiTitleLookupQuery ticketListAiTitleLookupQuery = new TicketListAiTitleLookupQuery(pgDBConf);
        ticketListAiTitleLookupQuery.open();
        final List<String> titleList = ticketListAiTitleLookupQuery.query(this.ticketList);
        final PromptUtils promptUtils = new PromptUtils();
        String title = null;
        for (int i = 0; i < 5; i++) {
            try {
                title = promptUtils.getTicketSummary(
                        String.join("\n", titleList), PromptUtils.PromptType.ClusterTitle
                );
                break;
            } catch (Exception e) {
                FlinkLog.getInstance().error(String.format(
                        "查询标题失败，重试%s/5, 标题：%s",
                        i,
                        String.join("\n", titleList)
                ));
            }
        }
        if (title == null) {
            FlinkLog.getInstance().error(String.format(
                    "生成Cluster标题失败： cluster_id：%s",
                    this.baseClusterId
            ));
            this.status = 0;
            return;
        }
        this.title = title;
        FlinkLog.getInstance().info(String.format(
                "查询工单标题：%s\n返回标题：%s",
                String.join("\n", titleList),
                this.title
        ));
        ticketListAiTitleLookupQuery.close();
    }

}
