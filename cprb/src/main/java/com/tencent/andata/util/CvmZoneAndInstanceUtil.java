package com.tencent.andata.util;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.CvmInstanceData;
import com.tencent.andata.model.CvmZoneData;
import com.tencent.andata.process.CvmZoneAndInstanceProcessFunction;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Pattern;

public class CvmZoneAndInstanceUtil {
    public static FlinkLog logger = FlinkLog.getInstance();
    public static DatabaseConf databaseConf;
    public static String url;

    static {
        // 初始化查询DB
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        KVConfBuilder<DatabaseConf> kvConfBuilder = null;
        try {
            kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class).setRainbowUtils(rainbowUtils);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
        try {
            databaseConf = kvConfBuilder
                    .setGroupName("cdc.database.mysql.cvm_demand")
                    .build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        url = String.format(
                "jdbc:mysql://%s:%s/%s",
                databaseConf.dbHost,
                databaseConf.dbPort,
                databaseConf.dbName
        );
    }

    /**
     * 从DB查询机型&园区数据
     *
     * @param date 日期
     * @return rs
     * @throws Exception ex
     */
    public static CvmZoneAndInstanceProcessFunction.CacheData getZoneAndInstanceData(String date) throws Exception {
        // 建立连接
        Connection connection = DriverManager.getConnection(url, databaseConf.userName, databaseConf.password);
        // 拼接SQL
        String zoneSql = String.format(
                "select\n"
                        + "region_type,\n"
                        + "zone_name,\n"
                        + "zone,\n"
                        + "area_name,\n"
                        + "region_name,\n"
                        + "type,\n"
                        + "type_name\n"
                        + "from inventory_health_main_zone_name_config \n"
                        + "where date = '%s' and deleted != 1 ",
                date
        );
        String instanceSql = String.format(
                "select\n"
                        + "region_type,\n"
                        + "instance_type,\n"
                        + "type1,\n"
                        + "type1_name,\n"
                        + "type2,\n"
                        + "type2_name\n"
                        + "from inventory_health_main_instance_type_config \n"
                        + "where date = '%s' and deleted != 1 ",
                date
        );
        final CvmZoneAndInstanceProcessFunction.CacheData res = new CvmZoneAndInstanceProcessFunction.CacheData();
        res.dataTimestamp = System.currentTimeMillis();
        res.zoneDataMap = new HashMap<>();
        res.instanceData = new HashMap<>();
        // statement
        final Statement sts = connection.createStatement();
        // 查询区域数据
        ResultSet resultSet = sts.executeQuery(zoneSql);
        while (resultSet.next()) {
            res.zoneDataMap.put(
                    // zoneName
                    resultSet.getString(2),
                    // zoneData
                    CvmZoneData.builder()
                            .regionType(resultSet.getString(1))
                            .zoneName(resultSet.getString(2))
                            .zone(resultSet.getString(3))
                            .areaName(resultSet.getString(4))
                            .regionName(resultSet.getString(5))
                            .type(resultSet.getString(6))
                            .typeName(resultSet.getString(7))
                            .build()
            );
        }
        // 查询机型数据
        resultSet = sts.executeQuery(instanceSql);
        while (resultSet.next()) {
            // 境内/境外
            final String regineType = resultSet.getString(1);
            // 实例名称
            final String instanceType = resultSet.getString(2);
            Map<String, CvmInstanceData> regineMap = res.instanceData.getOrDefault(regineType, new HashMap<>());
            regineMap.put(
                    instanceType,
                    CvmInstanceData.builder()
                            .regionType(resultSet.getString(1))
                            .instanceType(resultSet.getString(2))
                            .type1(resultSet.getString(3))
                            .type1Name(resultSet.getString(4))
                            .type2(resultSet.getString(5))
                            .type2Name(resultSet.getString(6))
                            .build()
            );
            res.instanceData.put(regineType, regineMap);
        }
        resultSet.close();
        sts.close();
        connection.close();
        return res;
    }

    /**
     * 是否需要从DB查询，一天查一次
     *
     * @param timestamp 数据时间
     * @return bool
     */
    public static boolean needQuery(long timestamp) {
        // 十二小时一次更新
        return System.currentTimeMillis() < timestamp + 1000 * 60 * 60 * 12;
    }

    /**
     * 拼接Pattern
     *
     * @param zoneList 可用区列表
     * @return
     */
    public static Pattern buildZonePattern(List<String> zoneList) {
        logger.info(String.format(
                "可用区正则：((?<!如)(?:南京|广州|北京|上海|深圳金融|上海金融|成都|重庆|香港|新加坡|雅加达|首尔|东京|"
                        + "孟买|曼谷|多伦多|圣保罗|硅谷|弗吉尼亚|法兰克福)[12345678一二三四五六七八]区)|(?<!如)(?:%s)",
                String.join("|", zoneList)
        ));
        return zoneList.size() != 0 ? Pattern.compile(
                String.format(
                        "((?<!如)(?:南京|广州|北京|上海|深圳金融|上海金融|成都|重庆|香港|新加坡|雅加达|首尔|东京|"
                                + "孟买|曼谷|多伦多|圣保罗|硅谷|弗吉尼亚|法兰克福)[12345678一二三四五六七八]区)|(?<!如)(?:%s)",
                        String.join("|", zoneList)
                )
        ) : Pattern.compile("((?<!如)(?:南京|广州|北京|上海|深圳金融|上海金融|成都|重庆|香港|新加坡|雅加达|首尔|东京|"
                + "孟买|曼谷|多伦多|圣保罗|硅谷|弗吉尼亚|法兰克福)[12345678一二三四五六七八]区)"
        );
    }

    /**
     * 拼接Pattern
     *
     * @param instanceList 机型列表
     * @return
     */
    public static Pattern buildInstancePattern(List<String> instanceList) {
        logger.info( String.format(
                "机型正则: (?<!(?:如|[0-9A-Za-z]))((?:SA[2345])|(?:S[1346])|(?:SR1)|(?:S5(?:se)?)|(?:S2(?:ne)?)|(?:SN3ne)"
                        + "|(?:M[12345])|(?:MA[23])|(?:M6(?:ce|p)?)|(?:IT[35])|(?:D[23])|(?:C[23456])|(?:CN3)"
                        + "|(?:B[CS]1)|%s)(?![0-9A-Za-z])",
                String.join("|", instanceList)
        ));
        return instanceList.size() != 0 ? Pattern.compile(
                String.format(
                        "(?<!(?:如|[0-9A-Za-z]))((?:SA[2345])|(?:S[1346])|(?:SR1)|(?:S5(?:se)?)|(?:S2(?:ne)?)|(?:SN3ne)"
                                + "|(?:M[12345])|(?:MA[23])|(?:M6(?:ce|p)?)|(?:IT[35])|(?:D[23])|(?:C[23456])|(?:CN3)"
                                + "|(?:B[CS]1)|%s)(?![0-9A-Za-z])",
                        String.join("|", instanceList)
                ), Pattern.CASE_INSENSITIVE
        ) : Pattern.compile(
                "(?<!(?:如|[0-9A-Za-z]))((?:SA[2345])|(?:S[1346])|(?:SR1)|(?:S5(?:se)?)|(?:S2(?:ne)?)|(?:SN3ne)"
                        + "|(?:M[12345])|(?:MA[23])|(?:M6(?:ce|p)?)|(?:IT[35])|(?:D[23])|(?:C[23456])|(?:CN3)"
                        + "|(?:B[CS]1))(?![0-9A-Za-z])", Pattern.CASE_INSENSITIVE
        );
    }

    public static String replaceZoneNumber(String zone){
        return zone.replace("1", "一")
                .replace("2", "二")
                .replace("3", "三")
                .replace("4", "四")
                .replace("5", "五")
                .replace("6", "六")
                .replace("7", "七")
                .replace("8", "八");
    }

}