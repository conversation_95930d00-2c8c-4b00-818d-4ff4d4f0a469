package com.tencent.andata.util.keyby;

import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.types.Row;

import java.io.Serializable;
import java.util.List;

@Builder
public class CombineKeyByUtil implements Serializable {

    public List<KeyByField> fieldList;
    public String separator;

    public String getKey(Row row) {
        StringBuilder builder = new StringBuilder();
        for (KeyByField keyByField : fieldList) {
            String key = keyByField.fieldType.getKey(row.getField(keyByField.fieldName));
            builder.append(key);
            builder.append("-");
        }
        return StringUtils.stripEnd(builder.toString(), "-");
    }
}
