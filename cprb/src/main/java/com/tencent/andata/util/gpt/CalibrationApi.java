package com.tencent.andata.util.gpt;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.gpt.AbstractGptApi;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.lang.NotImplementedException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// 校准接口
// https://iwiki.woa.com/p/4010308179
public class CalibrationApi extends AbstractGptApi {

    public static Logger logger = LoggerFactory.getLogger(CalibrationApi.class);
    public String requestId;
    public String project = "rerank";
    private ObjectMapper mapper = new ObjectMapper();

    public CalibrationApi(String token, String url) {
        super(token, url);
    }

    public CalibrationApi(String token, String url, String project) {
        super(token, url);
    }

    @Override
    public void buildRequest(Object data) {
        List<List<String>> req = (List<List<String>>) data;
        this.requestId = super.setRequestId();
        this.params.put("project", project);
        ArrayList<HashMap<String, ArrayList<String>>> maps = new ArrayList<>(req.size());
        for (List<String> list : req) {
            if (list.size() != 2) {
                continue;
            }
            maps.add(new HashMap<String, ArrayList<String>>() {{
                put("text", new ArrayList<String>() {{
                    add(list.get(0));
                    add(list.get(1));
                }});
            }});
        }
        this.params.put("value", maps);
    }

    @Override
    public void buildRequestWithRid(Object data, String requestId) {

    }

    @Override
    public CompletableFuture<String> asyncRequest(HttpClientUtils clientUtils) {
        throw new NotImplementedException();
    }

    @Override
    public Object parseResponse(JsonNode response) throws Exception {
        ArrayList<Double> list = new ArrayList<>();
        logger.info("rsp: {}", response);
        Iterator<JsonNode> elements = response.get("re_rank").elements();
        while (elements.hasNext()) {
            list.add(elements.next().asDouble());
        }
        return list;
    }

    public static void main(String[] args) throws Exception {
        final CalibrationApi commonGptApi = new CalibrationApi("2b663fe95b604593b74697950cac6c2e", "http://30.162.5.148:8080/rest-api/model/v1/rerankEngine");
        ArrayList<ArrayList<String>> lists = new ArrayList<>();
        lists.add(new ArrayList<String>() {{
            add("text1");
            add("text2");
        }});
        commonGptApi.buildRequest(lists);
        final JsonNode rsp = commonGptApi.request(new HttpClientUtils(
                300000,
                300000
        ));
        System.out.println(rsp);
        System.out.println(commonGptApi.requestAndParse(new HttpClientUtils(
                300000,
                300000
        )));
    }
}