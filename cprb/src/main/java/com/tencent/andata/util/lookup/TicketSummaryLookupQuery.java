package com.tencent.andata.util.lookup;

import com.tencent.andata.model.cluster.ClusterRelatedTicket;
import com.tencent.andata.model.cluster.StandardCluster;
import com.tencent.andata.utils.lookup.PostgresLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.api.java.tuple.Tuple2;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TicketSummaryLookupQuery extends PostgresLookupQuery<Long, ClusterRelatedTicket> {
    public TicketSummaryLookupQuery(DatabaseConf databaseConf) {
        super(databaseConf);
    }

    @Override
    public PreparedStatement buildStatement(Connection conn) throws SQLException {
        return conn.prepareStatement("select \n"
                + "service_scene_level1_id,\n"
                + "service_scene_level2_id,\n"
                + "service_scene_level3_id,\n"
                + "service_scene_level4_id,\n"
                + "ticket_close_time,\n"
                + "title,\n"
                + "ai_description,\n"
                + "ai_solution,\n"
                + "ai_title\n"
                + "from cprb_summary_embedding where ticket_id = ? limit 1");
    }

    @Override
    public ClusterRelatedTicket doQuery(PreparedStatement sts, Long ticketId) throws Exception {
        sts.setLong(1, ticketId);
        ClusterRelatedTicket res = null;
        final ResultSet resultSet = sts.executeQuery();
        while (resultSet.next()) {  //行遍历
            res = ClusterRelatedTicket.builder()
                    .serviceSceneLevel1Id(resultSet.getLong(1))
                    .serviceSceneLevel2Id(resultSet.getLong(2))
                    .serviceSceneLevel3Id(resultSet.getLong(3))
                    .serviceSceneLevel4Id(resultSet.getLong(4))
                    .ticketCloseTime(resultSet.getTimestamp(5))
                    .title(resultSet.getString(6))
                    .aiDesc(resultSet.getString(7))
                    .aiSolution(resultSet.getString(8))
                    .aiTitle(resultSet.getString(9))
                    .build();
        }
        return res;
    }

    @Override
    public List<ClusterRelatedTicket> doQueryList(PreparedStatement sts, Long aLong, boolean all, int cnt) throws Exception {
        return null;
    }
}
