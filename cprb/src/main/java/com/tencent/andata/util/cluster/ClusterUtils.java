package com.tencent.andata.util.cluster;

import org.apache.flink.api.java.tuple.Tuple2;

import java.util.ArrayList;
import java.util.List;

public class ClusterUtils {


    /**
     * 通过工单ID列表生成所有工单对
     *
     * @param ticketList 工单列表
     * @return 工单对
     */
    public static List<Tuple2<Long, Long>> getTupleByList(List<Long> ticketList) {
        final int size = ticketList.size();
        if (size < 2) {
            return null;
        }
        List<Tuple2<Long, Long>> res = new ArrayList<>();
        for (int i = 0; i < size - 1; i++) {
            for (int j = i + 1; j < size; j++) {
                final Long t1 = ticketList.get(i);
                final Long t2 = ticketList.get(j);
                Tuple2<Long, Long> tmp;
                if (t1 < t2) {
                    tmp = new Tuple2<>(t1, t2);
                } else if (t1 > t2) {
                    tmp = new Tuple2<>(t2, t1);
                } else {
                    continue;
                }
                // 去重
                if (!res.contains(tmp)) {
                    res.add(tmp);
                }
            }
        }
        return res;
    }

    /**
     * 获取两个列表的笛卡尔积工单对
     *
     * @param list1 列表1
     * @param list2 列表2
     * @return 工单对
     */
    public static List<Tuple2<Long, Long>> getTupleByList(List<Long> list1, List<Long> list2) {
        List<Tuple2<Long, Long>> res = new ArrayList<>();
        Tuple2<Long, Long> tmp;
        for (Long t1 : list1) {
            for (Long t2 : list2) {
                if (t1 < t2) {
                    tmp = new Tuple2<>(t1, t2);
                } else if (t1 > t2) {
                    tmp = new Tuple2<>(t2, t1);
                } else {
                    continue;
                }
                // 去重
                if (!res.contains(tmp)) {
                    res.add(tmp);
                }
            }
        }
        return res;
    }

    /**
     * 判断列表是否包含
     * 如 [1,2,3] 包含 [1,2]
     *
     * @param list1
     * @param list2
     * @return
     */
    public static boolean checkListContain(List<Long> list1, List<Long> list2) {
        for (Long t2 : list2) {
            if (!list1.contains(t2)) {
                return false;
            }
        }
        return true;
    }

    public static void main(String[] args) {
        System.out.println(checkListContain(new ArrayList<Long>() {{
            add(3L);
            add(2L);
        }},new ArrayList<Long>() {{
            add(3L);
            add(2L);
            add(1L);

        }}));
        System.out.println(ClusterUtils.getTupleByList(new ArrayList<Long>() {{
                                                           add(3L);
                                                           add(2L);
                                                       }},
                new ArrayList<Long>() {{
                    add(2L);
                }}));
    }
}
