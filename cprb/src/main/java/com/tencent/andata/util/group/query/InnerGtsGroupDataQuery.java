package com.tencent.andata.util.group.query;

import com.tencent.andata.model.GroupData;
import com.tencent.andata.utils.struct.DatabaseConf;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class InnerGtsGroupDataQuery extends MysqlGroupDataQuery<String> {
    public InnerGtsGroupDataQuery(DatabaseConf databaseConf) {
        super(databaseConf);
    }

    @Override
    public GroupData doQuery(PreparedStatement statement, String groupId) throws SQLException {
        // 设置参数
        statement.setString(1, groupId);
        // 执行SQL
        try (ResultSet resultSet = statement.executeQuery()) {
            while (resultSet.next()) {
                final long ticketId = resultSet.getLong("ticket_id");
                // 构造groupData数据
                return GroupData.builder()
                        .ticketId(ticketId)
                        .groupType(GroupData.GroupType.GTS_INTERNAL)
                        .groupId(groupId)
                        .build();
            }
        }
        // 没查到则返回Null
        return null;
    }

    @Override
    public Map<String, GroupData> doQueryStock(Connection conn) throws SQLException {
        // 拉取所有
        final PreparedStatement preparedStatement = conn.prepareStatement("select "
                + "ticket_id, group_id "
                + "from t_ticket_group "
                // 未解散的群
                + "where  group_dismissed = 0"
        );
        final HashMap<String, GroupData> res = new HashMap<>();
        // 执行SQL
        try (ResultSet resultSet = preparedStatement.executeQuery()) {
            while (resultSet.next()) {
                final long ticketId = resultSet.getLong("ticket_id");
                final String groupId = resultSet.getString("group_id");
                res.put(
                        groupId,
                        GroupData.builder()
                                .ticketId(ticketId)
                                .groupType(GroupData.GroupType.GTS_INTERNAL)
                                .groupId(groupId)
                                .build()
                );
            }
        }
        return res;
    }

    @Override
    public PreparedStatement createPreparedStatement(Connection conn) throws SQLException {
        // GTS内部群，从Antool获取
        return conn.prepareStatement(
                "select\n"
                        + " ticket_id\n"
                        + "from t_ticket_group\n"
                        // 只同步未解散的群
                        + "where group_dismissed = 0 and group_id = ?"
        );
    }
}
