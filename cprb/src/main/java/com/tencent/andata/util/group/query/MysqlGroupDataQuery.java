package com.tencent.andata.util.group.query;

import com.tencent.andata.utils.struct.DatabaseConf;

/**
 * 内部QA群获取Query
 */
public abstract class MysqlGroupDataQuery<T> extends JDBCGroupDataQuery<T> {
    public MysqlGroupDataQuery(DatabaseConf databaseConf) {
        super();
        // 使用Mysql Driver
        this.driverName = "com.mysql.jdbc.Driver";
        // url
        this.url = String.format(
                "jdbc:mysql://%s:%s/%s",
                databaseConf.dbHost,
                databaseConf.dbPort,
                databaseConf.dbName
        );
        this.userName = databaseConf.userName;
        this.password = databaseConf.password;
    }

}
