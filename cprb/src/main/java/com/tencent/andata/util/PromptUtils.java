package com.tencent.andata.util;

import com.tencent.andata.util.gpt.CalibrationApi;
import com.tencent.andata.utils.gpt.CommonGptApi;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PromptUtils {
    private final String rainbowGroup = "cprb.gpt.prompt";
    private RainbowUtils rainbowUtils;
    private CommonGptApi gptApi;
    private CalibrationApi calibrationApi;
    private HashMap<String, String> promptTemplateMap;
    private HttpClientUtils clientUtils;

    public enum PromptType {
        // 解决方案
        Solution,
        // 问题描述
        Description,
        // 工单标题
        TicketTitle,
        // 工单主题
        TicketTopic,
        // Cluster标题
        ClusterTitle;
    }

    public PromptUtils() throws IOException {
        Properties rainbowProperties = PropertyUtils.loadProperties("env.properties");
        this.rainbowUtils = new RainbowUtils(rainbowProperties);
        initPromptConfig();
        initCalibrationConfig();
    }

    private void initCalibrationConfig() {
        this.calibrationApi = new CalibrationApi(
                this.rainbowUtils.getStringValue(rainbowGroup, "token"),
                this.rainbowUtils.getStringValue(rainbowGroup, "rerank_url")
        );
    }

    private void initPromptConfig() {
        this.gptApi = new CommonGptApi(
                this.rainbowUtils.getStringValue(rainbowGroup, "token"),
                this.rainbowUtils.getStringValue(rainbowGroup, "url"),
                "hunyuan"
        );
        this.promptTemplateMap = new HashMap<String, String>();
        this.promptTemplateMap.put(
                "description_prompt",
                this.rainbowUtils.getStringValue(rainbowGroup, "description_prompt")
        );
        this.promptTemplateMap.put(
                "solution_prompt",
                this.rainbowUtils.getStringValue(rainbowGroup, "solution_prompt")
        );
        this.promptTemplateMap.put(
                "ticket_title_prompt",
                this.rainbowUtils.getStringValue(rainbowGroup, "ticket_title_prompt")
        );
        this.promptTemplateMap.put(
                "ticket_topic_prompt",
                this.rainbowUtils.getStringValue(rainbowGroup, "ticket_topic_prompt")
        );
        this.promptTemplateMap.put(
                "cluster_title",
                this.rainbowUtils.getStringValue(rainbowGroup, "cluster_title")
        );
        this.clientUtils = new HttpClientUtils(
                300000,
                300000
        );
    }

    public List<Double> reRankStr(List<List<String>> data) throws Exception {
        this.calibrationApi.buildRequest(data);
        return (List<Double>) this.calibrationApi.requestAndParse(this.clientUtils);
    }

    /**
     * 获取工单摘要
     *
     * @param ticketData 工单流水数据
     * @param type       摘要类型
     * @return 摘要结果
     * @throws Exception ex
     */
    public String getTicketSummary(String ticketData, PromptType type) throws Exception {
        String descTmp = "";
        switch (type) {
            case Solution:
                descTmp = this.promptTemplateMap.get("solution_prompt");
                break;
            case Description:
                descTmp = this.promptTemplateMap.get("description_prompt");
                break;
            case TicketTitle:
                descTmp = this.promptTemplateMap.get("ticket_title_prompt");
                break;
            case TicketTopic:
                descTmp = this.promptTemplateMap.get("ticket_topic_prompt");
                break;
            case ClusterTitle:
                descTmp = this.promptTemplateMap.get("cluster_title");
                break;
        }
        this.gptApi.buildRequest(String.format(descTmp, ticketData));
        return (String) this.gptApi.requestAndParse(this.clientUtils);
    }

    public static String cleanData(String data) {
        // 匹配连续的标点符号
        Pattern pattern = Pattern.compile("[*?？,，.。;；:：]{3,}");
        Matcher matcher = pattern.matcher(data);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String match = matcher.group();
            // 只保留最大的个数为3
            String replacement = match.substring(0, 3);
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        return result.toString();
    }

    public static void main(String[] args) {
        final PromptUtils propertyUtils;
        try {
            propertyUtils = new PromptUtils();
        } catch (IOException e) {
            // TODO 初始化报错
            throw new RuntimeException(e);
        }
        String res = null;
        try {
            String a= "备案主体与域名实名信息不一致导致无法提交备案订单\n"
                    + "单位账号主体与域名不一致的备案问题\n"
                    + "备案时账号实名认证与域名所有者不一致问题\n"
                    + "腾讯云账号实名与备案主体不一致问题\n"
                    + "账号实名与备案主体不一致如何处理\n"
                    + "备案主体与云账号认证主体不一致无法备案\n"
                    + "备案主体与腾讯云账号实名主体一致性要求\n"
                    + "备案主体与账号不一致的处理方法\n"
                    + "域名实名与备案主体不一致问题\n"
                    + "腾讯云备案信息与账号实名认证不一致如何处理\n"
                    + "域名迁移至腾讯云后备案问题\n"
                    + "备案时提示实名认证与备案主体不一致\n"
                    + "腾讯云账号与备案主体不一致，需修改实名重新备案\n"
                    + "备案主体与账号实名认证需一致吗？";
            res = propertyUtils.getTicketSummary(a, PromptType.ClusterTitle);
        } catch (Exception e) {
            //TODO 请求/解析 失败，重试
            throw new RuntimeException(e);
        }
        System.out.println(res);
    }

}
