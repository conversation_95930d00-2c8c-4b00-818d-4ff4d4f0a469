package com.tencent.andata.util;

import org.apache.flink.types.Row;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TicketFlowStatusUtil {

    // 2 去掉 2 是 进入详情,自动转为处理中 这种数据
    private static final List<Integer> innerTypes = Stream
            .of(1, 4, 5, 6, 8, 9, 10, 12, 15, 16, 27, 28)
            .collect(Collectors.toList());
    private static final long manualCloseStatus = -100;

    public static boolean isClose(Row row) {
        long targetStatus = Long.parseLong(String.valueOf(row.getField("target_status")));
        int operationType = Integer.parseInt(String.valueOf(row.getField("operation_type")));
        return operationType == 16 || operationType == 1 && targetStatus == 3;
    }

    public static void setManualCloseStatus(Row row) {
        row.setField("target_status", manualCloseStatus);
    }

    public static boolean isManualClose(Row row) {
        long targetStatus = Long.parseLong(String.valueOf(row.getField("target_status")));
        return targetStatus == manualCloseStatus;
    }

    public static String getOperatorTypeStr(Row row) {
        String operatorTypeStr = "";
        int operatorType = Integer.parseInt(String.valueOf(row.getField("operator_type")));
        if (operatorType == 1) {
            operatorTypeStr = "客户";
        } else if (operatorType == 2) {
            operatorTypeStr = "客服";
        } else {
            operatorTypeStr = "其他";
        }
        return operatorTypeStr;
    }

    public static boolean isInnerReply(Row row) {
        int operationType = Integer.parseInt(String.valueOf(row.getField("operation_type")));
        int operatorType = Integer.parseInt(String.valueOf(row.getField("operator_type")));
        if (operatorType == 3) {
            return false;
        }
        boolean innerFg = false;
        for (Integer innerType : innerTypes) {
            if (operationType == innerType) {
                innerFg = true;
                break;
            }
        }
        return innerFg;
    }

    public static boolean isExternReply(Row row) {
        int operationType = Integer.parseInt(String.valueOf(row.getField("operation_type")));
        int operatorType = Integer.parseInt(String.valueOf(row.getField("operator_type")));
        if (operatorType == 3) {
            return false;
        }
        return operationType != 4 && operationType != 21 && operationType != 27;
    }
}
