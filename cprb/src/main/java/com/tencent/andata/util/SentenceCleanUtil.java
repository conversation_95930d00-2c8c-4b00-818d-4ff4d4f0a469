package com.tencent.andata.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SentenceCleanUtil {

    /***
     * sentenceClean
     * @param text
     * @param keepLen
     * @return
     */
    public static String sentenceClean(String text, int keepLen) {
        text = text.toLowerCase();
        if (text.length() == 0) {
            return "";
        }
        // 去除换行符，缩进符
        text = text.trim().replace("\t", "").replace("\n", "");
        // 去除[]类型的标签
        text = removePairedTag(text, '[', ']', 6);
        // 去除【】类型的标签
        text = removePairedTag(text, '【', '】', 6);
        // 去除<>类型的标签
        text = removePairedTag(text, '<', '>', 6);

        // 移除http链接
//        String pattern = "http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+";
        String pattern = "https?://(?:[a-zA-Z0-9$-_@.&+!*\\(\\),]|%[0-9a-fA-F]{2})++";
        ;
        text = reStr(pattern, text);

        // 移除ip
        pattern = "[0-9]{1,3}(.[0-9]{1,3}){3}";
        text = reStr(pattern, text);

        // 移除各种id
        String[] patterns = {
                "ins-[a-zA-Z-_0-9*]+",
                "instance-[a-zA-Z-_0-9]+",
                "openid[a-zA-Z-_0-9]+",
                "wx_ticket_id[a-zA-Z-_0-9]+",
                "env_id[a-zA-Z-_0-9]+",
                "reqid[a-zA-Z-_0-9]+",
                "id[a-zA-Z-_0-9]+",
                ":mssql-[a-zA-Z-_0-9]+",
                "request id [a-zA-Z-_0-9]+",
                "trace id [a-zA-Z-_0-9]+",
                "taskid[a-zA-Z-_0-9]+"
        };
        for (String p : patterns) {
            text = reStr(p, text);
        }

        // 移除日志信息
        text = removeErrorInfo(text, 8);
        // 清理后的词如果都是asc码或长度太短，则舍去这部分文本，直接返回空
        if (isAscii(text)) {
            return "";
        }
        if (text.length() == 0) {
            return "";
        }
        if (text.charAt(0) == ',' || text.charAt(0) == '，' || text.charAt(0) == '.' || text.charAt(0) == '。') {
            text = text.substring(1);
        }
        if (text.length() == 0) {
            return "";
        }
        if (text.charAt(text.length() - 1) == ',' || text.charAt(text.length() - 1) == '，'
                || text.charAt(text.length() - 1) == '.' || text.charAt(text.length() - 1) == '。') {
            text = text.substring(0, text.length() - 1);
        }
        String trim = text.trim();
        if (trim.length() > keepLen) {
            trim = trim.substring(0, keepLen);
        }
        return trim;
    }

    /***
     * reStr
     * @param pattern
     * @param text
     * @return
     */
    public static String reStr(String pattern, String text) {
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(text);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(result, "");
        }
        matcher.appendTail(result);
        text = result.toString();
        return text;
    }


    /***
     * removePairedTag
     * @param text
     * @param leftTag
     * @param rightTag
     * @param tagLength
     * @return
     */
    public static String removePairedTag(String text, char leftTag, char rightTag, int tagLength) {
        while (text.contains(String.valueOf(leftTag)) && text.contains(String.valueOf(rightTag))) {
            int leftBrackets = text.indexOf(leftTag);
            int rightBrackets = text.indexOf(rightTag);
            // 间隔小于tagLength或者都是英文，移除两个tags之间的内容，通常中文tag不会特别长
            if (leftBrackets < rightBrackets && (rightBrackets - leftBrackets <= tagLength
                    || isAscii(text.substring(leftBrackets + 1, rightBrackets)))
            ) {
                text = text.substring(0, leftBrackets) + text.substring(rightBrackets + 1);
            } else {
                // 不成对，先移除left tag
                text = text.substring(0, rightBrackets) + text.substring(rightBrackets + 1);
            }
        }

        // 还有单个tag就直接移除
        text = text.replace(String.valueOf(leftTag), "").replace(String.valueOf(rightTag), "");

        return text;
    }

    /***
     * removeErrorInfo
     * @param text
     * @param keepLength
     * @return
     */
    public static String removeErrorInfo(String text, int keepLength) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        int[] dp = new int[text.length()];
        if (isAscii(String.valueOf(text.charAt(0)))) {
            dp[0] = 1;
        }
        for (int i = 1; i < text.length(); i++) {
            if (isAscii(String.valueOf(text.charAt(i)))) {
                dp[i] = dp[i - 1] + 1;
            } else {
                dp[i] = 0;
            }
        }

        StringBuilder cleanText = new StringBuilder();
        for (int i = 0; i < text.length(); i++) {
            if (dp[i] < keepLength) {
                cleanText.append(text.charAt(i));
            }
        }
        return cleanText.toString();
    }

    /***
     * isAscii
     * @param s
     * @return
     */
    public static boolean isAscii(String s) {
        for (char c : s.toCharArray()) {
            if (c >= 128) {
                return false;
            }
        }
        return true;
    }
}