package com.tencent.andata.util.group;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.GroupData;
import com.tencent.andata.util.group.query.GroupDataQuery;
import com.tencent.andata.util.group.query.InnerOaGroupDataQuery;
import com.tencent.andata.util.group.query.JDBCGroupDataQuery;
import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;
import com.tencent.andata.utils.LRUCache;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 一事两群处理门面类，对外提供门面
 */
public class OneThingTwoGroupFacade implements Serializable {
    public static FlinkLog logger = FlinkLog.getInstance();

    // LRU缓存
    private final int cacheCnt;
    private transient LRUCache<String, GroupData> groupCache;
    public List<GroupDataQuery<String>> groupDataQueryList;

    public OneThingTwoGroupFacade(
            int cacheCnt,
            List<GroupDataQuery<String>> groupDataQueryList
    ) {
        this.groupDataQueryList = groupDataQueryList;
        this.cacheCnt = cacheCnt;
    }

    /**
     * 内部设计JDBC链接，因此需要open/close方法
     */
    public void open() {
        this.groupCache = new LRUCache<>(cacheCnt);
        // 初始化Query
        groupDataQueryList.forEach(consumer(a -> ((JDBCGroupDataQuery<String>) a).open()));
        // 加载存量数据入缓存
        groupDataQueryList.forEach(
                new Consumer<GroupDataQuery<String>>() {
                    @Override
                    public void accept(GroupDataQuery<String> groupDataQuery) {
                        final Map<String, GroupData> stockData = groupDataQuery.queryStock();
                        if (stockData.size() == 0) {
                            logger.warn(
                                    String.format(
                                            "[OneThingTwoGroupFacade] get empty stock group data from %s",
                                            groupDataQuery.getClass()
                                    )
                            );
                        } else {
                            logger.info(
                                    String.format(
                                            "[OneThingTwoGroupFacade] init cached %d group data from: %s",
                                            stockData.size(),
                                            groupDataQuery.getClass()
                                    )
                            );
                        }
                        // 初始化存量缓存
                        groupCache.putAll(groupDataQuery.queryStock());
                    }
                }
        );
    }

    public void close() {
        groupDataQueryList.forEach(consumer(a -> ((JDBCGroupDataQuery<String>) a).close()));
        this.groupCache.clear();
    }

    /**
     * 对外提供查询群数据
     *
     * @param groupId 群ID
     * @return 工单ID
     */
    public GroupData getDataByGroupId(String groupId) {
        // 缓存命中直接返回
        if (this.groupCache.containsKey(groupId)) {
            final GroupData data = this.groupCache.get(groupId);
            logger.info(
                    String.format("[OneThingTwoGroupFacade] 从缓存获取数据:%s", data)
            );
            return data;
        }
        // 遍历查询器，挨个查询
        for (GroupDataQuery<String> groupDataQuery : this.groupDataQueryList) {
            // 这里做个特殊处理剪枝，ww开头的群都是OA内部群，降低工单主题表的查询频率
            if (groupDataQuery instanceof InnerOaGroupDataQuery && !groupId.startsWith("ww")) {
                continue;
            }
            final GroupData data = groupDataQuery.query(groupId);
            if (data != null) {
                logger.info(
                        String.format("[OneThingTwoGroupFacade] 从DB获取数据:%s", data)
                );
                // 缓存
                this.groupCache.put(groupId, data);
                return data;
            }
        }
        logger.info(
                String.format("[OneThingTwoGroupFacade] 未获取到该群数据: %s", groupId)
        );
        // 没查到返回Null
        return null;
    }
}
