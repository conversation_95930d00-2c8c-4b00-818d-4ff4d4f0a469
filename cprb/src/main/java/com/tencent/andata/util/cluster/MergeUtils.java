package com.tencent.andata.util.cluster;

import com.tencent.andata.model.cluster.AggregateLevel;
import com.tencent.andata.model.cluster.StandardCluster;
import com.tencent.andata.model.cluster.TicketTuple;
import com.tencent.andata.util.lookup.StandardClusterLookupQuery;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.lookup.LookupQueryable;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.api.java.tuple.Tuple2;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

public class MergeUtils {

    // 上游是经过baseId key by的，这里处理的数据都是同一个clusterID的
    public Long baseClusterId;

    public Merger lowLevelMerger;
    public Merger middleLevelMerger;
    public Merger highLevelMerger;
    private LookupQueryable<Tuple2<Long, Integer>, Map<Long, StandardCluster>> standardClusterLookupQuery;

    public MergeUtils() throws IOException, NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        final DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cprb.database", "pgsql", "aigc"))
                .build();
        this.standardClusterLookupQuery = new StandardClusterLookupQuery(pgDBConf);
    }


    /**
     * 返回校准Cluster数据
     *
     * @param ticketTuples
     * @return
     * @throws Exception
     */
    public List<StandardCluster> merge(List<TicketTuple> ticketTuples) throws Exception {
        if (ticketTuples.isEmpty()) {
            return new ArrayList<>();
        }

        List<TicketTuple> lowLevelData = new ArrayList<>();
        List<TicketTuple> middleLevelData = new ArrayList<>();
        List<TicketTuple> highLevelData = new ArrayList<>();

        // 根据工单对阈值进行区分等级
        for (TicketTuple ticketTuple : ticketTuples) {
            // 这里高阈值数据需要在低阈值内检测出来，所以没有加break
            switch (LevelChecker.CheckAggregateLevel(ticketTuple)) {
                case HIGH:
                    highLevelData.add(ticketTuple);
                case MIDDLE:
                    middleLevelData.add(ticketTuple);
                case LOW:
                    lowLevelData.add(ticketTuple);
            }
        }
        // 这里上游根据baseCLusterID做的Key by，进来的数据都一样，取第一个就行
        final Long baseClusterId = ticketTuples.get(0).baseClusterId;
        final int serviceSceneLevel1Id = ticketTuples.get(0).serviceSceneLevel1Id;
        final int serviceSceneLevel2Id = ticketTuples.get(0).serviceSceneLevel2Id;
        // 更新已有的Cluster
        this.updateClusterMeger(baseClusterId, serviceSceneLevel1Id, serviceSceneLevel2Id);
        // 三种等级Cluster各自Merge
        this.doMerger(lowLevelData, middleLevelData, highLevelData);
        // 发送需要更新的Cluster
        return this.getUpdateClusters();
    }

    public List<StandardCluster> getUpdateClusters() {
        final ArrayList<StandardCluster> res = new ArrayList<>();
        res.addAll(lowLevelMerger.updateStandardClusters);
        res.addAll(middleLevelMerger.updateStandardClusters);
        res.addAll(highLevelMerger.updateStandardClusters);
        return res;
    }

    public void doMerger(List<TicketTuple> lowLevelData, List<TicketTuple> middleLevelData, List<TicketTuple> highLevelData) throws Exception {
        this.lowLevelMerger.doMerge(lowLevelData);
        this.middleLevelMerger.doMerge(middleLevelData);
        this.highLevelMerger.doMerge(highLevelData);
    }

    /**
     * 更新Merger
     *
     * @param baseClusterId
     * @param serviceSceneLevel1Id
     * @param serviceSceneLevel2Id
     * @throws Exception
     */
    public void updateClusterMeger(Long baseClusterId, int serviceSceneLevel1Id, int serviceSceneLevel2Id) throws Exception {
        standardClusterLookupQuery.open();
        this.baseClusterId = baseClusterId;
        // 根据BaseCluster id查询三个等级的校准Cluster数据
        this.lowLevelMerger = new Merger(
                standardClusterLookupQuery.query(Tuple2.of(baseClusterId, AggregateLevel.LOW.value)),
                baseClusterId,
                AggregateLevel.LOW,
                serviceSceneLevel1Id,
                serviceSceneLevel2Id
        );
        this.middleLevelMerger = new Merger(
                standardClusterLookupQuery.query(Tuple2.of(baseClusterId, AggregateLevel.MIDDLE.value)),
                baseClusterId,
                AggregateLevel.MIDDLE,
                serviceSceneLevel1Id,
                serviceSceneLevel2Id
        );
        this.highLevelMerger = new Merger(
                standardClusterLookupQuery.query(Tuple2.of(baseClusterId, AggregateLevel.HIGH.value)),
                baseClusterId,
                AggregateLevel.HIGH,
                serviceSceneLevel1Id,
                serviceSceneLevel2Id
        );
        standardClusterLookupQuery.close();
    }
}
