package com.tencent.andata.util.cluster;

import com.tencent.andata.model.cluster.AggregateLevel;
import com.tencent.andata.model.cluster.TicketTuple;

public class LevelChecker {
    /**
     * 这里是写死的阈值检测方法
     *
     * @param ticketTuple 工单对
     * @return 最低阈值
     */
    public static AggregateLevel CheckAggregateLevel(TicketTuple ticketTuple) {
        if (
                ticketTuple.model_solution_score >= 4 &&
                        ticketTuple.model_desc_score >= 4 &&
                        ticketTuple.extract_desc_score >= 4 &&
                        ticketTuple.extract_solution_score >= 2
        ) {
            return AggregateLevel.HIGH;
        } else if (
                ticketTuple.model_solution_score >= 0 &&
                        ticketTuple.model_desc_score >= 4 &&
                        ticketTuple.extract_desc_score >= 4 &&
                        ticketTuple.extract_solution_score >= -4
        ) {
            return AggregateLevel.MIDDLE;
        } else if (
                ticketTuple.model_solution_score >= 0 &&
                        ticketTuple.model_desc_score >= 0 &&
                        ticketTuple.extract_desc_score >= 0 &&
                        ticketTuple.extract_solution_score >= -4
        ) {
            return AggregateLevel.LOW;
        } else {
            return AggregateLevel.ELSE;
        }
    }
}
