package com.tencent.andata.util.lookup;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.model.cluster.StandardCluster;
import com.tencent.andata.utils.lookup.PostgresLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.api.java.tuple.Tuple2;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class StandardClusterLookupQuery extends PostgresLookupQuery<Tuple2<Long, Integer>, Map<Long, StandardCluster>> {
    public StandardClusterLookupQuery(DatabaseConf databaseConf) {
        super(databaseConf);
    }

    @Override
    public PreparedStatement buildStatement(Connection conn) throws SQLException {
        return connection.prepareStatement("select * from cprb_standard_aggregated_cluster "
                + "where base_cluster_id = ? and aggregate_level = ? and status = 1");
    }

    @Override
    public Map<Long, StandardCluster> doQuery(PreparedStatement sts, Tuple2<Long, Integer> longIntegerTuple2) throws Exception {
        // 设置参数
        sts.setLong(1, longIntegerTuple2.f0);
        sts.setInt(2, longIntegerTuple2.f1);
        ObjectMapper objectMapper = new ObjectMapper();
        // 查询Cluster
        Map<Long, StandardCluster> res = new HashMap<>();
        final ResultSet resultSet = sts.executeQuery();
        while (resultSet.next()) {  //行遍历
            final String ticketIsListStr = resultSet.getString(11);
            final List<Integer> ticketIdList = objectMapper.readValue(ticketIsListStr, List.class);
            res.put(
                    resultSet.getLong(1),
                    StandardCluster.builder()
                            .id(resultSet.getLong(1))
                            .title(resultSet.getString(2))
                            .status(resultSet.getInt(4))
                            .baseClusterId(resultSet.getLong(3))
                            .serviceSceneLevel1Id(resultSet.getInt(7))
                            .serviceSceneLevel2Id(resultSet.getInt(8))
                            .aggregateType(resultSet.getString(9))
                            .aggregateLevel(resultSet.getInt(10))
                            .ticketList(ticketIdList.stream().map(Long::valueOf).collect(Collectors.toList()))
                            .build()
            );
        }
        return res;
    }

    @Override
    public List<Map<Long, StandardCluster>> doQueryList(PreparedStatement sts, Tuple2<Long, Integer> longIntegerTuple2, boolean all, int cnt) throws Exception {
        return null;
    }
}
