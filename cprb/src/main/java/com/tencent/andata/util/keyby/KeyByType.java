package com.tencent.andata.util.keyby;

import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@AllArgsConstructor
public class KeyByType implements Serializable {

    public Type type;

    public enum Type implements Serializable {
        Str,
        Instant_Month,
        Instant_Day,
        Integer,
    }

    public String getKey(Object object) {
        switch (type) {
            case Str:
            case Integer:
                return String.valueOf(object);
            case Instant_Month:
                return DateTimeFormatter.ofPattern("yyyy-MM")
                        .withZone(ZoneId.of("Asia/Shanghai"))
                        .format((Instant) object);
            case Instant_Day:
                return DateTimeFormatter.ofPattern("yyyy-MM-dd")
                        .withZone(ZoneId.of("Asia/Shanghai"))
                        .format((Instant) object);
            default:
                return "";
        }
    }
}