package com.tencent.andata.util;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.endpoint.EndpointBuilder;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.internal.BucketNameUtils;
import com.qcloud.cos.internal.UrlComponentUtils;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.UploadResult;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.TransferManager;
import com.qcloud.cos.transfer.Upload;
import lombok.AllArgsConstructor;
import org.apache.flink.util.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@AllArgsConstructor
public class CosUtil implements Serializable {

    private static final Logger log = LoggerFactory.getLogger(CosUtil.class);
    private TransferManager transferManager;
    private String bucketName;
    private String dir;

    public UploadResult upload(File file, String fileName) throws InterruptedException {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, dir + fileName, file);
        Upload upload = transferManager.upload(putObjectRequest);
        return upload.waitForUploadResult();
    }

    /***
     * scheduledWarmTask
     * @param period .
     */
    public void scheduledWarmTask(long period) {
        // 创建 ScheduledThreadPool 线程池
        ScheduledExecutorService threadPool = Executors.newScheduledThreadPool(1);
        threadPool
                .scheduleAtFixedRate(
                        new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    clientWarm();
                                } catch (Exception e) {
                                    // 打log
                                    log.error("warm error:{}", e);
                                }
                            }
                        }, 0L, period, TimeUnit.SECONDS
                ); // 描述上面两个参数的时间单位
    }

    // 预热
    public boolean clientWarm() throws IOException, InterruptedException {
        File temp = File.createTempFile("CprbWarmTempFile", ".txt");
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, dir + "CprbWarmTempFile.txt", temp);
        Upload upload = transferManager.upload(putObjectRequest);
        upload.waitForUploadResult();
        return true;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String secretId;
        private String secretKey;
        private String bucketName;
        private String region;
        private String dir;

        public Builder setSecretId(String secretId) {
            this.secretId = secretId;
            return this;
        }

        public Builder setSecretKey(String secretKey) {
            this.secretKey = secretKey;
            return this;
        }

        public Builder setRegion(String region) {
            this.region = region;
            return this;
        }

        public Builder setDir(String dir) {
            this.dir = dir;
            return this;
        }

        public Builder setBucketName(String bucketName) {
            this.bucketName = bucketName;
            return this;
        }

        /***
         * build .
         * @return .
         */
        public CosUtil build() {
            Preconditions.checkNotNull(secretId);
            Preconditions.checkNotNull(secretKey);
            Preconditions.checkNotNull(region);
            Preconditions.checkNotNull(bucketName);
            Preconditions.checkNotNull(dir);
            BasicCOSCredentials credentials = new BasicCOSCredentials(secretId, secretKey);
            // 2 设置 bucket 的地域
            // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分
            //COS_REGION 参数：配置成存储桶 bucket 的实际地域，例如 ap-beijing，更多 COS 地域的简称请参见 https://cloud.tencent.com/document/product/436/6224
            ClientConfig clientConfig = new ClientConfig(new Region(region));
            clientConfig.setConnectionTimeout(10000);
            clientConfig.setSocketTimeout(10000);
            clientConfig.setMaxErrorRetry(10);
            clientConfig.setIdleConnectionAlive(3);
            // 这里建议设置使用 https 协议
            // 从 5.6.54 版本开始，默认使用了 https
            clientConfig.setHttpProtocol(HttpProtocol.http);
            // 设置内网域名
            // 使用内网域名
            String endpoint = String.format("cos-internal.%s.tencentcos.cn", region);
            clientConfig.setEndpointBuilder(new CosEndpointBuilder(endpoint));
            // 3 生成 cos 客户端。
            COSClient client = new COSClient(credentials, clientConfig);
            // 自定义线程池大小，建议在客户端与 COS 网络充足（例如使用腾讯云的 CVM，同地域上传 COS）的情况下，设置成16或32即可，可较充分的利用网络资源
            // 对于使用公网传输且网络带宽质量不高的情况，建议减小该值，避免因网速过慢，造成请求超时。
            ExecutorService threadPool = Executors.newFixedThreadPool(8);
            // 传入一个 threadpool, 若不传入线程池，默认 TransferManager 中会生成一个单线程的线程池。
            TransferManager transferManager = new TransferManager(client, threadPool);
            return new CosUtil(transferManager, bucketName, dir);
        }
    }


    public static class CosEndpointBuilder implements EndpointBuilder {

        private String cosEndpoint;
        private String cosGetServiceEndpoint = "service.cos.tencentcos.cn";

        public CosEndpointBuilder(String endpoint) {
            super();
            if (endpoint == null) {
                throw new IllegalArgumentException("endpoint must not be null");
            }
            while (endpoint.startsWith(".")) {
                endpoint = endpoint.substring(1);
            }
            UrlComponentUtils.validateEndPointSuffix(endpoint);
            this.cosEndpoint = endpoint.trim();
        }

        @Override
        public String buildGeneralApiEndpoint(String bucketName) {
            BucketNameUtils.validateBucketName(bucketName);
            return String.format("%s.%s", bucketName, this.cosEndpoint);
        }

        @Override
        public String buildGetServiceApiEndpoint() {
            return cosGetServiceEndpoint;
        }
    }
}
