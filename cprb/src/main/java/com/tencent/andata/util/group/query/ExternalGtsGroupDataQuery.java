package com.tencent.andata.util.group.query;

import com.tencent.andata.model.GroupData;
import com.tencent.andata.utils.struct.DatabaseConf;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class ExternalGtsGroupDataQuery extends MysqlGroupDataQuery<String> {
    public ExternalGtsGroupDataQuery(DatabaseConf databaseConf) {
        super(databaseConf);
    }

    @Override
    public GroupData doQuery(PreparedStatement statement, String groupId) throws SQLException {
        // 设置参数
        statement.setString(1, groupId);
        // 执行SQL
        try (ResultSet resultSet = statement.executeQuery()) {
            while (resultSet.next()) {
                // 如果捞出来的数据不是工单ID，则跳过
                long ticketId;
                try {
                    ticketId = Long.parseLong(resultSet.getString("ticket_id"));
                } catch (NumberFormatException e) {
                    return null;
                }
                // 构造groupData数据
                return GroupData.builder()
                        .ticketId(ticketId)
                        .groupType(GroupData.GroupType.GTS_EXTERNAL)
                        .groupId(groupId)
                        .build();
            }
        }
        // 没查到则返回Null
        return null;
    }

    @Override
    public Map<String, GroupData> doQueryStock(Connection conn) throws SQLException {
        // 拉取所有
        final PreparedStatement preparedStatement = conn.prepareStatement("select\n"
                + " unique_value as ticket_id,\n"
                + " group_id\n"
                + "from t_sop_group_create_record\n"
                // 只同步未解散的群
                + "where group_type = 10 and (dismiss_at is null or dismiss_at = '0000-00-00 00:00:00')"
        );
        final HashMap<String, GroupData> res = new HashMap<>();
        // 执行SQL
        try (ResultSet resultSet = preparedStatement.executeQuery()) {
            while (resultSet.next()) {
                long ticketId;
                // 如果捞出来的数据不是工单ID，则跳过
                try {
                    ticketId = Long.parseLong(resultSet.getString("ticket_id"));
                } catch (NumberFormatException e) {
                    continue;
                }
                final String groupId = resultSet.getString("group_id");
                res.put(
                        groupId,
                        GroupData.builder()
                                .ticketId(ticketId)
                                .groupType(GroupData.GroupType.GTS_EXTERNAL)
                                .groupId(groupId)
                                .build()
                );
            }
        }
        return res;
    }

    @Override
    public PreparedStatement createPreparedStatement(Connection conn) throws SQLException {
        // GTS外部群，从Antool获取
        return conn.prepareStatement(
                "select\n"
                        + " unique_value as ticket_id\n"
                        + "from t_sop_group_create_record\n"
                        // 只同步未解散的群, group_type = 10 为一事两群
                        + "where group_type = 10 and group_id = ? "
                        + "and (dismiss_at is null or dismiss_at = '0000-00-00 00:00:00')\n"
                        + "limit 1"
        );
    }
}
