package com.tencent.andata.util.cluster;

import com.google.common.graph.GraphBuilder;
import com.google.common.graph.MutableGraph;
import org.apache.flink.api.java.tuple.Tuple2;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class GraphUtils {

    /**
     * 根据数据对返回连通子图，会有多个
     * @param tuples 工单对列表
     * @return 连通子图，一个子图可以理解成一个工单列表
     */
    public static List<List<Long>> getConnectedSubGraphByTuples(List<Tuple2<Long, Long>> tuples) {
        // 创建一个无向图
        MutableGraph<Long> graph = GraphBuilder
                .undirected()
                .build();

        // 添加顶点和边
        for (Tuple2<Long, Long> tuple : tuples) {
            graph.putEdge(tuple.f0, tuple.f1);
        }

        // 存储已访问的顶点
        Set<Long> visited = new HashSet<>();
        final List<List<Long>> res = new ArrayList<>();
        // 遍历所有顶点，寻找连通子图
        for (Long vertex : graph.nodes()) {
            if (!visited.contains(vertex)) {
                List<Long> component = new ArrayList<>();
                dfs(graph, vertex, visited, component);
                res.add(component);
            }
        }
        return res;
    }

    private static void dfs(MutableGraph<Long> graph, Long v, Set<Long> visited, List<Long> component) {
        if (!visited.contains(v)) {
            visited.add(v);
            component.add(v);
            for (Long adjacent : graph.adjacentNodes(v)) {
                dfs(graph, adjacent, visited, component);
            }
        }
    }

    public static void main(String[] args) {
        List<Tuple2<Long, Long>> a = new ArrayList<>();
        a.add(Tuple2.of(1L,2L));
        a.add(Tuple2.of(1L,3L));
        a.add(Tuple2.of(2L,4L));
        a.add(Tuple2.of(5L,6L));
        a.add(Tuple2.of(5L,7L));
        System.out.println(GraphUtils.getConnectedSubGraphByTuples(a));
    }
}
