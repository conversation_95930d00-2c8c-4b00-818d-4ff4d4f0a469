package com.tencent.andata.util.cluster;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.cluster.AggregateLevel;
import com.tencent.andata.model.cluster.StandardCluster;
import com.tencent.andata.model.cluster.SubGraph;
import com.tencent.andata.model.cluster.TicketTuple;
import org.apache.flink.api.java.tuple.Tuple2;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class Merger {
    private FlinkLog logger = FlinkLog.getInstance();
    public Long baseClusterId;
    public AggregateLevel aggregateLevel;
    public int serviceSceneLevel1Id;
    public int serviceSceneLevel2Id;
    public List<StandardCluster> updateStandardClusters;
    public Map<Long, StandardCluster> standardClusterMap;

    public Merger(Map<Long, StandardCluster> standardClusterMap, Long baseClusterId, AggregateLevel aggregateLevel, int serviceSceneLevel1Id, int serviceSceneLevel2Id) {
        this.logger.info(String.format(
                "初始化Merger：base_id=%s\nlevel1id=%s\nlevel2id=%s\nstandardCluster=%s",
                baseClusterId,
                serviceSceneLevel1Id,
                serviceSceneLevel2Id,
                standardClusterMap
        ));
        this.standardClusterMap = standardClusterMap;
        this.updateStandardClusters = new ArrayList<>();
        this.aggregateLevel = aggregateLevel;
        this.serviceSceneLevel1Id = serviceSceneLevel1Id;
        this.serviceSceneLevel2Id = serviceSceneLevel2Id;
        this.baseClusterId = baseClusterId;
    }

    public void doMerge(List<TicketTuple> ticketTupleData) throws Exception {
        // 生成连通子图
        final List<SubGraph> subGraphs = GraphUtils.getConnectedSubGraphByTuples(ticketTupleData.stream().map(a -> new Tuple2<Long, Long>(a.ticketId1, a.ticketId2)).collect(Collectors.toList())).stream().map(a -> new SubGraph(aggregateLevel, a)).collect(Collectors.toList());

        if (this.standardClusterMap.isEmpty()) {
            // 如果之前没有校准Cluster，则直接生成
            for (SubGraph subGraph : subGraphs) {
                logger.info(String.format(
                        "base_id=%s, 新建子图：%s", this.baseClusterId, subGraph
                ));
                this.newStandardCluster(subGraph.ticketList);
            }
        } else {
            // 已有校准Cluster
            this.mergeSubGraphToStandardCluster(subGraphs);
        }
    }

    public void mergeSubGraphToStandardCluster(List<SubGraph> subGraphs) throws Exception {
        logger.info(String.format(
                "base_id=%s, 合并子图：\n, 合并前：%s\n待合并: %s\n",
                this.baseClusterId,
                this.standardClusterMap.values().stream().map(StandardCluster::getSubGraph).collect(Collectors.toList()),
                subGraphs
        ));
        // 根据子图大小降序排序
        List<StandardCluster> tmpStandardCluster = new ArrayList<>(this.standardClusterMap.values());
        tmpStandardCluster.sort(new Comparator<StandardCluster>() {
            @Override
            public int compare(StandardCluster o1, StandardCluster o2) {
                return Integer.compare(o2.ticketList.size(), o1.ticketList.size());
            }
        });
        subGraphs.sort(new Comparator<SubGraph>() {
            @Override
            public int compare(SubGraph o1, SubGraph o2) {
                return Integer.compare(o2.ticketList.size(), o1.ticketList.size());
            }
        });

        // 全量标志为软删除
        this.standardClusterMap.values().forEach(a -> a.status = 0);

        // 从大到小查看新子图是否包含就子图
        int subGraphIndex = 0;
        // 遍历新子图
        while (subGraphIndex < subGraphs.size()) {
            boolean needCreate = true;
            for (StandardCluster standardCluster : tmpStandardCluster) {
                // 判断是否包含旧子图
                if (ClusterUtils.checkListContain(subGraphs.get(subGraphIndex).ticketList, standardCluster.ticketList)) {
                    needCreate = false;
                    // 更新旧子图
                    standardCluster.status = 1;
                    standardCluster.ticketList = mergeAndRemoveDuplicates(
                            standardCluster.ticketList,
                            subGraphs.get(subGraphIndex).ticketList
                    );
                    break;
                }
            }
            // 存在新的子图，新建Cluster
            if (needCreate) {
                this.newStandardCluster(subGraphs.get(subGraphIndex).ticketList);
            }
            subGraphIndex++;
        }
        // 发送下游，进行数据库更新
        this.updateStandardClusters.addAll(this.standardClusterMap.values());

        logger.info(
                String.format("base_id=%s,合并后子图:%s",
                        this.baseClusterId,
                        this.standardClusterMap
                                .values()
                                .stream()
                                .map(StandardCluster::getSubGraph)
                                .collect(Collectors.toList()))
        );
    }

    public List<Long> mergeAndRemoveDuplicates(List<Long> list1, List<Long> list2) {
        Set<Long> set = new HashSet<>();
        set.addAll(list1);
        set.addAll(list2);

        return new ArrayList<>(set);
    }


    /**
     * 新增Cluster
     *
     * @param ticketList
     */
    private void newStandardCluster(List<Long> ticketList) throws Exception {
        final StandardCluster standardCluster = StandardCluster.builder()
                .baseClusterId(this.baseClusterId)
                .status(1)
                .serviceSceneLevel1Id(this.serviceSceneLevel1Id)
                .serviceSceneLevel2Id(this.serviceSceneLevel2Id)
                // 固定值，根据描述聚合
                .aggregateType("description")
                .aggregateLevel(this.aggregateLevel.value)
                .ticketList(ticketList)
                .build();
        // 初始化标题
        standardCluster.initTitle();
        this.updateStandardClusters.add(standardCluster);
    }

}
