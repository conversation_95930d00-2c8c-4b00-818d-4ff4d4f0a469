package com.tencent.andata.util;

import com.starrocks.connector.flink.row.sink.StarRocksSinkOP;
import com.starrocks.connector.flink.row.sink.StarRocksSinkRowBuilder;
import com.starrocks.connector.flink.table.sink.StarRocksSinkOptions;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.types.Row;

import java.io.IOException;
import java.util.Properties;

/**
 * TODO 暂时使用Utils Sink StarRocks，后续抽象成统一Sink
 */
public class StarRocksUtils {
    public static String SINK_SR_GROUP = "cdc.olap.starrocks.dataware";

    public static StarRocksSinkOptions getSinkOperations(String tableName) throws IOException {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        return StarRocksSinkOptions.builder()
                .withProperty("jdbc-url", rainbowUtils.getStringValue(SINK_SR_GROUP, "jdbc-url"))
                .withProperty("load-url", rainbowUtils.getStringValue(SINK_SR_GROUP, "load-url"))
                .withProperty("database-name", rainbowUtils.getStringValue(SINK_SR_GROUP, "database-name"))
                .withProperty("table-name", tableName)
                .withProperty("username", rainbowUtils.getStringValue(SINK_SR_GROUP, "username"))
                .withProperty("password", rainbowUtils.getStringValue(SINK_SR_GROUP, "password"))
                .build();
    }

    /**
     * 根据Schema 生成StarRocks Transformer
     *
     * @param schema table schema
     * @return transformer
     */
    public static StarRocksSinkRowBuilder<Row> buildTransformerFromSchema(
            TableSchema schema,
            boolean isPrimaryKeyTable
    ) {
        // Schema 不可序列化，因此需要在外部把field捞出来
        final String[] fieldNames = schema.getFieldNames();
        return new StarRocksSinkRowBuilder<Row>() {
            @Override
            public void accept(Object[] objects, Row row) {
                // 基于Schema 从Row里面捞数据
                for (int i = 0; i < fieldNames.length; i++) {
                    objects[i] = row.getField(fieldNames[i]);
                }
                if (isPrimaryKeyTable) {
                    objects[objects.length - 1] = StarRocksSinkOP.UPSERT.ordinal();
                }
            }
        };
    }
}
