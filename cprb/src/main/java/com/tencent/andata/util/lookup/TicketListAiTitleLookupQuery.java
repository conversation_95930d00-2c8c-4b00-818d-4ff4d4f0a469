package com.tencent.andata.util.lookup;

import com.google.common.base.CaseFormat;
import com.tencent.andata.utils.lookup.JDBCLookupQuery;
import com.tencent.andata.utils.lookup.PostgresLookupQuery;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.commons.beanutils.BeanUtils;

import java.sql.Array;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class TicketListAiTitleLookupQuery extends PostgresLookupQuery<List<Long>, List<String>> {
    public TicketListAiTitleLookupQuery(DatabaseConf databaseConf) {
        super(databaseConf);
    }

    @Override
    public PreparedStatement buildStatement(Connection conn) throws SQLException {
        return conn.prepareStatement("");
    }

    @Override
    public List<String> doQuery(PreparedStatement sts, List<Long> longList) throws Exception {
        // 设置参数
        String sql =
                String.format("select ai_title from cprb_summary_embedding where ticket_id in (%s)",
                        String.join(",", longList.stream().map(a -> a.toString()).collect(Collectors.toList()))
                );
        final ResultSet resultSet = this.connection.createStatement().executeQuery(sql);
        List<String> res = new ArrayList<>();
        while (resultSet.next()) {  //行遍历
            res.add(resultSet.getString(1));
        }
        return res;
    }

    @Override
    public List<List<String>> doQueryList(PreparedStatement sts, List<Long> longList, boolean all, int cnt) throws Exception {
        return null;
    }

}
