package com.tencent.andata.util.group.query;

import com.tencent.andata.model.GroupData;
import com.tencent.andata.utils.struct.DatabaseConf;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class InnerOaGroupDataQuery extends MysqlGroupDataQuery<String> {
    public InnerOaGroupDataQuery(DatabaseConf databaseConf) {
        super(databaseConf);
    }

    /**
     * 具体查询数据
     *
     * @param statement sts
     * @param groupId   群ID
     * @return 群数据
     * @throws SQLException ex
     */
    @Override
    public GroupData doQuery(PreparedStatement statement, String groupId) throws SQLException {
        // 设置参数
        statement.setString(1, groupId);
        // 执行SQL
        try (ResultSet resultSet = statement.executeQuery()) {
            while (resultSet.next()) {
                final long ticketId = resultSet.getLong("ticket_id");
                // 构造groupData数据
                return GroupData.builder()
                        .ticketId(ticketId)
                        .groupType(GroupData.GroupType.OA)
                        .groupId(groupId)
                        .build();
            }
        }
        // 没查到则返回Null
        return null;
    }

    /**
     * 拉取存量群映射数据
     *
     * @param conn conn
     * @return groupDataMap
     * @throws SQLException ex
     */
    @Override
    public Map<String, GroupData> doQueryStock(Connection conn) throws SQLException {
        // 拉取半年群数据
        final PreparedStatement preparedStatement = conn.prepareStatement("select "
                + "ticket_id, chat_group_id "
                + "from dwm_incident_ticket_statistic "
                + "where chat_group_id != '' and create_time > '2023-08-01'"
        );
        final HashMap<String, GroupData> res = new HashMap<>();
        // 执行SQL
        try (ResultSet resultSet = preparedStatement.executeQuery()) {
            while (resultSet.next()) {
                final long ticketId = resultSet.getLong("ticket_id");
                final String groupId = resultSet.getString("chat_group_id");
                res.put(
                        groupId,
                        GroupData.builder()
                                .ticketId(ticketId)
                                .groupType(GroupData.GroupType.OA)
                                .groupId(groupId)
                                .build()
                );
            }
        }
        return res;
    }

    /**
     * 根据Sql生成PreparedStatement
     *
     * @param conn conn
     * @return sts
     * @throws SQLException ex
     */
    @Override
    public PreparedStatement createPreparedStatement(Connection conn) throws SQLException {
        // 内部OA群，从工单主题表获取
        return conn.prepareStatement("select "
                + "ticket_id "
                + "from dwm_incident_ticket_statistic "
                + "where chat_group_id = ? limit 1"
        );
    }
}
