package com.tencent.andata.util;

import com.starrocks.connector.flink.StarRocksSink;
import com.starrocks.connector.flink.row.sink.StarRocksSinkRowBuilder;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.map.GptDataCleanMap;
import com.tencent.andata.model.TaskFilterParams;
import com.tencent.andata.sql.CprbInProgressSql;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.PrimitiveArrayTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.types.logical.TimestampType;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class CPRBUtils {

    private static FlinkLog logger = FlinkLog.getInstance();
    private static final String CALLBACK_URL;
    private static final String GET_UNFINISHED_TASKIDS_URL;
    private static final HttpClientUtils httpClientUtils;
    private static final RainbowUtils rainbowUtils;

    static {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        rainbowUtils = new RainbowUtils(properties);
        CALLBACK_URL = rainbowUtils.getStringValue("cprb.backend.conf", "callback_url");
        GET_UNFINISHED_TASKIDS_URL = rainbowUtils.getStringValue("cprb.backend.conf", "get_unfinished_taskids_url");
        httpClientUtils = new HttpClientUtils(300000, 300000);
    }

    /**
     * 任务执行成功回调
     *
     * @param taskId        任务ID
     * @param tagId         标签ID
     * @param confId        配置ID
     * @param operationType 任务类型， 1： 上线。 2： 调试
     */
    public static void taskCallback(
            long taskId,
            long tagId,
            long confId,
            int operationType,
            String cosFileName
    ) throws Exception {
        HashMap<String, Object> requestData = new HashMap<String, Object>() {{
            put("tag_id", tagId);
            put("cos_file_name", cosFileName);
            put("task_id", taskId);
            put("config_id", confId);
            put("operation_type", operationType);
        }};
        final String resStr = httpClientUtils.post(
                CALLBACK_URL,
                requestData
        );
        final JsonNode res = JSONUtils.getJsonNodeByString(resStr, "base_rsp");
        if (res == null) {
            throw new RuntimeException(
                    String.format("response data is invalid: %s", resStr)
            );
        }
        final JsonNode code = res.get("code");
        final JsonNode message = res.get("msg");
        if (code == null || code.asInt() != 0) {
            throw new RuntimeException(
                    String.format("response code is not zero, msg: %s", message.asText())
            );
        }
        logger.info(
                String.format(
                        "[CPRB callback] callback success, task id : %s, tag id:%s, "
                                + "conf id: %s, operation type: %s, cos file: %s",
                        taskId,
                        tagId,
                        confId,
                        operationType,
                        cosFileName
                )
        );
    }

    /***
     * 获取未完成任务id
     * @return .
     * @throws Exception .
     */
    public static TaskFilterParams getUnfinishedTaskIds() throws Exception {
        final String resStr = httpClientUtils.get(GET_UNFINISHED_TASKIDS_URL);
        final JsonNode res = JSONUtils.getJsonNodeByString(resStr, "base_rsp");
        if (res == null) {
            throw new RuntimeException(
                    String.format("[Get Unfinished Task ID] response data is invalid: %s", resStr)
            );
        }
        final JsonNode code = res.get("code");
        final JsonNode message = res.get("msg");
        if (code == null || code.asInt() != 0) {
            throw new RuntimeException(
                    String.format("[Get Unfinished Task ID] response code is not zero, msg: %s", message.asText())
            );
        }
        logger.info(
                String.format(
                        "[CPRB unfinished task id] get success, %s", resStr
                )
        );
        final JsonNode taskIdsJson = JSONUtils.getJsonNodeByString(resStr, "unfinished_task_ids");
        ArrayList<Long> taskIds = new ArrayList<>();
        for (JsonNode node : taskIdsJson) {
            taskIds.add(node.asLong());
        }
        final Long lastId = JSONUtils.getJsonNodeByString(resStr, "latest_task_id").asLong();
        return TaskFilterParams.builder()
                .unfinishedTaskIds(taskIds)
                .lastTaskId(lastId)
                .build();
    }

    /**
     * 共性数据入库数仓
     *
     * @param env        Env
     * @param dataStream ds
     */
    public static void sinkToDataWare(
            FlinkEnvUtils.FlinkEnv env,
            SingleOutputStreamOperator<Row> dataStream,
            String flinkStarRocksTableName,
            String flinkPgsqlTableName
    ) {
        final StreamTableEnvironment tEnv = env.streamTEnv();
        // 筛选出入库字段
        final RowTypeInfo rowTypeInfo = CPRBUtils.buildCprbResultRowTypeInfo();
        final String[] fieldNames = rowTypeInfo.getFieldNames();
        final SingleOutputStreamOperator<Row> ds = dataStream
                // 数据清洗
                .map(new GptDataCleanMap())
                .map(
                        new MapFunction<Row, Row>() {
                            @Override
                            public Row map(Row row) {
                                Row res = Row.withNames(RowKind.INSERT);
                                Arrays.stream(fieldNames).forEach(f -> res.setField(f, row.getField(f)));
                                logger.info(String.format("[sinkToDataWare] 出库数仓: %s", res.toString()));
                                return res;
                            }
                        }
                ).returns(rowTypeInfo);
        // stream to table
        String sinkTableName = "sink_dataware_table";
        tEnv.createTemporaryView(sinkTableName, tEnv.fromDataStream(ds));
        // sink sr & pg
        env.stmtSet().addInsertSql(
                String.format(
                        CprbInProgressSql.CPRB_DATA_INSERT_SQL_SR,
                        flinkStarRocksTableName,
                        sinkTableName
                )
        ).addInsertSql(
                String.format(
                        CprbInProgressSql.CPRB_DATA_INSERT_SQL_PG,
                        flinkPgsqlTableName,
                        sinkTableName
                )
        );
    }

    public static void sinkStarRock(SingleOutputStreamOperator<Row> ds) throws IOException {
        TableSchema schema = TableSchema.builder()
                .field("ticket_id", DataTypes.BIGINT().notNull())
                .field("prompt_id", DataTypes.BIGINT().notNull())
                .field("source_type", DataTypes.STRING().notNull())
                .field("url", DataTypes.STRING())
                .field("service_scene", DataTypes.INT())
                .field("question", DataTypes.STRING())
                .field("prompt_result", DataTypes.STRING())
                .field("reasoning", DataTypes.STRING())
                .field("ticket_create_time", DataTypes.TIMESTAMP())
                .field("update_time", DataTypes.TIMESTAMP())
                .field("service_scene_level1_id", DataTypes.BIGINT())
                .field("service_scene_level2_id", DataTypes.BIGINT())
                .field("service_scene_level3_id", DataTypes.BIGINT())
                .field("service_scene_level4_id", DataTypes.BIGINT())
                .primaryKey("ticket_id", "prompt_id", "source_type")
                .build();
        StarRocksSinkRowBuilder<Row> transformer = StarRocksUtils.buildTransformerFromSchema(schema, false);
        SinkFunction<Row> sink = StarRocksSink.sink(
                schema,
                StarRocksUtils.getSinkOperations("dwd_comm_prob_ticket_classification"),
                transformer
        );
        ds.addSink(sink).name("sink_sr_dwd_comm_prob_ticket_classification");
    }

    public static RowTypeInfo buildCprbResultRowTypeInfo() {
        String[] fields = new String[]{"ticket_id", "source_type", "prompt_id", "url", "service_scene", "question",
                "prompt_result", "reasoning", "ticket_create_time", "update_time", "service_scene_level1_id",
                "service_scene_level2_id", "service_scene_level3_id", "service_scene_level4_id"
        };
        TypeInformation[] types = new TypeInformation[]{
                BasicTypeInfo.LONG_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.LONG_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.INT_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO, BasicTypeInfo.LONG_TYPE_INFO, BasicTypeInfo.LONG_TYPE_INFO,
                BasicTypeInfo.LONG_TYPE_INFO, BasicTypeInfo.LONG_TYPE_INFO
        };

        return new RowTypeInfo(types, fields);
    }
}