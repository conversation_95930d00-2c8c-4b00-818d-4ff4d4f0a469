package com.tencent.andata.util.group.query;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.GroupData;
import org.apache.flink.util.Preconditions;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Enumeration;
import java.util.Map;
import java.util.Properties;

public abstract class JDBCGroupDataQuery<T> implements GroupDataQuery<T>, Serializable {
    private static final FlinkLog logger = FlinkLog.getInstance();
    /*
     常量数据，暂时没配置化
     */
    // 检测链接是否有效的超时时间
    private static final int CONNECTION_CHECK_TIMEOUT_SECONDS = 5;
    // 重试次数
    private static final int RETRY_COUNT = 3;
    /*
    链接属性
     */
    protected String url;
    protected String userName;
    protected String password;
    protected String driverName;
    protected transient Driver loadedDriver;
    protected transient Connection connection;
    protected transient PreparedStatement statement;


    public JDBCGroupDataQuery() {
    }

    public JDBCGroupDataQuery(String driverName, String url, String userName, String password) {
        this.driverName = driverName;
        this.url = url;
        this.userName = userName;
        this.password = password;
    }

    /*
    子类实现具体逻辑
     */
    public abstract GroupData doQuery(PreparedStatement statement, T in) throws SQLException;

    public abstract Map<String, GroupData> doQueryStock(Connection conn) throws SQLException;

    public abstract PreparedStatement createPreparedStatement(Connection conn) throws SQLException;


    /**
     * open
     *
     * @throws SQLException           ex
     * @throws ClassNotFoundException ex
     */
    public void open() throws SQLException, ClassNotFoundException {
        Preconditions.checkNotNull(driverName);
        Preconditions.checkNotNull(url);
        Preconditions.checkNotNull(userName);
        Preconditions.checkNotNull(password);
        this.establishConnectionAndStatement();
    }

    /**
     * close
     *
     * @throws SQLException           ex
     * @throws ClassNotFoundException ex
     */
    public void close() throws SQLException {
        if (this.statement != null) {
            this.statement.close();
        }
        this.closeConnection();
    }

    /**
     * 接口方法
     *
     * @param param 参数
     * @return 群数据
     */
    @Override
    public GroupData query(T param) throws RuntimeException {
        int retry = 0;
        // 允许重试
        for (; retry <= RETRY_COUNT; retry++) {
            try {
                // 清空statement参数
                this.statement.clearParameters();
                // 子类设置参数 & 查询 & 解析
                return this.doQuery(statement, param);
            } catch (SQLException e) {
                logger.error(
                        String.format(
                                "[JDBCGroupDataQuery]JDBC execute query error, retry times = %d, error: %s",
                                retry,
                                e
                        )
                );
                if (retry == RETRY_COUNT) {
                    throw new RuntimeException("Execution of JDBC statement failed.", e);
                }

                try {
                    // 链接失效则重建
                    if (!isConnectionValid()) {
                        this.statement.close();
                        this.closeConnection();
                        this.establishConnectionAndStatement();
                    }
                } catch (SQLException | ClassNotFoundException exception) {
                    logger.error(
                            String.format(
                                    "[JDBCGroupDataQuery]JDBC connection is not valid, "
                                            + "and reestablish connection failed: %s",
                                    exception
                            )
                    );
                    throw new RuntimeException("Reestablish JDBC connection failed", exception);
                }
            }
        }
        throw new RuntimeException("Get group data failed");
    }

    /**
     * 接口方法
     *
     * @return 存量群映射
     */
    @Override
    public Map<String, GroupData> queryStock() throws RuntimeException {
        int retry = 0;
        // 允许重试
        for (; retry <= RETRY_COUNT; retry++) {
            try {
                return this.doQueryStock(this.connection);
            } catch (SQLException e) {
                logger.error(
                        String.format(
                                "[JDBCGroupDataQuery]JDBC execute query error, retry times = %d, error: %s",
                                retry,
                                e
                        )
                );
                if (retry == RETRY_COUNT) {
                    throw new RuntimeException("Execution of JDBC statement failed.", e);
                }

                try {
                    // 链接失效则重建
                    if (!isConnectionValid()) {
                        this.statement.close();
                        this.closeConnection();
                        this.establishConnectionAndStatement();
                    }
                } catch (SQLException | ClassNotFoundException exception) {
                    logger.error(
                            String.format(
                                    "[JDBCGroupDataQuery]JDBC connection is not valid, "
                                            + "and reestablish connection failed: %s",
                                    exception
                            )
                    );
                    throw new RuntimeException("Reestablish JDBC connection failed", exception);
                }
            }
        }
        throw new RuntimeException("Get group stock data failed");
    }

    /**
     * 加载Driver(Mysql/Pgsql)
     *
     * @param driverName name
     * @return driver
     * @throws SQLException           ex
     * @throws ClassNotFoundException ex
     */
    private static Driver loadDriver(String driverName) throws SQLException, ClassNotFoundException {
        Preconditions.checkNotNull(driverName);
        Enumeration<Driver> drivers = DriverManager.getDrivers();
        while (drivers.hasMoreElements()) {
            Driver driver = drivers.nextElement();
            if (driver.getClass().getName().equals(driverName)) {
                return driver;
            }
        }
        // We could reach here for reasons:
        // * Class loader hell of DriverManager(see JDK-8146872).
        // * driver is not installed as a service provider.
        Class<?> clazz = Class.forName(driverName, true, Thread.currentThread().getContextClassLoader());
        try {
            return (Driver) clazz.newInstance();
        } catch (Exception ex) {
            throw new SQLException("Fail to create driver of class " + driverName, ex);
        }
    }

    /**
     * 获取Driver
     *
     * @return Driver
     * @throws SQLException           ex
     * @throws ClassNotFoundException ex
     */
    private Driver getLoadedDriver() throws SQLException, ClassNotFoundException {
        if (loadedDriver == null) {
            loadedDriver = loadDriver(this.driverName);
        }
        return loadedDriver;
    }

    /**
     * 重建conn和statement
     *
     * @throws SQLException           ex
     * @throws ClassNotFoundException ex
     */
    private void establishConnectionAndStatement() throws SQLException, ClassNotFoundException {
        Connection dbConn = this.getOrEstablishConnection();
        statement = this.createPreparedStatement(dbConn);
    }

    /**
     * 获取/重建 链接
     *
     * @return conn
     * @throws SQLException           ex
     * @throws ClassNotFoundException ex
     */
    private Connection getOrEstablishConnection() throws SQLException, ClassNotFoundException {
        if (connection != null) {
            return connection;
        }
        if (this.driverName == null) {
            connection = DriverManager.getConnection(this.url, this.userName, this.password);
        } else {
            Driver driver = getLoadedDriver();
            Properties info = new Properties() {{
                put("user", userName);
                put("password", password);
            }};
            connection = driver.connect(this.url, info);
            if (connection == null) {
                // Throw same exception as DriverManager.getConnection when no driver found to match
                // caller expectation.
                throw new SQLException("No suitable driver found for " + url, "08001");
            }
        }
        return connection;
    }

    /**
     * 判断链接是否有效
     *
     * @return T/F
     * @throws SQLException ex
     */
    private boolean isConnectionValid() throws SQLException {
        return connection != null && connection.isValid(CONNECTION_CHECK_TIMEOUT_SECONDS);
    }

    /**
     * 关闭连接
     */
    private void closeConnection() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                logger.warn(
                        String.format("[JDBCGroupDataQuery]JDBC connection:%s close failed: %s", this.url, e)
                );
            } finally {
                connection = null;
            }
        }
    }
}
