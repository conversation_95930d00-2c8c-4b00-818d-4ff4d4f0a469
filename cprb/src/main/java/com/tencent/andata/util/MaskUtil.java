package com.tencent.andata.util;

import com.tencent.andata.asyncmap.AsyncDataMask;
import com.tencent.andata.asyncmap.DataMaskProcessor;
import com.tencent.andata.map.mask.FieldCleanMap;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class MaskUtil {

    public static Logger logger = LoggerFactory.getLogger(MaskUtil.class);

    public static DataStream<Row> cleanAndMaskFields(DataStream<Row> ds,
                                                     Map<String, String> fieldMap,
                                                     int maxLength,
                                                     int cap,
                                                     int connectTimeout,
                                                     int socketTimeout,
                                                     int asyncTimeOut) {

        // 字段清洗
        SingleOutputStreamOperator<Row> cleanDs = ds.map(new FieldCleanMap(fieldMap, maxLength)).returns(ds.getType());
        List<String> list = new ArrayList<>(fieldMap.values());
        // 脱敏
        return AsyncDataStream
                .unorderedWait(
                        cleanDs,
                        new AsyncDataMask<>(new DataMaskProcessor(
                                list,
                                list,
                                ";;newline;;"),
                                connectTimeout, socketTimeout),
                        // 异步超时时间
                        asyncTimeOut,
                        TimeUnit.SECONDS,
                        // 异步并发数
                        cap
                )
                .returns(cleanDs.getType());
    }
}
