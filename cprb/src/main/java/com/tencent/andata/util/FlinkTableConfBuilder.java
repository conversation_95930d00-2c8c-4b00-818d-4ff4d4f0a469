package com.tencent.andata.util;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.util.Preconditions;

import java.util.HashMap;

public class FlinkTableConfBuilder {

    private StreamTableEnvironment env;
    private HashMap<String, String> params = new HashMap<>();

    public FlinkTableConfBuilder setEnv(StreamTableEnvironment env) {
        this.env = env;
        return this;
    }

    public FlinkTableConfBuilder setParams(String key, String value) {
        params.put(key, value);
        return this;
    }

    public Configuration build() {
        Preconditions.checkNotNull(env);
        Configuration configuration = env.getConfig().getConfiguration();
        addDefaultConf(configuration);
        return configuration;
    }

    private void addDefaultConf(Configuration configuration) {
        //开启微批模式
        configuration.setString("table.exec.mini-batch.size", "5000");
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "3 s");
        // 状态保留3天
        configuration.setString("table.exec.state.ttl", "259200000");
        configuration.setString("execution.runtime-mode", "streaming");
        configuration.setString("execution.checkpointing.interval", "30s");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("table.exec.sink.upsert-materialize", "AUTO");
        configuration.setString("table.dynamic-table-options.enabled", "true");
        configuration.setString("table.exec.legacy-cast-behaviour", "enabled");
    }
}
