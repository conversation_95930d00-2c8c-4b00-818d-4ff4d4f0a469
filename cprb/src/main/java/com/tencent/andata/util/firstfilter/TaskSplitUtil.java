package com.tencent.andata.util.firstfilter;

import com.tencent.andata.utils.TimeUtil;
import org.apache.flink.types.Row;

import java.time.*;
import java.util.ArrayList;
import java.util.List;

public class TaskSplitUtil {

    public static String[] splitField(Row row, String field, String separator) {
        Object sourceTypeObj = row.getField(field);
        if (sourceTypeObj == null) {
            return new String[0];
        }
        return sourceTypeObj.toString().split(separator);
    }

    // 时间切割
    public static List<Instant> splitTime(Row row, String startTimeField, String endTimeField) {
        ArrayList<Instant> list = new ArrayList<>();
        Instant instantStart = TimeUtil.convert2Instant((LocalDateTime) row.getField(startTimeField));
        Instant instantEnd = TimeUtil.convert2Instant((LocalDateTime) row.getField(endTimeField));
        ZonedDateTime startDateTime = TimeUtil.convert2ZonedDateTime(instantStart);
        ZonedDateTime endDateTime = TimeUtil.convert2ZonedDateTime(instantEnd);
        // 计算两个日期之间的月数差异
        int monthsBetween = Period.between(startDateTime.toLocalDate(), endDateTime.toLocalDate()).getMonths();
        // 打印出这段时间内的每个月份
        YearMonth startMonth = YearMonth.from(startDateTime);
        YearMonth endMonth = YearMonth.from(endDateTime);
        while (!startMonth.isAfter(endMonth)) {
            // 将YearMonth转换为当月的第一天
            LocalDate firstDayOfMonth = startMonth.atDay(1);
            // 将LocalDate转换为当天开始时的Instant（通常是午夜）
            Instant instant = TimeUtil.convert2Instant(firstDayOfMonth);
            list.add(instant);
            startMonth = startMonth.plusMonths(1);
        }
        return list;
    }

    // 时间切割
    public static List<Instant> splitTimeByDay(Row row, String startTimeField, String endTimeField) {
        ArrayList<Instant> list = new ArrayList<>();
        Instant instantStart = TimeUtil.convert2Instant((LocalDateTime) row.getField(startTimeField));
        Instant instantEnd = TimeUtil.convert2Instant((LocalDateTime) row.getField(endTimeField));
        LocalDate startDateTime = TimeUtil.convert2LocalDate(instantStart);
        LocalDate endDateTime = TimeUtil.convert2LocalDate(instantEnd);
        while (!startDateTime.isAfter(endDateTime)) {
            Instant instant = TimeUtil.convert2Instant(startDateTime);
            list.add(instant);
            startDateTime = startDateTime.plusDays(1);
        }
        return list;
    }
}
