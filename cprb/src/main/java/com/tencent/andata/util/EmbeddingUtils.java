package com.tencent.andata.util;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

public class EmbeddingUtils {

//    private static FlinkLog logger = FlinkLog.getInstance();
    private static Logger logger = LoggerFactory.getLogger(EmbeddingUtils.class);
    private static final String EMBEDDING_URL;
    private static final HttpClientUtils httpClientUtils;
    private static final RainbowUtils rainbowUtils;

    static {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        rainbowUtils = new RainbowUtils(properties);
        EMBEDDING_URL = rainbowUtils.getStringValue("cprb.embedding.conf", "url");
        httpClientUtils = new HttpClientUtils(300000, 300000);
    }


    public static List<Double> embedding(String content) throws Exception {
        HashMap<String, Object> requestData = new HashMap<String, Object>() {{
            put("query", new ArrayList<String>() {{
                add(content);
            }});
        }};
        final String resStr = httpClientUtils.post(
                EMBEDDING_URL,
                requestData
        );
        final JsonNode res = JSONUtils.getJsonNodeByString(resStr, "base_rsp");
        if (res == null) {
            throw new RuntimeException(
                    String.format("response data is invalid: %s", resStr)
            );
        }
        final JsonNode code = res.get("code");
        final JsonNode message = res.get("msg");
        if (code == null || code.asInt() != 0) {
            throw new RuntimeException(
                    String.format("response code is not zero, msg: %s", message.asText())
            );
        }
//        logger.info(
//                String.format(
//                        "embedding success: %s",
//                        res
//                )
//        );
        JsonNode data = JSONUtils.getJsonNodeByString(resStr, "data");
        JsonNode node = data.get("arrays").get(0).get("items");
        ArrayList<Double> list = new ArrayList<>(node.size());
        Iterator<JsonNode> elements = node.elements();
        while (elements.hasNext()) {
            list.add(elements.next().asDouble());
        }
        return list;
    }
}