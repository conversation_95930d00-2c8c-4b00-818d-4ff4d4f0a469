package com.tencent.andata.process;

import com.tencent.andata.enums.CprbTaskOperation;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

/**
 * 根据任务类型分发数据
 */
public class TaskDataDispatcherProcessFunction extends ProcessFunction<Row, Row> {
    @Override
    public void processElement(
            Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector
    ) throws Exception {
        final Object taskTypeObj = row.getField("task_type");
        if (taskTypeObj == null) {
            return;
        }
        int taskType = (int)taskTypeObj;
        final CprbTaskOperation cprbTaskOperation = CprbTaskOperation.valueOf(taskType);
        // 数据分发测流
        context.output(new OutputTag<Row>(cprbTaskOperation.name){}, row);
    }
}
