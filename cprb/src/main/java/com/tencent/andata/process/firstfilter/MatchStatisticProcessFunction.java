package com.tencent.andata.process.firstfilter;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.firstfilter.MatchMetaData;
import com.tencent.andata.util.CPRBUtils;
import com.tencent.andata.util.firstfilter.TaskSplitUtil;
import com.tencent.andata.util.keyby.CombineKeyByUtil;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedCoProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.List;

public class MatchStatisticProcessFunction extends KeyedCoProcessFunction<String, Row, MatchMetaData, Row> {

    private final FlinkLog flinkLog = FlinkLog.getInstance();
    private static final Logger logger = LoggerFactory.getLogger(MatchStatisticProcessFunction.class);
    private MapState<String, MatchMetaData> taskPartitionMetaMap;
    private ValueState<Integer> countDown;
    private ValueState<Row> task;
    private final CombineKeyByUtil combineKeyByUtil;

    public MatchStatisticProcessFunction(CombineKeyByUtil combineKeyByUtil) {
        this.combineKeyByUtil = combineKeyByUtil;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        MapStateDescriptor<String, MatchMetaData> descriptor = new MapStateDescriptor<String, MatchMetaData>(
                "taskPartitionMetaDesc",
                String.class,
                MatchMetaData.class
        );
        ValueStateDescriptor<Integer> countDownDesc = new ValueStateDescriptor<Integer>(
                "countDownDesc",
                Integer.class
        );
        ValueStateDescriptor<Row> taskDesc = new ValueStateDescriptor<Row>(
                "taskDesc",
                Row.class
        );
        taskPartitionMetaMap = getRuntimeContext().getMapState(descriptor);
        countDown = getRuntimeContext().getState(countDownDesc);
        task = getRuntimeContext().getState(taskDesc);
    }

    @Override
    public void processElement1(Row row, KeyedCoProcessFunction<String, Row, MatchMetaData, Row>.Context context, Collector<Row> collector) throws Exception {
        // 做切割
        String[] split = TaskSplitUtil.splitField(row, "source_type", ",");
        List<Instant> list = null;
        switch (combineKeyByUtil.fieldList.get(1).fieldType.type) {
            case Instant_Month:
                list = TaskSplitUtil.splitTime(row, "start_run_time", "end_run_time");
                break;
            case Instant_Day:
                list = TaskSplitUtil.splitTimeByDay(row, "start_run_time", "end_run_time");
                break;
        }
        String taskId = String.valueOf(row.getField("task_id"));
        flinkLog.info(String.format("收到调试任务: task_id: %s , row: %s", taskId, row), "cprb_task-" + taskId);
        int count = 0;
        for (String source : split) {
            row.setField("partition_source", source);
            for (Instant time : list) {
                row.setField("partition_time", time);
                String key = combineKeyByUtil.getKey(row);
                if (!taskPartitionMetaMap.contains(key)) {
                    taskPartitionMetaMap.put(key, MatchMetaData.builder()
                            .taskId(taskId)
                            .partitionKey(key)
                            .matchCount(0)
                            .filterCount(0)
                            .finished(false)
                            .build());
                }
                count++;
            }
        }
        countDown.update(count);
        task.update(row);
    }

    @Override
    public void processElement2(MatchMetaData matchMetaData, KeyedCoProcessFunction<String, Row, MatchMetaData, Row>.Context context, Collector<Row> collector) throws Exception {
        flinkLog.info("收到元数据: " + matchMetaData, "cprb_task-" + matchMetaData.taskId);
        MatchMetaData metaData;
        // 元数据比调试任务先到的情况下，直接更新即可
        if (!taskPartitionMetaMap.contains(matchMetaData.partitionKey)) {
            metaData = matchMetaData;
        } else {
            metaData = taskPartitionMetaMap.get(matchMetaData.partitionKey);
            metaData.matchCount += matchMetaData.matchCount;
            metaData.filterCount += matchMetaData.filterCount;
            metaData.finished = matchMetaData.finished;
        }
        taskPartitionMetaMap.put(metaData.partitionKey, metaData);
        if (matchMetaData.finished) {
            Integer count = countDown.value();
            count--;
            countDown.update(count);
            if (count == 0) {
                // 触发回调
                if (noDataMatched()) {
                    callBackCprbBackEnd();
                }
                flinkLog.info(String.format("任务:%s, 任务id: %s 处理完毕", task, matchMetaData.taskId),
                        "cprb_task-" + matchMetaData.taskId);
                // 这里可以清除状态数据了
                task.clear();
                taskPartitionMetaMap.clear();
                countDown.clear();
            }
        }
    }

    public boolean noDataMatched() throws Exception {
        int allCount = 0;
        for (MatchMetaData metaData : taskPartitionMetaMap.values()) {
            allCount += metaData.filterCount;
        }
        return allCount == 0;
    }

    public void callBackCprbBackEnd() throws Exception {
        Row task = this.task.value();
        long taskId = Long.parseLong(String.valueOf(task.getField("task_id")));
        long tagId = Long.parseLong(String.valueOf(task.getField("tag_id")));
        long confId = Long.parseLong(String.valueOf(task.getField("config_id")));
        int operationType = Integer.parseInt(String.valueOf(task.getField("operation_type")));
        flinkLog.info(String.format("Task ID:%d NO_DATA", taskId), "cprb_task-" + taskId);
        CPRBUtils.taskCallback(
                taskId,
                tagId,
                confId,
                operationType,
                "no_data.csv");
    }
}
