package com.tencent.andata.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import lombok.val;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.joda.time.Instant;

import java.io.IOException;
import java.util.*;

public class GptDataProcessFunction extends ProcessFunction<Row, Row> {

    private FlinkLog logger = FlinkLog.getInstance();
    private final HttpClientUtils httpClientUtils;
    private final int retryCount;
    private static final String CONF_GROUP = "cprb.gpt.conf";
    private final String CONSUMER_NAME;
    private final String CONSUMER_ID;
    private final String URL;
    private final HashMap<String, Object> params = new HashMap<>();
    private final HashMap<String, String> headers = new HashMap<>();
    private final String requestWay;
    private final String token;
    private final String defaultModelType;
    private transient ValueStateDescriptor<String> fnCallCache;
    private transient ValueStateDescriptor<Long> cacheLastUpdateTime;

    public GptDataProcessFunction(
            int retryCount
    ) throws IOException {
        this.httpClientUtils = new HttpClientUtils(
                300000,
                300000
        );
        // 参数初始化
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        this.CONSUMER_NAME = rainbowUtils.getStringValue(CONF_GROUP, "consumer_name");
        this.CONSUMER_ID = rainbowUtils.getStringValue(CONF_GROUP, "consumer_id");
        this.URL = rainbowUtils.getStringValue(CONF_GROUP, "url");
        this.requestWay = rainbowUtils.getStringValue(CONF_GROUP, "request_way");
        this.token = rainbowUtils.getStringValue(CONF_GROUP, "token");
        this.defaultModelType = rainbowUtils.getStringValue(CONF_GROUP, "model_type");
        // 重试次数
        this.retryCount = retryCount;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.fnCallCache = new ValueStateDescriptor<>("fn_call_cache", String.class);
        this.cacheLastUpdateTime = new ValueStateDescriptor<>("cache_last_update_time", Long.class);
    }

    @Override
    public void processElement(
            Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector
    ) throws Exception {
        // 从Row获取调用接口所需的数据
        List<String> questions = this.getQuestionFromRow(row);
        String promptId = this.getPromptIdFromRow(row);
        final String requestId = UUID.randomUUID().toString();
        boolean useFunctionCall = this.isFunctionCall(row);
        if (useFunctionCall) {
            params.put("parameters", new ObjectMapper().convertValue(
                    JSONUtils.getJsonNodeByString(this.getFunctionCallParams()), Map.class));
        }
        String modelType = Objects.requireNonNull(row.getField("model_type")).toString();
        modelType = modelType.isEmpty() ? defaultModelType : modelType;
        // 封装请求
        params.put("data", questions);
        params.put("prompt_id", promptId);
        params.put("request_way", requestWay);
        params.put("model_type", modelType);
        // headers
        headers.put("x-consumer-username", CONSUMER_NAME);
        headers.put("x-consumer-id", CONSUMER_ID);
        headers.put("Authorization", "Bearer " + token);
        headers.put("requestid", requestId);
        headers.put("request-id", requestId);

        // 不设置会出现乱码
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json;charset=UTF-8");
        // 允许重试
        for (int i = 0; i < this.retryCount; i++) {
            // 发送请求
            final String res;
            try {
                res = httpClientUtils.post(URL, params, headers);
            } catch (Exception e) {
                logger.warn(
                        String.format(
                                "[Gpt Process]http post error: %s\nurl:%s\nparams:%s\ntry number:%s",
                                e,
                                URL,
                                params,
                                i
                        )
                );
                continue;
            }
            JsonNode resp = null;
            try {
                resp = JSONUtils.getJsonNodeByString(res);
            } catch (JsonProcessingException e) {
                logger.warn(
                        String.format(
                                "[Gpt Process]invalid response json: %s\nurl:%s\nparams:%s\ntry number:%s",
                                res,
                                URL,
                                params,
                                i
                        )
                );
                continue;
            }
            final JsonNode resNode = resp.get("answer");
            if (resNode == null) {
                logger.warn(
                        String.format(
                                "[Gpt Process]response not have answer: %s\nurl:%s\nparams:%s\ntry number:%s",
                                res,
                                URL,
                                params,
                                i
                        )
                );
                continue;
            }
            row.setField("gpt_answer", resNode.toString());
            // 保存现场日志
            logData(row, requestId);
            collector.collect(row);
            break;
        }
    }

    private void logData(Row row, String uuid) {
        StringBuilder stringBuilder = new StringBuilder("{\n");
        for (String filed : Objects.requireNonNull(row.getFieldNames(true))) {
            stringBuilder.append(String.format("%s: %s,\n", filed, row.getField(filed)));
        }
        stringBuilder.append("}");
        logger.info(String.format(
                "GPTProcess data: %s",
                stringBuilder.toString()
        ), uuid);
    }

    private List<String> getQuestionFromRow(Row row) {
        return new ArrayList<>(Collections.singleton(row.getField("msg").toString()));
    }

    private String getPromptIdFromRow(Row row) {
        return row.getField("prompt_group_id").toString();
    }

    private boolean isFunctionCall(Row row) {
        try {
            int status = Integer.parseInt(
                    Objects.requireNonNull(row.getField("function_call_active_status")).toString());
            return status == 1;
        } catch (Exception e) {
            logger.warn("获取function_call_active_status字段失败");
            return false;
        }
    }

    private String getFunctionCallParams() throws IOException {
        ValueState<String> fnCallCacheState = getRuntimeContext().getState(this.fnCallCache);

        long millsNow = Instant.now().getMillis();
        ValueState<Long> last = getRuntimeContext().getState(this.cacheLastUpdateTime);
        if (fnCallCacheState.value() != null && !fnCallCacheState.value().isEmpty()
                && (millsNow - last.value() <= 1000 * 120)) { // 2min失效
            return fnCallCacheState.value();
        }
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        String functionCall = rainbowUtils.getStringValue(CONF_GROUP, "function_call");
        fnCallCacheState.update(functionCall);
        last.update(millsNow);
        return functionCall;
    }


}