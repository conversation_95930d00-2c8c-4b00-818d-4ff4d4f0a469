package com.tencent.andata.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.CvmInstanceData;
import com.tencent.andata.model.CvmZoneData;
import com.tencent.andata.util.CvmZoneAndInstanceUtil;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class CvmZoneAndInstanceProcessFunction extends ProcessFunction<Row, Row> {
    public static FlinkLog logger = FlinkLog.getInstance();

    /**
     * 缓存数据
     */
    public static class CacheData implements Serializable {
        public long dataTimestamp;
        // 可用区映射<zoneName, data>
        public Map<String, CvmZoneData> zoneDataMap;
        // 境内/境外 机型名 机型Data映射
        public Map<String, Map<String, CvmInstanceData>> instanceData;

        @Override
        public String toString() {
            return "CacheData{" +
                    "dataTimestamp=" + dataTimestamp +
                    ", zoneDataMap=" + zoneDataMap +
                    ", instanceData=" + instanceData +
                    '}';
        }
    }

    public CacheData cacheData;
    public Pattern zonePattern;

    public CvmZoneAndInstanceProcessFunction() throws Exception {
        // 初始化的时候读取数据
        initOrUpdateCache();
        logger.info(String.format(
                "[CvmZoneAndInstanceProcessFunction]初始化获取CVM机型&园区: %s",
                cacheData
        ));
    }


    @Override
    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        initOrUpdateCache();
        Long ticketId = (Long) (row.getField("ticket_id"));
        final String reply = row.getField("reply").toString();
        final Matcher zoneMatcher = zonePattern.matcher(reply);
        CvmZoneData cvmZoneData;
        // 获取可用区
        if (zoneMatcher.find()) {
            final String zone = CvmZoneAndInstanceUtil.replaceZoneNumber(zoneMatcher.group());
            cvmZoneData = cacheData.zoneDataMap.getOrDefault(
                    zone,
                    CvmZoneData.builder()
                            .zoneName(zone)
                            .regionType("境内")
                            .typeName("")
                            .build()
            );
        } else {
            // 可用区不存在则默认境内
            cvmZoneData = CvmZoneData.builder()
                    .regionType("境内")
                    .zoneName("")
                    .typeName("")
                    .build();
        }

        // 获取该区域机型列表
        final String regionType = cvmZoneData.regionType;
        final Map<String, CvmInstanceData> instanceMap = cacheData.instanceData.getOrDefault(
                regionType,
                new HashMap<>()
        );
        // 根据机型列表生成正则
        final Pattern instancePattern = CvmZoneAndInstanceUtil.buildInstancePattern(
                new ArrayList<>(instanceMap.keySet())
        );
        final Matcher instanceMatcher = instancePattern.matcher(reply);
        CvmInstanceData cvmInstanceData;
        if (instanceMatcher.find()) {
            final String instance = instanceMatcher.group();
            cvmInstanceData = instanceMap.getOrDefault(
                    instance,
                    CvmInstanceData.builder().instanceType(instance).type2Name("").build()
            );
        } else {
            cvmInstanceData = CvmInstanceData.builder().instanceType("").type2Name("").build();
        }
        final Row res = Row.withNames();
        res.setField("ticket_id", ticketId);
        res.setField("is_main_area", cvmZoneData.isMainZone());
        res.setField("is_main_model", cvmInstanceData.isMainInstance());
        res.setField("machine_area", cvmZoneData.zoneName);
        res.setField("machine_model_list", cvmInstanceData.instanceType);
        res.setField("region_type", cvmZoneData.regionType);
        res.setField("zone_type_name", cvmZoneData.typeName);
        res.setField("instance_type_name", cvmInstanceData.type2Name);

        collector.collect(
                res
        );
    }

    /**
     * 初始化 or 更新 缓存数据
     */
    public void initOrUpdateCache() throws Exception {
        if (cacheData == null || CvmZoneAndInstanceUtil.needQuery(cacheData.dataTimestamp)) {
            try {
                cacheData = CvmZoneAndInstanceUtil.getZoneAndInstanceData(LocalDate.now().toString());
                logger.info(String.format(
                        "[CvmZoneAndInstanceProcessFunction]数据更新。CVM机型&园区: %s",
                        cacheData
                ));
            } catch (Exception e) {
                logger.error(
                        String.format(
                                "[CvmZoneAndInstanceProcessFunction]获取CVM机型&园区数据失败:%s",
                                e.getMessage()
                        )
                );
                e.printStackTrace();
                throw e;
            }
            // 可用区正则
            zonePattern = CvmZoneAndInstanceUtil.buildZonePattern(
                    new ArrayList<>(cacheData.zoneDataMap.keySet())
            );
        }
    }

}
