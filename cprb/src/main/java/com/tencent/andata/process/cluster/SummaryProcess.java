package com.tencent.andata.process.cluster;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.util.PromptUtils;
import com.tencent.andata.util.SentenceCleanUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class SummaryProcess extends ProcessFunction<Row, Row> {

    private static Logger logger = LoggerFactory.getLogger(SummaryProcess.class);
    private static ObjectMapper mapper = new ObjectMapper();
    private String srcField;
    private String dstField;
    private String promptType;
    private transient PromptUtils promptUtils = null;
    private int retryTimes;

    public SummaryProcess(String srcField, String dstField, String promptType, int retryTimes) throws IOException {
        this.srcField = srcField;
        this.dstField = dstField;
        this.promptType = promptType;
        this.retryTimes = retryTimes;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        if (promptUtils == null) {
            promptUtils = new PromptUtils();
        }
    }

    @Override
    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        Object obj = row.getField(srcField);
        if (obj == null) {
            return;
        }
        String data = obj.toString();
        if (StringUtils.isEmpty(data)) {
            return;
        }
        String summary = "";
        try {
            summary = promptUtils.getTicketSummary(data, PromptUtils.PromptType.valueOf(promptType));
        } catch (Exception e) {
            logger.error("prompt 调用error: {}", e.toString());
            // 清理了之后重试一下
            String[] split = data.split("\n");
            StringBuilder clean = new StringBuilder();
            for (String s : split) {
                clean.append(SentenceCleanUtil.sentenceClean(s, s.length()));
                clean.append("\n");
            }
            String cleanData = StringUtils.stripEnd(clean.toString(), "\n");
            logger.error("the source data: {}, cleanData: {}", data, cleanData);
            // 这里如果失败了说明是超时之类的问题，加一个重试
            for (int i = 0; i < retryTimes; i++) {
                try {
                    summary = promptUtils.getTicketSummary(cleanData, PromptUtils.PromptType.valueOf(promptType));
                    break;
                } catch (Exception err) {
                    logger.error("error重试: {}", err.toString());
                }
            }
            if (StringUtils.isEmpty(summary)) {
                throw new Exception("重试失败");
            }
        }
        row.setField(dstField, StringUtils.strip(summary, "\""));
        collector.collect(row);
    }
}
