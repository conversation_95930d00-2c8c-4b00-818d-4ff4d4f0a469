package com.tencent.andata.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.GroupData;
import com.tencent.andata.util.group.OneThingTwoGroupFacade;
import com.tencent.andata.util.group.query.GroupDataQuery;
import org.apache.commons.lang.ArrayUtils;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.ResultTypeQueryable;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.runtime.types.TypeInfoLogicalTypeConverter;
import org.apache.flink.table.runtime.typeutils.ExternalTypeInfo;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.BigIntType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.VarCharType;
import org.apache.flink.table.types.utils.LegacyTypeInfoDataTypeConverter;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * 工单群消息关联
 */
public class GroupTicketIdProcessFunction extends ProcessFunction<Row, Row> implements ResultTypeQueryable<Row> {
    private static final FlinkLog logger = FlinkLog.getInstance();

    public OneThingTwoGroupFacade oneThingTwoGroupFacade;
    private final TypeInformation<Row> srcTypeInfo;

    public GroupTicketIdProcessFunction(
            int groupDataCacheCnt,
            List<GroupDataQuery<String>> groupDataQueryList,
            TypeInformation<Row> srcTypeInfo) {
        this.oneThingTwoGroupFacade = new OneThingTwoGroupFacade(
                groupDataCacheCnt,
                groupDataQueryList
        );
        this.srcTypeInfo = srcTypeInfo;
    }

    @Override
    public void processElement(
            Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector
    ) throws Exception {
        final String groupId = row.getField("msg_source_id").toString();
        final GroupData data = this.oneThingTwoGroupFacade.getDataByGroupId(groupId);
        // 没查到直接跳过，只需要一事两群数据
        if (data == null) {
            return;
        }
        final Row res = Row.withNames(RowKind.INSERT);
        for (String field : Objects.requireNonNull(row.getFieldNames(true))) {
            res.setField(field, row.getField(field));
        }
        res.setField("ticket_id", data.ticketId);
        res.setField("group_type", data.groupType.toString());
        collector.collect(res);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        oneThingTwoGroupFacade.open();
    }

    @Override
    public void close() throws Exception {
        super.close();
        oneThingTwoGroupFacade.close();
    }

    @Override
    public TypeInformation<Row> getProducedType() {
        RowType rowType = (RowType) ((ExternalTypeInfo) srcTypeInfo).getDataType().getLogicalType();
        final DataType dataType = ((ExternalTypeInfo) srcTypeInfo).getDataType();
        // 增加两个字段
        final String[] fieldNames = Stream.concat(
                rowType.getFields().stream().map(RowType.RowField::getName),
                Arrays.stream(new String[]{"ticket_id", "group_type"})
        ).toArray(String[]::new);
        final TypeInformation<?>[] fieldTypes = Stream.concat(
                dataType.getChildren().stream().map(LegacyTypeInfoDataTypeConverter::toLegacyTypeInfo),
                Arrays.stream(new TypeInformation<?>[]{BasicTypeInfo.LONG_TYPE_INFO, BasicTypeInfo.STRING_TYPE_INFO})
        ).toArray(TypeInformation[]::new);
        return new RowTypeInfo(fieldTypes, fieldNames);
    }
}
