package com.tencent.andata.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.TagConfig;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.Map;
import java.util.Objects;

public class InProgressTicketRelateTagProcessFunction extends KeyedBroadcastProcessFunction<String, Row, Row, Row> {
    public static FlinkLog logger = FlinkLog.getInstance();

    public MapStateDescriptor<Long, TagConfig> tagConfigMapDescriptor = new MapStateDescriptor<>(
            "tagConfigMapState",
            BasicTypeInfo.LONG_TYPE_INFO,
            TypeInformation.of(new TypeHint<TagConfig>() {
            }));

    /**
     * 处理工单数据主流，对Tag进行关联
     *
     * @param row             工单数据
     * @param readOnlyContext context
     * @param collector       collector
     * @throws Exception ex
     */
    @Override
    public void processElement(
            Row row,
            KeyedBroadcastProcessFunction<String, Row, Row, Row>.ReadOnlyContext readOnlyContext,
            Collector<Row> collector
    ) throws Exception {
        // TODO 使用广播流会存在问题：数据先来，广播数据后来，会导致数据没有匹配上就被丢掉了，后续使用lookup join
        // 工单来源
        String ticketSource = (String) (row.getField("source_type"));
        if (ticketSource == null) {
            return;
        }
        // 遍历线上标签
        for (Map.Entry<Long, TagConfig> entry : readOnlyContext
                .getBroadcastState(tagConfigMapDescriptor)
                .immutableEntries()) {
            final TagConfig tagConfig = entry.getValue();
            final String tagSource = tagConfig.getSourceType();
            // 判断工单来源是否属于标签对应的来源
            if (!tagSource.contains(ticketSource)) {
                continue;
            }
            // 标签数据(初筛条件)组合工单数据发往下游
            collector.collect(embedTagDataToTicket(row, tagConfig));
        }
    }

    /**
     * 处理广播数据，将标签缓存到State中
     *
     * @param row       标签数据
     * @param context   context
     * @param collector collector
     * @throws Exception ex
     */
    @Override
    public void processBroadcastElement(
            Row row,
            KeyedBroadcastProcessFunction<String, Row, Row, Row>.Context context,
            Collector<Row> collector
    ) throws Exception {
        // 生成Config Bean
        final TagConfig tagConfig = TagConfig.buildFromRow(row);
        logger.info(String.format("[tag broadcast : %s, %s]", row.getKind(), tagConfig));
        // 根据类型 增加/删除 状态数据
        switch (row.getKind()) {
            case INSERT:
            case UPDATE_AFTER:
                context.getBroadcastState(tagConfigMapDescriptor).put(tagConfig.configId, tagConfig);
                break;
            case UPDATE_BEFORE:
            case DELETE:
                context.getBroadcastState(tagConfigMapDescriptor).remove(tagConfig.configId);
        }
    }

    /**
     * 将标签配置数据和工单数据组合
     *
     * @param ticketData 工单数据
     * @param tagConfig  标签数据
     * @return 组合数据
     */
    public Row embedTagDataToTicket(Row ticketData, TagConfig tagConfig) {
        final Row res = Row.withNames();
        // 原始工单字段
        for (String key : Objects.requireNonNull(ticketData.getFieldNames(true))) {
            res.setField(key, ticketData.getField(key));
        }
        // 标签配置数据
        res.setField("forward_filter", tagConfig.forwardFilter);
        res.setField("reverse_filter", tagConfig.reverseFilter);
        res.setField("ticket_service_scene_filter", tagConfig.ticketServiceSceneFilter);
        res.setField("config_id", tagConfig.configId);
        res.setField("prompt_group_id", tagConfig.promptGroupId);
        res.setField("tag_id", tagConfig.tagId);
        res.setField("gpt_answer", "");
        res.setField("prompt_id", "");
        res.setField("prompt_result", "");
        res.setField("reasoning", "");
        res.setField("model_type", tagConfig.modelType);
        res.setField("function_call_active_status", tagConfig.functionCallActiveStatus);
        return res;
    }
}
