package com.tencent.andata.process;

import com.alibaba.excel.EasyExcel;
import com.qcloud.cos.model.UploadResult;
import com.tencent.andata.convert.TimestampConverter;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.CprbTaskExcelData;
import com.tencent.andata.model.CprbTaskInfo;
import com.tencent.andata.model.CprbTaskResult;
import com.tencent.andata.util.CosUtil;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.poi.ss.SpreadsheetVersion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Properties;

public class DebugTaskGroupProcess extends KeyedProcessFunction<Long, CprbTaskInfo, CprbTaskResult> {
    public static FlinkLog logger = FlinkLog.getInstance();

    public static ObjectMapper mapper = new ObjectMapper();
    private transient CosUtil cosUtil;
    private ListState<CprbTaskExcelData> flowState;
    private ValueState<Boolean> finishState;
    private final Properties rainbowProperties;

    public DebugTaskGroupProcess() throws IOException {
        this.rainbowProperties = PropertyUtils.loadProperties("env.properties");
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        RainbowUtils rainbowUtils = new RainbowUtils(rainbowProperties);
        this.cosUtil = CosUtil.builder()
                .setSecretId(rainbowUtils.getStringValue("cprb.cos.conf", "secret_id"))
                .setSecretKey(rainbowUtils.getStringValue("cprb.cos.conf", "secret_key"))
                .setBucketName(rainbowUtils.getStringValue("cprb.cos.conf", "bucket_name"))
                .setRegion(rainbowUtils.getStringValue("cprb.cos.conf", "region"))
                .setDir(rainbowUtils.getStringValue("cprb.cos.conf", "dir"))
                .build();
        // 这里先预热一下，如果链接无法初始化直接抛出异常
        this.cosUtil.clientWarm();
        ListStateDescriptor<CprbTaskExcelData> descriptor = new ListStateDescriptor<>(
                "flowState", TypeInformation.of(new TypeHint<CprbTaskExcelData>() {
        }));
        ValueStateDescriptor<Boolean> finishStateDesc = new ValueStateDescriptor<>(
                "finishStateDesc", TypeInformation.of(new TypeHint<Boolean>() {
        }));
        // 保存所有流水状态
        flowState = getRuntimeContext().getListState(descriptor);
        // 本次是否下发
        finishState = getRuntimeContext().getState(finishStateDesc);
        this.initCellMaxTextLength();
    }

    private void initCellMaxTextLength() {
        SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
        if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
            Field field;
            try {
                field = excel2007.getClass().getDeclaredField("_maxTextLength");
                field.setAccessible(true);
                field.set(excel2007, Integer.MAX_VALUE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void processElement(CprbTaskInfo row,
                               KeyedProcessFunction<Long, CprbTaskInfo, CprbTaskResult>.Context context,
                               Collector<CprbTaskResult> collector) throws Exception {
        initState();
        // 将行数据存储到键控状态中
        addData(row);
        if (finishState.value()) {
            // 触发向下游发送
            // 这里有可能不会下发，如果流水状态没有发生改变
            emit(row, context, collector);
            finishState.update(false);
        }
    }

    private void initState() throws IOException {
        if (finishState.value() == null) {
            finishState.update(false);
        }
    }

    private void emit(CprbTaskInfo info,
                      KeyedProcessFunction<Long, CprbTaskInfo, CprbTaskResult>.Context context,
                      Collector<CprbTaskResult> collector) throws Exception {
        ArrayList<CprbTaskExcelData> list = new ArrayList<>();
        Iterator<CprbTaskExcelData> iterator = flowState.get().iterator();
        while (iterator.hasNext()) {
            list.add(iterator.next());
        }
        if (!list.isEmpty()) {
            String fileName = "cprb-" + info.getTagId() + "-" + info.getTaskId() + ".xlsx";
            EasyExcel
                    .write(fileName, CprbTaskExcelData.class)
                    .registerConverter(new TimestampConverter())
                    .sheet("sheet1")
                    .useDefaultStyle(true)
                    .doWrite(() -> {
                        // 分页查询数据
                        return list;
                    });
            File localFile = new File(fileName);

            CprbTaskResult.CprbTaskResultBuilder builder = CprbTaskResult.builder()
                    .taskId(info.getTaskId())
                    .confId(info.getConfId())
                    .tagId(info.getTagId())
                    .dataCount(list.size())
                    .operationType(2);
            try {
                UploadResult upload = cosUtil.upload(localFile, fileName);
                Path path = Paths.get(fileName);
                Files.deleteIfExists(path);
                builder
                        .cosFileName(fileName)
                        .success(true);
            } catch (Exception e) {
                // 这里传递错误信息到下游
                e.printStackTrace();
                builder
                        .success(false)
                        .errorMsg(e.getMessage());
            }
            final CprbTaskResult result = builder.build();
            logger.info(String.format(
                    "[DebugTaskGroupProcess]debug task res: %s", result.toString()
            ));
            collector.collect(result);
        }
    }

    private void addData(CprbTaskInfo cprbTaskInfo) throws Exception {
        if (cprbTaskInfo.isFinished()) {
            finishState.update(true);
        } else {
            flowState.add(cprbTaskInfo.getExcelData());
        }
    }
}
