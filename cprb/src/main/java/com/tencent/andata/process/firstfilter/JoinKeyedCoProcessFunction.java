package com.tencent.andata.process.firstfilter;

import com.tencent.andata.filter.TicketReplyTagFilter;
import com.tencent.andata.model.firstfilter.MatchMetaData;
import com.tencent.andata.utils.TimeUtil;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.KeyedCoProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;

public class JoinKeyedCoProcessFunction extends KeyedCoProcessFunction<String, Row, Row, Row> {

    private static Logger logger = LoggerFactory.getLogger(JoinKeyedCoProcessFunction.class);
    private MapState<String, Row> ticketsAggState;
    OutputTag<MatchMetaData> metaJoinTag;
    private final TicketReplyTagFilter filter = new TicketReplyTagFilter();


    public JoinKeyedCoProcessFunction(OutputTag<MatchMetaData> metaJoinTag) {
        this.metaJoinTag = metaJoinTag;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        MapStateDescriptor<String, Row> descriptor = new MapStateDescriptor<>(
                "ticketsAggState",
                BasicTypeInfo.STRING_TYPE_INFO,
                TypeInformation.of(new TypeHint<Row>() {
                })
        );
        // 保存所有流水状态
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.days(365))
                .setUpdateType(StateTtlConfig.UpdateType.Disabled)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();
        descriptor.enableTimeToLive(ttlConfig); // 禁用状态的自动过期
        ticketsAggState = getRuntimeContext().getMapState(descriptor);
    }


    @Override
    public void processElement1(Row row, KeyedCoProcessFunction<String, Row, Row, Row>.Context context, Collector<Row> collector) throws Exception {
        // 任务流
        int count = 0;
        int filterCount = 0;
        for (Row next : ticketsAggState.values()) {
            if (match(row, next)) {
                count++;
                Row sendData = constructSendData(row, next);
                // 初筛
                if (filter.filter(sendData)) {
                    filterCount++;
                    collector.collect(sendData);
                }
            }
        }
        // 元数据分发
        context.output(metaJoinTag, MatchMetaData.builder()
                .taskId(String.valueOf(row.getField("task_id")))
                .partitionKey(context.getCurrentKey())
                .matchCount(count)
                .filterCount(filterCount)
                .finished(true)
                .build());
    }

    // 首先只匹配最近一年的
    // 然后渠道要能够匹配 这里通过KeyBy就保证了
    // 最后是匹配范围在任务开始时间和结束时间的
    private boolean match(Row taskRow, Row ticketsRow) {
        Object closeTimeObj = ticketsRow.getField("close_time");
        if (closeTimeObj == null) {
            return false;
        }
        Instant closeTime = (Instant) closeTimeObj;
        LocalDate dateToCheck = TimeUtil.convert2LocalDate(closeTime);
        LocalDate currentDate = LocalDate.now();
        Period period = Period.between(dateToCheck, currentDate);
        // 判断是否是一年前
        if (period.getYears() >= 1) {
            return false;
        }
        Instant instantStart = TimeUtil.convert2Instant((LocalDateTime) taskRow.getField("start_run_time"));
        Instant instantEnd = TimeUtil.convert2Instant((LocalDateTime) taskRow.getField("end_run_time"));
        return instantStart.isBefore(closeTime) && instantEnd.isAfter(closeTime);
    }

    private Row constructSendData(Row taskRow, Row ticketsRow) {
        Row row = Row.withNames();
        row.setField("ticket_id", ticketsRow.getField("ticket_id"));
        row.setField("prompt_group_id", taskRow.getField("prompt_group_id"));
        row.setField("tag_id", taskRow.getField("tag_id"));
        row.setField("url", ticketsRow.getField("url"));
        row.setField("question", ticketsRow.getField("question"));
        row.setField("ticket_create_time", ticketsRow.getField("ticket_create_time"));
        row.setField("update_time", ticketsRow.getField("update_time"));
        row.setField("source_type", ticketsRow.getField("partition_source"));
        row.setField("service_scene_level1_id", ticketsRow.getField("service_scene_level1_id"));
        row.setField("service_scene_level2_id", ticketsRow.getField("service_scene_level2_id"));
        row.setField("service_scene_level3_id", ticketsRow.getField("service_scene_level3_id"));
        row.setField("service_scene_level4_id", ticketsRow.getField("service_scene_level4_id"));
        row.setField("forward_filter", taskRow.getField("forward_filter"));
        row.setField("reverse_filter", taskRow.getField("reverse_filter"));
        row.setField("ticket_service_scene_filter", taskRow.getField("ticket_service_scene_filter"));
        row.setField("close_time", ticketsRow.getField("close_time"));
        row.setField("reply", ticketsRow.getField("reply"));
        row.setField("msg", ticketsRow.getField("msg"));
        row.setField("service_scene", ticketsRow.getField("service_scene"));
        row.setField("task_id", taskRow.getField("task_id"));
        row.setField("task_type", taskRow.getField("operation_type"));
        row.setField("config_id", taskRow.getField("config_id"));
        Object type = taskRow.getField("model_type");
        if (type == null) {
            row.setField("model_type", "chatgpt");
        } else {
            int modelType = Integer.parseInt(String.valueOf(type));
            if (modelType == 4) {
                row.setField("model_type", "gpt4");
            } else {
                row.setField("model_type", "chatgpt");
            }
        }
        row.setField("function_call_active_status", Long.valueOf(
                        String.valueOf(taskRow.getField("function_call_active_status"))
                )
        );
        row.setField("c_reply", ticketsRow.getField("c_reply"));
        return row;
    }

    @Override
    public void processElement2(Row row, KeyedCoProcessFunction<String, Row, Row, Row>.Context context, Collector<Row> collector) throws Exception {
        // 工单流 需要保存在状态里面
        // 这里应该去重复,所以用map
        String id = row.getField("id").toString();
        ticketsAggState.put(id, row);
    }
}
