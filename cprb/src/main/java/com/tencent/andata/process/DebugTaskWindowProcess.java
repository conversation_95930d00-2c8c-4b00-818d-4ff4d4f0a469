package com.tencent.andata.process;

import com.tencent.andata.model.CprbTaskExcelData;
import com.tencent.andata.model.CprbTaskInfo;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Iterator;

public class DebugTaskWindowProcess extends ProcessWindowFunction<Row, CprbTaskInfo, String, TimeWindow> {

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final Integer maxExcelCellLen;

    public DebugTaskWindowProcess(Integer maxExcelCellLen) {
        this.maxExcelCellLen = maxExcelCellLen;
    }

    @Override
    public void process(String s,
                        ProcessWindowFunction<Row, CprbTaskInfo, String, TimeWindow>.Context context,
                        Iterable<Row> iterable, Collector<CprbTaskInfo> collector) throws Exception {
        Iterator<Row> iterator = iterable.iterator();
        Long taskId = null;
        Long tagId = null;
        Long confId = null;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            taskId = Long.valueOf(String.valueOf(row.getField("task_id")));
            tagId = Long.valueOf(String.valueOf(row.getField("tag_id")));
            confId = Long.valueOf(String.valueOf(row.getField("config_id")));
            Instant createTime = (Instant) row.getField("ticket_create_time");
            Instant updateTime = (Instant) row.getField("update_time");
            String reply = row.getField("reply").toString();
            String msg = row.getField("c_reply").toString();
            String reasoning = row.getField("reasoning").toString();
            if (reply.length() > maxExcelCellLen) {
                reply = reply.substring(0, maxExcelCellLen);
            }
            if (msg.length() > maxExcelCellLen) {
                msg = msg.substring(0, maxExcelCellLen);
            }
            if (reasoning.length() > maxExcelCellLen) {
                reasoning = reasoning.substring(0, maxExcelCellLen);
            }
            CprbTaskExcelData cprbTaskExcelData = CprbTaskExcelData.builder()
                    .ticketId(Long.parseLong(String.valueOf(row.getField("ticket_id"))))
                    .url(row.getField("url").toString())
                    .sourceType(row.getField("source_type").toString())
                    .question(row.getField("question").toString())
                    .serviceScene(Long.parseLong(String.valueOf(row.getField("service_scene"))))
                    .ticketCreateTime(Timestamp.from(createTime))
                    .updateTime(Timestamp.from(updateTime))
                    .promptId(Long.parseLong(String.valueOf(row.getField("prompt_id"))))
                    .promptResult(row.getField("prompt_result").toString())
                    .reply(reply)
                    .msg(msg)
                    .reasoning(reasoning)
                    .version(System.currentTimeMillis() / 1000)
                    .build();
            collector.collect(CprbTaskInfo.builder()
                    .taskId(taskId)
                    .tagId(tagId)
                    .confId(confId)
                    .excelData(cprbTaskExcelData)
                    .finished(false)
                    .build());
        }
        if (taskId != null) {
            collector.collect(CprbTaskInfo.builder()
                    .taskId(taskId)
                    .confId(confId)
                    .tagId(tagId)
                    .finished(true)
                    .build());
        }

    }
}
