package com.tencent.andata.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.HttpClientUtils;
import com.tencent.andata.utils.JSONUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.joda.time.Instant;

import java.io.IOException;
import java.util.*;

public class GptAnswerProcessFunction extends ProcessFunction<Row, Row> {

    private FlinkLog logger = FlinkLog.getInstance();
    private final HttpClientUtils httpClientUtils;
    private final int retryCount;
    private static final String CONF_GROUP = "cprb.gpt.conf";
    private final String CONSUMER_NAME;
    private final String CONSUMER_ID;
    private final String URL;
    private final HashMap<String, Object> params = new HashMap<>();
    private final HashMap<String, String> headers = new HashMap<>();
    private final String requestWay;
    private final String token;
    private final String defaultModelType;
    private transient ValueStateDescriptor<String> fnCallCache;
    private transient ValueStateDescriptor<Long> cacheLastUpdateTime;

    private final ObjectMapper objectMapper;


    public GptAnswerProcessFunction(
            int retryCount
    ) throws IOException {
        this.httpClientUtils = new HttpClientUtils(
                300000,
                300000
        );
        // 参数初始化
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        this.CONSUMER_NAME = rainbowUtils.getStringValue(CONF_GROUP, "consumer_name");
        this.CONSUMER_ID = rainbowUtils.getStringValue(CONF_GROUP, "consumer_id");
        this.URL = rainbowUtils.getStringValue(CONF_GROUP, "url");
        this.requestWay = rainbowUtils.getStringValue(CONF_GROUP, "request_way");
        this.token = rainbowUtils.getStringValue(CONF_GROUP, "token");
        this.defaultModelType = rainbowUtils.getStringValue(CONF_GROUP, "model_type"); // 默认混元
        // 重试次数
        this.retryCount = retryCount;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.fnCallCache = new ValueStateDescriptor<>("fn_call_cache", String.class);
        this.cacheLastUpdateTime = new ValueStateDescriptor<>("cache_last_update_time", Long.class);
    }

    @Override
    public void processElement(
            Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector
    ) throws Exception {
        // 从Row获取调用接口所需的数据
        List<String> questions = this.getQuestionFromRow(row);
        String promptId = this.getPromptIdFromRow(row);
        final String requestId = UUID.randomUUID().toString();
        boolean useFunctionCall = this.isFunctionCall(row);
        if (useFunctionCall) {
            params.put("parameters", new ObjectMapper().convertValue(
                    JSONUtils.getJsonNodeByString(this.getFunctionCallParams()), Map.class));
            params.put("model_type", "hunyuan-fc");
        }else{
            params.remove("parameters");
            params.put("model_type", "hunyuan");
        }
        // 封装请求
        params.put("data", questions);
        params.put("prompt_id", promptId);
        params.put("request_way", requestWay);
        // headers
        headers.put("x-consumer-username", CONSUMER_NAME);
        headers.put("x-consumer-id", CONSUMER_ID);
        headers.put("Authorization", "Bearer " + token);
        headers.put("requestid", requestId);
        headers.put("request-id", requestId);

        // 不设置会出现乱码
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json;charset=UTF-8");
        // 允许重试
        for (int i = 0; i < this.retryCount; i++) {
            // 发送请求
            final String res;
            try {
                res = httpClientUtils.post(URL, params, headers);
            } catch (Exception e) {
                logger.warn(
                        String.format("[Gpt Process]http post error: %s\nurl:%s\nparams:%s\ntry number:%s",
                                e, URL, params, i)
                );
                continue;
            }
            JsonNode resp = null;
            try {
                resp = JSONUtils.getJsonNodeByString(res);
            } catch (JsonProcessingException e) {
                logger.warn(
                        String.format("[Gpt Process]invalid response json: %s\nurl:%s\nparams:%s\ntry number:%s",
                                res, URL, params, i)
                );
                continue;
            }
            final JsonNode resNode = resp.get("answer");
            if (resNode == null) {
                logger.warn(
                        String.format("[Gpt Process]response not have answer: %s\nurl:%s\nparams:%s\ntry number:%s",
                                res, URL, params, i)
                );
                continue;
            }
            row.setField("gpt_answer", resNode.toString());
            try {
                List<Row> resultList = this.parseAnswer(row, context);
                if (resultList.isEmpty()) {
                    logger.warn(String.format("[Gpt Process]parse answer empty\nticket_id:%s\nanswer:%s\ntry number:%s",
                            row.getField("ticket_id"), resNode, i));
                    continue;
                }
                for (Row r : resultList) {
                    collector.collect(r);
                }
                // 保存现场日志
                for (Row r : resultList) {
                    logData(r, requestId);
                }
                break;
            } catch (Exception e) {
                logger.warn(String.format("[Gpt Process]parse answer error: %s\nticket_id:%s\nanswer:%s\ntry number:%s",
                        e, row.getField("ticket_id"), resNode, i));
            }
        }
    }

    private List<Row> parseAnswer(Row row, ProcessFunction<Row, Row>.Context context) throws Exception {
        final String gptAnswer = Objects.requireNonNull(row.getField("gpt_answer")).toString();
        final JsonNode answerNode = this.objectMapper.readTree(gptAnswer).get(0);
        final JsonNode valueNode = answerNode.get("answer_value");
        final JsonNode fnValueNode = answerNode.get("answer_function_value");
        List<Row> resultList = new ArrayList<>();
        boolean functionCallActiveStatus = row.getField("function_call_active_status").toString().equals("1");
        // answer_value里存的是个字典，key是prompt_id，值是gpt结果
        final Iterator<Map.Entry<String, JsonNode>> iterator = valueNode.fields();
        String traceID = String.format("%s-%s",
                row.getField("tag_id").toString(),
                row.getField("ticket_id").toString());
        this.logger.info(String.format("[GPT Result FlatMap]: ticket_id: %s, fc:%s: , result:%s",
                row.getField("ticket_id").toString(),
                functionCallActiveStatus,
                gptAnswer), traceID);
        while (!functionCallActiveStatus && iterator.hasNext()) {
            final Map.Entry<String, JsonNode> item = iterator.next();
            final String promptId = item.getKey();
            String value = item.getValue().asText();
            // 值如果是空字符串的话，默认是 是
            if (value.isEmpty()) {
                value = "是";
            }
            // 对value做数据清洗
            value = value.replaceAll("[\\u0000-\\u001f]|\\\\r|\\\\n|\\p{C}", "");
            final Row copyRow = Row.copy(row);
            copyRow.setField("prompt_id", Long.valueOf(promptId));
            copyRow.setField("prompt_result", value);
            copyRow.setField("reasoning", "没有使用function call, 此处为空");
            resultList.add(copyRow);
        }
        final Iterator<Map.Entry<String, JsonNode>> fnCallIterator = fnValueNode.fields();
        while (functionCallActiveStatus && fnCallIterator.hasNext()) {
            final Map.Entry<String, JsonNode> item = fnCallIterator.next();
            final String promptId = item.getKey();
            String proposal;
            String reason;
            if (item.getValue().get("arguments") == null) {
                proposal = "是";
                reason = "当前PROMPT模板为空，默认为\"是\"";
            } else {
                try {
                    final JsonNode argumentsNode = this.objectMapper.
                            readTree(item.getValue().get("arguments").asText(""));
                    proposal = argumentsNode.get("proposal").asText("error");
                    reason = argumentsNode.get("reason").asText("")
                            .replaceAll("[\\u0000-\\u001f]|\\\\r|\\\\n|\\p{C}", "");
                } catch (Exception e) {
                    logger.error(String.format("[GPT Result FlatMap]: 解析LLM结果失败，ticket_id: %s, %s, \n %s",
                            row.getField("ticket_id").toString(), item, e));
                    throw e;
                }
            }
            final Row copyRow = Row.copy(row);
            copyRow.setField("prompt_id", Long.valueOf(promptId));
            copyRow.setField("prompt_result", proposal);
            copyRow.setField("reasoning", reason);
            resultList.add(copyRow);
        }
        return resultList;
    }

    private void logData(Row row, String uuid) {
        StringBuilder stringBuilder = new StringBuilder("{\n");
        for (String filed : Objects.requireNonNull(row.getFieldNames(true))) {
            stringBuilder.append(String.format("%s: %s,\n", filed, row.getField(filed)));
        }
        stringBuilder.append("}");
        logger.info(String.format(
                "GPTProcess data: %s",
                stringBuilder.toString()
        ), uuid);
    }

    private List<String> getQuestionFromRow(Row row) {
        return new ArrayList<>(Collections.singleton(row.getField("c_reply").toString()));
    }

    private String getPromptIdFromRow(Row row) {
        return row.getField("prompt_group_id").toString();
    }

    private boolean isFunctionCall(Row row) {
        try {
            int status = Integer.parseInt(
                    Objects.requireNonNull(row.getField("function_call_active_status")).toString());
            return status == 1;
        } catch (Exception e) {
            logger.warn("获取function_call_active_status字段失败");
            return false;
        }
    }

    private String getFunctionCallParams() throws IOException {
        ValueState<String> fnCallCacheState = getRuntimeContext().getState(this.fnCallCache);

        long millsNow = Instant.now().getMillis();
        ValueState<Long> last = getRuntimeContext().getState(this.cacheLastUpdateTime);
        if (fnCallCacheState.value() != null && !fnCallCacheState.value().isEmpty()
                && (millsNow - last.value() <= 1000 * 120)) { // 2min失效
            return fnCallCacheState.value();
        }
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        String functionCall = rainbowUtils.getStringValue(CONF_GROUP, "function_call");
        fnCallCacheState.update(functionCall);
        last.update(millsNow);
        return functionCall;
    }


}