package com.tencent.andata.process.cluster;

import com.tencent.andata.model.cluster.CalibrationType;
import com.tencent.andata.model.cluster.TicketTuple;
import com.tencent.andata.model.cluster.TicketTupleScore;
import com.tencent.andata.util.PromptUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class CalibrationProcess extends ProcessFunction<Row, TicketTupleScore> {

    private static Logger logger = LoggerFactory.getLogger(CalibrationProcess.class);
    private transient PromptUtils promptUtils = null;
    // f0是判断是否存在工单对的校准数据，f1是这个工单对调用校准的分的索引
    private Tuple2<Bo<PERSON><PERSON>, Integer>[] flags = new Tuple2[4];
    private CalibrationType[] typeStr = new CalibrationType[]{CalibrationType.Ai_Solution, CalibrationType.Ai_Description,
            CalibrationType.Solution, CalibrationType.Description};

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        if (promptUtils == null) {
            promptUtils = new PromptUtils();
        }
    }

    @Override
    public void processElement(Row row, ProcessFunction<Row, TicketTupleScore>.Context context, Collector<TicketTupleScore> collector) throws Exception {
        long ticketIdA = Long.parseLong(row.getField("ticket_id_a").toString());
        long ticketIdB = Long.parseLong(row.getField("ticket_id_b").toString());
        List<List<String>> data = new ArrayList<>();
        // 没有归档则表示没有这条数据，跳过（可能是内部服务台的工单）
        if (row.getField("service_scene_level1_id") == null) {
            return;
        }
        // 调用校准模型
        // 先提取字段
        // ai solution
        flags[0] = addData(row, "ticket_a_ai_solution", "ticket_b_ai_solution", data);
        // ai description
        flags[1] = addData(row, "ticket_a_ai_description", "ticket_b_ai_description", data);
        // 结单模板 solution
        flags[2] = addData(row, "ticket_a_solution", "ticket_b_solution", data);
        // 结单模板 description
        flags[3] = addData(row, "ticket_a_description", "ticket_b_description", data);
        List<Double> doubles = promptUtils.reRankStr(data);
        for (int i = 0; i < flags.length; i++) {
            final Boolean isExists = flags[i].f0;
            final Integer scoreIndex = flags[i].f1;
            // 不存在的话则默认100分
            double score = isExists ? doubles.get(scoreIndex) : 100;
            collector.collect(TicketTupleScore.builder()
                    .ticketId1(ticketIdA)
                    .ticketId2(ticketIdB)
                    .baseClusterId(Long.parseLong(String.valueOf(row.getField("base_cluster_id"))))
                    .score(score)
                    .calibrationType(typeStr[i])
                    .serviceSceneLevel1Id(Integer.parseInt(row.getField("service_scene_level1_id").toString()))
                    .serviceSceneLevel2Id(Integer.parseInt(row.getField("service_scene_level2_id").toString()))
                    .build());
        }
    }


    private Tuple2<Boolean, Integer> addData(Row row, String fieldA, String fieldB, List<List<String>> data) {
        Object objA = row.getField(fieldA);
        Object objB = row.getField(fieldB);
        if (objA != null && objB != null) {
            String strA = objA.toString();
            String strB = objB.toString();
            if (StringUtils.isNotEmpty(strA) && StringUtils.isNotEmpty(strB)) {
                data.add(new ArrayList<String>() {{
                    add(strA);
                    add(strB);
                }});
                // 校准服务返回的得分数据跟data列表一一对应，所以取得分的时候按照data的index取就行
                return new Tuple2<>(true, data.size() - 1);
            }
        }
        return new Tuple2<>(false, -1);
    }
}
