package com.tencent.andata.process;

import com.tencent.andata.etl.CprbInProgressEtl;
import com.tencent.andata.log.FlinkLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

public class InProgressSubscribeDataFilterProcessFunction extends ProcessFunction<Row, Row> {
    public static FlinkLog logger = FlinkLog.getInstance();
    private final List<Long> subscribeTagList;

    public InProgressSubscribeDataFilterProcessFunction(List<Long> subscribeTagList) {
        // 云顾问订阅的Tag
        this.subscribeTagList = subscribeTagList;
    }

    @Override
    public void processElement(
            Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector
    ) throws Exception {
        // 只需要最近一个小时数据，避免堆积数据推送
        final String updateTime = row.getField("update_time").toString();
        Timestamp timestamp;
        try {
            timestamp = Timestamp.valueOf(updateTime);
        } catch (Exception e) {
            logger.warn(String.format(
                    "[InProgressSubscribeDataFilterProcessFunction]update_time: %s is invalid, skip this data",
                    updateTime
            ));
            return;
        }
        if (System.currentTimeMillis() > timestamp.getTime() + 60 * 60 * 1000) {
            logger.warn(String.format(
                    "延迟数据，不进行推送:%s",
                    row.toString()
            ));
            return;
        }
        // 过滤只要产生共性的单
        final String promptResult = (String) (row.getField("prompt_result"));
        if (!Objects.equals(promptResult, "是")) {
            return;
        }
        final Row res = Row.withNames(RowKind.INSERT);
        res.setField("ticket_id", row.getField("ticket_id"));
        res.setField("tag_id", row.getField("tag_id"));
        res.setField("service_scene", row.getField("service_scene"));
        res.setField("prompt_id", row.getField("prompt_id"));
        // 云顾问告警标识
        final Object tagId = row.getField("tag_id");
        if (tagId != null && subscribeTagList.contains((long) tagId)) {
            res.setField("send_to_yunguwen", 1);
        } else {
            res.setField("send_to_yunguwen", 0);

        }
        collector.collect(res);
    }
}
