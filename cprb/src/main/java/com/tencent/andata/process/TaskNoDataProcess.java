package com.tencent.andata.process;


import com.tencent.andata.util.CPRBUtils;
import lombok.Builder;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

public class TaskNoDataProcess extends KeyedBroadcastProcessFunction<String, Row, Long, Row> {
    private static final Logger logger = LoggerFactory.getLogger(TaskNoDataProcess.class);
    public static MapStateDescriptor<Long, Long> taskIdBroadCastDescriptor = new MapStateDescriptor<>(
            "taskIdBroadCastDescriptor",
            BasicTypeInfo.LONG_TYPE_INFO,
            BasicTypeInfo.LONG_TYPE_INFO);
    public long timeout;

    public TaskNoDataProcess(long timeout) {
        this.timeout = timeout;
    }

    @Override
    public void onTimer(long timestamp,
                        KeyedBroadcastProcessFunction<String, Row, Long, Row>.OnTimerContext ctx,
                        Collector<Row> out) throws Exception {
        super.onTimer(timestamp, ctx, out);
        final String currentKey = ctx.getCurrentKey();
        final String[] split = currentKey.split("-");
        final Long taskId = Long.parseLong(split[0]);
        if (ctx.getBroadcastState(taskIdBroadCastDescriptor).contains(taskId)) {
            return;
        }
        logger.info("Task ID:{} NO_DATA", taskId);
        CPRBUtils.taskCallback(
                taskId,
                Long.parseLong(split[1]),
                Long.parseLong(split[2]),
                Integer.parseInt(split[3]),
                "no_data.csv");
    }


    @Override
    public void processElement(Row row,
                               KeyedBroadcastProcessFunction<String, Row, Long, Row>.ReadOnlyContext ctx,
                               Collector<Row> collector) throws Exception {
        // 获取当前时间
        long currentTimestamp = ctx.currentProcessingTime();
        // 设置定时器，触发时间为当前时间加上一定的延迟
        ctx.timerService().registerProcessingTimeTimer(currentTimestamp + timeout);
    }

    @Override
    public void processBroadcastElement(
            Long taskId,
            KeyedBroadcastProcessFunction<String, Row, Long, Row>.Context context,
            Collector<Row> collector
    ) throws Exception {
        context.getBroadcastState(taskIdBroadCastDescriptor).put(taskId, taskId);
        // 打印日志
        logger.info("Timer triggered for Task ID: {}", taskId);
    }
}