package com.tencent.andata.process.cluster;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

public class ClusterFlatProcessFunction extends KeyedProcessFunction<String, Row, Row> {

    private static Logger logger = LoggerFactory.getLogger(ClusterFlatProcessFunction.class);
    private static ObjectMapper mapper = new ObjectMapper();

    @Override
    public void processElement(Row row, KeyedProcessFunction<String, Row, Row>.Context context, Collector<Row> collector) throws Exception {
        Object ticketListObj = row.getField("ticket_list");
        List<Tuple2<Long, Long>> ticketPairs = new ArrayList<>();
        // 每次都基于新的工单列表进行笛卡尔积，新的肯定会包含旧的
        // TODO 后续可以把表替换成base_cluster表，不需要changelog了
        if (ticketListObj != null) {
            ArrayList<Long> tickets = mapper.readValue(ticketListObj.toString(), new TypeReference<ArrayList<Long>>() {
            });
            ticketPairs = ticketsCartesianProduct(tickets, tickets);
        }
        ArrayNode arrayNode = mapper.createArrayNode();
        for (Tuple2<Long, Long> ticketPair : ticketPairs) {
            arrayNode.add(ticketPair.f0);
            arrayNode.add(ticketPair.f1);
            row.setField("ticket_id_a", ticketPair.f0);
            row.setField("ticket_id_b", ticketPair.f1);
            row.setKind(RowKind.INSERT);
            collector.collect(row);
        }
    }

    private List<Long> getIncrementTickets(List<Long> newTickets, List<Long> oldTickets) {
        HashSet<Long> set = new HashSet<>(oldTickets);
        ArrayList<Long> incrementTickets = new ArrayList<>();
        for (Long newTicket : newTickets) {
            if (!set.contains(newTicket)) {
                incrementTickets.add(newTicket);
            }
        }
        return incrementTickets;
    }

    private List<Tuple2<Long, Long>> ticketsCartesianProduct(List<Long> ticketsA, List<Long> ticketsB) {
        List<Tuple2<Long, Long>> res = new ArrayList<>();
        // 用来去重
        HashSet<String> set = new HashSet<>();
        for (Long ticketA : ticketsA) {
            for (Long ticketB : ticketsB) {
                if (Objects.equals(ticketA, ticketB)) {
                    continue;
                }
                String setStr = "";
                Tuple2<Long, Long> t;
                if (ticketA > ticketB) {
                    setStr = ticketsB + "_" + ticketA;
                    t = Tuple2.of(ticketB, ticketA);
                } else {
                    setStr = ticketsA + "_" + ticketB;
                    t = Tuple2.of(ticketA, ticketB);
                }
                if (set.contains(setStr)) {
                    continue;
                }
                set.add(setStr);
                res.add(t);
            }
        }
        return res;
    }

}
