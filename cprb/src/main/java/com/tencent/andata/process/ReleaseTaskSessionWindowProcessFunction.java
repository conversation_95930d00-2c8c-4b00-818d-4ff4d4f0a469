package com.tencent.andata.process;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.util.CPRBUtils;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.Objects;

/**
 * 上线任务的Session Window处理函数
 */
public class ReleaseTaskSessionWindowProcessFunction extends ProcessWindowFunction<Row, Row, Long, TimeWindow> {
    private static FlinkLog logger = FlinkLog.getInstance();

    @Override
    public void process(
            Long key,
            ProcessWindowFunction<Row, Row, Long, TimeWindow>.Context context,
            Iterable<Row> rowList,
            Collector<Row> collector
    ) {
        long count = 0;
        Row tmpRow = null;
        for (Row row : rowList) {
            count += 1;
            // 每个都发往下游Sink
            collector.collect(row);
            tmpRow = row;
        }
        // 获取TagId 和 condId
        long tagId = (long) Objects.requireNonNull(Objects.requireNonNull(tmpRow).getField("tag_id"));
        long confId = (long) Objects.requireNonNull(Objects.requireNonNull(tmpRow).getField("config_id"));
        final String logStr = String.format(
                "[ReleaseTaskSessionWindowProcessFunction] the release task: %s, tag id: %s, config id: %s, "
                        + " matched %d ticket data",
                key,
                tagId,
                confId,
                count
        );
        logger.info(logStr);
        // 回调后端
        try {
            CPRBUtils.taskCallback(key, tagId, confId, 1, "");
        } catch (Exception e) {
            // TODO 暂时写入告警日志，后续看怎么优化
            logger.error(
                    String.format(
                            "[ReleaseTaskSessionWindowProcessFunction] cprb callback error! %s\n info: %s",
                            e,
                            logStr
                    )
            );
        }
    }
}
