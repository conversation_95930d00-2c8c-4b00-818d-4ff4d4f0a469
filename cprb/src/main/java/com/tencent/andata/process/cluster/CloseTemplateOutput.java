package com.tencent.andata.process.cluster;

import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

public class CloseTemplateOutput extends ProcessFunction<Row, Row> {

    OutputTag<Row> solutionTag;
    OutputTag<Row> reasonTag;
    OutputTag<Row> descriptionTag;

    public CloseTemplateOutput(OutputTag<Row> solutionTag, OutputTag<Row> reasonTag, OutputTag<Row> descriptionTag) {
        this.solutionTag = solutionTag;
        this.reasonTag = reasonTag;
        this.descriptionTag = descriptionTag;
    }

    @Override
    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {

        if (row.getField("solution") != null) {
            context.output(solutionTag, row);
        }
        if (row.getField("reason") != null) {
            context.output(reasonTag, row);
        }
        if (row.getField("description") != null) {
            context.output(descriptionTag, row);
        }
    }
}
