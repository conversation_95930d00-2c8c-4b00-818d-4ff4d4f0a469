package com.tencent.andata.process.firstfilter;

import com.tencent.andata.util.firstfilter.TaskSplitUtil;
import com.tencent.andata.util.keyby.KeyByType;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.time.*;
import java.util.List;

public class TaskSplit extends ProcessFunction<Row, Row> {

    OutputTag<Row> taskTag;
    KeyByType.Type type;

    public TaskSplit(OutputTag<Row> taskTag, KeyByType.Type keyByType) {
        this.taskTag = taskTag;
        this.type = keyByType;
    }

    @Override
    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        // 侧流发一份
        context.output(taskTag, row);
        // 做切割
        String[] split = TaskSplitUtil.splitField(row, "source_type", ",");
        List<Instant> list = null;
        switch (type) {
            case Instant_Month:
                list = TaskSplitUtil.splitTime(row, "start_run_time", "end_run_time");
                break;
            case Instant_Day:
                list = TaskSplitUtil.splitTimeByDay(row, "start_run_time", "end_run_time");
                break;
            default:
                list = TaskSplitUtil.splitTime(row, "start_run_time", "end_run_time");
        }
        for (String source : split) {
            row.setField("partition_source", source);
            for (Instant time : list) {
                row.setField("partition_time", time);
                collector.collect(row);
            }
        }
    }
}
