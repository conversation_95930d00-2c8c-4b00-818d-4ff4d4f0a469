package com.tencent.andata.process.firstfilter;

import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

public class TaskDelayProcessFunction extends KeyedProcessFunction<String, Row, Row> implements CheckpointedFunction {

    private final long startTime = System.currentTimeMillis();
    private boolean needCheck = true;
    // 延迟时间（s）
    private final long delayTime;
    private ListState<Row> tasks;

    public TaskDelayProcessFunction(long delayTime) {
        this.delayTime = delayTime;
    }

    @Override
    public void snapshotState(FunctionSnapshotContext functionSnapshotContext) throws Exception {

    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        if (context.isRestored()) {
            needCheck = false;
        }
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        ListStateDescriptor<Row> descriptor = new ListStateDescriptor<>(
                "tasks",
                Row.class
        );
        tasks = getRuntimeContext().getListState(descriptor);
    }

    @Override
    public void onTimer(long timestamp, KeyedProcessFunction<String, Row, Row>.OnTimerContext ctx,
                        Collector<Row> out) throws Exception {
        super.onTimer(timestamp, ctx, out);
        long currentTimestamp = ctx.timestamp();
        // 看是不是超过了设置的延迟时间
        if (currentTimestamp >= startTime + delayTime * 1000) {
            needCheck = false;
            if (tasks == null || tasks.get() == null) {
                return;
            }
            for (Row row : tasks.get()) {
                out.collect(row);
            }
            tasks.clear();
        } else {
            // 设置定时器，每秒check一下
            ctx.timerService().registerProcessingTimeTimer(currentTimestamp + 1000);
        }
    }

    @Override
    public void processElement(Row row, KeyedProcessFunction<String, Row, Row>.Context context, Collector<Row> collector) throws Exception {
        if (!needCheck) {
            collector.collect(row);
            return;
        }
        tasks.add(row);
        long currentTimestamp = context.timerService().currentProcessingTime();
        context.timerService().registerProcessingTimeTimer(currentTimestamp + 1000);
    }
}
