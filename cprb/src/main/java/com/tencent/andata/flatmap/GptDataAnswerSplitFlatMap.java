package com.tencent.andata.flatmap;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.log.FlinkLog;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

/**
 * 一次请求传的是prompt_group_id，可能会返回多条prompt_id数据
 * 这里需要一拆多
 */
@Deprecated
public class GptDataAnswerSplitFlatMap implements FlatMapFunction<Row, Row> {
    private FlinkLog logger = FlinkLog.getInstance();

    private final ObjectMapper objectMapper;

    public GptDataAnswerSplitFlatMap() {
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {
        final String gptAnswer = Objects.requireNonNull(row.getField("gpt_answer")).toString();
        final JsonNode answerNode = this.objectMapper.readTree(gptAnswer).get(0);
        final JsonNode valueNode = answerNode.get("answer_value");
        final JsonNode fnValueNode = answerNode.get("answer_function_value");
        boolean functionCallActiveStatus = row.getField("function_call_active_status").toString().equals("1");
        // answer_value里存的是个字典，key是prompt_id，值是gpt结果
        final Iterator<Map.Entry<String, JsonNode>> iterator = valueNode.fields();
        String traceID = String.format("%s-%s",
                row.getField("tag_id").toString(),
                row.getField("ticket_id").toString());
        this.logger.info(String.format("[GPT Result FlatMap]: ticket_id: %s, fc:%s: , result:%s",
                row.getField("ticket_id").toString(),
                functionCallActiveStatus,
                gptAnswer), traceID);
        while (!functionCallActiveStatus && iterator.hasNext()) {
            final Map.Entry<String, JsonNode> item = iterator.next();
            final String promptId = item.getKey();
            String value = item.getValue().asText();
            // 值如果是空字符串的话，默认是 是
            if (value.isEmpty()) {
                value = "是";
            }
            // 对value做数据清洗
            value = value.replaceAll("[\\u0000-\\u001f]|\\\\r|\\\\n|\\p{C}", "");
            final Row copyRow = Row.copy(row);
            copyRow.setField("prompt_id", Long.valueOf(promptId));
            copyRow.setField("prompt_result", value);
            copyRow.setField("reasoning", "没有使用function call, 此处为空");
            collector.collect(copyRow);
        }
        final Iterator<Map.Entry<String, JsonNode>> fnCallIterator = fnValueNode.fields();
        while (functionCallActiveStatus && fnCallIterator.hasNext()) {
            final Map.Entry<String, JsonNode> item = fnCallIterator.next();
            final String promptId = item.getKey();
            String proposal;
            String reason;
            if (item.getValue().get("arguments") == null) {
                proposal = "是";
                reason = "当前PROMPT模板为空，默认为\"是\"";
            } else {
                try {
                    final JsonNode argumentsNode = this.objectMapper.
                            readTree(item.getValue().get("arguments").asText(""));
                    proposal = argumentsNode.get("proposal").asText("error");
                    reason = argumentsNode.get("reason").asText("")
                            .replaceAll("[\\u0000-\\u001f]|\\\\r|\\\\n|\\p{C}", "");
                } catch (JsonParseException e) {
                    logger.error(String.format("[GPT Result FlatMap]: 解析LLM结果失败，ticket_id: %s, %s, \n %s",
                            row.getField("ticket_id").toString(), item, e));
                    throw e;
                }
            }
            final Row copyRow = Row.copy(row);
            copyRow.setField("prompt_id", Long.valueOf(promptId));
            copyRow.setField("prompt_result", proposal);
            copyRow.setField("reasoning", reason);
            collector.collect(copyRow);
        }
    }
}
