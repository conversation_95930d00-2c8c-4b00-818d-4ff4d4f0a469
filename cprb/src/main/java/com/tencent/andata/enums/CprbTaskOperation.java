package com.tencent.andata.enums;

public enum CprbTaskOperation {

    RELEASE(1, "RELEASE"),
    DEBUG(2, "DEBUG");

    public final int value;
    public final String name;

    private CprbTaskOperation(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static CprbTaskOperation valueOf(int value) throws RuntimeException {
        switch (value) {
            case 1:
                return RELEASE;
            case 2:
                return DEBUG;
            default:
                throw new RuntimeException(String.format("CprbTaskOperation not such value %s", value));
        }
    }
}
