package com.tencent.andata.schema.mask.convert;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import java.text.ParseException;
import java.util.Map;

public class TicketFlowGroupAdaptor extends DefaultAdaptor {

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convert(String json, Map<String, String> meta) throws JsonProcessingException, ParseException {
        ObjectNode jsonNodes = objectMapper.readValue(json, ObjectNode.class);
        // 添加一些新的字段
        jsonNodes.put("c_msg", "");
        jsonNodes.put("mask_msg", "");
        jsonNodes.put("c_inner_msg", "");
        jsonNodes.put("mask_inner_msg", "");
        String operateTime = jsonNodes.get("operate_time").asText();
        if (!isUtcTimeStr(operateTime)) {
            jsonNodes.put("operate_time", this.convertTimeStampStr2Utc(operateTime));
        }
        return objectMapper.writeValueAsString(jsonNodes);
    }
}
