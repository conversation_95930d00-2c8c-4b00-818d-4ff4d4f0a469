package com.tencent.andata.schema.mask;

import com.tencent.andata.struct.avro.message.Message;
import org.apache.avro.io.BinaryEncoder;
import org.apache.avro.io.DatumWriter;
import org.apache.avro.io.EncoderFactory;
import org.apache.avro.reflect.ReflectDatumWriter;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class MessageSerialization<T> implements KafkaRecordSerializationSchema<T> {
    final String msgKafkaTopic;
    Class<T> tClass;
    DatumWriter<T> writer;
    BinaryEncoder encoder;

    public MessageSerialization(Class<T> tClass, String msgKafkaTopic) {
        this.tClass = tClass;
        this.msgKafkaTopic = msgKafkaTopic;
    }

    void initConverter() {
        if (writer == null) {
            writer = new ReflectDatumWriter<>(tClass);
        }
    }

    @Override
    public ProducerRecord<byte[], byte[]> serialize(T message, KafkaSinkContext kafkaSinkContext, Long aLong) {
        this.initConverter();
        // 这里每次都重新生成一个ByteArray
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        encoder = EncoderFactory.get().binaryEncoder(outputStream, encoder);
        try {
            this.writer.write(message, encoder);
            encoder.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return new ProducerRecord<>(msgKafkaTopic, outputStream.toByteArray());
    }
}
