package com.tencent.andata.schema.mask.convert;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.TimeZone;

// 这里是想把不同的输入转换为对应ice表的json字符串
public class DefaultAdaptor implements Serializable {

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat sdfUtc = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss'Z'");

    public DefaultAdaptor() {
        sdfUtc.setTimeZone(TimeZone.getTimeZone("UTC"));
//        sdfUtc.setTimeZone(TimeZone.getTimeZone("GMT"));
    }


    public String convert(String json, Map<String, String> meta) throws Exception {
        return json;
    }


    protected String convertTimeStampStr2Utc(String timeStr) throws ParseException {
        Date date = sdf.parse(timeStr);
        long time = date.getTime();
        date.setTime(time - 28800000);
        return sdfUtc.format(date);
    }

    protected boolean isUtcTimeStr(String timeStr) {
        return timeStr.charAt(timeStr.length() - 1) == 'Z';
    }

    protected String timestamp2UtcStr(Long time) {
        return sdfUtc.format(time * 1000L);
    }
}