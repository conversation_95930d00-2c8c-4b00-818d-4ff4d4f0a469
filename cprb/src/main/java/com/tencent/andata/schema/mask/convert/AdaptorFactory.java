package com.tencent.andata.schema.mask.convert;

public class AdaptorFactory {

    public static DefaultAdaptor getConvertByName(String name) {
        switch (name) {
            case "TicketFlowConvert":
                return new TicketFlowAdaptor();
            case "WebImFlowConvert":
                return new WebImFlowAdaptor();
            case "GroupMessageConvert":
                return new GroupMessageAdaptor();
            default:
                return new DefaultAdaptor();
        }
    }
}
