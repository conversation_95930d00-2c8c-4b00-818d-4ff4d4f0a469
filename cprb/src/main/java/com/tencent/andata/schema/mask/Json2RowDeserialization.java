package com.tencent.andata.schema.mask;


import com.tencent.andata.schema.mask.convert.DefaultAdaptor;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableIdentifier;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonToRowDataConverters;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.conversion.RowRowConverter;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class Json2RowDeserialization implements KafkaRecordDeserializationSchema<Row> {

    private static Logger logger = LoggerFactory.getLogger(Json2RowDeserialization.class);
    public DefaultAdaptor defaultAdaptor;
    JsonToRowDataConverters.JsonToRowDataConverter runtimeConverter;
    RowRowConverter rowRowConverter;
    private ObjectMapper objectMapper = new ObjectMapper();
    private TypeInformation<Row> rowTypeInfo;
//    JsonRowDeserializationSchema deserializeUtil;

    /***
     * Json2RowDeserialization
     * @param defaultAdaptor .
     * @param tableIdentifier .
     * @throws InterruptedException .
     * @throws TableNotExistException .
     * @throws NoSuchFieldException .
     * @throws IllegalAccessException .
     */
    public Json2RowDeserialization(
            DefaultAdaptor defaultAdaptor,
            TableIdentifier tableIdentifier) throws InterruptedException,
            TableNotExistException, NoSuchFieldException, IllegalAccessException {
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        objectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        this.defaultAdaptor = defaultAdaptor;
        // 定义 JSON 数据的解析规则
        IcebergCatalogReader reader = new IcebergCatalogReader();

        RowType rowType = reader
                .getTableRowType(tableIdentifier.getDbName(), tableIdentifier.getTableName());
        int fieldCount = rowType.getFieldCount();
        TypeInformation<?>[] types = new TypeInformation[fieldCount];
        String[] fieldNames = new String[fieldCount];
        List<String> fields = rowType.getFieldNames();
        for (int i = 0; i < fieldCount; i++) {
            LogicalType typeAt = rowType.getTypeAt(i);
            Class<?> type = typeAt.getDefaultConversion();
            types[i] = TypeInformation.of(type);
            fieldNames[i] = fields.get(i);
            System.out.printf("filedName: %s , type: %s%n", fieldNames[i], types[i]);
        }
        rowTypeInfo = new RowTypeInfo(types, fieldNames);

//        deserializeUtil = new JsonRowDeserializationSchema.Builder(rowTypeInfo)
//                .build();
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.enable(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS);
//        mapper.registerModule(new JavaTimeModule());
//        Field objectMapper = deserializeUtil.getClass().getDeclaredField("objectMapper");
//        objectMapper.setAccessible(true);
//        objectMapper.set(deserializeUtil, mapper);

        // json 先转rowData
        runtimeConverter =
                new JsonToRowDataConverters(false, true, TimestampFormat.SQL)
                        .createRowConverter(rowType);
        // 创建RowData到Row的转换器
        DataType dataType = IcebergCatalogReader.getDataType(
                reader.getTableInstance(
                        tableIdentifier.getDbName(),
                        tableIdentifier.getTableName()
                ), true);
        rowRowConverter = RowRowConverter.create(dataType);
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord,
            Collector<Row> collector) throws IOException {
        try {
            String string = defaultAdaptor.convert(new String(consumerRecord.value()), new HashMap<String, String>() {{
                put("kafkaTime", String.valueOf(consumerRecord.timestamp()));
            }});
            if (StringUtils.isEmpty(string)) {
                return;
            }
            JsonNode jsonNode = objectMapper.readValue(string, JsonNode.class);
            RowData convert = (RowData) runtimeConverter.convert(jsonNode);
            Row external = rowRowConverter.toExternal(convert);
//            int arity = external.getArity();
//            for (int i = 0; i < arity; i++) {
//                Object field = external.getField(i);
//                if (field instanceof LocalDateTime) {
//                    external.setField(i, ((LocalDateTime) field).toInstant(ZoneOffset.UTC));
//                }
//            }
            collector.collect(external);
//            collector.collect(deserializeUtil.deserialize(string.getBytes()));
        } catch (NullPointerException e) {
            // 上游工单同步是采用upsert模式，-D数据是null，跳过
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public TypeInformation<Row> getProducedType() {
        return rowTypeInfo;
    }
}