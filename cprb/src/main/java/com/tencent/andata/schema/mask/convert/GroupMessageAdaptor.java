package com.tencent.andata.schema.mask.convert;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import java.text.ParseException;
import java.util.Map;

public class GroupMessageAdaptor extends DefaultAdaptor {

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convert(String json, Map<String, String> meta) throws JsonProcessingException, ParseException {
        ObjectNode jsonNodes = objectMapper.readValue(json, ObjectNode.class);
        String createTime = jsonNodes.get("create_time").asText();
        if (!isUtcTimeStr(createTime)) {
            jsonNodes.put("create_time", this.convertTimeStampStr2Utc(createTime));
        }
        String createdAt = jsonNodes.get("created_at").asText();
        if (!isUtcTimeStr(createdAt)) {
            jsonNodes.put("created_at", this.convertTimeStampStr2Utc(createdAt));
        }
        return objectMapper.writeValueAsString(jsonNodes);
    }
}
