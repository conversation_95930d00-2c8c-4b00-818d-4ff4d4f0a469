package com.tencent.andata.schema.mask.convert;


import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class WebImFlowAdaptor extends DefaultAdaptor {

    private static Logger log = LoggerFactory.getLogger(WebImFlowAdaptor.class);
    private static ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convert(String json, Map<String, String> meta) throws JsonProcessingException, ParseException {
        log.info("webim convert req: {}", json);
        ObjectNode jsonNodes = objectMapper.readValue(json, ObjectNode.class);
        String tableName = jsonNodes.get("table").asText();
        if (!tableName.equals("ods_im_online_customer_service_backend_data")) {
            return "";
        }
        // 构造
        ObjectNode objectNode = objectMapper.createObjectNode();
        objectNode.put("value_of_primary_key", jsonNodes.get("value_of_primary_key").asText());
        objectNode.put("dwd_create_time", timestamp2UtcStr(System.currentTimeMillis() / 1000L));
        String recordUpdateTime = jsonNodes.get("record_update_time").asText();
        if (!isUtcTimeStr(recordUpdateTime)) {
            recordUpdateTime = convertTimeStampStr2Utc(recordUpdateTime);
        }
        objectNode.put("ftime", timestamp2UtcStr(Long.parseLong(meta.get("kafkaTime")) / 1000L));
        objectNode.put("record_update_time", recordUpdateTime);
        objectNode.put("request_id", jsonNodes.get("requestid").asText());
        objectNode.put("operation", jsonNodes.get("rpc_name").asText());

        if (jsonNodes.get("msgdata") != null) {
            objectNode.put("msg_seq", jsonNodes.get("msgseq").asText());
            objectNode.put("msg_data", jsonNodes.get("msgdata").asText());
        }
        JsonNode conversation = jsonNodes.get("conversation");
        Iterator<String> names = conversation.fieldNames();
        while (names.hasNext()) {
            String name = names.next();
            JsonNode value = conversation.get(name);
            if (name.equals("AllLevelCategory")) {
                String text = value.asText();
                if (StringUtils.isNotEmpty(text)) {
                    JsonNode category = objectMapper.readTree(text);
                    if (category.hasNonNull("first_level")) {
                        objectNode.put("category_level1_name", category.get("first_level").get("name").asText());
                    }
                    if (category.hasNonNull("second_level")) {
                        objectNode.put("category_level2_name", category.get("second_level").get("name").asText());
                    }
                    if (category.hasNonNull("third_level")) {
                        objectNode.put("category_level3_name", category.get("third_level").get("name").asText());
                    }
                }
            }
            objectNode.put(caseConversion(name), value);
        }
//        objectNode.put("conversation_id", conversation.get("ConversationId").asText());
//        objectNode.put("owner_uin", conversation.get("OwnerUin").asText());
//        objectNode.put("uin", conversation.get("Uin").asText());
//        objectNode.put("uid", conversation.get("Uid").asText());
//        objectNode.put("source", conversation.get("Source").asText());
//        objectNode.put("status", conversation.get("Status").asText());
//        objectNode.put("category_id", conversation.get("CategoryId").asInt());
//        objectNode.put("service_scene", conversation.get("ServiceScene").asText());
//        objectNode.put("current_staff", conversation.get("CurrentStaff").asText());
//        objectNode.put("staffs", conversation.get("Staffs").asText());
//        objectNode.put("appraise", conversation.get("Appraise").asText());
//        objectNode.put("service_rate", conversation.get("ServiceRate").asText());
//        objectNode.put("unsatisfy_reason", conversation.get("UnsatisfyReason").asText());
        // 重写时间属性
        List<String> timeFields = Stream.of("AppraiseTime", "CreateTime",
                "CustomerUpdatedTime", "StaffUpdatedTime", "CustomerUpdatedTime",
                "StaffFirstUpdatedTime", "FinishTime", "ApplyFinishTime",
                "AwaitingSupplementTime").collect(Collectors.toList());
        for (String timeField : timeFields) {
            JsonNode node = conversation.get(timeField);
            if (node != null) {
                objectNode.put(caseConversion(timeField), timestamp2UtcStr(node.asLong()));
            }
        }
//
//        objectNode.put("ticket_ids", conversation.get("TicketIds").asText());
//        objectNode.put("conversation_ticket_ids", conversation.get("ConversationTicketIds").asText());
//        objectNode.put("title", conversation.get("Title").asText());
        return objectMapper.writeValueAsString(objectNode);
    }

    private static String caseConversion(String text) {
        // 将首字母转为小写
        String firstChar = text.substring(0, 1).toLowerCase();
        // 将剩余部分转为小写，并在前面添加下划线
        String rest = text.substring(1).replaceAll("([A-Z])", "_$1").toLowerCase();
        // 拼接结果
        return firstChar + rest;
    }
}
