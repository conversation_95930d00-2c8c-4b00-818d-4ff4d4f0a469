package com.tencent.andata.filter.ticket;

import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.types.Row;

import java.time.Instant;

/**
 * 判断工单是否结单
 */
public class TicketConditionFilter implements FilterFunction<Row> {

    private final String timeField;
    private final Long startTime;
    private final Long endTime;
    private final Long increaseTime;


    public TicketConditionFilter(String timeField, Long startTime,
                                 Long endTime, Long increaseTime) {
        this.timeField = timeField;
        this.startTime = startTime;
        this.endTime = endTime;
        this.increaseTime = increaseTime;
    }

    @Override
    public boolean filter(Row row) throws Exception {
        Instant time = (Instant) row.getField(timeField);
        long second = time.getEpochSecond();
        // 已结单
        int status = Integer.parseInt(row.getField("status").toString());
        if (status != 3) {
            return false;
        }
        if (second >= increaseTime) {
            return true;
        }
        if (second >= startTime && second <= endTime) {
            return true;
        }
        return false;
//        int serviceChannel = Integer.parseInt(row.getField("service_channel").toString());
    }
}
