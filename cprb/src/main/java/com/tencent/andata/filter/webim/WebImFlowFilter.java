package com.tencent.andata.filter.webim;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.types.Row;


public class WebImFlowFilter implements FilterFunction<Row> {

    private final int[] closeStatus = new int[]{6, 7, 9, 10, 12};

    @Override
    public boolean filter(Row row) throws Exception {
        Object rowField = row.getField("mask_msg");
        if (rowField != null) {
            if (StringUtils.isNotEmpty(rowField.toString())) {
                return true;
            }
        }
        int status = Integer.parseInt(String.valueOf(row.getField("status")));
        for (int i : closeStatus) {
            if (status == i) {
                return true;
            }
        }
        return false;
    }
}
