package com.tencent.andata.filter.promt_group;

import org.apache.flink.types.Row;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegexFilter implements TagFilter {
    // 正向/反向匹配
    private final boolean isForward;
    private String regexStr;
    private Pattern pattern;
    private Matcher matcher;

    public RegexFilter(boolean isForward) {
        this.isForward = isForward;
    }

    @Override
    public boolean filter(Row row) {
        // 没有则返回False
        final Object msgObj = row.getField("reply");
        if (msgObj == null) {
            return false;
        }
        final String msg = msgObj.toString().substring(0, Math.min(msgObj.toString().length(), 8192));
        // 如果没有正则的话则不进行过滤
        if (this.regexStr == null || Objects.equals(this.regexStr, "")) {
            return true;
        }
        pattern = Pattern.compile(this.regexStr);
        matcher = pattern.matcher(msg);
        final boolean find = matcher.find();
        return this.isForward == find;
    }

    @Override
    public void clear() {
        this.regexStr = null;
    }

    @Override
    public void initFromRow(Row row) throws RuntimeException {
        final Object regexFilter;
        if (isForward) {
            regexFilter = row.getField("forward_filter");
        } else {
            regexFilter = row.getField("reverse_filter");
        }
        this.regexStr = regexFilter == null ? null : regexFilter.toString();
    }
}
