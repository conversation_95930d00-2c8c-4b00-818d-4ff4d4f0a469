package com.tencent.andata.filter;

import com.tencent.andata.filter.promt_group.RegexFilter;
import com.tencent.andata.filter.promt_group.TagFilter;
import com.tencent.andata.filter.promt_group.TicketServiceSceneFilter;
import com.tencent.andata.log.FlinkLog;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class TicketReplyTagFilter implements FilterFunction<Row> {
    public FlinkLog logger = FlinkLog.getInstance();
    private List<TagFilter> tagFilterList;

    public TicketReplyTagFilter() {
        this.tagFilterList = new ArrayList<TagFilter>();
    }

    public void init() {
        if (this.tagFilterList.isEmpty()) {
            // ticket_service_scene_filter
            tagFilterList.add(new TicketServiceSceneFilter());
            // reverse_regex_filter
            tagFilterList.add(new RegexFilter(false));
            // forward_regex_filter
            tagFilterList.add(new RegexFilter(true));
        }
    }

    /**
     * 基于Filter进行过滤
     * 当Filter无效（非json，为空）时返回false
     *
     * @param row
     * @return
     * @throws Exception
     */
    @Override
    public boolean filter(Row row) throws Exception {
        this.init();
        // 清理Filter
        this.tagFilterList.forEach(TagFilter::clear);
        // 从tag_config字段中初始化规则
        try {
            this.tagFilterList.forEach(f -> f.initFromRow(row));
        } catch (Exception e) {
            logger.warn(
                    String.format(
                            "[CPRB-CommonTagFilter] process tag filter json error: %s\ndata:%s",
                            e.getMessage(),
                            row
                    )
            );
            return false;
        }
        // 条件均满足则返回true
        for (TagFilter filter : this.tagFilterList) {
            final boolean res = filter.filter(row);
            // 一个不满足则直接返回，提高过滤性能
            if (!res) {
                return false;
            }
        }
        return true;
    }
}
