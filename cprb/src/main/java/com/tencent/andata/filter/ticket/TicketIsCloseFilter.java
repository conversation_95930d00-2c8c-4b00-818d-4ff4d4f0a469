package com.tencent.andata.filter.ticket;

import com.tencent.andata.utils.StreamUtils;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 判断工单是否结单
 */
public class TicketIsCloseFilter implements FilterFunction<Row> {
    private final ObjectMapper mapper;

    public TicketIsCloseFilter() {
        this.mapper = new ObjectMapper();

    }

    @Override
    public boolean filter(Row row) throws Exception {
        List<JsonNode> ticketOperations = StreamUtils
                .streamOf(mapper.readValue(row.<String>getFieldAs("operation_data"), ArrayNode.class))
                .collect(Collectors.toList());
        for (JsonNode operation : ticketOperations) {
            final long operationType = operation.get("operation_type").asLong();
            final long targetStatus = operation.get("target_status").asLong();
            // 判断是否结单
            if (operationType == 16 || operationType == 1 && targetStatus == 3) {
                return true;
            }
        }
        return false;
    }
}
