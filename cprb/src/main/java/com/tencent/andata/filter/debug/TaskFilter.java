package com.tencent.andata.filter.debug;

import com.tencent.andata.model.TaskFilterParams;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.types.Row;

import java.util.List;

public class TaskFilter implements FilterFunction<Row> {

    private final List<Long> unfinishedTaskIds;
    private final Long lastTaskId;

    public TaskFilter(TaskFilterParams params) {
        this.unfinishedTaskIds = params.getUnfinishedTaskIds();
        this.lastTaskId = params.getLastTaskId();
    }

    @Override
    public boolean filter(Row row) throws Exception {
        long taskId = Long.parseLong(String.valueOf(row.getField("task_id")));
        if (taskId > lastTaskId) {
            return true;
        }
        for (Long unfinishedTaskId : unfinishedTaskIds) {
            if (taskId == unfinishedTaskId) {
                return true;
            }
        }
        return false;
    }
}
