package com.tencent.andata.filter;

import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.types.Row;

import java.time.Instant;

public class TimeFilter implements FilterFunction<Row> {

    private final String timeField;
    private final Long startTime;
    private final Long endTime;
    private final Long increaseTime;


    public TimeFilter(String timeField, Long startTime,
                                 Long endTime, Long increaseTime) {
        this.timeField = timeField;
        this.startTime = startTime;
        this.endTime = endTime;
        this.increaseTime = increaseTime;
    }

    @Override
    public boolean filter(Row row) throws Exception {
        Instant time = (Instant) row.getField(timeField);
        long second = time.getEpochSecond();
        if (second >= increaseTime) {
            return true;
        }
        if (second >= startTime && second <= endTime) {
            return true;
        }
        return false;
    }
}