package com.tencent.andata.filter;

import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.types.Row;

import java.time.Instant;
import java.util.List;

public class ConditionInFilter implements FilterFunction<Row> {

    private final String field;
    private final List<String> values;


    public ConditionInFilter(String field, List<String> values) {
        this.field = field;
        this.values = values;
    }

    @Override
    public boolean filter(Row row) throws Exception {
        String data = row.getField(field).toString();
        for (String value : values) {
            if (value.equals(data)) {
                return true;
            }
        }
        return false;
    }
}