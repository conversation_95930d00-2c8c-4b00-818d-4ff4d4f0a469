package com.tencent.andata.filter.promt_group;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class TicketServiceSceneFilter implements TagFilter {
    private List<Map<String, Integer>> filterSceneList;
    private ObjectMapper objectMapper;

    public TicketServiceSceneFilter() {
        this.filterSceneList = new ArrayList<Map<String, Integer>>();
        this.objectMapper = new ObjectMapper();
    }

    public void addServiceScene(Map<String, Integer> sceneMap) {
        this.filterSceneList.add(sceneMap);
    }

    public void clear() {
        this.filterSceneList.clear();
    }

    /**
     * 从数据字段中获取过滤条件
     *
     * @param row 含数据
     * @throws RuntimeException 数据格式不正确
     */
    @Override
    public void initFromRow(Row row) throws RuntimeException {
        JsonNode serviceSceneFilter = null;
        try {
            final Object ticketServiceSceneFilter = row.getField("ticket_service_scene_filter");
            if (ticketServiceSceneFilter != null && !Objects.equals(ticketServiceSceneFilter.toString(), "")) {
                serviceSceneFilter = this.objectMapper.readTree(
                        ticketServiceSceneFilter.toString()
                );
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(
                    String.format(
                            "[Cprb-FilterInitFromRow] decode json ticket_service_scene_filter error: %s\n",
                            e.getMessage()
                    )
            );
        }
        if (serviceSceneFilter != null) {
            // 不为空则需要为数组
            if (!serviceSceneFilter.isNull() && !serviceSceneFilter.isArray()) {
                throw new RuntimeException(
                        "[Cprb-FilterInitFromRow]the ticket_service_scene_filter data should be a array or null!"
                );
            }
            for (JsonNode n : serviceSceneFilter) {
                final String sceneJsonString = n.toString();
                try {
                    this.filterSceneList.add(
                            this.objectMapper.readValue(sceneJsonString, HashMap.class)
                    );
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(String.format(
                            "[Cprb-FilterInitFromRow]the ticket_service_scene_filter is invalid json!\n %s",
                            sceneJsonString
                    ));
                }
            }
        }
    }

    @Override
    public boolean filter(Row row) {
        if (this.filterSceneList.isEmpty()) {
            return true;
        }
        for (Map<String, Integer> sceneMap : this.filterSceneList) {
            // 多个工单归档条件，取或
            boolean tmpRes = true;
            for (int i = 1; i <= 4; i++) {
                // 从Filter中获取归档
                final Integer stdValue = sceneMap.get(
                        String.format(
                                "sc%d_id",
                                i
                        )
                );
                // TagFilter没有定义归档则返回true
                if (stdValue == null) {
                    break;
                }
                final Object ticketScene = row.getField(
                        String.format(
                                "service_scene_level%d_id",
                                i
                        )
                );
                // 工单数据没有归档或者归档不一致则返回false
                if (ticketScene == null || (long) ticketScene != stdValue) {
                    tmpRes = false;
                }
            }
            // 如果已经命中了一个归档，则直接返回
            if (tmpRes) {
                return true;
            }
        }
        // 一个归档都没命中则返回false
        return false;
    }
}
