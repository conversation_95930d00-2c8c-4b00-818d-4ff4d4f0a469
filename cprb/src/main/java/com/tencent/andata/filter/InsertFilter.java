package com.tencent.andata.filter;

import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class InsertFilter implements FilterFunction<Row> {

    private static final Logger logger = LoggerFactory.getLogger(InsertFilter.class);

    @Override
    public boolean filter(Row row) throws Exception {
        if (row.getKind().equals(RowKind.INSERT)) {
            return true;
        }
        return false;
    }
}
