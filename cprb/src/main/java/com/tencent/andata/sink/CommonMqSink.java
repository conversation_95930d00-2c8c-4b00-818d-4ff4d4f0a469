package com.tencent.andata.sink;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.FlinkEnvUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Preconditions;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class CommonMqSink implements Sink {

    public static FlinkLog logger = FlinkLog.getInstance();
    private final MqConf mqConf;
    private List<String> columnNames;
    private List<String> columnTypes;
    private final String primaryKey;
    private boolean printLog = false;

    public CommonMqSink(MqConf mqConf, String primaryKey, boolean printLog) {
        this.mqConf = mqConf;
        this.primaryKey = primaryKey;
        this.printLog = printLog;
    }

    public CommonMqSink(MqConf mqConf, Table table, String primaryKey) {
        // 定义 Kafka 表的连接器和格式
        this.columnNames = table.getResolvedSchema().getColumnNames();
        this.columnTypes = table.getResolvedSchema().getColumnDataTypes().stream().map(type -> {
            return type.getLogicalType().toString();
        }).collect(Collectors.toList());
        this.mqConf = mqConf;
        this.primaryKey = primaryKey;
    }

    public CommonMqSink(MqConf mqConf, Table table, String primaryKey, Set<String> removeFields) {
        // 定义 Kafka 表的连接器和格式
        this.columnNames = table.getResolvedSchema().getColumnNames();
        this.columnTypes = table.getResolvedSchema().getColumnDataTypes().stream().map(type -> {
            return type.getLogicalType().toString();
        }).collect(Collectors.toList());
        for (int i = 0; i < this.columnNames.size(); i++) {
            String col = columnNames.get(i);
            if (removeFields.contains(col)) {
                columnNames.remove(i);
                columnTypes.remove(i);
            }
        }
        this.mqConf = mqConf;
        this.primaryKey = primaryKey;
    }

    public void setTable(Table table) {
        this.columnNames = table.getResolvedSchema().getColumnNames();
        this.columnTypes = table.getResolvedSchema().getColumnDataTypes().stream().map(type -> {
            return type.getLogicalType().toString();
        }).collect(Collectors.toList());
    }

    public static final String KAFKA_SINK_SQL = "CREATE TABLE kafka_sink_tb (\n"
            + "%s"
            + ",\nPRIMARY KEY (%s) NOT ENFORCED\n"
            + ") WITH (\n"
            + "  'connector' = 'upsert-kafka',\n"
            + "  'topic' = '%s',\n"
            + "  'properties.bootstrap.servers' = '%s',\n"
            + "  'properties.max.request.size' = '31457280',\n"
            + "  'properties.batch.size' = '16384',\n"
            + "  'properties.buffer.memory' = '31457280',\n"
            + "  'properties.linger.ms' = '5',\n"
            + "  'key.format' = 'json',\n"
            + "  'value.format' = 'json',\n"
            + "  'value.json.fail-on-missing-field' = 'false'"
            + ");";

    public static final String INSERT_KAFKA_SQL = "INSERT INTO kafka_sink_tb (\n"
            + "SELECT\n"
            + "%s"
            + "\nFROM flow_info_kafka_sink)";

    public String getCreateKafkaTbSql() {
        StringBuilder fieldsBuilder = new StringBuilder();
        for (int i = 0; i < this.columnNames.size(); i++) {
            String columnName = columnNames.get(i);
            String columnType = columnTypes.get(i);
            fieldsBuilder.append(columnName);
            fieldsBuilder.append(" ");
            fieldsBuilder.append(columnType);
            fieldsBuilder.append(",\n");
        }
        return String.format(KAFKA_SINK_SQL,
                StringUtils.stripEnd(fieldsBuilder.toString(), ",\n"),
                primaryKey, mqConf.getTopic(), mqConf.getBroker());
    }

    public String getInsertKafkaTbSql() {
        StringBuilder fieldsBuilder = new StringBuilder();
        for (int i = 0; i < this.columnNames.size(); i++) {
            String columnName = columnNames.get(i);
            fieldsBuilder.append(columnName);
            fieldsBuilder.append(",\n");
        }
        return String.format(INSERT_KAFKA_SQL,
                StringUtils.stripEnd(fieldsBuilder.toString(), ",\n"));
    }

    @Override
    public void sink(FlinkEnvUtils.FlinkEnv flinkEnv, Table tbl) {
        Preconditions.checkNotNull(columnNames);
        Preconditions.checkNotNull(columnTypes);
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        DataStream<Row> dataStream = tEnv.toDataStream(tbl);
        tEnv.createTemporaryView("flow_info_kafka_sink", tEnv.fromDataStream(
                dataStream.map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        if (printLog) {
                            logger.info(String.format("[CommonMqSink] 推送数据:%s", row.toString()),
                                    row.getField("ticket_id").toString());
                        }
                        return row;
                    }
                }).returns(dataStream.getType())));
        String createKafkaTbSql = this.getCreateKafkaTbSql();
        System.out.println(createKafkaTbSql);
        tEnv.executeSql(createKafkaTbSql);
        String insertKafkaTbSql = this.getInsertKafkaTbSql();
        System.out.println(insertKafkaTbSql);
        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertKafkaTbSql);
    }

}
