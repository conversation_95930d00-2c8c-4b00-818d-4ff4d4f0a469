package com.tencent.andata.sink;

import static com.tencent.andata.utils.TableUtils.row2Json;
import static com.tencent.andata.utils.TableUtils.sinkToHbase;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.TableUtils;
import java.util.List;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class CommonHbaseSink implements Sink {

    private final String srcHTblName;
    private final String fHTblName;
    List<String> pkList;


    public CommonHbaseSink(String srcHTblName, String fHtblName, List<String> pkList) {
        this.srcHTblName = srcHTblName;
        this.fHTblName = fHtblName;
        this.pkList = pkList;
    }


    @Override
    public void sink(FlinkEnv flinkEnv, Table tbl) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // 注册hbaseTable
        TableUtils.hbaseTable2FlinkTable(this.fHTblName, this.srcHTblName, "cf", tEnv);
        String rowKey = String.join(" || '-' || ", this.pkList);
        tEnv.createTemporaryView("source_view", tbl);
        Table hTbl = tEnv.sqlQuery(row2Json(tbl, rowKey, "source_view"));
        StatementSet stmtSet = flinkEnv.stmtSet();
        tEnv.createTemporaryView("h_view", hTbl);
        String sql = sinkToHbase("h_view", fHTblName);
        stmtSet.addInsertSql(sql);
    }

}
