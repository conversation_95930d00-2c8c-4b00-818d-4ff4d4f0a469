package com.tencent.andata.sink;

import com.tencent.andata.constant.TicketFlow;
import com.tencent.andata.utils.FlinkEnvUtils;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class MqSink implements Sink {

    private final String topic;

    public MqSink(String topic) {
        this.topic = topic;
    }

    public static final String KAFKA_SINK_SQL = "CREATE TABLE ticket_flow_kafka_sink (\n" 
            +             TicketFlow.CREATE_FIELDS 
            +             "    ,PRIMARY KEY (id) NOT ENFORCED\n" 
            +             ") WITH (\n" 
            +             "  'connector' = 'upsert-kafka',\n" 
            +             "  'topic' = '%s',\n" 
            +             "  'properties.bootstrap.servers' = '30.181.143.184:9092',\n" 
            +             "  'properties.max.request.size' = '31457280',\n" 
            +             "  'properties.batch.size' = '32768',\n" 
            +             "  'properties.buffer.memory' = '67108864',\n" 
            +             "  'properties.linger.ms' = '100',\n" 
            +             "  'key.format' = 'json',\n" 
            +             "  'value.format' = 'json',\n" 
            +             "  'value.json.fail-on-missing-field' = 'false'" 
            +             ");";

    public static final String INSERT_KAFKA_SQL = "INSERT INTO ticket_flow_kafka_sink (\n" 
            +             "SELECT\n" 
            +             TicketFlow.FIELDS 
            +             "FROM incident_flow_info_kafka_sink)";


    @Override
    public void sink(FlinkEnvUtils.FlinkEnv flinkEnv, Table tbl) {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        tEnv.createTemporaryView("incident_flow_info_kafka_sink", tbl);
        tEnv.executeSql(String.format(KAFKA_SINK_SQL, topic));
        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(INSERT_KAFKA_SQL);
//        tEnv.executeSql(INSERT_KAFKA_SQL);
    }
}
