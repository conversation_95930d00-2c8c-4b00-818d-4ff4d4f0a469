package com.tencent.andata.sink.debugtask;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.CprbTaskResult;
import com.tencent.andata.util.CPRBUtils;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

public class CallBackSink extends RichSinkFunction<CprbTaskResult> {
    public static FlinkLog logger = FlinkLog.getInstance();

    @Override
    public void invoke(CprbTaskResult result, Context context) {
        try {
            CPRBUtils.taskCallback(result.getTaskId(),
                    result.getTagId(),
                    result.getConfId(),
                    result.getOperationType(),
                    result.getCosFileName());
        } catch (Exception e) {
            logger.error(
                    String.format(
                            "[DebugTaskSink] callback error: %s, task res: %s",
                            e,
                            result.toString()
                    )
            );
        }
    }
}
