package com.tencent.andata.sink;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.map.Row2MessageMap;
import com.tencent.andata.schema.mask.MessageSerialization;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.TableIdentifier;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.types.Row;

import java.util.Properties;

public class DataBusSink implements Sink {

    MqConf mqConf;
    TableIdentifier tableIdentifier;

    public DataBusSink(MqConf mqConf, TableIdentifier tableIdentifier) {
        System.out.println("初始化DataBusSink");
        this.mqConf = mqConf;
        this.tableIdentifier = tableIdentifier;

    }

    /***
     * sink2MessageDataBus .
     * @param ds .
     * @return .
     * @throws TableNotExistException .
     */
    public DataStreamSink<Message> sink2MessageDataBus(DataStream<Row> ds) throws TableNotExistException {
        Properties properties = new Properties();
        properties.setProperty("bootstrap.servers", mqConf.getBroker());
        properties.setProperty("max.request.size", "12582912");
        KafkaSink<Message> sink = KafkaSink.<Message>builder()
                .setKafkaProducerConfig(
                        properties
                )
                .setBootstrapServers(mqConf.getBroker())
                .setRecordSerializer(new MessageSerialization<>(Message.class, mqConf.getTopic()))
                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();

        return ds
                .map(new Row2MessageMap(tableIdentifier, false))
                .sinkTo(sink)
                .name(getUid())
                .uid(getUid())
                .setParallelism(1);

    }

    @Override
    public void sink(FlinkEnvUtils.FlinkEnv flinkEnv, Table tbl) throws Exception {
        DataStream<Row> dataStream = flinkEnv.streamTEnv().toDataStream(tbl);
        this.sink2MessageDataBus(dataStream);
    }

    private String getUid() {
        return String.format("kafka-sink-%s", mqConf);
    }
}
