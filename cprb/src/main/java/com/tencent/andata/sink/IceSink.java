package com.tencent.andata.sink;

import com.tencent.andata.utils.FlinkEnvUtils;
import lombok.Builder;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;


public class IceSink implements Sink {

    private final String dstTb;

    public IceSink(String dstTb) {
        this.dstTb = dstTb;
    }

    @Override
    public void sink(FlinkEnvUtils.FlinkEnv flinkEnv, Table tbl) {
        StatementSet stmtSet = flinkEnv.stmtSet();
        flinkEnv.streamTEnv().createTemporaryView("tmp_tb", tbl);
        stmtSet.addInsertSql(insertIntoSql(
                "tmp_tb",
                dstTb, // 写入ice
                tbl,
                ICEBERG
        ));
    }

}
