package com.tencent.andata.sink;

import com.tencent.andata.constant.TicketFlow;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.apache.flink.util.Preconditions;

import java.util.ArrayList;

public class BaseSink {

    public static class Builder {

        private FlinkEnvUtils.FlinkEnv flinkEnv;
        private ArrayList<Sink> sinks = new ArrayList<>();

        public Builder setFlinkEnv(FlinkEnvUtils.FlinkEnv flinkEnv) {
            this.flinkEnv = flinkEnv;
            return this;
        }

        public Builder addSink(Sink sink) {
            this.sinks.add(sink);
            return this;
        }

        public BaseSink build(RainbowUtils rainbowUtils) throws Exception {
            Preconditions.checkNotNull(flinkEnv);

            KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                    .setRainbowUtils(rainbowUtils);

            DatabaseConf readPgDb = kvConfBuilder
                    // 只读库
                    .setGroupName("cdc.database.pgsql.dataware_r")
                    .build();

            flinkEnv.streamTEnv().executeSql(String.format(SR_SCENE_CREATE_SQL,
                    readPgDb.password, readPgDb.dbHost,
                    readPgDb.dbPort, readPgDb.dbName,
                    readPgDb.userName
            ));
            return new BaseSink(flinkEnv, sinks);
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final String FILTER_FIELD_SQL = ""
            + "SELECT \n"
            + TicketFlow.FIELDS
            + "FROM incident_flow_group_info\n"
            + "";

    public BaseSink(FlinkEnvUtils.FlinkEnv flinkEnv, ArrayList<Sink> sinks) {
        this.flinkEnv = flinkEnv;
        this.sinks = sinks;
    }

    private final FlinkEnvUtils.FlinkEnv flinkEnv;
    private final ArrayList<Sink> sinks;

    /***
     * ds写入
     * @param dataStream .
     * @throws Exception .
     */
    public void run(DataStream<Row> dataStream) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // 流转表
        Table filterTable = tEnv.fromChangelogStream(dataStream,
                Schema.newBuilder()
                        .primaryKey("id")
                        .build(),
                ChangelogMode.insertOnly());
        tEnv.createTemporaryView("incident_flow_base_info", filterTable);
        // look up join 工单归档维表
        Table table = tEnv.sqlQuery(JOIN_SCENE_SQL);
        tEnv.createTemporaryView("incident_flow_group_info", table);
        Table tbl = tEnv.sqlQuery(FILTER_FIELD_SQL);
        for (Sink sink : sinks) {
            if (sink instanceof CommonMqSink) {
                ((CommonMqSink) sink).setTable(tbl);
            }
            sink.sink(flinkEnv, tbl);
        }
    }

    public static final String JOIN_SCENE_SQL = ""
            + "with incident_flow_proc_info as (\n"
            + "SELECT \n"
            + "  id,\n"
            + "  ticket_id,\n"
            + "  reply,\n"
            + "  c_reply,\n"
            + "  msg,\n"
            + "  source,\n"
            + "  url,\n"
            + "  question,\n"
            + "  service_scene,\n"
            + "  ticket_create_time,\n"
            + "  update_time,\n"
            + "  close_time,\n"
            + "  PROCTIME() as process_time\n"
            + "FROM incident_flow_base_info\n"
            + ")\n"
            + "SELECT \n"
            + "  id,\n"
            + "  ticket_id,\n"
            + "  reply,\n"
            + "  c_reply,\n"
            + "  msg,\n"
            + "  source,\n"
            + "  url,\n"
            + "  question,\n"
            + "  service_scene,\n"
            + "  ticket_create_time,\n"
            + "  t1.update_time,\n"
            + "  close_time,\n"
            + "  t2.service_scene_level1_id as service_scene_level1_id,\n"
            + "  t2.service_scene_level2_id as service_scene_level2_id,\n"
            + "  t2.service_scene_level3_id as service_scene_level3_id,\n"
            + "  t2.service_scene_level4_id as service_scene_level4_id\n"
            + "FROM incident_flow_proc_info as t1\n"
            + "LEFT JOIN dim_service_scenes FOR SYSTEM_TIME AS OF t1.`process_time` AS t2 "
            + "ON (t1.service_scene = t2.dim_id)\n"
            + "";

    public static final String SR_SCENE_CREATE_SQL = "CREATE TABLE `dim_service_scenes` (\n"
            + "  `dim_id` bigint,\n"
            + "  `service_scene_level1_id` bigint,\n"
            + "  `service_scene_level2_id` bigint,\n"
            + "  `service_scene_level3_id` bigint,\n"
            + "  `service_scene_level4_id` bigint,\n"
            + "  `service_scene_level1_name` STRING,\n"
            + "  `service_scene_level2_name` STRING,\n"
            + "  `service_scene_level3_name` STRING,\n"
            + "  `service_scene_level4_name` STRING,\n"
            + "  `obs_id` STRING,\n"
            + "  `status` bigint,\n"
            + "  `disabled` bigint,\n"
            + "  `property` STRING,\n"
            + "  `update_time` timestamp(6),\n"
            + "  `modify_time` timestamp(6),\n"
            + "  PRIMARY KEY (`dim_id`) NOT ENFORCED\n"
            + ") WITH (\n"
            + "'connector'='jdbc',\n"
            + "'lookup.cache.max-rows'='1000',\n"
            + "'lookup.cache.ttl'='1h',\n"
            + "'password'='%s',\n"
            + "'table-name'='dim_service_scenes',\n"
            + "'url'='***************************************************************************&"
            + "serverTimezone=Asia/Shanghai',\n"
            + "'username'='%s'"
            + ")";
}