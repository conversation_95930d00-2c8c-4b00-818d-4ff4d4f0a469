package com.tencent.andata.map.mask;

import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

import java.util.regex.Pattern;

public class CleanFlowDataMap implements MapFunction<Row, Row> {
    @Override
    public Row map(Row row) throws Exception {
        row.setField("reply", Pattern.compile("[\\u0000-\\u001f]|\\p{C}")
                .matcher(row.getField("reply").toString())
                .replaceAll(""));
        row.setField("c_reply", Pattern.compile("[\\u0000-\\u001f]|\\p{C}")
                .matcher(row.getField("c_reply").toString())
                .replaceAll(""));
        row.setField("msg", Pattern.compile("[\\u0000-\\u001f]|\\p{C}")
                .matcher(row.getField("msg").toString())
                .replaceAll(""));
        row.setField("question", Pattern.compile("[\\u0000-\\u001f]|\\p{C}")
                .matcher(row.getField("question").toString())
                .replaceAll(""));
        return row;
    }
}
