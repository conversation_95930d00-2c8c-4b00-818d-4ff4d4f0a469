package com.tencent.andata.map.mask.v3.ticket;

import com.tencent.andata.util.TicketFlowStatusUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class TicketFlowGroupProcess extends KeyedProcessFunction<String, Row, Row> {

    public static Logger log = LoggerFactory.getLogger(TicketFlowGroupProcess.class);

    public static ObjectMapper mapper = new ObjectMapper();
    private ListState<Tuple3<Long, String, List<String>>> flowState;
    private ListState<Tuple3<Long, String, List<String>>> innerFlowState;
    private ValueState<Boolean> updateState;
    private ValueState<Boolean> innerUpdateState;
    private final int maxFlowCount;
    private ValueState<Boolean> finishState;
    private ValueState<Row> closeFlowState;

    public TicketFlowGroupProcess(int maxFlowCount) {
        this.maxFlowCount = maxFlowCount;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
//        StateTtlConfig ttlConfig = StateTtlConfig
//                .newBuilder(Time.minutes(5))
//                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
//                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
//                .build();
//        descriptor.enableTimeToLive(ttlConfig);
        ListStateDescriptor<Tuple3<Long, String, List<String>>> descriptor = new ListStateDescriptor<>(
                "flowState", TypeInformation.of(new TypeHint<Tuple3<Long, String, List<String>>>() {
        }));
        // 保存所有流水状态
        flowState = getRuntimeContext().getListState(descriptor);
        ListStateDescriptor<Tuple3<Long, String, List<String>>> innerDescriptor = new ListStateDescriptor<>(
                "innerFlowState", TypeInformation.of(new TypeHint<Tuple3<Long, String, List<String>>>() {
        }));
        innerFlowState = getRuntimeContext().getListState(innerDescriptor);
        // 流水状态是否改变
        ValueStateDescriptor<Boolean> updateStateDesc = new ValueStateDescriptor<>(
                "updateStateDesc", TypeInformation.of(new TypeHint<Boolean>() {
        }));
        updateState = getRuntimeContext().getState(updateStateDesc);
        ValueStateDescriptor<Boolean> innerUpdateStateDesc = new ValueStateDescriptor<>(
                "innerUpdateStateDesc", TypeInformation.of(new TypeHint<Boolean>() {
        }));
        innerUpdateState = getRuntimeContext().getState(innerUpdateStateDesc);
        // 本次是否下发
        ValueStateDescriptor<Boolean> finishStateDesc = new ValueStateDescriptor<>(
                "finishStateDesc", TypeInformation.of(new TypeHint<Boolean>() {
        }));
        finishState = getRuntimeContext().getState(finishStateDesc);
        ValueStateDescriptor<Row> closeFlowStateDesc = new ValueStateDescriptor<>(
                "closeFlowStateDesc", TypeInformation.of(new TypeHint<Row>() {
        }));
        // 结单流水
        closeFlowState = getRuntimeContext().getState(closeFlowStateDesc);
    }

    @Override
    public void processElement(Row row,
                               KeyedProcessFunction<String, Row, Row>.Context context,
                               Collector<Row> collector) throws Exception {
        initState();
        // 将行数据存储到键控状态中
        addData(row);
        if (finishState.value()) {
            // 触发向下游发送
            // 这里有可能不会下发，如果流水状态没有发生改变
            if (updateState.value()) {
                emit(context, collector, flowState, "ticket_extern");
                updateState.update(false);
            }
            if (innerUpdateState.value()) {
                emit(context, collector, innerFlowState, "ticket_inner");
                innerUpdateState.update(false);
            }
            finishState.update(false);
//            flowState.clear();
        }
    }

    private void initState() throws IOException {
        if (finishState.value() == null) {
            finishState.update(false);
        }
        if (updateState.value() == null) {
            updateState.update(false);
        }
        if (innerUpdateState.value() == null) {
            innerUpdateState.update(false);
        }
    }

    private void emit(KeyedProcessFunction<String, Row, Row>.Context context, Collector<Row> out,
                      ListState<Tuple3<Long, String, List<String>>> state, String source) throws Exception {
        // 处理外部流水
        ArrayList<StringBuilder> builders = new ArrayList<>(3);
        for (int i = 0; i < 3; i++) {
            builders.add(new StringBuilder());
        }
        int count = 0;
        for (Tuple3<Long, String, List<String>> flowData : state.get()) {
            count++;
            if (count > maxFlowCount) {
                break;
            }
            List<String> list = flowData.f2;
            for (int i = 0; i < 3; i++) {
                if (StringUtils.isNotEmpty(list.get(i))) {
                    builders.get(i).append(flowData.f1 + ":" + list.get(i) + "\r\n");
                }
            }
        }
        if (builders.get(0).length() > 0) {
            Row row = closeFlowState.value();
            String ticketId = row.getField("ticket_id").toString();
            if (StringUtils.isEmpty(ticketId)) {
                return;
            }
            row.setField("source", source);
            row.setField("reply", builders.get(0).toString());
            row.setField("c_reply", builders.get(1).toString());
            row.setField("msg", builders.get(2).toString());
            row.setField("id", row.getField("source") + "-" + ticketId);
            out.collect(row);
        }
    }

    private void addData(Row row) throws Exception {
        // 外部流水
        String reply = row.getField("extern_reply").toString();
        String cReply = row.getField("c_msg").toString();
        String maskMsg = row.getField("mask_msg").toString();
        // 内部流水
        String innerReply = row.getField("inner_reply").toString();
        String cInnerMsg = row.getField("c_inner_msg").toString();
        String maskInnerMsg = row.getField("mask_inner_msg").toString();
        ArrayList<String> replys = new ArrayList<>();
        ArrayList<String> innerReplys = new ArrayList<>();
        if (TicketFlowStatusUtil.isClose(row) || TicketFlowStatusUtil.isManualClose(row)) {
            finishState.update(true);
            if (!TicketFlowStatusUtil.isManualClose(row)) {
                closeFlowState.update(row);
            }
        }
        if (StringUtils.isEmpty(reply)
                && StringUtils.isEmpty(innerReply)) {
            return;
        }
        if (TicketFlowStatusUtil.isInnerReply(row)) {
            innerReplys.add(innerReply);
            innerReplys.add(cInnerMsg);
            innerReplys.add(maskInnerMsg);
        }
        if (TicketFlowStatusUtil.isExternReply(row)) {
            replys.add(reply);
            replys.add(cReply);
            replys.add(maskMsg);
        }
        long time = ((Instant) row.getField("operate_time")).getEpochSecond();
        String operatorType = TicketFlowStatusUtil.getOperatorTypeStr(row);
        // 外部流水
        if (!replys.isEmpty()) {
            flowState.add(Tuple3.of(time, operatorType, replys));
            // 获取排序后的行数据
            List<Tuple3<Long, String, List<String>>> sortedFlows = new ArrayList<>();
            for (Tuple3<Long, String, List<String>> storedData : flowState.get()) {
                sortedFlows.add(storedData);
            }
            sortedFlows.sort(Comparator.comparingLong(r -> r.f0));
            flowState.update(sortedFlows);
            updateState.update(true);
        }
        // 内部流水
        if (!innerReplys.isEmpty()) {
            innerFlowState.add(Tuple3.of(time, operatorType, innerReplys));
            // 获取排序后的行数据
            List<Tuple3<Long, String, List<String>>> sortedFlows = new ArrayList<>();
            for (Tuple3<Long, String, List<String>> storedData : innerFlowState.get()) {
                sortedFlows.add(storedData);
            }
            sortedFlows.sort(Comparator.comparingLong(r -> r.f0));
            innerFlowState.update(sortedFlows);
            innerUpdateState.update(true);
        }
    }
}
