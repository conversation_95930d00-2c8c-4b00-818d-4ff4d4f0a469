package com.tencent.andata.map;

import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.struct.avro.message.MessageType;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.rowdata.RowDataConverter;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.conversion.RowRowConverter;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.Row;

import java.nio.ByteBuffer;

public class Row2MessageMap implements MapFunction<Row, Message> {

    RowRowConverter rowRowConverter;
    RowDataConverter converter;
    String tableName;

    public Row2MessageMap(TableIdentifier tableIdentifier, boolean isDataCompressed) throws TableNotExistException {
        IcebergCatalogReader reader = new IcebergCatalogReader();
        DataType dataType = IcebergCatalogReader.getDataType(
                reader.getTableInstance(
                        tableIdentifier.getDbName(),
                        tableIdentifier.getTableName()
                ), true);
        // 创建RowData到Row的转换器
        rowRowConverter = RowRowConverter.create(dataType);

        RowType rowType = reader.getTableRowType(tableIdentifier.getDbName(), tableIdentifier.getTableName());

        converter = new RowDataConverter(rowType, isDataCompressed);

        tableName = tableIdentifier.getTableName();
    }

    @Override
    public Message map(Row row) throws Exception {

        RowData rowData = rowRowConverter.toInternal(row);
        Message retMessage = new Message();
        // TODO: 构造Message这里正常应该也有个Builder的，但这块比较简单，就先这样
        retMessage.setData(ByteBuffer.wrap(converter.serializeRowDataToBytes(rowData)));
        retMessage.setMsgType(MessageType.ROW_DATA);
        retMessage.setSchemaName(tableName);
        retMessage.setProcTime(System.currentTimeMillis());

        return retMessage;
    }

}
