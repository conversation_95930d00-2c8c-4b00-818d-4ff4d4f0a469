package com.tencent.andata.map.mask.v2.webim;

import com.tencent.andata.util.SentenceCleanUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class WebIMFlowGroupProcess extends KeyedProcessFunction<String, Row, Row> {

    public static Logger log = LoggerFactory.getLogger(WebIMFlowGroupProcess.class);

    public final ObjectMapper mapper = new ObjectMapper();
    private ListState<Tuple3<Integer, String, String>> flowState;
    private final int maxFlowCount;
    private ValueState<Boolean> updateState;
    private ValueState<Boolean> finishState;
    private ValueState<Row> closeFlowState;

    public WebIMFlowGroupProcess(int maxFlowCount) {
        this.maxFlowCount = maxFlowCount;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        ListStateDescriptor<Tuple3<Integer, String, String>> descriptor = new ListStateDescriptor<>(
                "flowState", TypeInformation.of(new TypeHint<Tuple3<Integer, String, String>>() {
        }));
        // 保存所有流水状态
        flowState = getRuntimeContext().getListState(descriptor);
        // 流水状态是否改变
        ValueStateDescriptor<Boolean> updateStateDesc = new ValueStateDescriptor<>(
                "updateStateDesc", TypeInformation.of(new TypeHint<Boolean>() {
        }));
        updateState = getRuntimeContext().getState(updateStateDesc);
        // 本次是否下发
        ValueStateDescriptor<Boolean> finishStateDesc = new ValueStateDescriptor<>(
                "finishStateDesc", TypeInformation.of(new TypeHint<Boolean>() {
        }));
        finishState = getRuntimeContext().getState(finishStateDesc);
        ValueStateDescriptor<Row> closeFlowStateDesc = new ValueStateDescriptor<>(
                "closeFlowStateDesc", TypeInformation.of(new TypeHint<Row>() {
        }));
        // 结单流水
        closeFlowState = getRuntimeContext().getState(closeFlowStateDesc);
    }

    @Override
    public void processElement(Row row,
                               KeyedProcessFunction<String, Row, Row>.Context context,
                               Collector<Row> collector) throws Exception {
        initState();
        // 将行数据存储到键控状态中
        addData(row);
        if (finishState.value()) {
            // 触发向下游发送
            // 这里有可能不会下发，如果流水状态没有发生改变
            if (updateState.value()) {
                emit(context, collector);
                updateState.update(false);
            }
            finishState.update(false);
//            flowState.clear();
        }
    }

    private void initState() throws IOException {
        if (finishState.value() == null) {
            finishState.update(false);
        }
        if (updateState.value() == null) {
            updateState.update(false);
        }
    }

    private void emit(KeyedProcessFunction<String, Row, Row>.Context context, Collector<Row> out) throws Exception {
        StringBuilder reply = new StringBuilder();
        StringBuilder cReply = new StringBuilder();
        int count = 0;
        for (Tuple3<Integer, String, String> flowData : flowState.get()) {
            count++;
            if (count > maxFlowCount) {
                break;
            }
            reply.append(flowData.f1 + ":" + flowData.f2 + "\r\n");
            String clean = SentenceCleanUtil.sentenceClean(flowData.f2, 256);
            if (StringUtils.isNotEmpty(clean)) {
                cReply.append(flowData.f1 + ":" + clean + "\r\n");
            }
        }
        if (reply.length() > 0) {
            Row row = closeFlowState.value();
            String ticketIds = row.getField("conversation_ticket_ids").toString();
            if (StringUtils.isEmpty(ticketIds)) {
                return;
            }
            row.setField("source", "webim_" + row.getField("source"));
            row.setField("reply", reply.toString());
            row.setField("c_reply", cReply.toString());
            String[] split = ticketIds.split(",");
            for (String ticketId : split) {
                row.setField("ticket_id", ticketId);
                row.setField("id", row.getField("source") + "-" + ticketId);
                out.collect(row);
            }
        }
    }

    private void addData(Row row) throws Exception {
        String msgData = row.getField("msg_data").toString();
        int status = Integer.parseInt(String.valueOf(row.getField("status")));
        if (status == 12 || status == 6 || status == 7 || status == 9 || status == 10 || status == 100) {
            finishState.update(true);
            if (status != 100) {
                if (status == 12) {
                    closeFlowState.update(row);
                } else if (closeFlowState.value() == null) {
                    closeFlowState.update(row);
                }
            }
        }
        if (StringUtils.isEmpty(msgData)) {
            return;
        }
        JsonNode jsonNode = mapper.readTree(msgData);
        JsonNode rich = jsonNode.get("Rich");
        if (rich == null) {
            return;
        }
        StringBuilder flowStr = new StringBuilder();
        for (JsonNode node : rich) {
            JsonNode content = node.get("Content");
            if (content == null) {
                continue;
            }
            String contentStr = content.asText() + "\n";
            flowStr.append(contentStr);
        }
        if (flowStr.length() == 0) {
            return;
        }
        String flow = flowStr.toString();
        String operatorType = row.getField("operator_type").toString();
        int msgSeq = Integer.parseInt(row.getField("msg_seq").toString());
        flowState.add(Tuple3.of(msgSeq, operatorType, flow));
        // 获取排序后的行数据
        List<Tuple3<Integer, String, String>> sortedFlows = new ArrayList<>();
        for (Tuple3<Integer, String, String> storedData : flowState.get()) {
            sortedFlows.add(storedData);
        }
        sortedFlows.sort(Comparator.comparingInt(r -> r.f0));
        flowState.update(sortedFlows);
        updateState.update(true);
    }
}
