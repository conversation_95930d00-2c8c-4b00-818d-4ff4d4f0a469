package com.tencent.andata.map.mask;

import com.google.common.collect.Lists;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;

public class WebImWindowSortProcess extends ProcessWindowFunction<Row, Row, String, TimeWindow> {

    public static Logger log = LoggerFactory.getLogger(WebImWindowSortProcess.class);
    private ValueState<Boolean> receivedCloseFlow;

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化键控状态
        ValueStateDescriptor<Boolean> receivedCloseFlowStateDesc = new ValueStateDescriptor<>(
                "receivedCloseFlowStateDesc", TypeInformation.of(new TypeHint<Boolean>() {
        }));
        receivedCloseFlow = getRuntimeContext().getState(receivedCloseFlowStateDesc);
    }

    @Override
    public void process(
            String key,
            ProcessWindowFunction<Row, Row, String, TimeWindow>.Context context,
            Iterable<Row> iterable, Collector<Row> collector) throws Exception {
        if (receivedCloseFlow.value() == null) {
            receivedCloseFlow.update(false);
        }
        ArrayList<Row> rows = Lists.newArrayList(iterable);
        Collections.sort(rows, new Comparator<Row>() {
            @Override
            public int compare(Row o1, Row o2) {
                long time1 = ((Instant) o1.getField("ftime")).getEpochSecond();
                long time2 = ((Instant) o2.getField("ftime")).getEpochSecond();
                return Long.compare(time1, time2);
            }
        });
        int size = rows.size();
        for (int i = 0; i < rows.size(); i++) {
            // 判断当前是不是结单了，如果结单了就不需要下发后面的了
            int status = Integer.parseInt(String.valueOf(rows.get(i).getField("status")));
            if (status == 12 || status == 6 || status == 7 || status == 9 || status == 10) {
                collector.collect(rows.get(i));
                receivedCloseFlow.update(true);
                break;
            }
            // 到这里来了说明有正常的流水
            // 如果到最后一条了且之前的窗口已经下发过结单了，说明这是增量的数据，需要手动设置结单触发update
            if ((i == size - 1) && receivedCloseFlow.value()) {
                rows.get(i).setField("status", "100");
            }
            collector.collect(rows.get(i));
        }
    }
}
