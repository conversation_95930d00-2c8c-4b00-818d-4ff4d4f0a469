package com.tencent.andata.map.mask.rt.group;

import com.tencent.andata.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class GroupMessageGroupRtProcess extends ProcessFunction<Row, Row> {

    public static Logger log = LoggerFactory.getLogger(GroupMessageGroupRtProcess.class);

    public static ObjectMapper mapper = new ObjectMapper();

    private final int maxFlowCount;

    // 2 去掉 2 是 进入详情,自动转为处理中 这种数据
    public static List<Integer> innerTypes = Stream
            .of(1, 4, 5, 6, 8, 9, 10, 12, 15, 16, 27, 28)
            .collect(Collectors.toList());

    public GroupMessageGroupRtProcess(int maxFlowCount) {
        this.maxFlowCount = maxFlowCount;
    }


    @Override
    public void processElement(Row row,
                               ProcessFunction<Row, Row>.Context context,
                               Collector<Row> collector) throws Exception {
        // 工单流水中的外部回复
        StringBuilder reply = new StringBuilder();
        StringBuilder cReply = new StringBuilder();
        StringBuilder maskReply = new StringBuilder();
        List<JsonNode> groupOperations = StreamUtils
                .streamOf(mapper.readValue(row.<String>getFieldAs("operation_data"), ArrayNode.class))
                .sorted(new Comparator<JsonNode>() {
                    @Override
                    public int compare(JsonNode o1, JsonNode o2) {
                        long time1 = o1.get("msg_time_ms").asLong();
                        long time2 = o2.get("msg_time_ms").asLong();
                        return Long.compare(time1, time2);
                    }
                })
                .collect(Collectors.toList());
        row.setField("close_time", Instant.ofEpochSecond(0));
        int count = 1;
        for (JsonNode operation : groupOperations) {
            String msgType = operation.get("msg_type").asText();
            if (!msgType.equals("text")) {
                continue;
            }
            // 聚合回复内容
            String content = operation.get("content")
                    .asText()
                    .replaceAll("NULL|null", "");
            if (StringUtils.isEmpty(content)) {
                continue;
            }
            int isStaff = operation.get("is_staff").asInt();
            String groupType = operation.get("group_type").asText();
            String operatorTypeStr = "";
            if (groupType.equals("GTS_EXTERNAL")) {
                if (isStaff == 1) {
                    operatorTypeStr = "客服";
                } else {
                    operatorTypeStr = "客户";
                }
            } else {
                operatorTypeStr = "客服";
            }
            // 原始内容
            reply.append(operatorTypeStr);
            reply.append(":");
            reply.append(content);
            reply.append("\r\n");
            if (count <= maxFlowCount) {
                // 清洗后内容
                String cExtern = operation.get("c_msg")
                        .asText()
                        .replaceAll("NULL|null", "");
                if (StringUtils.isNotEmpty(cExtern)) {
                    cReply.append(operatorTypeStr);
                    cReply.append(":");
                    cReply.append(cExtern);
                    cReply.append("\r\n");
                    count++;
                }
                // 脱敏后内容
                String maskMsg = operation.get("mask_msg")
                        .asText()
                        .replaceAll("NULL|null", "");
                if (StringUtils.isNotEmpty(maskMsg)) {
                    maskReply.append(operatorTypeStr);
                    maskReply.append(":");
                    maskReply.append(maskMsg);
                    maskReply.append("\r\n");
                }
            }
        }
        if (reply.length() > 0) {
            row.setField("reply", reply.toString());
            row.setField("c_reply", cReply.toString());
            row.setField("msg", maskReply.toString());
            row.setField("source", "group" + "_" + row.getField("group_type"));
            row.setField("id", "group" + "_" + row.getField("group_type").toString() + "-" +
                    row.getField("ticket_id").toString());
            collector.collect(row);
        }
    }
}