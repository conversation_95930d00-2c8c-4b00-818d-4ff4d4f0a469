package com.tencent.andata.map.mask.rt.ticket;

import com.tencent.andata.util.SentenceCleanUtil;
import com.tencent.andata.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TicketFlowGroupRtProcess extends ProcessFunction<Row, Row> {

    public static Logger log = LoggerFactory.getLogger(TicketFlowGroupRtProcess.class);

    public static ObjectMapper mapper = new ObjectMapper();

    private final int maxFlowCount;

    // 2 去掉 2 是 进入详情,自动转为处理中 这种数据
    public static List<Integer> innerTypes = Stream
            .of(1, 4, 5, 6, 8, 9, 10, 12, 15, 16, 27, 28)
            .collect(Collectors.toList());

    public TicketFlowGroupRtProcess(int maxFlowCount) {
        this.maxFlowCount = maxFlowCount;
    }


    @Override
    public void processElement(Row row,
                               ProcessFunction<Row, Row>.Context context,
                               Collector<Row> collector) throws Exception {
        // 工单流水中的外部回复
        StringBuilder externReply = new StringBuilder();
        StringBuilder cExternReply = new StringBuilder();
        StringBuilder maskExternReply = new StringBuilder();

        StringBuilder innerReply = new StringBuilder();
        StringBuilder cInnerReply = new StringBuilder();
        StringBuilder maskInnerReply = new StringBuilder();

        List<JsonNode> ticketOperations = StreamUtils
                .streamOf(mapper.readValue(row.<String>getFieldAs("operation_data"), ArrayNode.class))
                .sorted(new Comparator<JsonNode>() {
                    @Override
                    public int compare(JsonNode o1, JsonNode o2) {
                        long time1 = Instant.parse(o1.get("operate_time").asText()).getEpochSecond();
                        long time2 = Instant.parse(o2.get("operate_time").asText()).getEpochSecond();
                        return Long.compare(time1, time2);
                    }
                })
                .collect(Collectors.toList());
        row.setField("close_time", Instant.ofEpochSecond(0));
        int count = 1;
        for (JsonNode operation : ticketOperations) {
            final long operatorType = operation.get("operator_type").asLong();
            final String operator = operation.get("operator").asText();
            final long operationType = operation.get("operation_type").asLong();
            final long targetStatus = operation.get("target_status").asLong();
            // 聚合回复内容
            if (operatorType != 3 && !"SYSTEM".equals(operator)) {
                String extern = operation.get("extern_reply")
                        .asText()
                        .replaceAll("NULL|null", "");
                boolean innerFg = false;
                for (Integer innerType : innerTypes) {
                    if (operationType == innerType) {
                        innerFg = true;
                        break;
                    }
                }

                String operatorTypeStr = "";
                if (operatorType == 1) {
                    operatorTypeStr = "客户";
                } else if (operatorType == 2) {
                    operatorTypeStr = "客服";
                } else {
                    operatorTypeStr = "其他";
                }
                // 外部流水回复
                if ((operationType != 4 && operationType != 21 && operationType != 27)
                        && StringUtils.isNotEmpty(extern)) {
                    // 原始内容
                    externReply.append(operatorTypeStr);
                    externReply.append(":");
                    externReply.append(extern);
                    externReply.append("\r\n");
                    if (count <= maxFlowCount) {
                        // 清洗后内容
                        String cExtern = operation.get("c_msg")
                                .asText()
                                .replaceAll("NULL|null", "");
                        if (StringUtils.isNotEmpty(cExtern)) {
                            cExternReply.append(operatorTypeStr);
                            cExternReply.append(":");
                            cExternReply.append(cExtern);
                            cExternReply.append("\r\n");
                            count++;
                        }
                        // 脱敏后内容
                        String maskExtern = operation.get("mask_msg")
                                .asText()
                                .replaceAll("NULL|null", "");
                        if (StringUtils.isNotEmpty(maskExtern)) {
                            maskExternReply.append(operatorTypeStr);
                            maskExternReply.append(":");
                            maskExternReply.append(maskExtern);
                            maskExternReply.append("\r\n");
                        }
                    }
                }
                String inner = operation.get("inner_reply")
                        .asText()
                        .replaceAll("NULL|null", "");
                // 内部回复流水
                if (innerFg && StringUtils.isNotEmpty(inner)) {
                    innerReply.append(operatorTypeStr);
                    innerReply.append(":");
                    innerReply.append(inner);
                    innerReply.append("\r\n");
                    if (count <= maxFlowCount) {
                        // 清洗后内容
                        String cInner = operation.get("c_inner_msg")
                                .asText()
                                .replaceAll("NULL|null", "");
                        if (StringUtils.isNotEmpty(cInner)) {
                            cInnerReply.append(operatorTypeStr);
                            cInnerReply.append(":");
                            cInnerReply.append(cInner);
                            cInnerReply.append("\r\n");
                            count++;
                        }
                        // 脱敏后内容
                        String maskInner = operation.get("mask_inner_msg")
                                .asText()
                                .replaceAll("NULL|null", "");
                        if (StringUtils.isNotEmpty(maskInner)) {
                            maskInnerReply.append(operatorTypeStr);
                            maskInnerReply.append(":");
                            maskInnerReply.append(maskInner);
                            maskInnerReply.append("\r\n");
                        }
                    }
                }
            }
            // 是不是结单流水
            if (operationType == 16 || operationType == 1 && targetStatus == 3) {
                String closeTime = operation.get("operate_time").asText();
                Instant instant = Instant.parse(closeTime);
                row.setField("close_time", instant);
            }
        }
        if (externReply.length() > 0) {
            row.setField("reply", externReply.toString());
            row.setField("c_reply", cExternReply.toString());
            row.setField("msg", maskExternReply.toString());
            row.setField("source", "ticket_extern");
            row.setField("id", "ticket_extern-" + row.getField("ticket_id"));
            collector.collect(row);
        }
        if (innerReply.length() > 0) {
            row.setField("reply", innerReply.toString());
            row.setField("c_reply", cInnerReply.toString());
            row.setField("msg", maskInnerReply.toString());
            row.setField("source", "ticket_inner");
            row.setField("id", "ticket_inner-" + row.getField("ticket_id"));
            collector.collect(row);
        }

    }
}