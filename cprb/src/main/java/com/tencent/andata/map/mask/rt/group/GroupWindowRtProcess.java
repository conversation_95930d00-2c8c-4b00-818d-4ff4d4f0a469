package com.tencent.andata.map.mask.rt.group;

import com.google.common.collect.Lists;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;

public class GroupWindowRtProcess extends ProcessWindowFunction<Row, Row, String, TimeWindow> {

    public static Logger log = LoggerFactory.getLogger(GroupWindowRtProcess.class);

    @Override
    public void process(String key,
                        ProcessWindowFunction<Row, Row, String, TimeWindow>.Context context,
                        Iterable<Row> iterable,
                        Collector<Row> collector) throws Exception {
        ArrayList<Row> rows = Lists.newArrayList(iterable);
        Collections.sort(rows, new Comparator<Row>() {
            @Override
            public int compare(Row o1, Row o2) {
                long time1 = (long) o1.getField("msg_time_ms");
                long time2 = (long) o2.getField("msg_time_ms");
                return Long.compare(time1, time2);
            }
        });
        // 下发一条就好了，下游需要从hbase捞流水
        Row row = rows.get(rows.size() - 1);
        log.info("窗口下发的row: {}", row);
        collector.collect(row);
    }
}
