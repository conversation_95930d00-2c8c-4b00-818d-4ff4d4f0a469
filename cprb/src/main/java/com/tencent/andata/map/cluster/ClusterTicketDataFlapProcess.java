package com.tencent.andata.map.cluster;

import com.tencent.andata.model.cluster.StandardCluster;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

public class ClusterTicketDataFlapProcess extends ProcessFunction<StandardCluster, Object> {



    @Override
    public void processElement(StandardCluster standardCluster, ProcessFunction<StandardCluster, Object>.Context context, Collector<Object> collector) throws Exception {

    }
}
