package com.tencent.andata.map.mask;

import com.tencent.andata.util.SentenceCleanUtil;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

import java.util.HashMap;
import java.util.Map;

public class FieldCleanMap implements MapFunction<Row, Row> {

    private final Map<String, String> fieldsMap;
    private final int maxLength;

    public FieldCleanMap(String field, String dstField, int maxLength) {
        this.fieldsMap = new HashMap<String, String>() {{
            put(field, dstField);
        }};
        this.maxLength = maxLength;
    }

    public FieldCleanMap(Map<String, String> fieldsMap, int maxLength) {
        this.fieldsMap = fieldsMap;
        this.maxLength = maxLength;
    }

    @Override
    public Row map(Row row) throws Exception {
        for (Map.Entry<String, String> entry : fieldsMap.entrySet()) {
            String field = entry.getKey();
            String dstField = entry.getValue();
            Object rowField = row.getField(field);
            if (rowField != null) {
                String fieldString = rowField.toString();
                row.setField(dstField, SentenceCleanUtil.sentenceClean(fieldString, maxLength));
            }
        }
        return row;
    }
}
