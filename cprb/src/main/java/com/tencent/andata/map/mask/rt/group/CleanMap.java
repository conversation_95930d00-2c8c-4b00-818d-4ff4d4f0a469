package com.tencent.andata.map.mask.rt.group;

import com.tencent.andata.util.SentenceCleanUtil;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

public class CleanMap implements MapFunction<Row, Row> {

    private int flowMaxLength;

    public CleanMap(int flowMaxLength) {
        this.flowMaxLength = flowMaxLength;
    }

    @Override
    public Row map(Row row) throws Exception {
        String msgType = row.getField("msg_type").toString();
        if ("text".equals(msgType)) {
            Object rowField = row.getField("content");
            if (rowField != null) {
                String fieldString = rowField.toString();
                row.setField("c_msg",
                        SentenceCleanUtil.sentenceClean(fieldString, flowMaxLength));
            }
        }
        return row;
    }
}
