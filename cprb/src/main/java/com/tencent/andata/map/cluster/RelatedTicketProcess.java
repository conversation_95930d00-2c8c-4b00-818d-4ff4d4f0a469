package com.tencent.andata.map.cluster;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.cluster.ClusterRelatedTicket;
import com.tencent.andata.model.cluster.StandardCluster;
import com.tencent.andata.util.lookup.StandardClusterLookupQuery;
import com.tencent.andata.util.lookup.TicketSummaryLookupQuery;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.Properties;

public class RelatedTicketProcess extends ProcessFunction<StandardCluster, ClusterRelatedTicket> {

    private TicketSummaryLookupQuery ticketSummaryLookupQuery;
    private FlinkLog logger = FlinkLog.getInstance();

    public RelatedTicketProcess() throws IOException, NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        final DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cprb.database", "pgsql", "aigc"))
                .build();
        this.ticketSummaryLookupQuery = new TicketSummaryLookupQuery(pgDBConf);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.ticketSummaryLookupQuery.open();
    }

    @Override
    public void close() throws Exception {
        super.close();
        this.ticketSummaryLookupQuery.close();
    }

    @Override
    public void processElement(StandardCluster standardCluster, ProcessFunction<StandardCluster, ClusterRelatedTicket>.Context context, Collector<ClusterRelatedTicket> collector) throws Exception {
        for (Long ticketId : standardCluster.ticketList) {
            final ClusterRelatedTicket ticketData = this.ticketSummaryLookupQuery.query(ticketId);
            if (ticketData == null) {
                this.logger.error(String.format(
                        "查询工单数据失败: %s",
                        ticketId
                ));
                continue;
            }
            ticketData.ticketId = ticketId;
            ticketData.standardClusterId = standardCluster.id;
            ticketData.status = standardCluster.status;
            collector.collect(ticketData);
        }
    }
}
