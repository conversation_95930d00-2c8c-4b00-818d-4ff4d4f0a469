package com.tencent.andata.map.mask.v2.ticket;

import com.tencent.andata.util.SentenceCleanUtil;
import com.tencent.andata.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TicketFlowGroupProcess extends ProcessFunction<Row, Row> {

    public static Logger log = LoggerFactory.getLogger(TicketFlowGroupProcess.class);

    public static ObjectMapper mapper = new ObjectMapper();

    // 2 去掉 2 是 进入详情,自动转为处理中 这种数据
    public static List<Integer> innerTypes = Stream
            .of(1, 4, 5, 6, 8, 9, 10, 12, 15, 16, 27, 28)
            .collect(Collectors.toList());


    @Override
    public void processElement(Row row,
                               ProcessFunction<Row, Row>.Context context,
                               Collector<Row> collector) throws Exception {
        // 工单流水中的外部回复
        StringBuilder externReply = new StringBuilder();
        StringBuilder cExternReply = new StringBuilder();
        StringBuilder innerReply = new StringBuilder();
        StringBuilder cInnerReply = new StringBuilder();

        List<JsonNode> ticketOperations = StreamUtils
                .streamOf(mapper.readValue(row.<String>getFieldAs("operation_data"), ArrayNode.class))
                .collect(Collectors.toList());
        for (JsonNode operation : ticketOperations) {
            final long operatorType = operation.get("operator_type").asLong();
            final long operationType = operation.get("operation_type").asLong();
            final long targetStatus = operation.get("target_status").asLong();
            // 聚合回复内容
            if (operatorType != 3) {
                String extern = operation.get("extern_reply")
                        .asText()
                        .replaceAll("NULL|null", "");
                boolean innerFg = false;
                for (Integer innerType : innerTypes) {
                    if (operationType == innerType) {
                        innerFg = true;
                        break;
                    }
                }
                String operatorTypeStr = "";
                if (operatorType == 1) {
                    operatorTypeStr = "客户";
                } else if (operatorType == 2) {
                    operatorTypeStr = "客服";
                } else {
                    operatorTypeStr = "其他";
                }
                // 外部流水回复
                if ((operationType != 4 && operationType != 21 && operationType != 27)
                        && StringUtils.isNotEmpty(extern)) {
                    externReply.append(operatorTypeStr);
                    externReply.append(":");
                    externReply.append(extern);
                    externReply.append("\r\n");

                    String cExtern = SentenceCleanUtil.sentenceClean(extern, 256);
                    if (StringUtils.isNotEmpty(cExtern)) {
                        cExternReply.append(operatorTypeStr);
                        cExternReply.append(":");
                        cExternReply.append(cExtern);
                        cExternReply.append("\r\n");
                    }
                }
                String inner = operation.get("inner_reply")
                        .asText()
                        .replaceAll("NULL|null", "");
                // 内部回复流水
                if (innerFg && StringUtils.isNotEmpty(inner)) {
                    innerReply.append(operatorTypeStr);
                    innerReply.append(":");
                    innerReply.append(inner);
                    innerReply.append("\r\n");

                    String cInner = SentenceCleanUtil.sentenceClean(inner, 256);
                    if (StringUtils.isNotEmpty(cInner)) {
                        cInnerReply.append(operatorTypeStr);
                        cInnerReply.append(":");
                        cInnerReply.append(cInner);
                        cInnerReply.append("\r\n");
                    }
                }
            }
            if (operationType == 16 || operationType == 1 && targetStatus == 3) {
                String closeTime = operation.get("operate_time").asText();
                if (!closeTime.contains("+08:00")) {
                    closeTime += "+08:00";
                }
                OffsetDateTime offsetDateTime = OffsetDateTime.parse(closeTime);
                Instant instant = offsetDateTime.toInstant();
                row.setField("close_time", instant);
            }
        }
        String title = row.getField("question").toString();
        if (externReply.length() > 0) {
            row.setField("reply", title + "\n" + externReply.toString());
            row.setField("c_reply", title + "\n" + cExternReply.toString());
            row.setField("source", "ticket_extern");
            row.setField("id", "ticket_extern-" + row.getField("ticket_id"));
            collector.collect(row);
        }
        if (innerReply.length() > 0) {
            row.setField("reply", title + "\n" + innerReply.toString());
            row.setField("c_reply", title + "\n" + cInnerReply.toString());
            row.setField("source", "ticket_inner");
            row.setField("id", "ticket_inner-" + row.getField("ticket_id"));
            collector.collect(row);
        }
    }
}