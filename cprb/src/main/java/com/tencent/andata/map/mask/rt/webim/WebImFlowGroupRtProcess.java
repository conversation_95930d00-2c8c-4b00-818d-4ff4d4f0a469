package com.tencent.andata.map.mask.rt.webim;

import com.tencent.andata.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class WebImFlowGroupRtProcess extends ProcessFunction<Row, Row> {

    public static Logger log = LoggerFactory.getLogger(WebImFlowGroupRtProcess.class);

    public final ObjectMapper mapper = new ObjectMapper();

    private final int[] closeStatus = new int[]{6, 7, 9, 10, 12};

    private final int maxFlowCount;

    public WebImFlowGroupRtProcess(int maxFlowCount) {
        this.maxFlowCount = maxFlowCount;
    }


    @Override
    public void processElement(Row row,
                               ProcessFunction<Row, Row>.Context context,
                               Collector<Row> collector) throws Exception {
        List<JsonNode> rawOperations = StreamUtils
                .streamOf(mapper.readValue(row.<String>getFieldAs("operation_data"), ArrayNode.class))
                .collect(Collectors.toList());
        List<JsonNode> operations = new ArrayList<>();
        JsonNode closeFlow = null;
        // 只需要有回复的数据
        for (JsonNode operation : rawOperations) {
            int status = Integer.parseInt(operation.get("status").asText());
            for (int i : closeStatus) {
                if (status == i) {
                    // 结单流水
                    closeFlow = operation;
                }
            }
            String data = operation.get("msg_data")
                    .asText()
                    .replaceAll("NULL|null", "");
            if (StringUtils.isNotEmpty(data)) {
                operations.add(operation);
            }
        }
        // 排序
        operations = operations.stream().sorted(new Comparator<JsonNode>() {
            @Override
            public int compare(JsonNode o1, JsonNode o2) {
                int seq1 = Integer.parseInt(o1.get("msg_seq").asText());
                int seq2 = Integer.parseInt(o2.get("msg_seq").asText());
                return seq1 - seq2;
            }
        }).collect(Collectors.toList());
        // webim流水中回复
        StringBuilder reply = new StringBuilder();
        StringBuilder cReply = new StringBuilder();
        StringBuilder maskReply = new StringBuilder();
        int count = 0;
        for (JsonNode operation : operations) {
            String operationStr = operation.get("operation").asText();
            int creatorType = operation.get("creator_type").asInt();
            String operatorTypeStr = "";
            if ("SendZXMsg".equals(operationStr)) {
                operatorTypeStr = "客服";
            } else if ("SendUserMsg".equals(operationStr)) {
                operatorTypeStr = "客户";
            } else if ("SendWeworkExternalGroupMsg".equals(operationStr) && creatorType == 1) {
                operatorTypeStr = "客户";
            } else if ("SendChatCallbackMsg".equals(operationStr) && creatorType == 1) {
                operatorTypeStr = "客户";
            } else {
                operatorTypeStr = "客服";
            }
            // 聚合回复内容
            String msgData = operation.get("msg_data")
                    .asText()
                    .replaceAll("NULL|null", "");
            // 原始内容
            reply.append(operatorTypeStr);
            reply.append(":");
            reply.append(msgData);
            reply.append("\r\n");
            if (count <= maxFlowCount) {
                // 清洗后内容
                String cMsg = operation.get("c_msg")
                        .asText()
                        .replaceAll("NULL|null", "");
                if (StringUtils.isNotEmpty(cMsg)) {
                    cReply.append(operatorTypeStr);
                    cReply.append(":");
                    cReply.append(cMsg);
                    cReply.append("\r\n");
                    count++;
                }
                // 脱敏后内容
                String maskMsg = operation.get("mask_msg")
                        .asText()
                        .replaceAll("NULL|null", "");
                if (StringUtils.isNotEmpty(maskMsg)) {
                    maskReply.append(operatorTypeStr);
                    maskReply.append(":");
                    maskReply.append(maskMsg);
                    maskReply.append("\r\n");
                }
            }
        }
        if (reply.length() > 0) {
            String ticketIds = row.getField("conversation_ticket_ids").toString();
            if (StringUtils.isEmpty(ticketIds)) {
                return;
            }
            if (closeFlow == null) {
                // 还没有结单设置结单时间为0
                row.setField("close_time", Instant.ofEpochSecond(0));
            } else {
                row.setField("close_time", Instant.parse(closeFlow.get("record_update_time").asText()));
            }
            row.setField("source", "webim_" + row.getField("source"));
            row.setField("reply", reply.toString());
            row.setField("c_reply", cReply.toString());
            row.setField("msg", maskReply.toString());
            String[] split = ticketIds.split(",");
            for (String ticketId : split) {
                row.setField("ticket_id", Long.parseLong(ticketId));
                row.setField("id", row.getField("source") + "-" + ticketId);
                collector.collect(row);
            }
         }
    }
}