package com.tencent.andata.map.mask.v2.desensitization;

import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

import java.util.List;


public class LengthSubMap implements MapFunction<Row, Row> {

    private final int len;
    private final List<String> fields;
    private final int flowMaxCount;

    public LengthSubMap(int len, List<String> fields, Integer flowMaxCount) {
        this.len = len;
        this.fields = fields;
        this.flowMaxCount = flowMaxCount;
    }

    @Override
    public Row map(Row row) throws Exception {
        for (String field : fields) {
            String content = row.getField(field).toString();
            StringBuilder builder = new StringBuilder();
            String[] split = content.split("\r\n");

            int length = Math.min(split.length, flowMaxCount);
            for (int i = 0; i < length; i++) {
                String flow = split[i];
                if (flow.length() > len) {
                    builder.append(flow.substring(0, len));
                } else {
                    builder.append(flow);
                }
                builder.append("\r\n");
            }
            if (builder.length() > 0) {
                row.setField(field, builder.toString());
            }
        }
        return row;
    }
}
