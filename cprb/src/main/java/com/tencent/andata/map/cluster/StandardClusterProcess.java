package com.tencent.andata.map.cluster;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.model.cluster.StandardCluster;
import com.tencent.andata.model.cluster.TicketTuple;
import com.tencent.andata.model.cluster.TicketTupleScore;
import com.tencent.andata.util.cluster.MergeUtils;
import it.unimi.dsi.fastutil.Hash;
import lombok.SneakyThrows;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

public class StandardClusterProcess extends KeyedProcessFunction<Long, TicketTupleScore, StandardCluster> {

    private transient MapState<String, TicketTuple> cachedTicketTuple;
    private transient MapState<Long, Boolean> clusterInit;

    private transient MergeUtils mergeUtils;
    private FlinkLog logger = FlinkLog.getInstance();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        MapStateDescriptor<String, TicketTuple> descriptor =
                new MapStateDescriptor<>(
                        "cached-ticket-tuple",
                        TypeInformation.of(String.class),
                        TypeInformation.of(TicketTuple.class));
        MapStateDescriptor<Long, Boolean> initDesc =
                new MapStateDescriptor<>(
                        "cluster-cache-init",
                        TypeInformation.of(Long.class),
                        TypeInformation.of(Boolean.class));

        this.clusterInit = getRuntimeContext().getMapState(initDesc);
        this.cachedTicketTuple = getRuntimeContext().getMapState(descriptor);
        this.mergeUtils = new MergeUtils();
    }


    @Override
    public void onTimer(long timestamp, KeyedProcessFunction<Long, TicketTupleScore, StandardCluster>.OnTimerContext ctx, Collector<StandardCluster> out) throws Exception {
        super.onTimer(timestamp, ctx, out);
        // 处理融合
        mergeStandardCluster(out);
        // 注册下一个定时器
        ctx.timerService().registerProcessingTimeTimer(
                // 5min 处理一次
                ctx.timerService().currentProcessingTime() + 5 * 60 * 1000
        );
    }

    @Override
    public void processElement(TicketTupleScore ticketTupleScore, KeyedProcessFunction<Long, TicketTupleScore, StandardCluster>.Context context, Collector<StandardCluster> collector) throws Exception {
        // 第一次处理就注册定时器
        if (!this.clusterInit.contains(context.getCurrentKey())) {
            this.clusterInit.put(context.getCurrentKey(), true);
            context.timerService().registerProcessingTimeTimer(
                    context.timerService().currentProcessingTime() + 5 * 60 * 1000
            );
        }
        // 缓存数据
        this.cachedData(ticketTupleScore);
    }

    /**
     * 这里根据工单对，将四个分值合并在一起
     *
     * @param ticketTupleScore 工单对单个类型的分值
     * @throws Exception
     */
    public void cachedData(TicketTupleScore ticketTupleScore) throws Exception {
        // 这里需要上游保证工单ID小的在前面
        String tupleKey = String.format("%s-%s", ticketTupleScore.ticketId1, ticketTupleScore.ticketId2);
        TicketTuple tuple;
        if (this.cachedTicketTuple.contains(tupleKey)) {
            tuple = this.cachedTicketTuple.get(tupleKey);
        } else {
            tuple = TicketTuple.builder().
                    ticketId1(ticketTupleScore.ticketId1)
                    .ticketId2(ticketTupleScore.ticketId2)
                    .baseClusterId(ticketTupleScore.baseClusterId)
                    .serviceSceneLevel1Id(ticketTupleScore.serviceSceneLevel1Id)
                    .serviceSceneLevel2Id(ticketTupleScore.serviceSceneLevel2Id)
                    .build();

        }
        switch (ticketTupleScore.calibrationType) {
            case Ai_Solution:
                tuple.model_solution_score = ticketTupleScore.score;
                break;
            case Ai_Description:
                tuple.model_desc_score = ticketTupleScore.score;
                break;
            case Solution:
                tuple.extract_solution_score = ticketTupleScore.score;
                break;
            case Description:
                tuple.extract_desc_score = ticketTupleScore.score;
                break;

        }
        this.cachedTicketTuple.put(tupleKey, tuple);
    }

    public void mergeStandardCluster(Collector<StandardCluster> out) throws Exception {
        // 获取待合并工单对, 需要是四个分值都有的工单对
        List<TicketTuple> newTicketTuples = new ArrayList<>();
        for (TicketTuple ticketTuple : this.cachedTicketTuple.values()) {
            if (ticketTuple.readyToMerge()) {
                newTicketTuples.add(ticketTuple);
            }
        }
        if (newTicketTuples.isEmpty()) {
            return;
        }
        newTicketTuples.forEach(new Consumer<TicketTuple>() {
            @SneakyThrows
            @Override
            public void accept(TicketTuple ticketTuple) {
                // 删除缓存数据
                cachedTicketTuple.remove(
                        String.format("%s-%s", ticketTuple.ticketId1, ticketTuple.ticketId2)
                );
            }
        });
        this.logger.info(String.format(
                "开始合并：base_id=%s, 工单对数量：%s",
                newTicketTuples.get(0).baseClusterId,
                newTicketTuples.size()
        ));
        // 合并校准工单对
        final List<StandardCluster> returnCluster = this.mergeUtils.merge(newTicketTuples);
        for (StandardCluster standardCluster : returnCluster) {
            out.collect(standardCluster);
        }
    }
}
