package com.tencent.andata.map;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.PropertyUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

import java.io.IOException;
import java.util.Properties;

/**
 * 拼接推送的URl
 */
public class PushDataUrlSplice implements MapFunction<Row, Row> {
    public static FlinkLog logger = FlinkLog.getInstance();

    private final String urlTmp;

    public PushDataUrlSplice() throws IOException {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        String baseUrl;
        if ("Default".equals(properties.get("RAINBOW_ENV_NAME"))) {
            baseUrl = "https://andon.woa.com/";
        } else {
            baseUrl = "https://test-andon.woa.com/";
        }
        urlTmp = baseUrl + "mobile/customer/commonPrompt/productPromptDetails?routerForm=group&problem=%s"
                + "&productCenter=%s&productCenterId=%s&ticketId=%s";
    }

    @Override
    public Row map(Row row) throws Exception {
        final String problem = row.getField("name").toString();
        final long ticketId = (long) row.getField("ticket_id");
        final String productCenterId = row.getField("service_scene_level1_id").toString();
        final String productCenterName = row.getField("service_scene_level1_name").toString();
        row.setField("push_url", String.format(
                urlTmp,
                problem,
                productCenterName,
                productCenterId,
                ticketId
        ));
        logger.info(String.format("推送云顾问数据: %s", row.toString()));
        return row;
    }
}
