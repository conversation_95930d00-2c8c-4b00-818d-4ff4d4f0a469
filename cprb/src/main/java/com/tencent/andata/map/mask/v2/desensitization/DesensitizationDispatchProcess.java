package com.tencent.andata.map.mask.v2.desensitization;

import com.tencent.andata.log.FlinkLog;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;


public class DesensitizationDispatchProcess extends ProcessFunction<Row, Row> {

    private final OutputTag<Row> outputTag;

    public DesensitizationDispatchProcess(OutputTag<Row> outputTag) {
        this.outputTag = outputTag;
    }

    @Override
    public void processElement(Row row,
                               ProcessFunction<Row, Row>.Context context,
                               Collector<Row> collector) throws Exception {
        String cReply = row.getField("c_reply").toString();
        String msg = row.getField("msg").toString();
        if (StringUtils.isNotEmpty(cReply) && StringUtils.isEmpty(msg)) {
            context.output(outputTag, row);
        } else {
            collector.collect(row);
        }
    }
}
