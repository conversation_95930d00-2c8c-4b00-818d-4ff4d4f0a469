package com.tencent.andata.map;

import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

import java.util.Objects;

public class GptDataCleanMap implements MapFunction<Row, Row> {

    /**
     * 数据做清洗 & 截断
     *
     * @param row data
     * @return row
     * @throws Exception ex
     */
    @Override
    public Row map(Row row) throws Exception {
        String question = Objects.requireNonNull(
                        row.getField("question")
                ).toString()
                .replaceAll("[\\u0000-\\u001f]|\\\\r|\\\\n|\\p{C}", "");
        String reason = Objects.requireNonNull(
                        row.getField("reasoning")
                ).toString()
                .replaceAll("[\\u0000-\\u001f]|\\\\r|\\\\n|\\p{C}", "");
        row.setField("question", question.length() > 256 ? question.substring(0, 256) : question);
        String sourceType = Objects.requireNonNull(row.getField("source_type")).toString();
        row.setField("source_type", sourceType.length() > 50 ? sourceType.substring(0, 50) : sourceType);
        row.setField("reasoning", reason.length() > 256 ? reason.substring(0, 256): reason);
        return row;
    }
}
