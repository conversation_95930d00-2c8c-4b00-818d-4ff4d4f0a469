package com.tencent.andata.map.mask.v3.webim;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.data.StringData;
import org.apache.flink.types.Row;

public class MsgExtractMap implements MapFunction<Row, Row> {

    private static final ObjectMapper mapper = new ObjectMapper();
    private final String field;
    private final String dstField;


    public MsgExtractMap(String field, String dstField) {
        this.field = field;
        this.dstField = dstField;
    }

    @Override
    public Row map(Row row) throws Exception {
        Object msgObj = row.getField(field);
        if (msgObj == null) {
            row.setField(dstField, "");
            return row;
        }
        String msgData = msgObj.toString();
        if (StringUtils.isEmpty(msgData)) {
            return row;
        }
        JsonNode jsonNode = mapper.readTree(msgData);
        JsonNode rich = jsonNode.get("Rich");
        if (rich == null) {
            row.setField(dstField, "");
            return row;
        }
        StringBuilder flowStr = new StringBuilder();
        for (JsonNode node : rich) {
            JsonNode content = node.get("Content");
            if (content == null) {
                continue;
            }
            String contentStr = content.asText() + "\n";
            flowStr.append(contentStr);
        }
        if (flowStr.length() == 0) {
            row.setField(dstField, "");
        } else {
            row.setField(dstField, flowStr.toString());
        }
        return row;
    }
}
