package com.tencent.andata.map;

import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.LinkedList;
import java.util.List;


public class RateLimitProcess extends ProcessFunction<Row, Row> {

    private final int count;
    private final long timeWindow;

    /**
     * 队列里面存储的是每一次通过时候的时间戳
     */
    private List<Long> list = new LinkedList<>();

    public RateLimitProcess(int count, long timeWindow) {
        this.count = count;
        this.timeWindow = timeWindow;
    }

    @Override
    public void processElement(
            Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector
    ) throws Exception {
        while (!isGo()) {
            Thread.sleep(100);
        }
        collector.collect(row);
    }

    /**
     * 滑动时间窗口限流算法
     * 在指定时间窗口，指定限制次数内，是否允许通过
     *
     * @return 是否允许通过
     */
    public boolean isGo() {
        // 获取当前时间
        long nowTime = System.currentTimeMillis();
        // 根据队列id，取出对应的限流队列，若没有则创建
        // 如果队列还没满，则允许通过，并添加当前时间戳到队列开始位置
        if (list.size() < count) {
            list.add(0, nowTime);
            return true;
        }

        // 队列已满（达到限制次数），则获取队列中最早添加的时间戳
        Long farTime = list.get(count - 1);
        // 用当前时间戳 减去 最早添加的时间戳
        if (nowTime - farTime <= timeWindow) {
            // 若结果小于等于timeWindow，则说明在timeWindow内，通过的次数大于count
            // 不允许通过
            return false;
        } else {
            // 若结果大于timeWindow，则说明在timeWindow内，通过的次数小于等于count
            // 允许通过，并删除最早添加的时间戳，将当前时间添加到队列开始位置
            list.remove(count - 1);
            list.add(0, nowTime);
            return true;
        }
    }
}
