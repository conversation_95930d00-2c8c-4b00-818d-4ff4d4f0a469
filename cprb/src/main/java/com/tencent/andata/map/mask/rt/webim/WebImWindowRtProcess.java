package com.tencent.andata.map.mask.rt.webim;

import com.google.common.collect.Lists;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;

public class WebImWindowRtProcess extends ProcessWindowFunction<Row, Row, String, TimeWindow> {

    public static Logger log = LoggerFactory.getLogger(WebImWindowRtProcess.class);

    @Override
    public void process(String key,
                        ProcessWindowFunction<Row, Row, String, TimeWindow>.Context context,
                        Iterable<Row> iterable, Collector<Row> collector) throws Exception {
        ArrayList<Row> rows = Lists.newArrayList(iterable);
        Collections.sort(rows, new Comparator<Row>() {
            @Override
            public int compare(Row o1, Row o2) {
                long time1 = ((Instant) o1.getField("record_update_time")).getEpochSecond();
                long time2 = ((Instant) o2.getField("record_update_time")).getEpochSecond();
                return Long.compare(time1, time2);
            }
        });
        // 下发一条就好了，下游需要从hbase捞流水
        Row row = rows.get(rows.size() - 1);
        log.info("窗口下发的数据： {}", row);
        collector.collect(row);
    }
}
