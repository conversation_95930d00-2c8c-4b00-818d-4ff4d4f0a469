package com.tencent.andata.map.mask;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;

public class AddTitleMap implements MapFunction<Row, Row> {

    private List<String> dstFields = new ArrayList<>();

    public AddTitleMap(String dstFieldStr) {
        String[] split = dstFieldStr.split(",");
        for (String s : split) {
            dstFields.add(s);
        }
    }

    @Override
    public Row map(Row row) throws Exception {
        Object question = row.getField("question");
        if (question != null) {
            for (String dstField : dstFields) {
                Object rowField = row.getField(dstField);
                if (rowField != null && StringUtils.isNotEmpty(rowField.toString())) {
                    row.setField(dstField, question + "\n" + rowField);
                }
            }
        }
        return row;
    }
}
