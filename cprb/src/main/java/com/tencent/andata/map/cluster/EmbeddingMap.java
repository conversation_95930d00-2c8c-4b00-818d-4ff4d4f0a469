package com.tencent.andata.map.cluster;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.util.EmbeddingUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

import java.util.List;

public class EmbeddingMap implements MapFunction<Row, Row> {

    private static ObjectMapper mapper = new ObjectMapper();
    String srcField;
    String dstField;

    public EmbeddingMap(String srcField, String dstField) {
        this.srcField = srcField;
        this.dstField = dstField;
    }


    @Override
    public Row map(Row row) throws Exception {
        String data = row.getField(srcField).toString();
        List<Double> embedding = EmbeddingUtils.embedding(data);
        row.setField(dstField, mapper.writeValueAsString(embedding));
        return row;
    }
}
