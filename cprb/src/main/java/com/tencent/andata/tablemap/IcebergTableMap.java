package com.tencent.andata.tablemap;

public class IcebergTableMap {

    public static String FIRST_FILTER_ICEBERG_MAP = "[\n"
            + "    {\n"
            + "         \"icebergTable\": \"dwd_comm_prob_ticket_first_filter\",\n"
            + "         \"fTable\": \"f_ice_dwd_first_filter\",\n"
            + "         \"primaryKey\": \"ticket_id,prompt_group_id,source_type,task_id\"\n"
            + "    }\n"
            + "]";


    public static String DWS_TICKET_MAP = "[\n"
            + "    {\n"
            + "         \"icebergTable\": \"dws_ticket_flow\",\n"
            + "         \"fTable\": \"f_dws_ticket_flow\",\n"
            + "         \"primaryKey\": \"id\"\n"
            + "    }\n"
            + "]";
}
