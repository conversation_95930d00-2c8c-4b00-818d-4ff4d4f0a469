package com.tencent.andata.asyncmap;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.types.Row;

import java.util.List;

public class DataMaskProcessor implements AsyncDataMask.DataProcessor<Row, Row> {

    private final List<String> fields;
    private final List<String> dstFields;
    private final String separator;

    public DataMaskProcessor(List<String> fields, List<String> dstFields, String separator) {
        this.fields = fields;
        this.dstFields = dstFields;
        this.separator = separator;
    }

    @Override
    public String getMaskData(Row row) {
        boolean needMask = false;
        StringBuilder dataBuilder = new StringBuilder();
        for (int i = 0; i < fields.size(); i++) {
            Object rowField = row.getField(fields.get(i));
            if (rowField != null) {
                String fieldString = rowField.toString();
                if (StringUtils.isNotBlank(fieldString)) {
                    dataBuilder.append(fieldString);
                    needMask = true;
                }
            }
            dataBuilder.append(separator);
        }
        if (!needMask) {
            return "";
        }
        return StringUtils.stripEnd(dataBuilder.toString(), separator);
    }

    @Override
    public Row retMaskedData(Row row, String maskedString) {
        if (StringUtils.isEmpty(maskedString)) {
            return row;
        }
        String[] split = maskedString.split(separator);
        for (int i = 0; i < split.length; i++) {
            row.setField(dstFields.get(i), split[i]);
        }
        return row;
    }
}
