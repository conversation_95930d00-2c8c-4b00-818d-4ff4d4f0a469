package com.tencent.andata.asyncmap;

import com.tencent.andata.utils.AsyncHttpClient;
import com.tencent.andata.utils.JSONUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Serializable;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

/**
 * 异步数据脱敏
 * 调用接口进行脱敏
 */
public class AsyncDataMask<IN, OUT> extends RichAsyncFunction<IN, OUT> {

    // TODO 切换到FlinkLog
    private static final Logger LOG = LoggerFactory.getLogger(AsyncDataMask.class);
    private static CloseableHttpAsyncClient httpAsyncClient;
    private final static String MASK_SERVER_URL = "http://11.141.226.95:8080/detect";

    private final DataProcessor<IN, OUT> processor;
    private final int connectTimeout;
    private final int socketTimeout;

    private final JSONUtils jsonUtils;

    public AsyncDataMask(
            DataProcessor<IN, OUT> processor,
            int connectTimeout,
            int socketTimeout
    ) {
        this.processor = processor;
        this.jsonUtils = new JSONUtils();
        this.connectTimeout = connectTimeout;
        this.socketTimeout = socketTimeout;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        LOG.info("AsyncDataMask start open ...");
        // 创建异步HttpClient
        httpAsyncClient = AsyncHttpClient.getHttpClient(connectTimeout * 1000,
                socketTimeout * 1000, 10, 5);
        httpAsyncClient.start();
        int count = 0;
        while (!httpAsyncClient.isRunning()) {
            if (count == 30) {
                httpAsyncClient = AsyncHttpClient.getHttpClient(connectTimeout * 1000,
                        socketTimeout * 1000, 10, 5);
                httpAsyncClient.start();
            }
            if (count == 60) {
                throw new RuntimeException("AsyncDataMask start error");
            }
            Thread.sleep(1000);
            count++;
        }
        LOG.info("AsyncDataMask end open.");
    }


    @Override
    public void close() throws IOException {
        LOG.info("AsyncDataMask start close ...");
        httpAsyncClient.close();
        LOG.info("AsyncDataMask end close.");
    }

    @Override
    public void asyncInvoke(IN in, ResultFuture<OUT> resultFuture) throws Exception {
        // 提取待脱敏数据
        String maskData = this.processor.getMaskData(in);
        if (StringUtils.isEmpty(maskData)) {
            processor.retMaskedData(in, "");
            CompletableFuture.supplyAsync(() -> {
                return processor.retMaskedData(in, "");
            }).thenAcceptAsync((OUT result) -> resultFuture.complete(Collections.singleton(result)));
            return;
        }
        // 封装请求
        JSONObject params = new JSONObject();
        params.put("data", maskData);
        HttpPost postBody = AsyncHttpClient.doHttpPost(
                MASK_SERVER_URL,
                params.toString(),
                ContentType.APPLICATION_JSON
        );
        // 发送异步请求
        Future<HttpResponse> future = httpAsyncClient.execute(postBody, null);
        // 提交异步回调，不阻塞本方法
        CompletableFuture.supplyAsync(() -> {
            String maskedData = "";
            // 用try包住，处理get不到值时的报错程序
            try {
                HttpResponse response = future.get();
                if (response.getStatusLine().getStatusCode() == 200) {
                    //拿出响应的实例对象
                    String content = EntityUtils.toString(response.getEntity(), "UTF-8");
                    ObjectNode resp = jsonUtils.getJSONObjectNodeByString(content);
                    final JsonNode resNode = resp.get("result");
                    if (resNode == null) {
                        throw new RuntimeException(String.format(
                                "Http response error: %s",
                                response.getEntity().getContent()
                        ));
                    }
                    maskedData = resNode.asText();
                }
                // TODO 脱敏失败返回的是null，下游需要做特殊处理
                return processor.retMaskedData(in, maskedData);
            } catch (Exception e) {
                LOG.warn(
                        String.format(
                                "data async mask error: %s\ndata:%s\n",
                                e,
                                maskData
                        )
                );
                return processor.retMaskedData(in, maskedData);
            }
        }).thenAcceptAsync((OUT result) -> resultFuture.complete(Collections.singleton(result)));
    }

    @Override
    public void timeout(IN input, ResultFuture<OUT> resultFuture) throws Exception {
        super.timeout(input, resultFuture);
    }

    /**
     * 待脱敏数据的提取 & 脱敏后数据注入
     */
    public interface DataProcessor<IN, OUT> extends Serializable {

        public String getMaskData(IN in);

        public OUT retMaskedData(IN in, String maskedString);
    }
}