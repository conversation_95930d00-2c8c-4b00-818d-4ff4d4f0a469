package com.tencent.andata.etl.mask.v3.webim;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.common.manage.KafkaManager;
import com.tencent.andata.filter.ConditionInFilter;
import com.tencent.andata.filter.InsertFilter;
import com.tencent.andata.filter.TimeFilter;
import com.tencent.andata.filter.webim.WebImFlowFilter;
import com.tencent.andata.map.mask.AddTitleMap;
import com.tencent.andata.map.mask.CleanFlowDataMap;
import com.tencent.andata.map.mask.rt.webim.WebImFlowGroupRtProcess;
import com.tencent.andata.map.mask.rt.webim.WebImWindowRtProcess;
import com.tencent.andata.schema.mask.Json2RowDeserialization;
import com.tencent.andata.schema.mask.convert.AdaptorFactory;
import com.tencent.andata.sink.BaseSink;
import com.tencent.andata.sink.CommonMqSink;
import com.tencent.andata.sink.IceSink;
import com.tencent.andata.sql.TicketBaseInfoJoinSql;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.*;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.utils.udf.AsyncScanHbase;
import lombok.Builder;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Builder
public class WebIMMaskFlowGroupEtl {

    private static final Logger logger = LoggerFactory.getLogger(WebIMMaskFlowGroupEtl.class);
    private final ObjectMapper mapper = new ObjectMapper();
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    // 聚合条件
    private final int maxFlowCount;
    private final int windowSize;
    // interval join
    private final Long ticketInfoTime;
    // 是不是回刷
    private final boolean refresh;
    // 实时流程的参数
    private final String groupID;
    private final String convertName;
    // 离线 webim 筛选条件
    private final String icebergDbName;
    public final String streaming;
    private final Long startTime;
    private final Long endTime;
    private final Long increaseTime;
    private final String startingStrategy;
    private final String source;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // 注册ice表
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );

        // 筛选通道和时间范围
        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));
        DataStream<Row> filterStream = getSourceByIsRefreshed(flinkEnv, rainbowUtils);
        // 流水聚合 窗口排序+状态保存&最终排序
//        SingleOutputStreamOperator<Row> groupStream = filterStream
//                .assignTimestampsAndWatermarks(
//                        WatermarkStrategy.noWatermarks()
//                )
//                .map(new MsgExtractMap("msg_data", "msg_data"))
//                .keyBy(row -> row.getField("conversation_id").toString())
//                .window(ProcessingTimeSessionWindows.withGap(Time.seconds(windowSize)))
//                .process(new WebImWindowSortProcess())
//                .keyBy(row -> row.getField("conversation_id").toString())
//                .process(new WebImFlowV3GroupProcess(maxFlowCount))
//                .returns(filterStream.getType());
        // 从hbase中捞流水
        SingleOutputStreamOperator<Row> flowStream = filterStream
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.noWatermarks()
                )
                .keyBy(row -> row.getField("conversation_id").toString())
                .window(TumblingProcessingTimeWindows.of(Time.seconds(windowSize)))
                // 这个窗口内去捞hbase流水然后聚合下发
                .process(new WebImWindowRtProcess())
                .returns(filterStream.getType());
        // 异步从hbase捞工单流水并聚合
        SingleOutputStreamOperator<Row> groupStream = AsyncDataStream
                .unorderedWait(
                        flowStream.map(r -> new Tuple3<>(r, "conversation_id", "operation_data"))
                                .returns(new TypeHint<Tuple3<Row, String, String>>() {
                                }),
                        constructHbaseScanOp(rainbowUtils), 2 * 60 * 1000L, TimeUnit.MILLISECONDS, 100)
                // 工单内外部回复流水聚合
                .process(new WebImFlowGroupRtProcess(maxFlowCount))
                .map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        logger.info("聚合之后的数据：{}", row);
                        return row;
                    }
                })
                .returns(flowStream.getType());
        // join 工单信息
        DataStream<Row> dataStream = new TicketBaseInfoJoinSql(ticketInfoTime)
                .richDataByLookupJoinPG(tEnv, groupStream, rainbowUtils);
        SingleOutputStreamOperator<Row> resStream = dataStream
                .map(new AddTitleMap("reply,c_reply,msg"))
                .returns(dataStream.getType());
        // 写 ice
        BaseSink.Builder builder = BaseSink.builder()
                .setFlinkEnv(flinkEnv);
        if (refresh) {
            // 回刷直接写ice
            // todo 这块因为正常由writer写ice会有一个清洗的逻辑，回刷这里就保持一致
            resStream = resStream.map(new CleanFlowDataMap()).returns(resStream.getType());
            builder.addSink(new IceSink("iceberg_sink_ticket_flow"));
        } else {
            // 实时写 聚合mq
            builder.addSink(new CommonMqSink(
                            MqConf.fromRainbow(rainbowUtils, "cprb.mask.kafka.webimgroup", groupID),
                            "id",
                            true
                    )
            );
        }
        builder.build(rainbowUtils).run(resStream);
    }

    private RichAsyncFunction<Tuple3<Row, String, String>, Row> constructHbaseScanOp(RainbowUtils rainbowUtils) {
        // 从hbase捞工单流水 异步算子
        String zkQuorum = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_QUORUM");
        String zkNodeParent = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_ZNODE_PARENT");

        return AsyncScanHbase.builder()
                .hbaseZookeeperQuorum(zkQuorum)
                .zookeeperZnodeParent(zkNodeParent)
                .hTableName(rainbowUtils.getStringValue("cprb.mask.hbase.webim", "HTBIName"))
                .columnFamily("cf")
                .qualifier("data")
                .build();
    }

    /***
     * getSourceByIsRefreshed .
     * @param flinkEnv .
     * @param rainbowUtils .
     * @return .
     * @throws TableNotExistException .
     * @throws InterruptedException .
     * @throws NoSuchFieldException .
     * @throws IllegalAccessException .
     */
    public DataStream<Row> getSourceByIsRefreshed(FlinkEnv flinkEnv, RainbowUtils rainbowUtils)
            throws TableNotExistException, InterruptedException, NoSuchFieldException, IllegalAccessException {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        DataStream<Row> dataStream = null;
        if (refresh) {
            // 全量+增量读取webim mask ice表
            Table filterTable = tEnv.sqlQuery(String.format(ICE_FILTER_WEBIM_SQL,
                    WEBIM_FIELDS_SQL, streaming, startingStrategy,
                    sdf.format(increaseTime * 1000L),
                    sdf.format(startTime * 1000L),
                    sdf.format(endTime * 1000L)));
            dataStream = tEnv.toDataStream(filterTable);
            // 筛选时间范围
            dataStream = dataStream
                    .filter(new InsertFilter())
                    .filter(new TimeFilter("create_time", startTime, endTime, increaseTime))
                    .filter(
                            new ConditionInFilter("source",
                                    Stream.of(source.split(",")).collect(Collectors.toList()))
                    )
                    .filter(new WebImFlowFilter())
                    .returns(dataStream.getType());
        } else {
            DataStream<Row> source = KafkaManager.<Row>builder()
                    .setMqConf(MqConf.fromRainbow(rainbowUtils,
                            "cprb.mask.kafka.webimflowmask", groupID))
                    .setKafkaRecordDeserializationSchema(
                            new Json2RowDeserialization(AdaptorFactory.getConvertByName(convertName),
                                    new TableIdentifier(DatabaseEnum.ICEBERG, icebergDbName,
                                            "dwd_im_online_customer_service_backend_mask_data")))
                    .build()
                    .getDataStreamSource(flinkEnv.env());
            // 这里的source的格式是脱敏流水表的格式
            // 需要转换一下，必须包含工单聚合表里面的字段
            Table table = tEnv.fromDataStream(source);
            tEnv.createTemporaryView("webim_flow_mask", table);
            dataStream = tEnv.toDataStream(tEnv.sqlQuery(String.format(FILTER_WEBIM_SQL, WEBIM_FIELDS_SQL)));
        }

        return dataStream.filter(
                        new ConditionInFilter("source",
                                Stream.of(source.split(",")).collect(Collectors.toList()))
                ).filter(new WebImFlowFilter())
                .returns(dataStream.getType());
    }

    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_im_online_customer_service_backend_mask_data\",\n"
            + "        \"fTable\":\"iceberg_webim_flow_mask\",\n"
            + "        \"primaryKey\":\"\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_base_info\",\n"
            + "        \"fTable\":\"iceberg_incident_ticket_base_info\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dws_ticket_flow\",\n"
            + "        \"fTable\":\"iceberg_sink_ticket_flow\",\n"
            + "        \"primaryKey\":\"id,ticket_id,ticket_create_time\"\n"
            + "    }\n"
            + "]";

    public static final String FILTER_WEBIM_SQL = ""
            + "SELECT \n"
            + "%s"
            + "FROM webim_flow_mask\n"
            + "";

    public static final String ICE_FILTER_WEBIM_SQL = ""
            + "SELECT \n"
            + "%s"
            + "FROM iceberg_webim_flow_mask\n"
            + "/*+ OPTIONS('streaming'='%s', 'monitor-interval'='1s', 'starting-strategy'='%s')*/\n"
            + "where (create_time > '%s' or (create_time between '%s' and '%s'))\n"
            + "";


    public static final String WEBIM_FIELDS_SQL = ""
            + "    value_of_primary_key,\n"
            + "    conversation_id,\n"
            + "    conversation_ticket_ids,\n"
            + "    CAST(0 as bigint) as ticket_id,\n"
            + "    '' as id,\n"
            + "    record_update_time as close_time,\n"
            + "    record_update_time,\n"
            + "    operation,\n"
            + "    creator_type,\n"
            + "    case when msg_data is null then '' else msg_data end as msg_data,\n"
            + "    case when c_msg is null then '' else c_msg end as c_msg,\n"
            + "    case when mask_msg is null then '' else mask_msg end as mask_msg,\n"
            + "    case when msg_seq is null then '' else msg_seq end as msg_seq,\n"
            + "    source,\n"
            + "    status,\n"
            + "    create_time,\n"
            + "    ftime,\n"
            + "    '' as reply,\n"
            + "    '' as c_reply,\n"
            + "    '' as msg,\n"
            + "    '' as operation_data\n"
            + "";
}
