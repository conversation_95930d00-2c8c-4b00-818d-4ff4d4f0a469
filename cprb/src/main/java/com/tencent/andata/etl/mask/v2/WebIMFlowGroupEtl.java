package com.tencent.andata.etl.mask.v2;

import com.tencent.andata.filter.ConditionInFilter;
import com.tencent.andata.filter.InsertFilter;
import com.tencent.andata.filter.TimeFilter;
import com.tencent.andata.map.mask.AddTitleMap;
import com.tencent.andata.map.mask.v2.webim.WebIMFlowGroupProcess;
import com.tencent.andata.map.mask.WebImWindowSortProcess;
import com.tencent.andata.sink.BaseSink;
import com.tencent.andata.sink.MqSink;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.windowing.assigners.ProcessingTimeSessionWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Builder
public class WebIMFlowGroupEtl {

    public static final String FILTER_WEBIM_SQL = ""
            + "SELECT \n"
            + "    value_of_primary_key,\n"
            + "    conversation_id,\n"
            + "    conversation_ticket_ids,\n"
            + "    '' as ticket_id,\n"
            + "    '' as id,\n"
            + "    ftime as close_time,\n"
            + "    case \n"
            + "        when operation = 'SendZXMsg' then '客服'\n"
            + "        when operation = 'SendUserMsg' then '客户'\n"
            + "        when operation = 'SendWeworkExternalGroupMsg' and creator_type = 1 then '客户'\n"
            + "        when operation = 'SendChatCallbackMsg' and creator_type = 1 then '客户'\n"
            + "        else '客服'\n"
            + "    end as operator_type,\n"
            + "    case when msg_data is null then '' else msg_data end as msg_data,\n"
            + "    case when msg_seq is null then '' else msg_seq end as msg_seq,\n"
            + "    source,\n"
            + "    status,\n"
            + "    create_time,\n"
            + "    ftime,\n"
            + "    '' as reply,\n"
            + "    '' as c_reply,\n"
            + "    '' as msg\n"
            + "FROM iceberg_webim_flow\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', 'starting-strategy'='%s')*/\n"
            + "where (create_time > '%s' or (create_time between '%s' and '%s')) and \n"
            + "(status in (6,7,9,10,12) or msg_data is not null)\n"
            + "";

    private static final Logger logger = LoggerFactory.getLogger(WebIMFlowGroupEtl.class);
    private final String icebergDbName;
    private final Long startTime;
    private final Long endTime;
    private final Long increaseTime;
    private final String startingStrategy;
    private final ObjectMapper mapper = new ObjectMapper();
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final String topic;
    private final int maxFlowCount;
    private final int windowSize;
    private final String source;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        // 注册ice表
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );

        // 全量+增量读取webim ice表
        Table filterTable = tEnv.sqlQuery(
                String.format(FILTER_WEBIM_SQL, startingStrategy,
                        sdf.format(increaseTime), sdf.format(startTime), sdf.format(endTime)));
        DataStream<Row> rowDataStream = tEnv.toChangelogStream(filterTable,
                Schema.newBuilder()
                        .primaryKey("value_of_primary_key")
                        .build(),
                ChangelogMode.upsert());
        // 筛选通道和时间范围
        SingleOutputStreamOperator<Row> filterStream = rowDataStream
                .filter(new TimeFilter("create_time", startTime, endTime, increaseTime))
                .filter(
                        new ConditionInFilter("source",
                                Stream.of(source.split(",")).collect(Collectors.toList()))
                );

        // 窗口排序+状态保存&最终排序
        SingleOutputStreamOperator<Row> groupStream = filterStream
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.noWatermarks()
                )
                .filter(new InsertFilter())
                .keyBy(row -> row.getField("conversation_id").toString())
                .window(ProcessingTimeSessionWindows.withGap(Time.seconds(windowSize)))
                .process(new WebImWindowSortProcess())
                .keyBy(row -> row.getField("conversation_id").toString())
                .process(new WebIMFlowGroupProcess(maxFlowCount))
                .returns(rowDataStream.getType());

        // join 工单信息
        DataStream<Row> dataStream = replenishTicketBaseInfo(tEnv, groupStream);
        // 写 mq
        BaseSink.builder()
                .setFlinkEnv(flinkEnv)
                .addSink(new MqSink(topic))
                .build(rainbowUtils)
                .run(dataStream.map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        Object question = row.getField(7);
                        if (question != null) {
                            Object rowField = row.getField(2);
                            if (rowField != null && StringUtils.isNotEmpty(rowField.toString())) {
                                row.setField(2, question + "\n" + rowField);
                            }
                            Object cReply = row.getField(3);
                            if (cReply != null && StringUtils.isNotEmpty(cReply.toString())) {
                                row.setField(3, question + "\n" + cReply);
                            }
                        }
                        return row;
                    }
                }).returns(dataStream.getType()));

    }

    /***
     * replenishTicketBaseInfo
     * @param tEnv
     * @param groupStream
     * @return
     */
    private DataStream<Row> replenishTicketBaseInfo(StreamTableEnvironment tEnv, DataStream<Row> groupStream) {
        // 注册工单基础表
        Table ticketBaseInfo = tEnv.sqlQuery(TICKET_BASE_INFO_SQL);
        tEnv.createTemporaryView("dim_ticket_base_info", ticketBaseInfo);
        // 聚合流转表
        Table groupTable = tEnv.fromChangelogStream(
                groupStream,
                Schema.newBuilder()
                        .columnByExpression("process_time", "PROCTIME()")
                        .build(), ChangelogMode.insertOnly()
        );
        tEnv.createTemporaryView("group_webim_flow_data", groupTable);
        // interval join
        Table table = tEnv.sqlQuery(INTERVAL_JOIN_SQL);
        // 表转流
        return tEnv.toChangelogStream(table, Schema.newBuilder()
                .primaryKey("id")
                .build(), ChangelogMode.upsert());
    }

    public static String INTERVAL_JOIN_SQL = ""
            + "select\n"
            + "    t1.id,\n"
            + "    cast(t1.ticket_id as bigint) as ticket_id,\n"
            + "    t1.reply,\n"
            + "    t1.c_reply,\n"
            + "    t1.msg,\n"
            + "    t1.source,\n"
            + "    t2.url,\n"
            + "    CASE t2.title WHEN '' THEN t2.question ELSE t2.title END AS question,\n"
            + "    if(t2.service_scene_checked > 0, t2.service_scene_checked, t2.service_scene) as service_scene,\n"
            + "    t2.create_time as ticket_create_time,\n"
            + "    t2.update_time,\n"
            + "    t1.close_time\n"
            + "from group_webim_flow_data t1\n"
            + "inner join dim_ticket_base_info t2\n"
            + "on (cast(t1.ticket_id as bigint) = t2.ticket_id AND "
            + "(t1.process_time between t2.process_time - INTERVAL '2' HOUR and t2.process_time + INTERVAL '2' HOUR))"
            + "";


    public static final String TICKET_BASE_INFO_SQL = ""
            + "SELECT \n"
            + "    ticket_id,\n"
            + "    url,\n"
            + "    title,\n"
            + "    question,\n"
            + "    service_scene_checked,\n"
            + "    service_scene,\n"
            + "    create_time,\n"
            + "    status,\n"
            + "    create_time,\n"
            + "    update_time,\n"
            + "    process_time\n"
            + "FROM iceberg_incident_ticket_base_info\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
            + "";

    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_im_online_customer_service_backend_data\",\n"
            + "        \"fTable\":\"iceberg_webim_flow\",\n"
            + "        \"primaryKey\":\"value_of_primary_key\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_base_info\",\n"
            + "        \"fTable\":\"iceberg_incident_ticket_base_info\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dws_ticket_flow\",\n"
            + "        \"fTable\":\"iceberg_sink_ticket_flow\",\n"
            + "        \"primaryKey\":\"id,ticket_create_time\"\n"
            + "    }\n"
            + "]";
}
