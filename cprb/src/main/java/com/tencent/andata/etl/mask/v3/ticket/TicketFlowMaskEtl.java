package com.tencent.andata.etl.mask.v3.ticket;

import com.google.common.collect.ImmutableList;
import com.tencent.andata.asyncmap.AsyncDataMask;
import com.tencent.andata.asyncmap.DataMaskProcessor;
import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.common.manage.KafkaManager;
import com.tencent.andata.map.mask.FieldCleanMap;
import com.tencent.andata.schema.mask.Json2RowDeserialization;
import com.tencent.andata.schema.mask.convert.AdaptorFactory;
import com.tencent.andata.sink.CommonHbaseSink;
import com.tencent.andata.sink.CommonMqSink;
import com.tencent.andata.sink.IceSink;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.struct.DatabaseEnum;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Builder
public class TicketFlowMaskEtl {

    private StreamExecutionEnvironment environment;
    private static final Logger logger = LoggerFactory.getLogger(TicketFlowMaskEtl.class);
    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_operation\",\n"
            + "        \"fTable\":\"iceberg_incident_ticket_flow\",\n"
            + "        \"primaryKey\":\"operation_id,operate_time,ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_operation_mask\",\n"
            + "        \"fTable\":\"iceberg_incident_ticket_flow_mask\",\n"
            + "        \"primaryKey\":\"operation_id,operate_time,ticket_id\"\n"
            + "    }\n"
            + "]";
    private final ObjectMapper mapper = new ObjectMapper();
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    // 工单流水筛选条件
    private final String icebergDbName;
    // 脱敏相关参数
    private final Integer cap;
    private int connectTimeout;
    private int socketTimeout;
    private int asyncTimeOut;
    private Integer flowMaxLength;
    // convert
    private String convertName = "";
    // 发送mq相关参数
    private String groupID;
    // 是不是回刷
    private boolean refresh = false;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // 注册flink表
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );
        TableIdentifier identifier = new TableIdentifier(
                DatabaseEnum.ICEBERG, icebergDbName, "dwd_incident_ticket_operation_mask");
        // kafka实时读取
        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));
        String groupPath = refresh ? "cprb.mask.kafka.ticketflow_refresh" : "cprb.mask.kafka.ticketflow";
        DataStream<Row> startStream = KafkaManager.<Row>builder()
                .setMqConf(MqConf.fromRainbow(rainbowUtils, groupPath, groupID))
                .setKafkaRecordDeserializationSchema(new Json2RowDeserialization(
                        AdaptorFactory.getConvertByName(convertName), identifier
                ))
                .build()
                .getDataStreamSource(flinkEnv.env());
        // 字段清洗
        SingleOutputStreamOperator<Row> filterStream = startStream
                .map(new FieldCleanMap("inner_reply", "c_inner_msg", flowMaxLength))
                .map(new FieldCleanMap("extern_reply", "c_msg", flowMaxLength))
                .returns(startStream.getType());
                
        // 直接复制原始数据到脱敏字段
        DataStream<Row> result = filterStream
                .map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        row.setField("mask_msg", row.getField("c_msg"));
                        row.setField("mask_inner_msg", row.getField("c_inner_msg"));
                        return row;
                    }
                })
                .returns(startStream.getType());

        Table table = tEnv.fromChangelogStream(result,
                Schema.newBuilder()
                        .primaryKey("operation_id")
                        .build(),
                ChangelogMode.insertOnly());
        if (refresh) {
            // 回刷 脱敏成功的数据写入ice
            new IceSink("iceberg_incident_ticket_flow_mask")
                    .sink(flinkEnv, table);
        } else {
            // 不是回刷 脱敏成功就写mq
            // 转table 发mq
            new CommonMqSink(MqConf.fromRainbow(rainbowUtils, "cprb.mask.kafka.ticketflowmask", groupID),
                    table, "operation_id").sink(flinkEnv, table);
        }
        new CommonHbaseSink(
                rainbowUtils.getStringValue("cprb.mask.hbase.ticket", "HTBIName"),
                "h_incident_ticket_operation_mask",
                ImmutableList.of("CAST(`ticket_id` as STRING)", "CAST(`operation_id` as STRING)"))
                .sink(flinkEnv, table);
    }
}
