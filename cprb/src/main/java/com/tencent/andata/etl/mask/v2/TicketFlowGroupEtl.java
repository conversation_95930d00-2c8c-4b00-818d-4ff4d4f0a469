package com.tencent.andata.etl.mask.v2;


import com.tencent.andata.filter.ticket.TicketConditionFilter;
import com.tencent.andata.map.mask.v2.ticket.TicketFlowGroupProcess;
import com.tencent.andata.sink.BaseSink;
import com.tencent.andata.sink.MqSink;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.udf.AsyncScanHbase;
import lombok.Builder;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;
import java.util.concurrent.TimeUnit;

@Builder
public class TicketFlowGroupEtl {

    // INCREMENTAL_FROM_LATEST_SNAPSHOT 读增量数据
    // TABLE_SCAN_THEN_INCREMENTAL 读全量数据
    public static final String TICKET_READ_SQL = ""
            + "SELECT \n"
            + "    '' as id,\n"
            + "    ticket_id, \n"
            + "    url,\n"
            + "    service_channel,\n"
            + "    status,\n"
            + "    CASE title WHEN '' THEN question ELSE title END AS question,\n"
            + "    if(service_scene_checked > 0, service_scene_checked, service_scene) as service_scene,\n"
            + "    create_time as ticket_create_time,\n"
            + "    update_time,\n"
            + "    '' as reply,\n"
            + "    '' as c_reply,\n"
            + "    '' as msg,\n"
            + "    '' as source,\n"
            + "    '' AS operation_data,\n"
            + "    update_time AS close_time\n"
            + "FROM iceberg_incident_ticket_base_info\n"
            + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='%s')*/\n"
            + "";

    private static final Logger logger = LoggerFactory.getLogger(TicketFlowGroupEtl.class);
    private final String icebergDbName;
    private final Long startTime;
    private final Long endTime;
    private final Long increaseTime;
    private final String timeField;
    private final String startingStrategy;
    private final String topic;
    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        // 注册flink表
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );

        // 全量+增量读取ice
        Table startTable = tEnv.sqlQuery(String.format(TICKET_READ_SQL, startingStrategy));
        // 这里读upsert流，因为工单状态可能改变
        DataStream<Row> startStream = tEnv.toChangelogStream(startTable,
                Schema.newBuilder()
                        .primaryKey("ticket_id")
                        .build(),
                ChangelogMode.upsert());
        // 时间范围+结单筛选
        SingleOutputStreamOperator<Tuple3<Row, String, String>> filterStream = startStream
                .filter(new TicketConditionFilter(timeField, startTime, endTime, increaseTime))
                .map(r -> new Tuple3<>(r, "ticket_id", "operation_data"))
                .returns(new TypeHint<Tuple3<Row, String, String>>() {
                });
        // 异步从hbase捞工单流水并聚合
        SingleOutputStreamOperator<Row> groupedStream = AsyncDataStream
                .unorderedWait(filterStream, constructHbaseScanOp(rainbowUtils),
                        2 * 60 * 1000L, TimeUnit.MILLISECONDS, 100)
                // 工单内外部回复流水聚合
                .process(new TicketFlowGroupProcess())
                .returns(startStream.getType());
        // 写 mq
        BaseSink.builder()
                .setFlinkEnv(flinkEnv)
                .addSink(new MqSink(topic))
                .build(rainbowUtils)
                .run(groupedStream);
    }


    private RichAsyncFunction<Tuple3<Row, String, String>, Row> constructHbaseScanOp(RainbowUtils rainbowUtils) {
        // 从hbase捞工单流水 异步算子
        String zkQuorum = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_QUORUM");
        String zkNodeParent = rainbowUtils.getStringValue("cdc.database.hbase", "ZOOKEEPER_ZNODE_PARENT");

        return AsyncScanHbase.builder()
                .hbaseZookeeperQuorum(zkQuorum)
                .zookeeperZnodeParent(zkNodeParent)
                .hTableName("Incident_ticket_operation")
                .columnFamily("cf")
                .qualifier("data")
                .build();
    }


    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_base_info\",\n"
            + "        \"fTable\":\"iceberg_incident_ticket_base_info\",\n"
            + "        \"primaryKey\":\"ticket_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dws_ticket_flow\",\n"
            + "        \"fTable\":\"iceberg_sink_ticket_flow\",\n"
            + "        \"primaryKey\":\"id,ticket_create_time\"\n"
            + "    }\n"
            + "]";
}
