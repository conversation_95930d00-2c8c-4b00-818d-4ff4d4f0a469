package com.tencent.andata.etl.mask.v3.ticket;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.filter.TimeFilter;
import com.tencent.andata.sink.CommonMqSink;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.HashSet;

@Builder
public class TicketFlowReadEtl {

    // INCREMENTAL_FROM_LATEST_SNAPSHOT 读增量数据
    // TABLE_SCAN_THEN_INCREMENTAL 读全量数据
    public static final String TICKET_READ_SQL = ""
            + "SELECT \n"
            + "    *,\n"
            + "    '' as c_msg,\n"
            + "    '' as mask_msg,\n"
            + "    '' as c_inner_msg,\n"
            + "    '' as mask_inner_msg\n"
            + "FROM iceberg_incident_ticket_flow\n"
            + "/*+ OPTIONS('streaming'='%s', 'monitor-interval'='1s', 'starting-strategy'='%s')*/\n"
            + "where (operate_time > '%s' or (operate_time between '%s' and '%s'))\n"
            + "";

    private static final Logger logger = LoggerFactory.getLogger(TicketFlowReadEtl.class);
    private final ObjectMapper mapper = new ObjectMapper();
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    // 工单流水筛选条件
    private final String icebergDbName;
    public final String streaming;
    private final String startingStrategy;
    private final Long startTime;
    private final Long endTime;
    private final Long increaseTime;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // 注册flink表
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );
        // sql的方式全量或增量读取ice并转ds
        Table startTable = tEnv.sqlQuery(String.format(
                        TICKET_READ_SQL,
                        streaming, startingStrategy,
                        sdf.format(increaseTime * 1000L),
                        sdf.format(startTime * 1000L),
                        sdf.format(endTime * 1000L)
                )
        );
        DataStream<Row> startStream = tEnv.toDataStream(startTable);
        // 时间范围去过滤数据
        SingleOutputStreamOperator<Row> filterStream = startStream
                .filter(new TimeFilter("operate_time", startTime, endTime, increaseTime))
                .returns(startStream.getType());
        // 转table 发mq
        Table flowTb = tEnv.fromChangelogStream(filterStream,
                Schema.newBuilder()
                        .primaryKey("operation_id")
                        .build(),
                ChangelogMode.insertOnly());
        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));
        new CommonMqSink(
                MqConf.fromRainbow(rainbowUtils, "cprb.mask.kafka.ticketflow_refresh"),
                flowTb, "operation_id"
        ).sink(flinkEnv, flowTb);
    }

    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_operation\",\n"
            + "        \"fTable\":\"iceberg_incident_ticket_flow\",\n"
            + "        \"primaryKey\":\"\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_incident_ticket_operation_mask\",\n"
            + "        \"fTable\":\"iceberg_incident_ticket_flow_mask\",\n"
            + "        \"primaryKey\":\"\"\n"
            + "    }\n"
            + "]";
}
