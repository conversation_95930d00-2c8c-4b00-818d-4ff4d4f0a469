package com.tencent.andata.etl.mask.v3.webim;

import com.google.common.collect.ImmutableList;
import com.tencent.andata.asyncmap.AsyncDataMask;
import com.tencent.andata.asyncmap.DataMaskProcessor;
import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.common.manage.KafkaManager;
import com.tencent.andata.map.mask.FieldCleanMap;
import com.tencent.andata.map.mask.v3.webim.MsgExtractMap;
import com.tencent.andata.schema.mask.Json2RowDeserialization;
import com.tencent.andata.schema.mask.convert.AdaptorFactory;
import com.tencent.andata.sink.CommonHbaseSink;
import com.tencent.andata.sink.CommonMqSink;
import com.tencent.andata.sink.IceSink;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.struct.DatabaseEnum;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Builder
public class WebIMFlowMaskEtl {

    private static final Logger logger = LoggerFactory.getLogger(WebIMFlowMaskEtl.class);
    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_im_online_customer_service_backend_data\",\n"
            + "        \"fTable\":\"iceberg_webim_flow\",\n"
            + "        \"primaryKey\":\"value_of_primary_key,ftime,conversation_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_im_online_customer_service_backend_mask_data\",\n"
            + "        \"fTable\":\"iceberg_webim_flow_mask\",\n"
            + "        \"primaryKey\":\"value_of_primary_key,ftime,conversation_id\"\n"
            + "    }\n"
            + "]";
    private final ObjectMapper mapper = new ObjectMapper();
    // webim 筛选条件
    private final String icebergDbName;
    // 脱敏相关参数
    private final Integer cap;
    private int connectTimeout;
    private int socketTimeout;
    private int asyncTimeOut;
    private Integer flowMaxLength;
    // 是不是回刷
    private boolean refresh;
    // 发送mq相关参数
    private String groupID;
    // convert
    private String convertName = "";

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // 注册ice表
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );
        TableIdentifier identifier = new TableIdentifier(DatabaseEnum.ICEBERG, icebergDbName,
                "dwd_im_online_customer_service_backend_mask_data");
        // 读mq
        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));
        String groupPath = refresh ? "cprb.mask.kafka.webimflow_refresh" : "cprb.mask.kafka.webimflow";
        DataStream<Row> startStream = KafkaManager.<Row>builder()
                .setMqConf(MqConf.fromRainbow(rainbowUtils, groupPath, groupID))
                .setKafkaRecordDeserializationSchema(
                        new Json2RowDeserialization(AdaptorFactory.getConvertByName(convertName), identifier))
                .build()
                .getDataStreamSource(flinkEnv.env());

//        DataStream<Row> startStream = new CommonMqSource(topic, tEnv.from("iceberg_webim_flow"), groupID,
//                Stream.of("c_msg", "mask_msg")
//                        .collect(Collectors.toList()), "value_of_primary_key").read(flinkEnv);
        // 清洗
        SingleOutputStreamOperator<Row> filterStream = startStream
                .map(new MsgExtractMap("msg_data", "c_msg"))
                .map(new FieldCleanMap("c_msg", "c_msg", flowMaxLength))
                .returns(startStream.getType());
        // 直接复制原始数据到脱敏字段
        SingleOutputStreamOperator<Row> result = filterStream
                .map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        row.setField("mask_msg", row.getField("c_msg"));
                        return row;
                    }
                })
                .returns(startStream.getType());

        Table table = tEnv.fromChangelogStream(result,
                Schema.newBuilder()
                        .primaryKey("value_of_primary_key")
                        .build(),
                ChangelogMode.insertOnly());
        if (refresh) {
            // 脱敏成功的数据写入ice
            new IceSink("iceberg_webim_flow_mask").sink(flinkEnv, table);
        } else {
            // 发送到脱敏成功mq
            Table flowTb = tEnv.fromChangelogStream(result,
                    Schema.newBuilder()
                            .primaryKey("conversation_id")
                            .build(),
                    ChangelogMode.insertOnly());
            new CommonMqSink(MqConf.fromRainbow(rainbowUtils, "cprb.mask.kafka.webimflowmask", groupID),
                    flowTb, "conversation_id").sink(flinkEnv, flowTb);
        }
        new CommonHbaseSink(
                rainbowUtils.getStringValue("cprb.mask.hbase.webim", "HTBIName"),
                "h_webim_flow_mask",
                // rowkey: conversationId-value_of_primary_key
                // 避免rowkey过长，去掉value_of_primary_key中的conversationId信息
                ImmutableList.of("CAST(`conversation_id` as STRING)", "SPLIT_INDEX(`value_of_primary_key`, '|', 0)"))
                .sink(flinkEnv, table);
    }
}
