package com.tencent.andata.etl.mask.v2;


import com.tencent.andata.asyncmap.AsyncDataMask;
import com.tencent.andata.constant.TicketFlow;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.map.mask.v2.desensitization.DesensitizationDispatchProcess;
import com.tencent.andata.map.mask.v2.desensitization.LengthSubMap;
import com.tencent.andata.map.RateLimitProcess;
import com.tencent.andata.sink.IceSink;
import com.tencent.andata.sink.MqSink;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import lombok.Builder;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

// 96775
@Builder
public class DesensitizationEtl {

    public static final String KAFKA_SOURCE_SQL = "CREATE TABLE ticket_flow_kafka_source (\n"
            + TicketFlow.CREATE_FIELDS
            + ") WITH (\n"
            + "  'connector' = 'kafka',\n"
            + "  'topic' = '%s',\n"
            + "  'properties.bootstrap.servers' = '30.181.143.184:9092',\n"
            + "  'properties.group.id' = '%s',\n"
            + "  'properties.max.request.size' = '31457280',\n"
            + "  'scan.startup.mode' = 'latest-offset',\n"
            + "  'key.format' = 'json',\n"
            + "  'key.fields' = 'id',\n"
            + "  'value.format' = 'json',\n"
            + "  'value.json.fail-on-missing-field' = 'false'"
            + ");";

    public static final String TICKET_READ_SQL = ""
            + "SELECT \n"
            + TicketFlow.FIELDS
            + "FROM ticket_flow_kafka_source"
            + "";

    private static final Logger logger = LoggerFactory.getLogger(DesensitizationEtl.class);
    private final String icebergDbName;
    private final Integer cap;
    private final int count;
    private final long timeWindow;
    private int connectTimeout;
    private int socketTimeout;
    private int asyncTimeOut;
    private final ObjectMapper mapper = new ObjectMapper();
    private String topic;
    private Integer flowMaxCount;
    private Integer flowMaxLength;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );
        // 从kafka读取数据
        tEnv.executeSql(String.format(KAFKA_SOURCE_SQL, topic, topic + "_read_group"));
        Table startTable = tEnv.sqlQuery(TICKET_READ_SQL);
        DataStream<Row> rowDataStream = tEnv.toDataStream(startTable);
        OutputTag<Row> outputTag = new OutputTag<Row>("desensitization-error-stream") {
        };
        // 脱敏
        SingleOutputStreamOperator<Row> result = AsyncDataStream
                .orderedWait(
                        rowDataStream.map(new LengthSubMap(flowMaxLength,
                                Stream.of("c_reply").collect(Collectors.toList()),
                                flowMaxCount)),
                        new AsyncDataMask<>(new TicketDataProcessor(), connectTimeout, socketTimeout),
                        // 异步超时时间
                        asyncTimeOut,
                        TimeUnit.SECONDS,
                        // 异步并发数
                        cap
                )
                .returns(rowDataStream.getType())
                .process(new DesensitizationDispatchProcess(outputTag))
                .returns(rowDataStream.getType());
        // 获取脱敏失败的数据，往mq里面发
        SingleOutputStreamOperator<Row> errorStream = result.getSideOutput(outputTag)
                .process(new RateLimitProcess(count, timeWindow))
                .setParallelism(1)
                .map(new MapFunction<Row, Row>() {
                    private final FlinkLog flinkLog = FlinkLog.getInstance();
                    @Override
                    public Row map(Row row) throws Exception {
                        flinkLog.warn(String.format("ticket_id %s: 脱敏失败", row.getField("ticket_id")));
                        return row;
                    }
                })
                .setParallelism(1)
                .returns(rowDataStream.getType());
        new MqSink(topic).sink(flinkEnv, tEnv.fromChangelogStream(errorStream,
                Schema.newBuilder()
                        .primaryKey("id")
                        .build(),
                ChangelogMode.insertOnly()));

        // 脱敏成功的数据写入ice
        new IceSink("iceberg_sink_ticket_flow").sink(flinkEnv, tEnv.fromChangelogStream(result,
                Schema.newBuilder()
                        .primaryKey("id")
                        .build(),
                ChangelogMode.upsert()));
    }

    public static class TicketDataProcessor implements AsyncDataMask.DataProcessor<Row, Row> {

        @Override
        public String getMaskData(Row row) {
            return row.getField("c_reply").toString();
        }

        @Override
        public Row retMaskedData(Row row, String maskedString) {
            row.setField("msg", maskedString);
            return row;
        }

    }


    public static String icebergTable2FlinkTable = "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dws_ticket_flow\",\n"
            + "        \"fTable\":\"iceberg_sink_ticket_flow\",\n"
            + "        \"primaryKey\":\"id,ticket_id,ticket_create_time\"\n"
            + "    }\n"
            + "]";
}
