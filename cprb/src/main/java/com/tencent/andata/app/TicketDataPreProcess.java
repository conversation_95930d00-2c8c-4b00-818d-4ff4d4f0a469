package com.tencent.andata.app;

import com.starrocks.connector.flink.StarRocksSink;
import com.starrocks.connector.flink.row.sink.StarRocksSinkRowBuilder;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.process.CvmZoneAndInstanceProcessFunction;
import com.tencent.andata.util.StarRocksUtils;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.iceberg.Table;
import org.ehcache.shadow.org.terracotta.offheapstore.HashingMap;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TODO 临时做的数据预处理任务，后续搞成统一的
 */
public class TicketDataPreProcess {
    public static FlinkLog logger = FlinkLog.getInstance();
    public static Pattern mainAreaPattern =
            Pattern.compile("(南京[13一三]区)|((?:北京|广州)[67六七]区)|(上海[58五八]区)");
    public static Pattern mainModelPattern =
            Pattern.compile("([Ss]5)|([Ss]6)|([Ss][Aa]2)|([Ss][Aa]3)|([Ss][Aa]4)|([Ss][Aa]5)");
    public static Pattern areaPattern =
            Pattern.compile("((?<!如)(?:南京|广州|北京|上海|深圳金融|上海金融|成都|重庆|香港|新加坡|雅加达|首尔|东京|"
                    + "孟买|曼谷|多伦多|圣保罗|硅谷|弗吉尼亚|法兰克福)[12345678一二三四五六七八]区)");
    public static Pattern modelPattern = Pattern.compile(
            "(?<!(?:如|[0-9A-Za-z]))((?:SA[2345])|(?:S[1346])|(?:SR1)|(?:S5(?:se)?)|(?:S2(?:ne)?)|(?:SN3ne)"
                    + "|(?:M[12345])|(?:MA[23])|(?:M6(?:ce|p)?)|(?:IT[35])|(?:D[23])|(?:C[23456])|(?:CN3)"
                    + "|(?:B[CS]1))(?![0-9A-Za-z])", Pattern.CASE_INSENSITIVE
    );

    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        flinkEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));
        flinkEnv.env().getConfig().enableObjectReuse();
        final StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        // 参数
        final String icebergDbName = parameterTool.get("icebergDbName");
        final Table dwsTicketFlow = catalog.getTableInstance(
                icebergDbName,
                "dws_ticket_flow"
        );
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("f_dws_ticket_flow")
                        .tableBuilderStrategy(new IcebergTableBuilderStrategy(dwsTicketFlow))
                        .build()
        );
        final DataStream<Row> source = tEnv.toChangelogStream(
                tEnv.sqlQuery("select ticket_id, reply \n"
                        + "from f_dws_ticket_flow \n"
                        + "/*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
                        + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/\n"
                        + "where source = 'ticket_inner'\n"
                )
        );
        final SingleOutputStreamOperator<Row> resStream = source
                .rebalance()
                .process(
                        new CvmZoneAndInstanceProcessFunction()
                )
                .setParallelism(parameterTool.getInt("parallelism"));
        sinkStarRock(resStream);
        flinkEnv.env().execute("ticket data pre process");
    }

    public static void sinkStarRock(SingleOutputStreamOperator<Row> ds) throws IOException {
        TableSchema schema = TableSchema.builder()
                .field("ticket_id", DataTypes.BIGINT().notNull())
                .field("is_main_area", DataTypes.BOOLEAN())
                .field("is_main_model", DataTypes.BOOLEAN())
                .field("machine_area", DataTypes.STRING())
                .field("machine_model_list", DataTypes.STRING())
                .field("region_type", DataTypes.STRING())
                .field("zone_type_name", DataTypes.STRING())
                .field("instance_type_name", DataTypes.STRING())
                .primaryKey("ticket_id")
                .build();
        StarRocksSinkRowBuilder<Row> transformer = StarRocksUtils.buildTransformerFromSchema(schema, true);
        SinkFunction<Row> sink = StarRocksSink.sink(
                schema,
                StarRocksUtils.getSinkOperations("dwm_incident_ticket_extra"),
                transformer
        );
        ds.addSink(sink).setParallelism(1).name("sink_sr_dwm_incident_ticket_extra");
    }
}
