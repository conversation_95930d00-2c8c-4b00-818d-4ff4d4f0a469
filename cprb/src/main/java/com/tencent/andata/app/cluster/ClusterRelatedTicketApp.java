package com.tencent.andata.app.cluster;

import com.tencent.andata.etl.cluster.ClusterFlatEtl;
import com.tencent.andata.etl.cluster.ClusterRelatedTicketEtl;
import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.java.utils.ParameterTool;

import java.util.ArrayList;
import java.util.List;

public class ClusterRelatedTicketApp {

    /***
     * main .
     * @param args .
     * @throws Exception .
     */
    public static void main(String[] args) throws Exception {
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        boolean ck = parameterTool.getBoolean("ck", true);
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args, ck);

        flinkEnv.env().getConfig().enableObjectReuse();
        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        new ClusterRelatedTicketEtl().run(flinkEnv, catalog);
        // execute the sql statements
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("CPRB Cluster related ticket Application ");
    }
}
