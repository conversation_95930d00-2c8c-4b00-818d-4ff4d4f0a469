package com.tencent.andata.app.debugapp;

import com.tencent.andata.etl.debugetl.TaskNoDataCallbackEtl;
import com.tencent.andata.util.FlinkTableConfBuilder;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

@Deprecated
public class TaskNoDataCallbackApplication {
    public static void main(String[] args) throws Exception{
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        flinkEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));

        flinkEnv.env().getConfig().enableObjectReuse();
        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        // table env config
        Configuration configuration = new FlinkTableConfBuilder()
                .setEnv(flinkEnv.streamTEnv())
                // 状态保留1小时
                .setParams("table.exec.state.ttl", "3600000")
                .build();
        String icebergDbName = parameterTool.get("icebergDbName");

        // instantiate ETL
        List<Object> appList = new ArrayList<>();
        // 设置flink应用程序名称
        configuration.setString("pipeline.name", "Task No Data Callback Application");
        appList.add(TaskNoDataCallbackEtl.builder().timeOut(600).icebergDbName(icebergDbName).build());
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));
        flinkEnv.env().execute("Task No Data Callback Application");
    }
}
