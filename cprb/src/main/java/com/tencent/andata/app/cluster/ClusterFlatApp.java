package com.tencent.andata.app.cluster;

import com.tencent.andata.etl.cluster.ClusterFlatEtl;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.java.utils.ParameterTool;

import java.util.ArrayList;
import java.util.List;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

// ClusterFlatApp.
public class ClusterFlatApp {

    /***
     * main .
     * @param args .
     * @throws Exception .
     */
    public static void main(String[] args) throws Exception {
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        boolean ck = parameterTool.getBoolean("ck", true);
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args, ck);

        flinkEnv.env().getConfig().enableObjectReuse();
        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        List<Object> appList = new ArrayList<>();
        appList.add(ClusterFlatEtl.builder()
                .build());
        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));
        // execute the sql statements
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("CPRB ClusterFlat Application ");
    }
}
