package com.tencent.andata.app.group;

import com.tencent.andata.etl.group.TicketGroupMessageSyncEtl;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.CheckpointingMode;

import java.util.Properties;

/**
 * 工单一事两群数据同步
 */
public class TicketGroupMessageSyncApplication {

    public static void main(String[] args) throws Exception {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        boolean refresh = parameterTool.getBoolean("refresh", false);
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        // Runner
        TicketGroupMessageSyncEtl.builder()
                .rainbowUtils(rainbowUtils)
                .groupDataCacheCnt(parameterTool.getInt("groupDataCacheCnt"))
                .env(parameterTool.get("env", "Test"))
                .refresh(refresh)
                .startTime(parameterTool.getLong("startTime", System.currentTimeMillis() / 1000L))
                .endTime(parameterTool.getLong("endTime", System.currentTimeMillis() / 1000L))
                .build()
                .run(flinkEnv);
        flinkEnv.env().enableCheckpointing(10 * 1000, CheckpointingMode.EXACTLY_ONCE);
        flinkEnv.env().disableOperatorChaining();
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("Ticket Group Message Sync" + (refresh ? "-refresh" : ""));
    }
}
