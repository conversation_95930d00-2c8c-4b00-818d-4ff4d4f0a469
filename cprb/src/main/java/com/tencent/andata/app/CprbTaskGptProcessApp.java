package com.tencent.andata.app;

import com.tencent.andata.etl.TaskDataGptETL;
import com.tencent.andata.util.FlinkTableConfBuilder;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.CheckpointingMode;

import java.util.concurrent.TimeUnit;

public class CprbTaskGptProcessApp {

    /***
     * main .
     * @param args .
     * @throws Exception .
     */
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        flinkEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));
        flinkEnv.env().getConfig().enableObjectReuse();
        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        // Runner
        TaskDataGptETL.builder()
                .gptProcParallelism(parameterTool.getInt("parallelism", 1))
                .sessionWindowGapMinutes(parameterTool.getInt("sessionWindowGapMinutes", 2))
                .maxExcelCellLen(parameterTool.getInt("maxExcelCellLen", 32767))
                .icebergDbName(parameterTool.get("icebergDbName", "andata_dev"))
                .isIncrease(parameterTool.getBoolean("isIncrease", true))
                .startSnapshot(parameterTool.getLong("startSnapshot", -1L))
                .build()
                .run(flinkEnv, catalog);
        // Sink StarRocks，CK可以短一点
        flinkEnv.env().enableCheckpointing(10 * 1000, CheckpointingMode.EXACTLY_ONCE);
        flinkEnv.env().execute("Cprb Task GPT Process Application");
    }

}
