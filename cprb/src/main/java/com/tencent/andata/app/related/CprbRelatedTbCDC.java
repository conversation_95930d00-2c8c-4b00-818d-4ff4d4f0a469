package com.tencent.andata.app.related;

import com.tencent.andata.etl.related.anstory.AnstoryCDCEtl;
import com.tencent.andata.etl.related.problem.ProblemCDCEtl;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.java.utils.ParameterTool;

import java.util.ArrayList;
import java.util.List;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

public class CprbRelatedTbCDC {
    public static void main(String[] args) throws Exception {

        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        boolean ck = parameterTool.getBoolean("ck");
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args, ck);

        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        List<Object> appList = new ArrayList<>();
        String appName = parameterTool.get("app");
        String startMode = parameterTool.get("startMode");
        // 设置flink应用程序名称
        if (appName.equals("anstory")) {
            appList.add(AnstoryCDCEtl.builder()
                    .setCap(parameterTool.getInt("cap"))
                    .setConnectTimeout(parameterTool.getInt("connectTimeout"))
                    .setSocketTimeout(parameterTool.getInt("socketTimeout"))
                    .setAsyncTimeOut(parameterTool.getInt("asyncTimeOut"))
                    .setMaxLength(parameterTool.getInt("maxLength"))
                    .setMaskColumns(parameterTool.get("maskColumns"))
                    .setEnv(parameterTool.get("env"))
                    .setStartUpMode(startMode)
                    .build());
        } else if (appName.equals("problem")) {
            appList.add(ProblemCDCEtl.builder()
                    .setCap(parameterTool.getInt("cap"))
                    .setConnectTimeout(parameterTool.getInt("connectTimeout"))
                    .setSocketTimeout(parameterTool.getInt("socketTimeout"))
                    .setAsyncTimeOut(parameterTool.getInt("asyncTimeOut"))
                    .setMaxLength(parameterTool.getInt("maxLength"))
                    .setMaskColumns(parameterTool.get("maskColumns"))
                    .setStartUpMode(startMode)
                    .build());
        }
        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));
        // execute the sql statements
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute(String.format("CPRB Related Tb %s CDC Application %s", appName, startMode));
    }
}
