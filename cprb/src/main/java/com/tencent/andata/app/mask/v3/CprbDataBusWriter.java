package com.tencent.andata.app.mask.v3;

import com.tencent.andata.common.conf.MqConf;
import com.tencent.andata.common.manage.KafkaManager;
import com.tencent.andata.schema.mask.Json2RowDeserialization;
import com.tencent.andata.schema.mask.convert.AdaptorFactory;
import com.tencent.andata.sink.DataBusSink;
import com.tencent.andata.struct.avro.message.Message;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableIdentifier;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.types.Row;

public class CprbDataBusWriter {
    /***
     * main .
     * @param args .
     * @throws Exception .
     */
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        RainbowUtils rainbowUtils = new RainbowUtils(PropertyUtils.loadProperties("env.properties"));
        String icebergDbName = parameterTool.get("icebergDbName");
        String env = parameterTool.get("env");

        // webim 脱敏数据写入数据总线
        cprbData2DataBus(flinkEnv, rainbowUtils,
                "cprb.mask.kafka.webimflowmask",
                "cprb-webim-databus-writer-group-" + env,
                icebergDbName, "dwd_im_online_customer_service_backend_mask_data")
                .name("cprb-webim-databus-writer-uid-" + env)
                .uid("cprb-webim-databus-writer-uid-" + env);
        // 工单脱敏写入数据总线
        cprbData2DataBus(flinkEnv, rainbowUtils,
                "cprb.mask.kafka.ticketflowmask",
                "cprb-ticket-databus-writer-group-" + env,
                icebergDbName, "dwd_incident_ticket_operation_mask")
                .name("cprb-ticket-databus-writer-uid-" + env)
                .uid("cprb-ticket-databus-writer-uid-" + env);
        // 群聊脱敏写入数据总线
        cprbData2DataBus(flinkEnv, rainbowUtils,
                "cprb.mask.kafka.groupmessagemask",
                "cprb-groupmessage-databus-writer-group-" + env,
                icebergDbName, "dwd_group_message_mask_data")
                .name("cprb-groupmessage-databus-writer-uid-" + env)
                .uid("cprb-groupmessage-databus-writer-uid-" + env);
        // 聚合数据写入数据总线
        cprbData2DataBus(flinkEnv, rainbowUtils,
                "cprb.mask.kafka.ticketgroup",
                "cprb-flow-agg-databus-writer-group-" + env,
                icebergDbName, "dws_ticket_flow")
                .name("cprb-flow-agg-databus-writer-uid-" + env)
                .uid("cprb-flow-agg-databus-writer-uid-" + env);
        flinkEnv.env().execute("CPRB Data 2 DataBus Writer Application " + env);
    }


    /***
     * cprbData2DataBus
     * @param flinkEnv .
     * @param rainbowUtils .
     * @param groupPath .
     * @param groupID .
     * @param icebergDbName .
     * @param tableName .
     * @return .
     * @throws TableNotExistException .
     * @throws NoSuchFieldException .
     * @throws InterruptedException .
     * @throws IllegalAccessException .
     */
    public static DataStreamSink<Message> cprbData2DataBus(
            FlinkEnvUtils.FlinkEnv flinkEnv,
            RainbowUtils rainbowUtils,
            String groupPath,
            String groupID,
            String icebergDbName,
            String tableName) throws TableNotExistException,
            NoSuchFieldException, InterruptedException, IllegalAccessException {

        TableIdentifier identifier = new TableIdentifier(DatabaseEnum.ICEBERG, icebergDbName, tableName);
        DataStream<Row> source = KafkaManager.<Row>builder()
                .setMqConf(MqConf.fromRainbow(rainbowUtils, groupPath, groupID))
                .setKafkaRecordDeserializationSchema(
                        new Json2RowDeserialization(AdaptorFactory.getConvertByName(""), identifier)
                )
                .build()
                .getDataStreamSource(flinkEnv.env());

        return new DataBusSink(MqConf.builder()
                .broker(rainbowUtils.getStringValue("mq.kafka.internal_data_bus", "ENTRYPOINT"))
                .topic(rainbowUtils.getStringValue("mq.kafka.internal_data_bus", "TOPICS"))
                .group(rainbowUtils.getStringValue("mq.kafka.internal_data_bus", "CONSUMER_GROUP"))
                .build(), identifier)
                .sink2MessageDataBus(source);
    }
}
