package com.tencent.andata.app.cluster;

import com.tencent.andata.etl.cluster.SummaryEmbeddingEtl;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.java.utils.ParameterTool;

import java.util.ArrayList;
import java.util.List;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

// SummaryEmbeddingApp.
public class SummaryEmbeddingApp {

    /***
     * main .
     * @param args .
     * @throws Exception .
     */
    public static void main(String[] args) throws Exception {
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        boolean ck = parameterTool.getBoolean("ck");
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args, ck);

        flinkEnv.env().getConfig().enableObjectReuse();
        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        String icebergDbName = parameterTool.get("icebergDbName");
        List<Object> appList = new ArrayList<>();
        String sourceName = parameterTool.get("sourceName");
        appList.add(SummaryEmbeddingEtl.builder()
                .icebergDbName(icebergDbName)
                .startTime(parameterTool.getLong("startTime", System.currentTimeMillis() / 1000L))
                .endTime(parameterTool.getLong("endTime", System.currentTimeMillis() / 1000L))
                .increaseTime(parameterTool.getLong("increaseTime", System.currentTimeMillis() / 1000L))
                .startingStrategy(parameterTool.get("startingStrategy", "INCREMENTAL_FROM_LATEST_SNAPSHOT"))
                .streaming(parameterTool.get("streaming", "true"))
                .source(parameterTool.get("source", ""))
                .retryTimes(parameterTool.getInt("retryTimes", 2))
                .sourceName(sourceName)
                .build());

        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));
        // execute the sql statements
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("CPRB Summary Embedding Application " + sourceName);
    }
}
