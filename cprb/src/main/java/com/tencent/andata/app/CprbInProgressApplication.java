package com.tencent.andata.app;

import com.tencent.andata.etl.CprbInProgressEtl;
import com.tencent.andata.etl.TaskDataGptETL;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.CheckpointingMode;

import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * 共性事中处理逻辑 初筛&GPT
 */
public class CprbInProgressApplication {

    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        flinkEnv.env().getConfig().enableObjectReuse();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        // Runner
        CprbInProgressEtl.builder()
                .parallelism(parameterTool.getInt("parallelism", 1))
                .icebergDb(parameterTool.get("icebergDbName", "andata_dev"))
                .rainbowUtils(rainbowUtils)
                .build()
                .run(flinkEnv, catalog);
        // Sink StarRocks，CK可以短一点
        flinkEnv.env().enableCheckpointing(10 * 1000, CheckpointingMode.EXACTLY_ONCE);
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("Cprb Task GPT Process Application");
    }
}
