package com.tencent.andata.app.first_filter;

import com.tencent.andata.etl.CprbTaskTagFilterEtl;
import com.tencent.andata.util.FlinkTableConfBuilder;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

public class CprbTaskTagFilterApplication {
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        flinkEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));

        flinkEnv.env().getConfig().enableObjectReuse();
        final IcebergCatalogReader catalog = new IcebergCatalogReader();
        // table env config
        Configuration configuration = new FlinkTableConfBuilder()
                .setEnv(flinkEnv.streamTEnv())
                .setParams("table.exec.state.ttl", "86400000")
                .build();

        String icebergDbName = parameterTool.get("icebergDbName");
        // instantiate ETL
        List<Object> appList = new ArrayList<>();
        // 设置flink应用程序名称
        configuration.setString("pipeline.name", "Tag First Filter Application");
        appList.add(CprbTaskTagFilterEtl
                .builder()
                .delayTime(parameterTool.getInt("delayTime", 60))
                .partitionKey(parameterTool.get("partitionKey", "Instant_Month"))
                .icebergDbName(icebergDbName)
                .build());

        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));
        // execute the sql statements
        flinkEnv.env().execute("Tag First Filter Application");
    }
}
