package com.tencent.andata.constant;

public class TicketFlow {

    public static final String CREATE_FIELDS = ""
            + "    id STRING,\n"
            + "    ticket_id bigint,\n"
            + "    reply STRING,\n"
            + "    c_reply STRING,\n"
            + "    msg STRING,\n"
            + "    source STRING,\n"
            + "    url STRING,\n"
            + "    question STRING,\n"
            + "    service_scene bigint,\n"
            + "    ticket_create_time timestamp,\n"
            + "    update_time timestamp,\n"
            + "    service_scene_level1_id bigint,\n"
            + "    service_scene_level2_id bigint,\n"
            + "    service_scene_level3_id bigint,\n"
            + "    service_scene_level4_id bigint,\n"
            + "    close_time timestamp"
            + "";

    public static final String FIELDS = ""
            + "  id,\n"
            + "  ticket_id,\n"
            + "  reply,\n"
            + "  c_reply,\n"
            + "  msg,\n"
            + "  source,\n"
            + "  url,\n"
            + "  question,\n"
            + "  service_scene,\n"
            + "  ticket_create_time,\n"
            + "  update_time,\n"
            + "  service_scene_level1_id,\n"
            + "  service_scene_level2_id,\n"
            + "  service_scene_level3_id,\n"
            + "  service_scene_level4_id,\n"
            + "  close_time\n"
            + "";

}
