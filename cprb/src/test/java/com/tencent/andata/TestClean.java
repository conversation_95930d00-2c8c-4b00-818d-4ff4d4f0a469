package com.tencent.andata;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TestClean {
    public static void main(String[] args) {
        String pattern = "https?://\\S+";
        System.out.println(reStr(pattern, "dasfhttps://www.baidu.com]f h"));
    }

    public static String reStr(String pattern, String text) {
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(text);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(result, "");
        }
        matcher.appendTail(result);
        text = result.toString();
        return text;
    }
}
