package com.tencent.andata;

import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.util.HashMap;
import java.util.Iterator;

public class SocketTest2 {

    /***
     * main
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 模拟工单流
        DataStreamSource<String> source1 = env.socketTextStream("localhost", 8888, "\n");
        SingleOutputStreamOperator<HashMap<String, String>> ticket =
                source1.map(new MapFunction<String, HashMap<String, String>>() {
                    @Override
                    public HashMap<String, String> map(String s) throws Exception {
                        String[] split = s.split(";");
                        HashMap<String, String> map = new HashMap<>();
                        for (String string : split) {
                            String[] split1 = string.split(":");
                            map.put(split1[0], split1[1]);
                        }
                        return map;
                    }
                });

        // 模拟任务流
        DataStreamSource<String> source2 = env.socketTextStream("localhost", 9999, "\n");
        SingleOutputStreamOperator<HashMap<String, String>> task =
                source2.map(new MapFunction<String, HashMap<String, String>>() {
                    @Override
                    public HashMap<String, String> map(String s) throws Exception {
                        String[] split = s.split(";");
                        HashMap<String, String> map = new HashMap<>();
                        for (String string : split) {
                            String[] split1 = string.split(":");
                            map.put(split1[0], split1[1]);
                        }
                        return map;
                    }
                });


        task.union(ticket)
                .keyBy(new KeySelector<HashMap<String, String>, String>() {
                    @Override
                    public String getKey(HashMap<String, String> s) throws Exception {
                        return s.get("source");
                    }
                })
                .process(new KeyedProcessFunction<String, HashMap<String, String>, HashMap<String, String>>() {

                    private ListState<HashMap<String, String>> flowState;

                    @Override
                    public void open(Configuration parameters) throws Exception {
                        ListStateDescriptor<HashMap<String, String>> descriptor = new ListStateDescriptor<>(
                                "flowState", TypeInformation.of(new TypeHint<HashMap<String, String>>() {
                        }));
                        // 保存所有流水状态
                        flowState = getRuntimeContext().getListState(descriptor);
                    }

                    @Override
                    public void processElement(HashMap<String, String> value, KeyedProcessFunction<String, HashMap<String, String>, HashMap<String, String>>.Context context, Collector<HashMap<String, String>> collector) throws Exception {
                        if (value.containsKey("taskId")) {
                            Iterator<HashMap<String, String>> iterator = flowState.get().iterator();
                            while (iterator.hasNext()) {
                                HashMap<String, String> next = iterator.next();
                                if (next.get("source").equals(value.get("source"))) {
                                    value.put("ticket", next.get("ticketId"));
                                }
                                collector.collect(value);
                            }
                        } else {
                            System.out.println("存储状态:"+value);
                            flowState.add(value);
                        }
                    }
                })
                .setParallelism(processTaskNum)
                .print();

        env.setMaxParallelism(maxParallelism);
        env.setParallelism(processTaskNum);
        env.execute("socket test");
    }

    static int processTaskNum = 3;
    static int maxParallelism = 1024;
}
