package com.tencent.andata.filter.promt_group;

import org.apache.flink.types.Row;
import static org.junit.Assert.*;
import org.junit.Test;

import java.util.HashMap;

public class TicketServiceSceneFilterTest {
    public static Row row;

    static {
        row = Row
                .withNames();
        row.setField("service_scene_level1_id", 123L);
        row.setField("service_scene_level2_id", 456L);
        row.setField("service_scene_level3_id", 111L);
        row.setField("service_scene_level4_id", 222L);
    }

    @Test
    public void test() {
        final TicketServiceSceneFilter ticketServiceSceneFilter = new TicketServiceSceneFilter();
        ticketServiceSceneFilter.addServiceScene(
                new HashMap<String, Integer>() {{
                    put("sc1_id", 123);
                    put("sc2_id", 456);
                    put("sc3_id", 111);
                    put("sc4_id", 222);
                }}
        );
        ticketServiceSceneFilter.addServiceScene(
                new HashMap<String, Integer>() {{
                    put("sc1_id", 111);
                    put("sc2_id", 111);
                    put("sc3_id", 111);
                    put("sc4_id", 111);
                }}
        );
        assertTrue(ticketServiceSceneFilter.filter(row));
        ticketServiceSceneFilter.clear();
        assertTrue(ticketServiceSceneFilter.filter(row));
    }

    @Test
    public void test2() {
        final TicketServiceSceneFilter ticketServiceSceneFilter = new TicketServiceSceneFilter(
        );
        ticketServiceSceneFilter.addServiceScene(
                new HashMap<String, Integer>() {{
                    put("sc1_id", 123);
                    put("sc2_id", 456);
                    put("sc3_id", 222);
                    put("sc4_id", 111);
                }}
        );
        ticketServiceSceneFilter.addServiceScene(
                new HashMap<String, Integer>() {{
                    put("sc1_id", 123);
                    put("sc2_id", 456);
                    put("sc3_id", 111);
                    put("sc4_id", 2);
                }}
        );
        assertFalse(ticketServiceSceneFilter.filter(row));
    }

    @Test
    public void test3() {
        final TicketServiceSceneFilter ticketServiceSceneFilter = new TicketServiceSceneFilter(
        );
        ticketServiceSceneFilter.addServiceScene(
                new HashMap<String, Integer>() {{
                    put("sc1_id", 123);
                    put("sc2_id", 456);
                    put("sc3_id", 111);
                    put("sc4_id", 222);
                }}
        );
        final Row copy = Row.copy(row);
        copy.setField("service_scene_level2_id", null);
        assertFalse(ticketServiceSceneFilter.filter(copy));
    }

    @Test
    public void test4() {
        final TicketServiceSceneFilter ticketServiceSceneFilter = new TicketServiceSceneFilter(
        );
        ticketServiceSceneFilter.addServiceScene(
                new HashMap<String, Integer>() {{
                    put("sc1_id", 123);
                    put("sc2_id", 456);
                    put("sc3_id", 111);
                    put("sc4_id", null);
                }}
        );

        assertTrue(ticketServiceSceneFilter.filter(row));
    }

    @Test
    public void test5() {
        final TicketServiceSceneFilter ticketServiceSceneFilter = new TicketServiceSceneFilter(
        );
        ticketServiceSceneFilter.addServiceScene(
                new HashMap<String, Integer>() {{
                    put("sc1_id", null);
                    put("sc2_id", null);
                    put("sc3_id", null);
                    put("sc4_id", null);
                }}
        );
        assertTrue(ticketServiceSceneFilter.filter(row));
    }
}
