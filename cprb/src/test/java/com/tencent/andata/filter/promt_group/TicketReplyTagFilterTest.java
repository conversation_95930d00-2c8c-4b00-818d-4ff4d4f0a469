package com.tencent.andata.filter.promt_group;

import com.tencent.andata.filter.TicketReplyTagFilter;
import org.apache.flink.types.Row;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.ArrayList;

public class TicketReplyTagFilterTest {
    public static Row row;
    public static TicketReplyTagFilter filter;

    static {
        row = Row
                .withNames();
        row.setField("service_scene_level1_id", 19408L);
        row.setField("service_scene_level2_id", 11597L);
        row.setField("service_scene_level3_id", 25842L);
        row.setField("service_scene_level4_id", 25843L);
        filter = new TicketReplyTagFilter();
    }

    /**
     * 测试命中关键词
     *
     * @throws Exception
     */
    @Test
    public void test() throws Exception {
        row.setField("forward_filter", "(?si)进度|7~15天|7-15天|15个|退款到账");
        row.setField("reverse_filter", "");
        row.setField("ticket_service_scene_filter", "[{\"sc1_id\": 19408}]");
        // 测试命中正向匹配
        assertTrue(filter.filter(row));
    }

    @Test
    public void test2() throws Exception {
        row.setField("forward_filter", "");
        row.setField("reverse_filter", "(?si)退款诉求|公对公转账");
        row.setField("ticket_service_scene_filter", "[{\"sc1_id\": 19408}]");
        // 测试命中反向匹配
        assertFalse(filter.filter(row));
    }

    @Test
    public void test22() throws Exception {
        row.setField("forward_filter", "");
        row.setField("reverse_filter", "");
        row.setField("ticket_service_scene_filter", "");
        assertTrue(filter.filter(row));

        row.setField("ticket_service_scene_filter", null);
        assertTrue(filter.filter(row));
    }


    /**
     * 测试命中归档
     *
     * @throws Exception
     */
    @Test
    public void test3() throws Exception {
        row.setField("ticket_service_scene_filter", "[{\"sc1_id\": 12644, \"sc2_id\": 12645}]");
        assertFalse(filter.filter(row));
    }

    @Test
    public void test4() throws Exception {
        row.setField("ticket_service_scene_filter", "[{\"sc1_id\": 19408, \"sc2_id\": 12645}]");
        assertFalse(filter.filter(row));
    }

    @Test
    public void test5() throws Exception {
        row.setField("forward_filter", "");
        row.setField("reverse_filter", "");
        row.setField("ticket_service_scene_filter",
                "[{\"sc1_id\": 19408, \"sc2_id\": 11597},{\"sc1_id\": 1111, \"sc2_id\": 2222}]");
        assertTrue(filter.filter(row));
    }


    /**
     * 测试多条数据匹配
     *
     * @throws Exception
     */
    @Test
    public void test7() throws Exception {
        row.setField("forward_filter", "(?si)有oob");
        row.setField("reverse_filter", "");
        row.setField("ticket_service_scene_filter", "[{\"sc1_id\": 19408}]");
        assertTrue(filter.filter(row));
        row.setField("forward_filter", "(?si)阿萨德离开家啊v那就");
        row.setField("reverse_filter", "(?si)阿里云");
        assertFalse(filter.filter(row));
    }

}
