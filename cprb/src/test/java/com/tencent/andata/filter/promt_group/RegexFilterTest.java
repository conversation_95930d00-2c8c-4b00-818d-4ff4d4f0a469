package com.tencent.andata.filter.promt_group;

import org.apache.flink.types.Row;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import org.junit.Test;

public class RegexFilterTest {
    public static Row row;
    public static RegexFilter regexFilter;
    public static RegexFilter reverseRegexFilter;


    static {
        row = Row
                .withNames();
        row.setField("reply", "我在测试");
        row.setField("forward_filter", "(?si)我在|吃饭");
        row.setField("reverse_filter", "(?si)我不在|你好");
        regexFilter = new RegexFilter(true);
        reverseRegexFilter = new RegexFilter(false);
    }

    @Test
    public void test() {
        // 测试正向匹配
        row.setField("reply", "我在测试");
        regexFilter.initFromRow(row);
        assertTrue(regexFilter.filter(row));

        row.setField("reply", "吃饭没有");
        assertTrue(regexFilter.filter(row));
    }

    @Test
    public void test1() {
        // 测试正向匹配
        row.setField("reply", "你好呀");
        row.setField("forward_filter", "(?si)我在|吃饭");
        regexFilter.initFromRow(row);
        assertFalse(regexFilter.filter(row));
    }

    @Test
    public void test11() {
        // 测试正向匹配
        row.setField("forward_filter", "");
        regexFilter.initFromRow(row);
        assertTrue(regexFilter.filter(row));

        row.setField("forward_filter", null);
        regexFilter.initFromRow(row);
        assertTrue(regexFilter.filter(row));
    }

    @Test
    public void test2() {
        // 测试反向匹配
        row.setField("reverse_filter", "(?si)我不在|你好");
        row.setField("reply", "sss");
        reverseRegexFilter.initFromRow(row);
        assertTrue(reverseRegexFilter.filter(row));

        row.setField("reply", "我不在家里");
        assertFalse(reverseRegexFilter.filter(row));

        row.setField("reply", "你好不好");
        assertFalse(reverseRegexFilter.filter(row));
    }

    @Test
    public void test21() {
        // 测试反向匹配
        row.setField("reply", "sss");
        row.setField("reverse_filter", "");
        reverseRegexFilter.initFromRow(row);
        assertTrue(reverseRegexFilter.filter(row));

        row.setField("reverse_filter", null);
        reverseRegexFilter.initFromRow(row);
        assertTrue(reverseRegexFilter.filter(row));

    }
}
