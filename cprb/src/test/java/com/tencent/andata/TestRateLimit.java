package com.tencent.andata;

import com.tencent.andata.map.RateLimitProcess;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;

public class TestRateLimit {

    /***
     * main .
     * @param args .
     * @throws Exception .
     */
    public static void main(String[] args) throws Exception {
        Row row = new Row(RowKind.INSERT, 2);
        row.setField(0, "syq");
        row.setField(1, 1);

        Row row1 = new Row(RowKind.INSERT, 2);
        row1.setField(0, "syq");
        row1.setField(1, 3);

        Row row2 = new Row(RowKind.INSERT, 2);
        row2.setField(0, "syq");
        row2.setField(1, 2);

        Row row3 = new Row(RowKind.INSERT, 2);
        row3.setField(0, "syq");
        row3.setField(1, 2);

        Row row4 = new Row(RowKind.INSERT, 2);
        row4.setField(0, "syq");
        row4.setField(1, 2);

        Row row5 = new Row(RowKind.INSERT, 2);
        row5.setField(0, "syq");
        row5.setField(1, 2);

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        DataStreamSource<Row> source = env.fromElements(row,row1,row2,row3,row4,row5);
        source.process(new RateLimitProcess(2, 3000)).setParallelism(1).print();
        env.execute("test rate limit");
    }
}
