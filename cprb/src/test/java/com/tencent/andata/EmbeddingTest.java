package com.tencent.andata;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.util.PromptUtils;
import com.tencent.andata.util.SentenceCleanUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;

public class EmbeddingTest {
    public static void main(String[] args) throws Exception {
//        List<Double> test = EmbeddingUtils.embedding("test");
//        System.out.println(test);
        ArrayList<Long> list = new ArrayList<Long>() {{
            add(12312L);
            add(323L);
            add(2L);
        }};
        ObjectMapper mapper = new ObjectMapper();
        String s = mapper.writeValueAsString(list);
        System.out.println(s);
        ArrayList<Long> longs = mapper.readValue(s, new TypeReference<ArrayList<Long>>() {
        });
        System.out.println(longs);
    }
}
