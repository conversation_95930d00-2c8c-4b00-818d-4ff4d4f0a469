package com.tencent.andata;

import com.tencent.andata.util.firstfilter.TaskSplitUtil;
import com.tencent.andata.utils.TimeUtil;

import java.time.*;
import java.util.ArrayList;

public class TimeTest {

    public static void main(String[] args) {
        System.out.println(Instant.now());
        System.out.println("local date test ====");
        Instant instant = Instant.parse("2023-12-10T16:00:00Z");
        LocalDate localDate = TimeUtil.convert2LocalDate(instant);
        System.out.println(localDate);
        System.out.println(TimeUtil.convert2Instant(localDate));
        System.out.println("local date test ====");

        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of("Asia/Shanghai"));
        System.out.println(zonedDateTime);
        System.out.println("local date time test ====");
        LocalDateTime localDateTime = TimeUtil.convert2LocalDateTime(instant);
        System.out.println(localDateTime);
        System.out.println(TimeUtil.convert2Instant(localDateTime));
        System.out.println("local date time test ====");
        System.out.println("test between ===");
        testBetween(TimeUtil.convert2LocalDateTime(Instant.parse("2023-11-30T16:00:00Z")),
                TimeUtil.convert2LocalDateTime(Instant.parse("2024-02-10T16:00:00Z")));
        testSplitByDay(TimeUtil.convert2LocalDateTime(Instant.parse("2023-07-31T16:00:00Z")),
                TimeUtil.convert2LocalDateTime(Instant.parse("2024-04-15T16:00:00Z")));
    }

    public static void testSplitByDay(LocalDateTime localDateTime1, LocalDateTime localDateTime2) {
        ArrayList<Instant> list = new ArrayList<>();
        Instant instantStart = TimeUtil.convert2Instant(localDateTime1);
        Instant instantEnd = TimeUtil.convert2Instant(localDateTime2);
        LocalDate startDateTime = TimeUtil.convert2LocalDate(instantStart);
        LocalDate endDateTime = TimeUtil.convert2LocalDate(instantEnd);
        while (!startDateTime.isAfter(endDateTime)) {
            Instant instant = TimeUtil.convert2Instant(startDateTime);
            list.add(instant);
            startDateTime = startDateTime.plusDays(1);
        }
        int count = 0;
        for (Instant instant : list) {
            count++;
            System.out.println(instant);
        }
        System.out.println(count);
    }

    public static void testBetween(LocalDateTime localDateTime1, LocalDateTime localDateTime2) {
        Instant instantStart = TimeUtil.convert2Instant(localDateTime1);
        Instant instantEnd = TimeUtil.convert2Instant(localDateTime2);
        ZonedDateTime startDateTime = TimeUtil.convert2ZonedDateTime(instantStart);
        ZonedDateTime endDateTime = TimeUtil.convert2ZonedDateTime(instantEnd);
        // 计算两个日期之间的月数差异
        int monthsBetween = Period.between(startDateTime.toLocalDate(), endDateTime.toLocalDate()).getMonths();
        // 打印出这段时间内的每个月份
        YearMonth startMonth = YearMonth.from(startDateTime);
        YearMonth endMonth = YearMonth.from(endDateTime);
        while (!startMonth.isAfter(endMonth)) {
            // 将YearMonth转换为当月的第一天
            LocalDate firstDayOfMonth = startMonth.atDay(1);
            // 将LocalDate转换为当天开始时的Instant（通常是午夜）
            Instant instant = TimeUtil.convert2Instant(firstDayOfMonth);
            System.out.println(instant);
            startMonth = startMonth.plusMonths(1);
        }
    }
}
