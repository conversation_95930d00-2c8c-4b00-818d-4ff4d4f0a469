package com.tencent.andata.process;

import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.runtime.state.KeyGroupRange;
import org.apache.flink.runtime.state.KeyGroupRangeAssignment;
import org.apache.flink.runtime.state.KeyedStateFunction;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.MathUtils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class KeyedProcessSyq extends KeyedBroadcastProcessFunction<String, HashMap<String, String>, HashMap<String, String>, HashMap<String, String>> {

    MapStateDescriptor<String, HashMap<String, String>> taskIdBroadCastDescriptor;

    public KeyedProcessSyq(MapStateDescriptor<String, HashMap<String, String>> taskIdBroadCastDescriptor) {
        this.taskIdBroadCastDescriptor = taskIdBroadCastDescriptor;
    }

    @Override
    public void processElement(HashMap<String, String> stringStringHashMap, KeyedBroadcastProcessFunction<String, HashMap<String, String>, HashMap<String, String>, HashMap<String, String>>.ReadOnlyContext readOnlyContext, Collector<HashMap<String, String>> collector) throws Exception {
        Iterator<Map.Entry<String, HashMap<String, String>>> iterator = readOnlyContext.getBroadcastState(taskIdBroadCastDescriptor).immutableEntries().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, HashMap<String, String>> next = iterator.next();
            String key = next.getKey();
            HashMap<String, String> value = next.getValue();
            System.out.println(String.format("key : %s, vale: %s", key ,value));
        }
        // 这里应该是每个subtask收到一个source的数据
        int indexOfThisSubtask = getRuntimeContext().getIndexOfThisSubtask();
        int numberOfParallelSubtasks = getRuntimeContext().getNumberOfParallelSubtasks();
        int maxTasks = getRuntimeContext().getMaxNumberOfParallelSubtasks();
        int totalParallelism = getRuntimeContext().getNumberOfParallelSubtasks();
        KeyGroupRange keyGroupRange = KeyGroupRangeAssignment.computeKeyGroupRangeForOperatorIndex(
                maxTasks, totalParallelism, indexOfThisSubtask);
        System.out.println(String.format("subtask: %d, numberOfParallelSubtasks: %d, max: %d, key_group_start:%d, end:%d",
                indexOfThisSubtask,
                numberOfParallelSubtasks,
                maxTasks,
                keyGroupRange.getStartKeyGroup(),
                keyGroupRange.getEndKeyGroup()));
        System.out.println(String.format("key: %s, 收到task数据: %s", readOnlyContext.getCurrentKey(), stringStringHashMap.get("source")));
    }

    @Override
    public void processBroadcastElement(HashMap<String, String> stringStringHashMap, KeyedBroadcastProcessFunction<String, HashMap<String, String>, HashMap<String, String>, HashMap<String, String>>.Context context, Collector<HashMap<String, String>> collector) throws Exception {

        // 这里是收到的广播的数据
        int indexOfThisSubtask = getRuntimeContext().getIndexOfThisSubtask();
        int numberOfParallelSubtasks = getRuntimeContext().getNumberOfParallelSubtasks();
        int maxTasks = getRuntimeContext().getMaxNumberOfParallelSubtasks();
        int totalParallelism = getRuntimeContext().getNumberOfParallelSubtasks();
        int i = MathUtils.murmurHash(stringStringHashMap.get("source").hashCode()) % maxTasks;
        KeyGroupRange keyGroupRange = KeyGroupRangeAssignment.computeKeyGroupRangeForOperatorIndex(
                maxTasks, totalParallelism, indexOfThisSubtask);
        if (keyGroupRange.getStartKeyGroup() <= i && i <= keyGroupRange.getEndKeyGroup()) {
            System.out.println(String.format("subtask: %d , 加入广播状态：%d", indexOfThisSubtask, i));
            context.getBroadcastState(taskIdBroadCastDescriptor).put(stringStringHashMap.get("source"), stringStringHashMap);
        }
        System.out.println(String.format("广播subtask: %d, numberOfParallelSubtasks: %d, max: %d, key_group_start:%d, end:%d",
                indexOfThisSubtask,
                numberOfParallelSubtasks,
                maxTasks,
                keyGroupRange.getStartKeyGroup(),
                keyGroupRange.getEndKeyGroup()));
        System.out.println(String.format("收到广播数据: %s", stringStringHashMap.get("source")));
    }
}
