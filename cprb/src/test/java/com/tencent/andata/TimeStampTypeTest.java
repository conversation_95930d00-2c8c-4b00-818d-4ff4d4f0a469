package com.tencent.andata;

import com.ververica.cdc.connectors.postgres.PostgreSQLSource;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

public class TimeStampTypeTest {

    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tEnv = StreamTableEnvironment.create(env);

        String dataGenSource = "CREATE TABLE tTable (\n"
                + "    id bigint,\n"
                + "    end_time timestamp,\n"
                + "    start_time timestamp\n"
                + ") WITH  (" +
                " 'connector' = 'postgres-cdc'," +
                " 'hostname' = '127.0.0.1'," +
                " 'port' = '5432'," +
                " 'schema-name' = 'public'," +
                " 'slot.name' = 'my_rep_slot_2'," +
                " 'username' = 'postgres'," +
                " 'password' = 'password'," +
                " 'decoding.plugin.name' = 'pgoutput'," +
                " 'database-name' = 'testdb'," +
                " 'table-name' = 'test'" + // 不指定表名，会把库中所有表同步过来
                ")";
        tEnv.executeSql(dataGenSource);

        tEnv.toChangelogStream(tEnv.from("tTable"))
                .map(new MapFunction<Row, Row>() {
                    @Override
                    public Row map(Row row) throws Exception {
                        LocalDateTime localDateTime =  (LocalDateTime) row.getField("start_time");

                        System.out.println(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                                .withZone(ZoneId.of("Asia/Shanghai"))
                                .format((localDateTime.toInstant(ZoneOffset.of("+8")))));
//                        Instant instant1 =  (Instant) row.getField("end_time");
//                        System.out.println(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
//                                .withZone(ZoneId.of("Asia/Shanghai"))
//                                .format((instant1)));
                        System.out.println(row);
                        return row;
                    }
                });


        env.execute("test");
    }
}
