package com.tencent.andata;

import com.tencent.andata.process.KeyedProcessSyq;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.runtime.plugable.SerializationDelegate;
import org.apache.flink.runtime.state.KeyGroupRange;
import org.apache.flink.runtime.state.KeyGroupRangeAssignment;
import org.apache.flink.streaming.api.datastream.*;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.co.KeyedBroadcastProcessFunction;
import org.apache.flink.streaming.api.transformations.PartitionTransformation;
import org.apache.flink.streaming.runtime.partitioner.KeyGroupStreamPartitioner;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.table.data.RowData;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.MathUtils;
import org.apache.flink.util.Preconditions;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.stream.Stream;

public class SocketTest {

    public static class CustomKeyGroupStreamPartitioner<T, K> extends KeyGroupStreamPartitioner<T, K> {

        private final KeySelector<T, K> keySelector;
        private int processTaskNum;

        public CustomKeyGroupStreamPartitioner(KeySelector<T, K> keySelector, int maxParallelism) {
            super(keySelector, maxParallelism);
            this.keySelector = (KeySelector) Preconditions.checkNotNull(keySelector);
        }

        public CustomKeyGroupStreamPartitioner(KeySelector<T, K> keySelector, int maxParallelism, int processTaskNum) {
            super(keySelector, maxParallelism);
            this.keySelector = (KeySelector) Preconditions.checkNotNull(keySelector);
            this.processTaskNum = processTaskNum;
        }


        public void setProcessTaskNum(int processTaskNum) {
            this.processTaskNum = processTaskNum;
        }

        @Override
        public int selectChannel(SerializationDelegate<StreamRecord<T>> record) {
            Object key;
            try {
                key = this.keySelector.getKey((T) ((StreamRecord) record.getInstance()).getValue());
            } catch (Exception var4) {
                throw new RuntimeException("Could not extract key from " + ((StreamRecord) record.getInstance()).getValue(), var4);
            }
            // 通过 key groupId 算出发往下游的subtaskID
            return KeyGroupRangeAssignment
                    .computeOperatorIndexForKeyGroup(
                            this.getMaxParallelism(),
                            this.processTaskNum,
                            (int) key
                    );
        }
    }


    /***
     * main
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 模拟工单流
        DataStreamSource<String> source1 = env.socketTextStream("localhost", 8888, "\n");
        SingleOutputStreamOperator<HashMap<String, String>> ticket =
                source1.map(new MapFunction<String, HashMap<String, String>>() {
                    @Override
                    public HashMap<String, String> map(String s) throws Exception {
                        String[] split = s.split(";");
                        HashMap<String, String> map = new HashMap<>();
                        for (String string : split) {
                            String[] split1 = string.split(":");
                            map.put(split1[0], split1[1]);
                        }
                        return map;
                    }
                });

        // 模拟任务流
        DataStreamSource<String> source2 = env.socketTextStream("localhost", 9999, "\n");
        SingleOutputStreamOperator<HashMap<String, String>> task =
                source2.map(new MapFunction<String, HashMap<String, String>>() {
                    @Override
                    public HashMap<String, String> map(String s) throws Exception {
                        String[] split = s.split(";");
                        HashMap<String, String> map = new HashMap<>();
                        for (String string : split) {
                            String[] split1 = string.split(":");
                            map.put(split1[0], split1[1]);
                        }
                        return map;
                    }
                });


        // 工单广播状态
        MapStateDescriptor<String, HashMap<String, String>> taskIdBroadCastDescriptor = new MapStateDescriptor<String, HashMap<String, String>>(
                "tickets",
                BasicTypeInfo.STRING_TYPE_INFO,
                TypeInformation.of(new TypeHint<HashMap<String, String>>() {
                }));
        BroadcastStream<HashMap<String, String>> broadcast = ticket.broadcast(taskIdBroadCastDescriptor);
        // keyby
        KeySelector<HashMap<String, String>, Integer> keySelector = new KeySelector<HashMap<String, String>, Integer>() {
            @Override
            public Integer getKey(HashMap<String, String> stringStringHashMap) throws Exception {
                return MathUtils.murmurHash(stringStringHashMap.get("source").hashCode()) % maxParallelism;
            }
        };
        KeyedStream<HashMap<String, String>, Integer> keyedStream = task.keyBy(keySelector);
        CustomKeyGroupStreamPartitioner<HashMap<String, String>, Integer> partitioner = new CustomKeyGroupStreamPartitioner<HashMap<String, String>, Integer>(
                keySelector, maxParallelism, processTaskNum);
        Field transField = DataStream.class.getDeclaredField("transformation");
        transField.setAccessible(true);
        transField.set(
                keyedStream, new PartitionTransformation<>(
                        keyedStream.getTransformation(), partitioner
                )
        );

        keyedStream
                .connect(broadcast)
                .process(new KeyedProcessSyq(taskIdBroadCastDescriptor))
                .setParallelism(processTaskNum)
                .print();

        env.setMaxParallelism(maxParallelism);
        env.setParallelism(processTaskNum);
        env.execute("socket test");
    }

    static int processTaskNum = 3;
    static int maxParallelism = 1024;
}
