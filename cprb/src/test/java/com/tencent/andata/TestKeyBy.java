package com.tencent.andata;

import com.tencent.andata.util.keyby.CombineKeyByUtil;
import com.tencent.andata.util.keyby.KeyByField;
import com.tencent.andata.util.keyby.KeyByType;
import org.apache.flink.types.Row;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TestKeyBy {
    public static void main(String[] args) {

        Instant instantStart = toInstant("2023-09-29 00:00:00");
        Instant instantEnd = toInstant("2023-09-30 00:00:00");

        // 将Instant转换为ZonedDateTime
        ZonedDateTime startDateTime = instantStart.atZone(ZoneId.of("Asia/Shanghai"));
        ZonedDateTime endDateTime = instantEnd.atZone(ZoneId.of("Asia/Shanghai"));
        // 计算两个日期之间的月数差异
        int monthsBetween = Period.between(startDateTime.toLocalDate(), endDateTime.toLocalDate()).getMonths();
        System.out.println("Months between: " + monthsBetween);
        // 打印出这段时间内的每个月份
        YearMonth startMonth = YearMonth.from(startDateTime);
        YearMonth endMonth = YearMonth.from(endDateTime);
        while (!startMonth.isAfter(endMonth)) {
            // 将YearMonth转换为当月的第一天
            LocalDate firstDayOfMonth = startMonth.atDay(1);
            // 将LocalDate转换为当天开始时的Instant（通常是午夜）
            Instant instant = firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant();
            System.out.println(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                    .withZone(ZoneId.of("Asia/Shanghai"))
                    .format((instant)));
            startMonth = startMonth.plusMonths(1);
        }


        Row row = Row.withNames();
        row.setField("source", "webim");
        row.setField("time", instantEnd);

        CombineKeyByUtil combineKeyByUtil = CombineKeyByUtil.builder()
                .separator("-")
                .fieldList(Stream.of(
                        KeyByField.builder().fieldName("source").fieldType(new KeyByType(KeyByType.Type.Str)).build(),
                        KeyByField.builder().fieldName("time").fieldType(new KeyByType(KeyByType.Type.Instant_Month)).build()
                ).collect(Collectors.toList()))
                .build();
        System.out.println(combineKeyByUtil.getKey(row));
    }

    public static Instant toInstant(String t) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //将 string 装换成带有 T 的国际时间，但是没有进行，时区的转换，即没有将时间转为国际时间，只是格式转为国际时间
        LocalDateTime parse = LocalDateTime.parse(t, dateTimeFormatter);
        //+8 小时，offset 可以理解为时间偏移量
        ZoneOffset offset = OffsetDateTime.now().getOffset();
        //转换为 通过时间偏移量将 string -8小时 变为 国际时间，因为亚洲上海是8时区
        Instant instant = parse.toInstant(offset);
        return instant;
    }
}
