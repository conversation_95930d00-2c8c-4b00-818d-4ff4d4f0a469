<?xml version="1.0" encoding="utf-8"?>

<configuration>
    <property>
        <name>hive.metastore.execute.setugi</name>
        <value>false</value>
    </property>
    <property>
        <name>hive.metastore.uris</name>
        <value>thrift://ss-qe-nginx-tauth.tencent-distribute.com:8106</value>
        <description>IP address (or fully-qualified domain name) and port of the metastore host</description>
    </property>
    <property>
        <name>hive.metastore.client.connect.retry.delay</name>
        <value>1</value>
    </property>
    <property>
        <name>hive.security.authorization.enabled</name>
        <value>false</value>
    </property>
    <property>
        <name>hive.metastore.schema.verification</name>
        <value>true</value>
    </property>
    <property>
        <name>hive.metastore.client.socket.timeout</name>
        <value>1800</value>
    </property>
</configuration>
