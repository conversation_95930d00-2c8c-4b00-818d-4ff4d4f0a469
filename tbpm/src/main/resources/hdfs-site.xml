<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>

<configuration>

    <property>
        <name>fs.hdfs.impl</name>
        <value>org.apache.hadoop.hdfs.DistributedFileSystem</value>
    </property>
    <property>
        <name>fs.AbstractFileSystem.hdfs.impl</name>
        <value>org.apache.hadoop.fs.Hdfs</value>
        <description>The FileSystem for hdfs: uris.</description>
    </property>
    <property>
        <name>hadoop.security.group.mapping</name>
        <value>org.apache.hadoop.security.TqRestGroupMappingServiceProvider</value>
    </property>

    <property>
        <name>distributed.config.enable</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.cdh3.namenode.list</name>
        <value>
            tl-if-nn-tdw,tl-if-nn-tdw.tencent-distribute.com,tl-nn-tdw.tencent-distribute.com,tl-nn-tdw
        </value>
    </property>

    <property>
        <name>dfs.namenode.ha.zookeeper.address.tl-ad</name>
        <value>tl-zk-nd1:2181,tl-zk-nd2:2181,tl-zk-nd3:2181,tl-zk-nd4:2181,tl-zk-nd5:2181</value>
    </property>

    <property>
        <name>dfs.namenode.ha.zookeeper.timeout</name>
        <value>1200000</value>
    </property>

    <property>
        <name>dfs.namenode.ha.sync.timeout</name>
        <value>600000</value>
    </property>

    <property>
        <name>tdw.support.raid</name>
        <value>false</value>
    </property>
    <property>
        <name>dfs.nameservices</name>
        <value>
            ss-omg-1-v2,ss-omg-3-v2,ss-sng-dc-v2,ss-wxg-3-v2,ss-teg-3-v2,ss-teg-4-v2,tl-ieg-3-v2,ss-cdg-3-v2,tl-ieg-4-v2,ss-wxg-4-v2,ss-mig-1-v2,ss-cdg-4-v2,ss-cdg-6-v2,ss-pcg-close-v2,ss-cdg-wxad-1-v2,ss-omg-7-v2,ss-wxg-11-v2,qy-ieg-4-v2,ss-pcg-4-v2,qy-pcg-1-v2,ss-pcg-iceberg-v3
        </value>
    </property>

    <property>
        <name>dfs.client.failover.connection.retries.on.timeouts</name>
        <value>1</value>
    </property>

    <property>
        <name>dfs.client.failover.connection.retries</name>
        <value>2</value>
    </property>

    <property>
        <name>config.zookeeper.quorum</name>
        <value>ss-zk-nd6:2181,ss-zk-nd7:2181,ss-zk-nd8:2181,ss-zk-nd9:2181,ss-zk-nd10:2181</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-sng-dc-v2</name>
        <value>nn1,nn2</value>
        <description>enable HA mode, one nn is active while another is hot backup.</description>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-sng-dc-v2.nn1</name>
        <value>ss-sng-dc-v2-nn-1.tencent-distribute.com:9000</value>
        <description>RPC address that handles all clients requests.</description>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-sng-dc-v2.nn1</name>
        <value>ss-sng-dc-v2-nn-1.tencent-distribute.com:8081</value>
        <description>
            The address and the base port where the dfs namenode web ui will listen on.
            If the port is 0 then the server will start on a free port.
        </description>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-sng-dc-v2.nn2</name>
        <value>ss-sng-dc-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-sng-dc-v2.nn2</name>
        <value>ss-sng-dc-v2-nn-2.tencent-distribute.com:8081</value>
        <description>
            The address and the base port where the dfs namenode web ui will listen on.
            If the port is 0 then the server will start on a free port.
        </description>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-sng-dc-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-wxg-3-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-wxg-3-v2.nn1</name>
        <value>ss-wxg-3-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-wxg-3-v2.nn1</name>
        <value>ss-wxg-3-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-wxg-3-v2.nn2</name>
        <value>ss-wxg-3-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-wxg-3-v2.nn2</name>
        <value>ss-wxg-3-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-wxg-3-v2.nn3</name>
        <value>ss-wxg-3-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-wxg-3-v2.nn3</name>
        <value>ss-wxg-3-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-wxg-3-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-omg-3-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-omg-3-v2.nn1</name>
        <value>ss-omg-3-v2-nn-1.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-omg-3-v2.nn1</name>
        <value>ss-omg-3-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-omg-3-v2.nn2</name>
        <value>ss-omg-3-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-omg-3-v2.nn2</name>
        <value>ss-omg-3-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-omg-3-v2.nn3</name>
        <value>ss-omg-3-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-omg-3-v2.nn3</name>
        <value>ss-omg-3-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-omg-3-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-omg-1-v2</name>
        <value>nn1,nn2</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-omg-1-v2.nn1</name>
        <value>ss-omg-1-v2-nn-1.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-omg-1-v2.nn1</name>
        <value>ss-omg-1-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-omg-1-v2.nn2</name>
        <value>ss-omg-1-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-omg-1-v2.nn2</name>
        <value>ss-omg-1-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-omg-1-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-teg-4-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-teg-4-v2.nn1</name>
        <value>ss-teg-4-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-teg-4-v2.nn1</name>
        <value>ss-teg-4-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-teg-4-v2.nn2</name>
        <value>ss-teg-4-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-teg-4-v2.nn2</name>
        <value>ss-teg-4-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-teg-4-v2.nn3</name>
        <value>ss-teg-4-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-teg-4-v2.nn3</name>
        <value>ss-teg-4-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-teg-4-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.tl-ieg-3-v2</name>
        <value>nn1,nn2</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.tl-ieg-3-v2.nn1</name>
        <value>tl-ieg-3-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.tl-ieg-3-v2.nn1</name>
        <value>tl-ieg-3-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.tl-ieg-3-v2.nn2</name>
        <value>tl-ieg-3-v2-nn-2.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.tl-ieg-3-v2.nn2</name>
        <value>tl-ieg-3-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.tl-ieg-3-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>


    <property>
        <name>dfs.ha.namenodes.ss-teg-3-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-teg-3-v2.nn1</name>
        <value>ss-teg-3-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-teg-3-v2.nn1</name>
        <value>ss-teg-3-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-teg-3-v2.nn2</name>
        <value>ss-teg-3-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-teg-3-v2.nn2</name>
        <value>ss-teg-3-v2-nn-2.tencent-distribute.com:8080</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.ss-teg-3-v2.nn3</name>
        <value>ss-teg-3-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-teg-3-v2.nn3</name>
        <value>ss-teg-3-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-teg-3-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-cdg-3-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-3-v2.nn1</name>
        <value>ss-cdg-3-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-3-v2.nn1</name>
        <value>ss-cdg-3-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-3-v2.nn2</name>
        <value>ss-cdg-3-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-3-v2.nn2</name>
        <value>ss-cdg-3-v2-nn-2.tencent-distribute.com:8080</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-3-v2.nn3</name>
        <value>ss-cdg-3-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-3-v2.nn3</name>
        <value>ss-cdg-3-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-cdg-3-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.tl-ieg-4-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.tl-ieg-4-v2.nn1</name>
        <value>tl-ieg-4-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.tl-ieg-4-v2.nn1</name>
        <value>tl-ieg-4-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.tl-ieg-4-v2.nn2</name>
        <value>tl-ieg-4-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.tl-ieg-4-v2.nn2</name>
        <value>tl-ieg-4-v2-nn-2.tencent-distribute.com:8080</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.tl-ieg-4-v2.nn3</name>
        <value>tl-ieg-4-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.tl-ieg-4-v2.nn3</name>
        <value>tl-ieg-4-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.tl-ieg-4-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-wxg-4-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-wxg-4-v2.nn1</name>
        <value>ss-wxg-4-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-wxg-4-v2.nn1</name>
        <value>ss-wxg-4-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-wxg-4-v2.nn2</name>
        <value>ss-wxg-4-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-wxg-4-v2.nn2</name>
        <value>ss-wxg-4-v2-nn-2.tencent-distribute.com:8080</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.ss-wxg-4-v2.nn3</name>
        <value>ss-wxg-4-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-wxg-4-v2.nn3</name>
        <value>ss-wxg-4-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-wxg-4-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-mig-1-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-mig-1-v2.nn1</name>
        <value>ss-mig-1-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-mig-1-v2.nn1</name>
        <value>ss-mig-1-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-mig-1-v2.nn2</name>
        <value>ss-mig-1-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-mig-1-v2.nn2</name>
        <value>ss-mig-1-v2-nn-2.tencent-distribute.com:8080</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.ss-mig-1-v2.nn3</name>
        <value>ss-mig-1-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-mig-1-v2.nn3</name>
        <value>ss-mig-1-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-mig-1-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-cdg-4-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-4-v2.nn1</name>
        <value>ss-cdg-4-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-4-v2.nn1</name>
        <value>ss-cdg-4-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-4-v2.nn2</name>
        <value>ss-cdg-4-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-4-v2.nn2</name>
        <value>ss-cdg-4-v2-nn-2.tencent-distribute.com:8080</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-4-v2.nn3</name>
        <value>ss-cdg-4-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-4-v2.nn3</name>
        <value>ss-cdg-4-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-cdg-4-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>


    <property>
        <name>dfs.ha.namenodes.ss-cdg-6-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-6-v2.nn1</name>
        <value>ss-cdg-6-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-6-v2.nn1</name>
        <value>ss-cdg-6-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-6-v2.nn2</name>
        <value>ss-cdg-6-v2-nn-2.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-cdg-6-v2.nn2</name>
        <value>ss-cdg-6-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-6-v2.nn3</name>
        <value>ss-cdg-6-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-6-v2.nn3</name>
        <value>ss-cdg-6-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-cdg-6-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-pcg-close-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-pcg-close-v2.nn1</name>
        <value>ss-pcg-close-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-pcg-close-v2.nn1</name>
        <value>ss-pcg-close-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-pcg-close-v2.nn2</name>
        <value>ss-pcg-close-v2-nn-2.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-pcg-close-v2.nn2</name>
        <value>ss-pcg-close-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-pcg-close-v2.nn3</name>
        <value>ss-pcg-close-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-pcg-close-v2.nn3</name>
        <value>ss-pcg-close-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-pcg-close-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-cdg-wxad-1-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-wxad-1-v2.nn1</name>
        <value>ss-cdg-wxad-1-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-wxad-1-v2.nn1</name>
        <value>ss-cdg-wxad-1-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-wxad-1-v2.nn2</name>
        <value>ss-cdg-wxad-1-v2-nn-2.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-cdg-wxad-1-v2.nn2</name>
        <value>ss-cdg-wxad-1-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-cdg-wxad-1-v2.nn3</name>
        <value>ss-cdg-wxad-1-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-cdg-wxad-1-v2.nn3</name>
        <value>ss-cdg-wxad-1-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-cdg-wxad-1-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-omg-7-v2</name>
        <value>nn1,nn2,nn3</value>
        <description>enable HA mode, one nn is active while another is hot backup.</description>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-omg-7-v2.nn1</name>
        <value>ss-omg-7-v2-nn-1.tencent-distribute.com:9000</value>
        <description>RPC address that handles all clients requests.</description>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-omg-7-v2.nn1</name>
        <value>ss-omg-7-v2-nn-1.tencent-distribute.com:8080</value>
        <description>
            The address and the base port where the dfs namenode web ui will listen on.
            If the port is 0 then the server will start on a free port.
        </description>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-omg-7-v2.nn2</name>
        <value>ss-omg-7-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-omg-7-v2.nn2</name>
        <value>ss-omg-7-v2-nn-2.tencent-distribute.com:8080</value>
        <description>
            The address and the base port where the dfs namenode web ui will listen on.
            If the port is 0 then the server will start on a free port.
        </description>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-omg-7-v2.nn3</name>
        <value>ss-omg-7-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-omg-7-v2.nn3</name>
        <value>ss-omg-7-v2-nn-3.tencent-distribute.com:8080</value>
        <description>
            The address and the base port where the dfs namenode web ui will listen on.
            If the port is 0 then the server will start on a free port.
        </description>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-omg-7-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>


    <property>
        <name>dfs.ha.namenodes.ss-wxg-11-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-wxg-11-v2.nn1</name>
        <value>ss-wxg-11-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-wxg-11-v2.nn1</name>
        <value>ss-wxg-11-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-wxg-11-v2.nn2</name>
        <value>ss-wxg-11-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-wxg-11-v2.nn2</name>
        <value>ss-wxg-11-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-wxg-11-v2.nn3</name>
        <value>ss-wxg-11-v2-nn-3.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-wxg-11-v2.nn3</name>
        <value>ss-wxg-11-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-wxg-11-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>


    <property>
        <name>dfs.ha.namenodes.qy-ieg-4-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.qy-ieg-4-v2.nn1</name>
        <value>qy-ieg-4-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.qy-ieg-4-v2.nn1</name>
        <value>qy-ieg-4-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.qy-ieg-4-v2.nn2</name>
        <value>qy-ieg-4-v2-nn-2.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.qy-ieg-4-v2.nn2</name>
        <value>qy-ieg-4-v2-nn-2.tencent-distribute.com:8080</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.qy-ieg-4-v2.nn3</name>
        <value>qy-ieg-4-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.qy-ieg-4-v2.nn3</name>
        <value>qy-ieg-4-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.qy-ieg-4-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>


    <property>
        <name>dfs.ha.namenodes.ss-pcg-4-v2</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-pcg-4-v2.nn1</name>
        <value>ss-pcg-4-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-pcg-4-v2.nn1</name>
        <value>ss-pcg-4-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-pcg-4-v2.nn2</name>
        <value>ss-pcg-4-v2-nn-2.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-pcg-4-v2.nn2</name>
        <value>ss-pcg-4-v2-nn-2.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-pcg-4-v2.nn3</name>
        <value>ss-pcg-4-v2-nn-3.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-pcg-4-v2.nn3</name>
        <value>ss-pcg-4-v2-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-pcg-4-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.qy-pcg-1-v2</name>
        <value>nn1,nn2</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.qy-pcg-1-v2.nn1</name>
        <value>qy-pcg-1-v2-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.qy-pcg-1-v2.nn1</name>
        <value>qy-pcg-1-v2-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.qy-pcg-1-v2.nn2</name>
        <value>qy-pcg-1-v2-nn-2.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.client.failover.proxy.provider.qy-pcg-1-v2</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>


    <property>
        <name>dfs.ha.namenodes.qy-pcg-11-v3</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.qy-pcg-11-v3.nn1</name>
        <value>qy-pcg-11-v3-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.qy-pcg-11-v3.nn1</name>
        <value>qy-pcg-11-v3-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.qy-pcg-11-v3.nn2</name>
        <value>qy-pcg-11-v3-nn-2.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.qy-pcg-11-v3.nn2</name>
        <value>qy-pcg-11-v3-nn-2.tencent-distribute.com:8080</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.qy-pcg-11-v3.nn3</name>
        <value>qy-pcg-11-v3-nn-3.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.qy-pcg-11-v3.nn3</name>
        <value>qy-pcg-11-v3-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.qy-pcg-11-v3</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.ss-pcg-iceberg-v3</name>
        <value>nn1,nn2,nn3</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-pcg-iceberg-v3.nn1</name>
        <value>ss-pcg-iceberg-v3-nn-1.tencent-distribute.com:9000</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.ss-pcg-iceberg-v3.nn1</name>
        <value>ss-pcg-iceberg-v3-nn-1.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.ss-pcg-iceberg-v3.nn2</name>
        <value>ss-pcg-iceberg-v3-nn-2.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-pcg-iceberg-v3.nn2</name>
        <value>ss-pcg-iceberg-v3-nn-2.tencent-distribute.com:8080</value>
    </property>
    <property>
        <name>dfs.namenode.rpc-address.ss-pcg-iceberg-v3.nn3</name>
        <value>ss-pcg-iceberg-v3-nn-3.tencent-distribute.com:9000</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.ss-pcg-iceberg-v3.nn3</name>
        <value>ss-pcg-iceberg-v3-nn-3.tencent-distribute.com:8080</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.ss-pcg-iceberg-v3</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>
</configuration>
