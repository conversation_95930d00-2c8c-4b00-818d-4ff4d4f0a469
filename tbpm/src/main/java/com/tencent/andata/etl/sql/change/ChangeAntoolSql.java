package com.tencent.andata.etl.sql.change;

public class ChangeAntoolSql {

    public static String QUERY_DWD_CHANGE_VAR_INFO_TABLE_SQL = ""
            + "SELECT \n"
            + "         proc_inst_id, \n"
            + "         CAST('1970-01-01 08:00:00' AS TIMESTAMP) AS record_update_time,\n"
            + "         MAX(change_id) AS change_id,\n"
            + "         MAX(uin) AS uin,\n"
            + "         MAX(j_stage) AS j_stage,\n"
            + "         MAX(is_verified) AS is_verified,\n"
            + "         MAX(unverified_reason) AS unverified_reason,\n"
            + "         MAX(inspect_report_1) AS inspect_report_1,\n"
            + "         MAX(inspect_report_2) AS inspect_report_2,\n"
            + "         MAX(inspect_report_1_info) AS inspect_report_1_info,\n"
            + "         MAX(inspect_report_2_info) AS inspect_report_2_info,\n"
            + "         MAX(product_name) AS product_name,\n"
            + "         MAX(product_list_second) AS product_list_second,\n"
            + "         MAX(channel) AS channel,\n"
            + "         MAX(state) AS state,\n"
            + "         MAX(source) AS source,\n"
            + "         MAX(title) AS title,\n"
            + "         MAX(c_type) AS c_type,\n"
            + "         MAX(tapd_url) AS tapd_url,\n"
            + "         MAX(materials_url) AS materials_url,\n"
            + "         MAX(emergency_level) AS emergency_level,\n"
            + "         MAX(priority) AS priority,\n"
            + "         MAX(risk) AS risk,\n"
            + "         MAX(impact) AS impact,\n"
            + "         MAX(key_component) AS key_component,\n"
            + "         MAX(impact_scope) AS impact_scope,\n"
            + "         MAX(request_impact) AS request_impact,\n"
            + "         MAX(net_break) AS net_break,\n"
            + "         MAX(business_break) AS business_break,\n"
            + "         MAX(description) AS description,\n"
            + "         MAX(initiator) AS initiator,\n"
            + "         MAX(plan_range) AS plan_range,\n"
            + "         MAX(real_range) AS real_range,\n"
            + "         MAX(authorization_credential) AS authorization_credential,\n"
            + "         MAX(plan_execute_corp) AS plan_execute_corp,\n"
            + "         MAX(plan_verify_corp) AS plan_verify_corp,\n"
            + "         MAX(plan_start) AS plan_start,\n"
            + "         MAX(plan_end) AS plan_end,\n"
            + "         MAX(real_execute_corp) AS real_execute_corp,\n"
            + "         MAX(real_verify_corp) AS real_verify_corp,\n"
            + "         MAX(real_start) AS real_start,\n"
            + "         MAX(real_end) AS real_end,\n"
            + "         MAX(plan_execute_corp_company) AS plan_execute_corp_company,\n"
            + "         MAX(plan_verify_corp_company) AS plan_verify_corp_company,\n"
            + "         MAX(real_execute_corp_company) AS real_execute_corp_company,\n"
            + "         MAX(real_verify_corp_company) AS real_verify_corp_company,\n"
            + "         MAX(deploy_mode) AS deploy_mode,\n"
            + "         MAX(is_invalid) AS is_invalid,\n"
            + "         MAX(phase) AS phase,\n"
            + "         MAX(trade) AS trade,\n"
            + "         MAX(ticket_url_list) AS ticket_url_list\n"
            + "FROM (\n"
            + "     SELECT proc_inst_id,\n"
            + "            CASE WHEN var_key='ChangeID' THEN var_value ELSE NULL  END AS change_id,\n"
            + "            CASE WHEN var_key='Uin' THEN var_value ELSE NULL  END AS uin,\n"
            + "            CASE WHEN var_key='projectInfo' THEN get_json_object(var_value,'$.JStage') "
            + "            ELSE NULL  END AS j_stage,\n"
            + "            CASE WHEN var_key='IsVerified' THEN var_value ELSE NULL  END AS is_verified,\n"
            + "            CASE WHEN var_key='UnverifiedReason' THEN var_value ELSE NULL  END AS unverified_reason,\n"
            + "            CASE WHEN var_key='InspectReport_1' THEN var_value ELSE NULL  END AS inspect_report_1,\n"
            + "            CASE WHEN var_key='InspectReport_2' THEN var_value ELSE NULL  END AS inspect_report_2,\n"
            + "            CASE WHEN var_key='InspectReport_1_info' THEN var_value ELSE NULL  END "
            + "            AS inspect_report_1_info,\n"
            + "            CASE WHEN var_key='InspectReport_2_info' THEN var_value ELSE NULL  END "
            + "            AS inspect_report_2_info,\n"
            + "            CASE WHEN var_key='ProductName' THEN var_value ELSE NULL  END AS product_name,\n"
            + "            CASE WHEN var_key='ProductListSecond' THEN var_value ELSE NULL  END "
            + "            AS product_list_second,\n"
            + "            CASE WHEN var_key='Channel' THEN var_value ELSE NULL  END AS channel,\n"
            + "            CASE WHEN var_key='State' THEN var_value ELSE NULL  END AS state,\n"
            + "            CASE WHEN var_key='Source' THEN var_value ELSE NULL  END AS source,\n"
            + "            CASE WHEN var_key='Title' THEN var_value ELSE NULL  END AS title,\n"
            + "            CASE WHEN var_key='CType' THEN var_value ELSE NULL  END AS c_type,\n"
            + "            CASE WHEN var_key='TapdLinks' THEN var_value ELSE NULL  END AS tapd_url,\n"
            + "            CASE WHEN var_key='MaterialsUrl' THEN var_value ELSE NULL  END AS materials_url,\n"
            + "            CASE WHEN var_key='EmergencyLevel' THEN var_value ELSE NULL  END AS emergency_level,\n"
            + "            CASE WHEN var_key='Priority' THEN var_value ELSE NULL  END AS priority,\n"
            + "            CASE WHEN var_key='Risk' THEN var_value ELSE NULL  END AS risk,\n"
            + "            CASE WHEN var_key='Impact' THEN var_value ELSE NULL  END AS impact,\n"
            + "            CASE WHEN var_key='KeyComponent' THEN var_value ELSE NULL  END AS key_component,\n"
            + "            CASE WHEN var_key='ImpactScope' THEN var_value ELSE NULL  END AS impact_scope,\n"
            + "            CASE WHEN var_key='RequestImpact' THEN var_value ELSE NULL  END AS request_impact,\n"
            + "            CASE WHEN var_key='NetBreak' THEN var_value ELSE NULL  END AS net_break,\n"
            + "            CASE WHEN var_key='BusinessBreak' THEN var_value ELSE NULL  END AS business_break,\n"
            + "            CASE WHEN var_key='Desc' THEN var_value ELSE NULL  END AS description,\n"
            + "            CASE WHEN var_key='initiator' THEN var_value ELSE NULL  END AS initiator,\n"
            + "            CASE WHEN var_key='PlanRange' THEN var_value ELSE NULL  END AS plan_range,\n"
            + "            CASE WHEN var_key='RealRange' THEN var_value ELSE NULL  END AS real_range,\n"
            + "            CASE WHEN var_key='AuthorizationCredential' THEN var_value ELSE NULL  END "
            + "            AS authorization_credential,\n"
            + "            CASE WHEN var_key='PlanExecuteCorp' THEN var_value ELSE NULL  END AS plan_execute_corp,\n"
            + "            CASE WHEN var_key='PlanVerifyCorp' THEN var_value ELSE NULL  END AS plan_verify_corp,\n"
            + "            CASE WHEN var_key='PlanStart' AND var_value <> '' THEN var_value ELSE NULL  END "
            + "            AS plan_start,\n"
            + "            CASE WHEN var_key='PlanEnd' AND var_value <> '' THEN var_value ELSE NULL  END AS plan_end,\n"
            + "            CASE WHEN var_key='RealExecuteCorp' THEN var_value ELSE NULL  END AS real_execute_corp,\n"
            + "            CASE WHEN var_key='RealVerifyCorp' THEN var_value ELSE NULL  END AS real_verify_corp,\n"
            + "            CASE WHEN var_key='RealStart' AND var_value <> '' THEN var_value ELSE NULL  END "
            + "            AS real_start,\n"
            + "            CASE WHEN var_key='RealEnd' AND var_value <> '' THEN var_value ELSE NULL  END AS real_end,\n"
            + "            CASE WHEN var_key='PlanExecuteCorp_company' THEN var_value ELSE NULL  END "
            + "            AS plan_execute_corp_company,\n"
            + "            CASE WHEN var_key='PlanVerifyCorp_company' THEN var_value ELSE NULL  END "
            + "            AS plan_verify_corp_company,\n"
            + "            CASE WHEN var_key='RealExecuteCorp_company' THEN var_value ELSE NULL  END "
            + "            AS real_execute_corp_company,\n"
            + "            CASE WHEN var_key='RealVerifyCorp_company' THEN var_value ELSE NULL  END "
            + "            AS real_verify_corp_company,\n"
            + "            CASE WHEN var_key='DeployMode' THEN var_value ELSE NULL  END AS deploy_mode,\n"
            + "            CASE WHEN var_key='IsInvalid' THEN var_value ELSE NULL  END AS is_invalid,\n"
            + "            CASE WHEN var_key='Phase' THEN var_value ELSE NULL  END AS phase,\n"
            + "            CASE WHEN var_key='Trade' THEN var_value ELSE NULL  END AS trade,\n"
            + "            CASE WHEN var_key='TicketUrlList' THEN var_value ELSE NULL  END AS ticket_url_list\n"
            + "         FROM pgsql_source_dwd_change_var_inst\n"
            + ") AS t\n"
            + "GROUP BY proc_inst_id\n";


    public static String QUERY_DWM_CHANGE_TAPD_INFO_TABLE_SQL = ""
            + "SELECT \n"
            + "         a.proc_inst_id,\n"
            + "         a.tapd_info,\n"
            + "         a.tapd_url,\n"
            //+ "         SUBSTRING(a.tapd_info FROM POSITION('bug_id=' IN a.tapd_info) + LENGTH('bug_id=')) AS bug_id\n"
            + "         get_bugid_udf(a.tapd_info) AS bug_id\n"
            + "FROM (\n"
            + "     SELECT proc_inst_id,\n"
            + "            tapd_url,\n"
            + "            get_json_object(tapd_info_json, '$.url') AS tapd_info\n"
            + "      FROM dwd_change_var_info_view,"
            + "      LATERAL TABLE(string_array_explode_jsonstr(tapd_url)) AS T(tapd_info_json)\n"
            + "      WHERE COALESCE(tapd_url, '') <> '' AND tapd_url <> '[]'\n"
            + ") AS a\n";

    public static String QUERY_DWM_CHANGE_VERTICAL_PRODUCT_TABLE_SQL = ""
            + "SELECT \n"
            + "         proc_inst_id,\n"
            + "         product_list_second,\n"
            + "         vertical_product\n"
            + "FROM dwd_change_var_info_view,"
            + "LATERAL TABLE(string_array_explode(product_list_second)) AS T(vertical_product)\n";




    public static String QUERY_DWM_CHANGE_TICKET_URL_TABLE_SQL = ""
            + "SELECT \n"
            + "         a.proc_inst_id,\n"
            + "         a.ticket_url_list,\n"
            + "         a.ticket_url,\n"
            + "         get_url_param_udf(a.ticket_url, 'id') AS ticket_id\n"
            + "FROM (\n"
            + "     SELECT proc_inst_id,\n"
            + "            ticket_url_list,\n"
            + "            ticket_url\n"
            + "      FROM dwd_change_var_info_view,"
            + "      LATERAL TABLE(string_array_explode(ticket_url_list)) AS T(ticket_url)\n"
            + "      WHERE COALESCE(ticket_url_list, '') <> '' AND ticket_url_list <> '[]'\n"
            + ") AS a\n";


}
