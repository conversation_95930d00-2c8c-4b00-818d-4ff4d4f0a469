package com.tencent.andata.etl.tablemap;


public class AntoolAccountMapping {


    public static String mysqlChangeTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_users\",\n"
            + "        \"fTable\":\"mysql_source_t_users\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_companies\",\n"
            + "        \"fTable\":\"mysql_source_t_companies\"\n"
            + "    }\n"
            + "]";

    public static final String PG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_antool_t_users_rt\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_antool_t_users_rt\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_antool_t_companies_rt\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_antool_t_companies_rt\"\n"
            + "    }\n"
            + "]";

}
