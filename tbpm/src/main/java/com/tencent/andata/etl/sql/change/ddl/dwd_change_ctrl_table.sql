-- pg: dwd_change_ctrl_table_rt
CREATE TABLE dwd_change_ctrl_table_rt
(
    primary_id                      TEXT PRIMARY KEY,
    id                              BIGINT,
    proc_inst_id                    TEXT,
    task_id                         TEXT,
    activity_id                     TEXT,
    cid                             INTEGER,
    ctrl_info                       TEXT,
    prepare_phase_items             TEXT,
    vertical_product                TEXT,
    tool_name                       TEXT,
    tool_version                    TEXT,
    target_product_version          TEXT,
    target_small_product_version    TEXT,
    deployment_order                TEXT,
    product_name                    TEXT,
    current_product_version         TEXT,
    create_time                     TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    update_time                     TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL
);

-- create index
CREATE INDEX idx_dwd_change_ctrl_table_rt_proc_inst_id on dwd_change_ctrl_table_rt(proc_inst_id);

-- add comments
COMMENT ON TABLE dwd_change_ctrl_table_rt IS '变更控制表';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.proc_inst_id IS '流程实例id';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.task_id IS '任务id';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.ctrl_info IS '变更控制信息';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.prepare_phase_items IS '准备阶段项';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.vertical_product IS '垂直产品';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.tool_name IS '组件名称';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.tool_version IS '组件版本';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.target_product_version IS '目标产品版本';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.target_small_product_version IS '目标小版本号';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.deployment_order IS '部署顺序';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.product_name IS '产品名称';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.current_product_version IS '现场产品版本';
