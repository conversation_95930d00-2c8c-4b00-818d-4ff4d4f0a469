package com.tencent.andata.etl.sql.maintenance;

public class MaintenanceActivationAntoolSql {


    public static String QUERY_DWD_MAINTENANCE_ACTIVATION_VAR_INFO_TABLE_SQL = ""
            + "SELECT \n"
            + "         proc_inst_id, \n"
            + "         CAST('1970-01-01 08:00:00' AS TIMESTAMP) AS record_update_time,\n"
            + "         MAX(sid) AS sid,\n"
            + "         MAX(ltc_no) AS ltc_no,\n"
            + "         MAX(judian_info) AS judian_info,\n"
            + "         MAX(service_type) AS service_type,\n"
            + "         MAX(service_scene) AS service_scene,\n"

            + "         MAX(poid_list) AS poid_list,\n"
            + "         MAX(judian_id) AS judian_id,\n"
            + "         MAX(node_name) AS node_name,\n"
            + "         MAX(product) AS product,\n"
            + "         MAX(team_members) AS team_members,\n"

            + "         MAX(server_time_inner) AS server_time_inner,\n"
            + "         MAX(server_time_outer) AS server_time_outer,\n"
            + "         MAX(asp_uin) AS asp_uin,\n"
            + "         MAX(asp_name) AS asp_name,\n"
            + "         MAX(initiator) AS initiator\n"
            + "FROM (\n"
            + "     SELECT proc_inst_id,\n"
            + "            CASE WHEN var_key='SID' THEN var_value ELSE NULL  END AS sid,\n"
            + "            CASE WHEN var_key='ltc_no' THEN var_value ELSE NULL  END AS ltc_no,\n"
            + "            CASE WHEN var_key='JudianInfo' THEN var_value ELSE NULL  END AS judian_info,\n"
            + "            CASE WHEN var_key='service_type' THEN var_value ELSE NULL  END AS service_type,\n"
            + "            CASE WHEN var_key='service_scene' THEN var_value ELSE NULL  END AS service_scene,\n"

            + "            CASE WHEN var_key='POIDList' THEN var_value ELSE NULL  END AS poid_list,\n"
            + "            CASE WHEN var_key='JuDianId' THEN var_value ELSE NULL  END AS judian_id,\n"
            + "            CASE WHEN var_key='NodeName' THEN var_value ELSE NULL  END AS node_name,\n"
            + "            CASE WHEN var_key='product' THEN var_value ELSE NULL  END AS product,\n"
            + "            CASE WHEN var_key='TeamMembers' THEN var_value ELSE NULL  END AS team_members,\n"

            + "            CASE WHEN var_key='server_time_inner' THEN var_value ELSE NULL  END AS server_time_inner,\n"
            + "            CASE WHEN var_key='server_time_outer' THEN var_value ELSE NULL  END AS server_time_outer,\n"
            + "            CASE WHEN var_key='AspUin' THEN var_value ELSE NULL  END AS asp_uin,\n"
            + "            CASE WHEN var_key='AspName' THEN var_value ELSE NULL  END AS asp_name,\n"
            + "            CASE WHEN var_key='initiator' THEN var_value ELSE NULL  END AS initiator\n"
            + "         FROM pgsql_source_dwd_maintenance_activation_var_inst\n"
            + ") AS t\n"
            + "GROUP BY proc_inst_id\n";


    public static String QUERY_DWD_MAINTENANCE_ACTIVATION_JUDIAN_INFO_TABLE_SQL = ""
            + "SELECT \n"
            + "         proc_inst_id, \n"
            + "         CAST('1970-01-01 08:00:00' AS TIMESTAMP) AS record_update_time,\n"
            + "         get_json_object(var_value, '$.CustomerName') AS customer_name,\n"
            + "         get_json_object(var_value, '$.CustomerUin') AS customer_uin,\n"
            + "         get_json_object(var_value, '$.Trade') AS trade,\n"
            + "         get_json_object(var_value, '$.Region') AS region,\n"
            + "         get_json_object(var_value, '$.ProjectName') AS project_name\n"
            + "FROM pgsql_source_dwd_maintenance_activation_var_inst\n"
            + "WHERE var_key = 'JudianInfo'\n";


}
