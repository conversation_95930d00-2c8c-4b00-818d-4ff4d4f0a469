CREATE TABLE dwd_visit_proc_inst
(
    proc_inst_id               VARCHAR(255) PRIMARY KEY,
    proc_def_id                VARCHAR(255) DEFAULT '',
    start_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    end_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    duration                   BIGINT DEFAULT 0,
    start_user_id              VARCHAR(255) DEFAULT '',
    delete_reason              VARCHAR(4000) DEFAULT '',
    tenant_id                  INT DEFAULT 0,
    name                       VARCHAR(255) DEFAULT '',
    rev                        INT DEFAULT 0
);

CREATE TABLE dwd_visit_ru_task
(
    id                           VARCHAR(255) PRIMARY KEY,
    rev                          BIGINT DEFAULT 0,
    execution_id                 VARCHAR(255) DEFAULT '',
    proc_inst_id                 VARCHAR(255) DEFAULT '',
    proc_def_id                  VARCHAR(255) DEFAULT '',
    task_def_id                  VARCHAR(255) DEFAULT '',
    scope_id                     VARCHAR(255) DEFAULT '',
    sub_scope_id                 VARCHAR(255) DEFAULT '',
    scope_type                   VARCHAR(255) DEFAULT '',
    scope_definition_id          VARCHAR(255) DEFAULT '',
    propagated_stage_inst_id     VARCHAR(255) DEFAULT '',
    name                         VARCHAR(255) DEFAULT '',
    parent_task_id               VARCHAR(255) DEFAULT '',
    description                  VARCHAR(255) DEFAULT '',
    task_def_key                 VARCHAR(255) DEFAULT '',
    owner                        VARCHAR(255) DEFAULT '',
    assignee                     VARCHAR(255) DEFAULT '',
    delegation                   VARCHAR(255) DEFAULT '',
    priority                     BIGINT DEFAULT 0,
    create_time                  TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    due_date                     TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    category                     VARCHAR(255) DEFAULT '',
    suspension_state             BIGINT DEFAULT 0,
    tenant_id                    VARCHAR(255) DEFAULT '',
    form_key                     VARCHAR(255) DEFAULT '',
    claim_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    is_count_enabled             INT DEFAULT 0,
    var_count                    BIGINT DEFAULT 0,
    id_link_count                BIGINT DEFAULT 0,
    sub_task_count               BIGINT DEFAULT 0
);

CREATE TABLE dwd_visit_task_inst_rt
(
    id                         VARCHAR(255) PRIMARY KEY,
    rev                        BIGINT DEFAULT 0,
    proc_def_id                VARCHAR(255) DEFAULT '',
    task_def_id                VARCHAR(255) DEFAULT '',
    task_def_key               VARCHAR(255) DEFAULT '',
    proc_inst_id               VARCHAR(255) DEFAULT '',
    execution_id               VARCHAR(255) DEFAULT '',
    scope_id                   VARCHAR(255) DEFAULT '',
    sub_scope_id               VARCHAR(255) DEFAULT '',
    scope_type                 VARCHAR(255) DEFAULT '',
    scope_definition_id        VARCHAR(255) DEFAULT '',
    propagated_stage_inst_id   VARCHAR(255) DEFAULT '',
    name                       VARCHAR(255) DEFAULT '',
    parent_task_id             VARCHAR(255) DEFAULT '',
    description                VARCHAR(4000) DEFAULT '',
    owner                      VARCHAR(255) DEFAULT '',
    assignee                   VARCHAR(255) DEFAULT '',
    start_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    claim_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    end_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    duration                   BIGINT DEFAULT 0,
    delete_reason              VARCHAR(4000) DEFAULT '',
    priority                   BIGINT DEFAULT 0,
    due_date                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    form_key                   VARCHAR(255) DEFAULT '',
    category                   VARCHAR(255) DEFAULT '',
    tenant_id                  VARCHAR(255) DEFAULT '',
    last_updated_time          TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL
);

CREATE TABLE dwd_visit_var_info
(
        proc_inst_id varchar(1024)  PRIMARY KEY,
        record_update_time          TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
        visit_id     varchar(1024)  default '',
        current_activity    varchar(1024)  default '',
        visit_state         varchar(1024)  default '',
        ltc_no		        varchar(1024)  default '',
        creator  varchar(1024)  default '',

        current_assign  varchar(1024)  default '',
        project_name  varchar(1024)  default '',
        customer_name  varchar(1024)  default '',


        customer_uin  varchar(1024)  default '',
        trade  varchar(1024)  default '',
        spmo  varchar(1024)  default '',
        pms_pre_salesa_id  varchar(1024)  default '',
        sales_manager_id  varchar(1024)  default '',
        deliver_pm_name varchar(1024)  default '',

        visit_product varchar(1024)  default '',
        visit_type  varchar(1024)  default '',
        visit_category  varchar(1024)  default '',
        plan_participants  varchar(1024)  default '',
        plan_visit_time  timestamp(6) without time zone default null,

        visit_area  text,
        visit_resion  varchar(1024)  default '',
        activity_kubxnu  varchar(1024)  default '',

        activity_lwcifu  varchar(1024)  default '',
        activity_t6ha6u  varchar(1024)  default '',
        activity_lzhkr   varchar(1024)  default '',
        meeting_topic   varchar(1024)  default '',
        meeting_agenda   varchar(1024)  default '',


        meeting_time  timestamp(6) without time zone default null,
        meeting_address  varchar(1024)  default '',
        customer_participants   varchar(1024)  default '',
        tencent_participants   varchar(1024)  default '',

        table_visit_id   text,
        custom   varchar(1024)  default '',
        problem   varchar(1024)  default '',
        email_type   varchar(1024)  default '',
        formal_email1   varchar(1024)  default '',
        formal_email3   varchar(1024)  default '',
        formal_email2   varchar(1024)  default '',

        formal_email4   varchar(1024)  default '',
        test_email   varchar(1024)  default '',
        region varchar(1024) default '',
        server_provider varchar(1024) default '',
        url text default '',

        visit_second_type varchar(1024)  default '',
        to_do_list text default '',
        jielun text default '',
        sub_task_list text default '',
        associated_visits text default '',

        initiator varchar(1024)  default '',
        visit_order_type varchar(1024)  default '',--0:asp,1:原厂2:主动服务
        asp_uin varchar(1024)  default '',
        uins varchar(1024)  default ''
);


CREATE INDEX index_dwd_visit_ru_task_proc_id ON dwd_visit_ru_task (proc_inst_id);
CREATE INDEX index_dwd_visit_ru_task_assignee ON dwd_visit_ru_task (assignee);
CREATE INDEX index_dwd_visit_ru_task_name ON dwd_visit_ru_task (name);
CREATE INDEX index_dwd_visit_task_inst_rt_assignee ON dwd_visit_task_inst_rt(assignee);
CREATE INDEX index_dwd_visit_task_inst_rt_end_time ON dwd_visit_task_inst_rt(end_time);
CREATE INDEX index_dwd_visit_task_inst_rt_proc_id ON dwd_visit_task_inst_rt(proc_inst_id);
CREATE INDEX index_dwd_visit_proc_inst_start_time ON dwd_visit_proc_inst (start_time);

-- 新增字段同步
alter table dwd_visit_var_info
    add column sensitive_reason text default '';
comment on column dwd_visit_var_info.sensitive_reason is '背景原因';

alter table dwd_visit_var_info
    add column app_id varchar(1024) default '';
comment on column dwd_visit_var_info.app_id is 'appid';

alter table dwd_visit_var_info
    add column log_id varchar(1024) default '';
comment on column dwd_visit_var_info.log_id is 'logId是否发送邮件';

alter table dwd_visit_var_info
    add column task_desc text default '';
comment on column dwd_visit_var_info.task_desc is '任务描述';

alter table dwd_visit_var_info
    add column chat_id varchar(1024)  default '';
comment on column dwd_visit_var_info.chat_id is '群id';

alter table dwd_visit_var_info
    add column big_title varchar(1024)  default '';
comment on column dwd_visit_var_info.big_title is '标题';

alter table dwd_visit_var_info
    add column submit_action varchar(1024)  default '';
comment on column dwd_visit_var_info.submit_action is '提交或保存';

alter table dwd_visit_var_info
    add column what_task varchar(1024)  default '',
    add column meeting_location varchar(1024)  default '',
    add column meeting_when varchar(1024)  default '',
    add column customer_people varchar(1024)  default '',
    add column tencent_people varchar(1024)  default '',
    add column meeting_title varchar(1024)  default '',
    add column meeting_what varchar(1024)  default '';
comment on column dwd_visit_var_info.what_task is '任务类型';
comment on column dwd_visit_var_info.meeting_location is '会议地点';
comment on column dwd_visit_var_info.meeting_when is '实际会议时间';
comment on column dwd_visit_var_info.customer_people is '客户侧参会人员';
comment on column dwd_visit_var_info.tencent_people is '腾讯侧参会人员';
comment on column dwd_visit_var_info.meeting_title is '会议主题';
comment on column dwd_visit_var_info.meeting_what is '会议纪要';

ALTER TABLE dwd_visit_var_info ALTER COLUMN meeting_what TYPE text;


alter table dwd_visit_var_info
    add column file_upload varchar(1024)  default '';
comment on column dwd_visit_var_info.file_upload is '附件地址';

ALTER TABLE dwd_visit_var_info ALTER COLUMN file_upload TYPE text;



CREATE TABLE dwd_visit_t_task_log
(
    id                         BIGINT PRIMARY KEY,
    tenant_id                  VARCHAR(255) DEFAULT '',
    proc_inst_id               VARCHAR(255) DEFAULT '',
    event_type                 VARCHAR(255) DEFAULT '',
    task_id                    VARCHAR(255) DEFAULT '',
    task_def_key               VARCHAR(255) DEFAULT '',
    task_name                  VARCHAR(255) DEFAULT '',
    operator_id                VARCHAR(255) DEFAULT '',
    operator_company_name      VARCHAR(255) DEFAULT '',
    operator_time              TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    submit_action              VARCHAR(255) DEFAULT '',
    action                     VARCHAR(255) DEFAULT '',
    assignee                   VARCHAR(255) DEFAULT '',
    approval_comment           VARCHAR(255) DEFAULT '',
    update_content             text
);
CREATE INDEX index_dwd_visit_t_task_log_proc_id ON dwd_visit_t_task_log (proc_inst_id);
CREATE INDEX index_dwd_visit_t_task_log_action ON dwd_visit_t_task_log (action);

CREATE TABLE dwd_visit_andon_pv
(
    value_of_primary_key       VARCHAR(255) PRIMARY KEY,
    record_update_time         TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    access_time                TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    user_id                    VARCHAR(255) DEFAULT '',
    company                    BIGINT DEFAULT 0,
    first_module               VARCHAR(255) DEFAULT '',
    second_module              VARCHAR(255) DEFAULT '',
    channel                    VARCHAR(255) DEFAULT '',
    source_type                VARCHAR(255) DEFAULT '',
    link                       VARCHAR(1024) DEFAULT '',
    object_key                 VARCHAR(255) DEFAULT '',
    extra_params               VARCHAR(1024) DEFAULT '',
    id                         VARCHAR(1024) DEFAULT '',
    task_type                  int DEFAULT 0
);
CREATE INDEX index_dwd_visit_andon_pv_id_task_type ON dwd_visit_andon_pv (id,task_type,company);
CREATE INDEX index_dwd_visit_andon_pv_user_id ON dwd_visit_andon_pv (user_id);
CREATE INDEX index_dwd_visit_andon_pv_access_time ON dwd_visit_andon_pv (access_time);


create table dim_staff_arch_info
(
    dim_id                  bigint primary key not null default 0,
    dim_is_valid            boolean default true not null,
    value_of_primary_key    varchar(1024) not null default '',
    dim_create_time         timestamp(6) without time zone default (now() at time zone ('utc-8')),
    record_update_time      timestamp(6) without time zone default (now() at time zone ('utc-8')),

    staff_id bigint not null default 0,
    eng_name varchar(1024) not null default '',
    chn_name varchar(1024) not null default '',
    pos_name varchar(1024) not null default '',
    pos_type int not null default 0,
    leader_eng_name varchar(1024) not null default '',
    leader_level varchar(1024) not null default ''
);

comment on table dim_staff_arch_info is '员工组织架构信息';

create index index_dim_staff_arch_info_of_dim_is_valid on dim_staff_arch_info(dim_is_valid);
create index index_dim_staff_arch_info_of_table_primary_key on dim_staff_arch_info(value_of_primary_key);
create index index_dim_staff_arch_info_of_eng_name on dim_staff_arch_info(eng_name,pos_type,dim_is_valid,pos_name);


comment on column dim_staff_arch_info.staff_id is '员工id';
comment on column dim_staff_arch_info.eng_name is '员工英文名';
comment on column dim_staff_arch_info.chn_name is '员工中文名';
comment on column dim_staff_arch_info.pos_name is '职位名';
comment on column dim_staff_arch_info.pos_type is '岗位类型：1主岗位，2兼职岗位';
comment on column dim_staff_arch_info.leader_eng_name is '领导英文名';
comment on column dim_staff_arch_info.leader_level is '领导级别';

-- 增加新字段(2021/11/15 jingzhenhe)
alter table dim_staff_arch_info add column department_id int default 0;
comment on column dim_staff_arch_info.department_id is '部门ID';
alter table dim_staff_arch_info add column department_name varchar(1024) default '';
comment on column dim_staff_arch_info.department_id is '部门名称';

-- 新增字段(2022/05/24 julioguo)
alter table dim_staff_arch_info add column title varchar(1024) default '';


CREATE TABLE dwd_visit_var_inst
(
    proc_inst_id         VARCHAR(255),
    var_key              VARCHAR(255) DEFAULT '',
    var_value            TEXT DEFAULT '',
    rev                  INT DEFAULT 0,
    CONSTRAINT pk_dwd_visit_var_inst PRIMARY KEY (proc_inst_id, var_key)
);
create index index_dwd_visit_var_inst_var_key on dwd_visit_var_inst(var_key);





-- 日志view
CREATE OR REPLACE VIEW dwd_visit_t_task_log_view AS
SELECT
    t.*,
    CASE WHEN COALESCE(t1.user_name, '') != '' THEN t1.user_name ELSE t.operator_id END AS operator
FROM dwd_visit_t_task_log t
LEFT JOIN dim_antool_t_users_rt t1 ON t.operator_id = t1.id
where t.action not in ('派单');


-- 组织架构集团只显示主岗，云智只显示兼岗
CREATE OR REPLACE VIEW dim_staff_arch_view AS
SELECT *
FROM dim_staff_arch_info t1
WHERE t1.dim_is_valid = true
  AND (
        (t1.pos_type = 1 AND t1.pos_name NOT LIKE '%云智研发中心%')
        OR
        (t1.pos_type = 0 AND EXISTS (
            SELECT 1
            FROM dim_staff_arch_info t2
            WHERE t2.pos_name LIKE '%云智研发中心%'
              AND t2.pos_type = 1
              AND t1.eng_name = t2.eng_name
              AND t2.dim_is_valid = true
        ))
    );


CREATE OR REPLACE VIEW dwd_visit_andon_pv_view AS
SELECT
    t.*,
    CASE WHEN COALESCE(t1.user_name, '') != '' THEN t1.user_name ELSE t.user_id END AS user_name,
    t2.pos_name AS company_name,
    CASE WHEN t.company > 0 OR t.user_id LIKE 'v\_%' OR t.user_id LIKE 'p\_%' THEN 2 ELSE 1 END AS is_regular
FROM dwd_visit_andon_pv t
LEFT JOIN dim_antool_t_users_rt t1 ON t.user_id = t1.user_id
LEFT JOIN dim_staff_arch_view t2 ON t.user_id = t2.eng_name;


ALTER TABLE public.dwd_visit_var_inst REPLICA IDENTITY FULL;


-- 增加新字段(2024/3/29 camillemli)
alter table dwd_visit_var_info
    add column meeting_file text default '';
comment on column dwd_visit_var_info.meeting_file is '附件';

alter table dwd_visit_var_info
    add column asp_meeting_result text default '';
comment on column dwd_visit_var_info.asp_meeting_result is '会议纪要';


alter table dwd_visit_var_info
    add column new_tencent_participants text default '';
comment on column dwd_visit_var_info.new_tencent_participants is 'asp腾讯侧参会人员';

alter table dwd_visit_var_info
    add column title text default '';
comment on column dwd_visit_var_info.title is 'asp拜访标题字段';

alter table dwd_visit_var_info
    add column new_plan_participants text default '';
comment on column dwd_visit_var_info.new_plan_participants is 'asp计划参与人员';


alter table dwd_visit_var_info
    add column email_content text default '';
comment on column dwd_visit_var_info.email_content is '邮件内容';


alter table dwd_visit_var_info
    add column source_channel text default '',
    add column opportunity_id text default '',
    add column task_source text default '';

comment on column dwd_visit_var_info.source_channel is '来源渠道';
comment on column dwd_visit_var_info.opportunity_id is '商机编号';
comment on column dwd_visit_var_info.task_source is '任务来源';


ALTER TABLE dim_staff_arch_info ALTER COLUMN leader_eng_name DROP NOT NULL;


alter table dwd_visit_var_info
    add column plan_visit_end_time date;
comment on column dwd_visit_var_info.plan_visit_end_time is '计划服务结束时间';
