package com.tencent.andata.etl.tablemap.maintenance;

public class MaintenanceActivationAntoolMapping {

    public static final String MAINTENANCE_ACTIVATION_PG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_maintenance_activation_var_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_maintenance_activation_var_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_maintenance_activation_judian_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_maintenance_activation_judian_info\"\n"
            + "    }\n"
            + "]";

}
