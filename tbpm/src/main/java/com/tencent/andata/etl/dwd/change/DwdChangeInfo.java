package com.tencent.andata.etl.dwd.change;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Properties;
import static com.tencent.andata.etl.sql.change.DwdChangeOperateSql.QUERY_69_CHANGE_OPERATE_LOG_SQL;
import static com.tencent.andata.etl.sql.change.DwdChangeOperateSql.QUERY_69_CHANGE_INFO_SQL;
import static com.tencent.andata.etl.sql.change.DwdChangeOperateSql.QUERY_69_CHANGE_CTRL_TABLE_SQL;
import static com.tencent.andata.etl.sql.change.DwdChangeOperateSql.QUERY_CHANGE_CTRL_TABLE_SQL;
import static com.tencent.andata.etl.tablemap.change.DwdChangeOperateMapping.mysqlChangeTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.change.DwdChangeOperateMapping.icebergTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.change.DwdChangeOperateMapping.PG_TABLE_TO_FLINK_TABLE;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;

@Builder
public class DwdChangeInfo {

    private static final Logger logger = LoggerFactory.getLogger(DwdChangeInfo.class);
    private final String icebergDbName;
    private final String pgDbName;


    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        // 出库PG表注册
        DatabaseConf dataWareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();
        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "change"))
                .build();
        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(mysqlChangeTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, MYSQL, tEnv);

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class),
                tEnv,
                catalog
        );

        // pgTable mapping to flinkTable
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(PG_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                PGSQL,
                tEnv
        );

        // 操作日志
        tEnv.createTemporaryView("change_operate_view", tEnv.sqlQuery(QUERY_69_CHANGE_OPERATE_LOG_SQL));
        // 69_changeCtrlTable
        tEnv.createTemporaryView("change_ctrl_view", tEnv.sqlQuery(QUERY_69_CHANGE_CTRL_TABLE_SQL));
        // 69_changeinfo
        tEnv.createTemporaryView("change_info_view", tEnv.sqlQuery(QUERY_69_CHANGE_INFO_SQL));
        // dwd_change_ctrl
        tEnv.createTemporaryView("pg_change_ctrl_view", tEnv.sqlQuery(QUERY_CHANGE_CTRL_TABLE_SQL));

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                        "change_operate_view",
                        "iceberg_sink_ods_69_change_operate_log",
                        tEnv.from("iceberg_sink_ods_69_change_operate_log"),
                        ICEBERG))
                .addInsertSql(insertIntoSql(
                        "change_ctrl_view",
                        "iceberg_sink_ods_69_change_ctrl_table",
                        tEnv.from("iceberg_sink_ods_69_change_ctrl_table"),
                        ICEBERG))
                .addInsertSql(insertIntoSql(
                        "change_info_view",
                        "iceberg_sink_ods_69_change_info",
                        tEnv.from("iceberg_sink_ods_69_change_info"),
                        ICEBERG))
                .addInsertSql(insertIntoSql(
                        "pg_change_ctrl_view",
                        "pgsql_sink_dwd_change_ctrl_table",
                        tEnv.from("pgsql_sink_dwd_change_ctrl_table"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "change_info_view",
                        "pgsql_sink_dwd_change_project_info",
                        tEnv.from("pgsql_sink_dwd_change_project_info"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "change_operate_view",
                        "pgsql_sink_dwd_change_operate_log",
                        tEnv.from("pgsql_sink_dwd_change_operate_log"),
                        PGSQL));

    }
}
