package com.tencent.andata.etl.sql.change;

public class ChangeTbpmSql {


    public static String QUERY_DWD_CHANGE_RU_TASK_SQL = ""
            + "SELECT ID_ AS id,\n"
            + "       REV_ AS rev,\n"
            + "       EXECUTION_ID_ AS execution_id,\n"
            + "       PROC_INST_ID_ AS proc_inst_id,\n"
            + "       PROC_DEF_ID_ AS proc_def_id,\n"
            + "       TASK_DEF_ID_ AS task_def_id,\n"
            + "       SCOPE_ID_ AS scope_id,\n"
            + "       SUB_SCOPE_ID_ AS sub_scope_id,\n"
            + "       SCOPE_TYPE_ AS scope_type,\n"
            + "       SCOPE_DEFINITION_ID_ AS scope_definition_id,\n"
            + "       PROPAGATED_STAGE_INST_ID_ AS propagated_stage_inst_id,\n"
            + "       NAME_ AS name,\n"
            + "       PARENT_TASK_ID_ AS parent_task_id,\n"
            + "       DESCRIPTION_ AS description,\n"
            + "       TASK_DEF_KEY_ AS task_def_key,\n"
            + "       OWNER_ AS owner,\n"
            + "       ASSIGNEE_ AS assignee,\n"
            + "       DELEGATION_ AS delegation,\n"
            + "       PRIORITY_ AS priority,\n"
            + "       CREATE_TIME_ AS create_time,\n"
            + "       DUE_DATE_ AS due_date,\n"
            + "       CATEGORY_ AS category,\n"
            + "       SUSPENSION_STATE_ AS suspension_state,\n"
            + "       TENANT_ID_ AS tenant_id,\n"
            + "       FORM_KEY_ AS form_key,\n"
            + "       CLAIM_TIME_ AS claim_time,\n"
            + "       IS_COUNT_ENABLED_ AS is_count_enabled,\n"
            + "       VAR_COUNT_ AS var_count,\n"
            + "       ID_LINK_COUNT_ AS id_link_count,\n"
            + "       SUB_TASK_COUNT_ AS sub_task_count\n"
            + "FROM mysql_source_ACT_RU_TASK /*+ OPTIONS('server-id'='1505-1605') */\n"
            + "WHERE PROC_DEF_ID_ LIKE 'mainchangenew%' AND TENANT_ID_='69'\n";



    public static String QUERY_DWD_CHANGE_TASK_INST_SQL = ""
            + "SELECT ID_ AS id,\n"
            + "       REV_ AS rev,\n"
            + "       PROC_DEF_ID_ AS proc_def_id,\n"
            + "       TASK_DEF_ID_ AS task_def_id,\n"
            + "       TASK_DEF_KEY_ AS task_def_key,\n"
            + "       PROC_INST_ID_ AS proc_inst_id,\n"
            + "       EXECUTION_ID_ AS execution_id,\n"
            + "       SCOPE_ID_ AS scope_id,\n"
            + "       SUB_SCOPE_ID_ AS sub_scope_id,\n"
            + "       SCOPE_TYPE_ AS scope_type,\n"
            + "       SCOPE_DEFINITION_ID_ AS scope_definition_id,\n"
            + "       PROPAGATED_STAGE_INST_ID_ AS propagated_stage_inst_id,\n"
            + "       NAME_ AS name,\n"
            + "       PARENT_TASK_ID_ AS parent_task_id,\n"
            + "       DESCRIPTION_ AS description,\n"
            + "       OWNER_ AS owner,\n"
            + "       ASSIGNEE_ AS assignee,\n"
            + "       START_TIME_ AS start_time,\n"
            + "       CLAIM_TIME_ AS claim_time,\n"
            + "       END_TIME_ AS end_time,\n"
            + "       DURATION_ AS duration,\n"
            + "       DELETE_REASON_ AS delete_reason,\n"
            + "       PRIORITY_ AS priority,\n"
            + "       DUE_DATE_ AS due_date,\n"
            + "       FORM_KEY_ AS form_key,\n"
            + "       CATEGORY_ AS category,\n"
            + "       TENANT_ID_ AS tenant_id,\n"
            + "       LAST_UPDATED_TIME_\n AS last_updated_time\n"
            + "FROM mysql_source_ACT_HI_TASKINST /*+ OPTIONS('server-id'='1606-1706') */\n"
            + "WHERE PROC_DEF_ID_ LIKE 'mainchangenew%' AND TENANT_ID_='69'\n";




    public static String QUERY_DWD_CHANGE_PROC_INST_SQL = ""
            + "SELECT PROC_INST_ID_ AS proc_inst_id,\n"
            + "       PROC_DEF_ID_ AS proc_def_id,\n"
            + "       START_TIME_ AS start_time,\n"
            + "       END_TIME_ AS end_time,\n"
            + "       DURATION_ AS duration,\n"
            + "       START_USER_ID_ AS start_user_id,\n"
            + "       DELETE_REASON_ AS delete_reason,\n"
            + "       CAST(TENANT_ID_ AS INTEGER) AS tenant_id,\n"
            + "       NAME_ AS name,\n"
            + "       REV_ AS rev\n"
            + "FROM mysql_source_ACT_HI_PROCINST /*+ OPTIONS('server-id'='1707-1808') */\n"
            + "WHERE PROC_DEF_ID_ LIKE 'mainchangenew%' AND TENANT_ID_='69'\n";



    public static String QUERY_DWD_TBPM_ANTOOL_VAR_INST_SQL = ""
            + "WITH t1 AS (\n"
            + "    SELECT t2.ID_ AS id,\n"
            + "           t2.PROC_INST_ID_ AS proc_inst_id,\n"
            + "           t2.EXECUTION_ID_ AS execution_id,\n"
            + "           t2.TASK_ID_ AS task_id,\n"
            + "           t2.NAME_ AS name,\n"
            + "           t2.VAR_TYPE_ AS var_type,\n"
            + "           t2.SCOPE_ID_ AS scope_id,\n"
            + "           t2.SUB_SCOPE_ID_ AS sub_scope_id,\n"
            + "           t2.SCOPE_TYPE_ AS scope_type,\n"
            + "           t2.BYTEARRAY_ID_ AS bytearray_id,\n"
            + "           t2.DOUBLE_ AS `double`,\n"
            + "           t2.LONG_ AS `long`,\n"
            + "           t2.TEXT_ AS `text`,\n"
            + "           t2.TEXT2_ AS `text2`,\n"
            + "           t2.CREATE_TIME_ AS create_time,\n"
            + "           t2.LAST_UPDATED_TIME_ AS last_updated_time,\n"
            + "           t2.REV_ AS rev\n"
            + "    FROM mysql_source_ACT_HI_VARINST /*+ OPTIONS('server-id'='1809-1909') */ t2\n"
            + "    INNER JOIN (\n"
            + "        SELECT PROC_INST_ID_\n"
            + "        FROM mysql_source_ACT_HI_VARINST /*+ OPTIONS('server-id'='1910-2010') */\n"
            + "        WHERE NAME_ = 'ChangeID'\n"
            + "    ) t1 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_\n"
            + ")\n"
            + "SELECT\n"
            + "    t1.`id`,\n"
            + "    t1.`proc_inst_id`,\n"
            + "    t1.`execution_id`,\n"
            + "    t1.`task_id`,\n"
            + "    t1.`name`,\n"
            + "    t1.`var_type`,\n"
            + "    t1.`scope_id`,\n"
            + "    t1.`sub_scope_id`,\n"
            + "    t1.`scope_type`,\n"
            + "    t1.`bytearray_id`,\n"
            + "    t1.`double`,\n"
            + "    t1.`long`,\n"
            + "    t1.`text`,\n"
            + "    t1.`text2`,\n"
            + "    t3.`json`,\n"
            + "    t1.`create_time`,\n"
            + "    t1.`last_updated_time`,\n"
            + "    t1.`rev`\n"
            + "FROM t1\n"
            + "LEFT JOIN (\n"
            + "        SELECT `ID_`,\n"
            + "            udf_cast_jser_to_json(`BYTES_`, true) json \n"
            + "        FROM mysql_source_ACT_GE_BYTEARRAY /*+ OPTIONS('server-id'='2011-2112') */\n"
            + ") t3 ON t1.`bytearray_id` = t3.`ID_`";



    public static String QUERY_DWD_CHANGE_TBPM_ANTOOL_VAR_INST_INFO_SQL = ""
            + "SELECT \n"
            + "   proc_inst_id, \n"
            + "   name as var_key,\n"
            + "   udf_extract_tbpm_var_val(cast(var_type AS string), `double`, `long`, cast(text AS string), "
            + "   cast(text2 AS string), json) as var_value,\n"
            + "   rev\n"
            + "FROM dwd_tbpm_antool_var_inst_view\n";




}
