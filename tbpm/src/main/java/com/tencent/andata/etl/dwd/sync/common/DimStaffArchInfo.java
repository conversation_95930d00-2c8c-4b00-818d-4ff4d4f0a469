package com.tencent.andata.etl.dwd.sync.common;


import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import java.util.Properties;
import static com.tencent.andata.etl.sql.visit.VisitTbpmAntoolSql.QUERY_DIM_STAFF_ARCH_INFO_SQL;
import static com.tencent.andata.etl.tablemap.visit.DimStaffArchInfoMapping.PG_SINK_TABLE_TO_FLINK_TABLE;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

/**
 * DimStaffArchInfo
 * <AUTHOR>
 */
@Builder
public class DimStaffArchInfo {

    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {

        String sourceTableName = "dim_staff_arch_info";

        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        // 注册Iceberg Table
        org.apache.iceberg.Table tableInstance = catalog.getTableInstance(
                icebergDbName,
                sourceTableName
        );
        StreamTableEnvironment tableEnv = flinkEnv.streamTEnv();
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("iceberg_source_dim_staff_arch_info")
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(tableInstance)
                                        .primaryKeyList(
                                                new String[]{"value_of_primary_key"}
                                        )
                        )
                        .build()
        );
        // pg mapping to flinkTable
        ObjectMapper mapper = new ObjectMapper();

        TableUtils.rdbTable2FlinkTable(
                pgDBConf,
                mapper.readValue(PG_SINK_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                PGSQL,
                tableEnv
        );

        StatementSet stmtSet = flinkEnv.stmtSet();
        // 创建flink临时表
        flinkEnv.streamTEnv().createTemporaryView(
                "dim_staff_arch_info_view",
                flinkEnv.streamTEnv().sqlQuery(QUERY_DIM_STAFF_ARCH_INFO_SQL));
        // insert
        stmtSet.addInsertSql(insertIntoSql(
                "dim_staff_arch_info_view",
                "pgsql_sink_dim_staff_arch_info",
                tableEnv.from("pgsql_sink_dim_staff_arch_info"),
                PGSQL));


    }
}
