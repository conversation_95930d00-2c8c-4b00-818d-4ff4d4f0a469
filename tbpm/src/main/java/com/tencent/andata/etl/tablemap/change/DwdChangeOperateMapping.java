package com.tencent.andata.etl.tablemap.change;

public class DwdChangeOperateMapping {

    public static String mysqlChangeTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"69_changeOperateLog\",\n"
            + "        \"fTable\":\"mysql_source_69_changeOperateLog\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"69_changeCtrlTable\",\n"
            + "        \"fTable\":\"mysql_source_69_changeCtrlTable\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"69_changeinfo\",\n"
            + "        \"fTable\":\"mysql_source_69_changeinfo\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_69_change_operate_log\",\n"
            + "        \"fTable\":\"iceberg_sink_ods_69_change_operate_log\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_69_change_ctrl_table\",\n"
            + "        \"fTable\":\"iceberg_sink_ods_69_change_ctrl_table\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"ods_69_change_info\",\n"
            + "        \"fTable\":\"iceberg_sink_ods_69_change_info\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    }\n"
            + "]";

    public static final String PG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_change_project_info_rt\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_change_project_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_change_ctrl_table_rt\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_change_ctrl_table\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_change_operate_log_rt\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_change_operate_log\"\n"
            + "    }\n"
            + "]";

}
