package com.tencent.andata.etl.dwd.sync.common;

import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import java.util.Properties;
import static com.tencent.andata.etl.sql.AntoolAccountSql.QUERY_DIM_COMPANY_SQL;
import static com.tencent.andata.etl.sql.AntoolAccountSql.QUERY_DIM_USERS_SQL;
import static com.tencent.andata.etl.tablemap.AntoolAccountMapping.PG_TABLE_TO_FLINK_TABLE;
import static com.tencent.andata.etl.tablemap.AntoolAccountMapping.mysqlChangeTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

@Builder
public class AntoolAccount {

    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "antool_account"))
                .build();
        // 出库PG表注册
        DatabaseConf dataWareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();
        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(mysqlChangeTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, MYSQL, tEnv);

        // pgTable mapping to flinkTable
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(PG_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                PGSQL,
                tEnv
        );

        // t_users
        tEnv.createTemporaryView("t_users_view", tEnv.sqlQuery(QUERY_DIM_USERS_SQL));
        // t_companies
        tEnv.createTemporaryView("t_companies_view", tEnv.sqlQuery(QUERY_DIM_COMPANY_SQL));

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                        "t_users_view",
                        "pgsql_sink_dim_antool_t_users_rt",
                        tEnv.from("pgsql_sink_dim_antool_t_users_rt"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "t_companies_view",
                        "pgsql_sink_dim_antool_t_companies_rt",
                        tEnv.from("pgsql_sink_dim_antool_t_companies_rt"),
                        PGSQL));
    }
}
