package com.tencent.andata.etl.tablemap.visit;

public class DimStaffArchInfoMapping {

    public static String pgSourceTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dim_staff_arch_info\",\n"
            + "        \"fTable\":\"pgsql_source_dim_staff_arch_info\"\n"
            + "    }\n"
            + "]";

    public static final String PG_SINK_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_staff_arch_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dim_staff_arch_info\"\n"
            + "    }\n"
            + "]";

}
