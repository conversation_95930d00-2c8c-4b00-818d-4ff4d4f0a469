package com.tencent.andata.etl.dwd.sync;


import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Properties;

import static com.tencent.andata.etl.sql.change.ChangeTbpmSql.QUERY_DWD_CHANGE_RU_TASK_SQL;
import static com.tencent.andata.etl.sql.change.ChangeTbpmSql.QUERY_DWD_CHANGE_PROC_INST_SQL;
import static com.tencent.andata.etl.sql.change.ChangeTbpmSql.QUERY_DWD_CHANGE_TASK_INST_SQL;
import static com.tencent.andata.etl.sql.change.ChangeTbpmSql.QUERY_DWD_TBPM_ANTOOL_VAR_INST_SQL;
import static com.tencent.andata.etl.sql.change.ChangeTbpmSql.QUERY_DWD_CHANGE_TBPM_ANTOOL_VAR_INST_INFO_SQL;
import static com.tencent.andata.etl.tablemap.change.ChangeTbpmMapping.PG_TABLE_TO_FLINK_TABLE;
import static com.tencent.andata.etl.tablemap.change.ChangeTbpmMapping.mysqlChangeTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

@Builder
public class ChangeTbpmInfo {

    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "change"))
                .build();
        // 出库PG表注册
        DatabaseConf dataWareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();
        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(mysqlChangeTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, MYSQL, tEnv);

        // pgTable mapping to flinkTable
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(PG_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                PGSQL,
                tEnv
        );


        // dwd_tbpm_antool_ru_task
        tEnv.createTemporaryView("dwd_change_ru_task_view", tEnv.sqlQuery(QUERY_DWD_CHANGE_RU_TASK_SQL));

        // dwd_tbpm_antool_proc_inst
        tEnv.createTemporaryView("dwd_change_proc_inst_view", tEnv.sqlQuery(QUERY_DWD_CHANGE_PROC_INST_SQL));

        // dwd_tbpm_antool_task_inst
        tEnv.createTemporaryView("dwd_change_task_inst_view", tEnv.sqlQuery(QUERY_DWD_CHANGE_TASK_INST_SQL));

        // dwd_tbpm_antool_var_inst
        tEnv.createTemporaryView("dwd_tbpm_antool_var_inst_view",
                tEnv.sqlQuery(QUERY_DWD_TBPM_ANTOOL_VAR_INST_SQL));

        // dwd_change_var_inst
        tEnv.createTemporaryView("dwd_change_var_inst_view",
                tEnv.sqlQuery(QUERY_DWD_CHANGE_TBPM_ANTOOL_VAR_INST_INFO_SQL));

        StatementSet stmtSet = flinkEnv.stmtSet();

        stmtSet.addInsertSql(insertIntoSql(
                        "dwd_change_ru_task_view",
                        "pgsql_sink_dwd_change_ru_task",
                        tEnv.from("pgsql_sink_dwd_change_ru_task"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "dwd_change_var_inst_view",
                        "pgsql_sink_dwd_change_var_inst",
                        tEnv.from("pgsql_sink_dwd_change_var_inst"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "dwd_change_proc_inst_view",
                        "pgsql_sink_dwd_change_proc_inst",
                        tEnv.from("pgsql_sink_dwd_change_proc_inst"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "dwd_change_task_inst_view",
                        "pgsql_sink_dwd_change_task_inst_rt",
                        tEnv.from("pgsql_sink_dwd_change_task_inst_rt"),
                        PGSQL));
    }
}
