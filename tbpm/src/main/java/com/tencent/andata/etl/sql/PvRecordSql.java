package com.tencent.andata.etl.sql;

public class PvRecordSql {


    public static String QUERY_DWD_SOP_PV_SQL = ""
            + "SELECT\n"
            + "  value_of_primary_key,\n"
            + "  CAST(record_update_time AS TIMESTAMP) record_update_time,\n"
            + "  CAST(access_time AS TIMESTAMP) access_time,\n"
            + "  user AS user_id,\n"
            + "  company,\n"
            + "  first_module,\n"
            + "  second_module,\n"
            + "  channel,\n"
            + "  source_type,\n"
            + "  link,\n"
            + "  object_key,\n"
            + "  extra_params,\n"
            + "  get_json_object(extra_params,'$.id') AS id,\n"
            + "  get_json_object(extra_params,'$.type')AS task_type,\n"
            + "  get_json_object(extra_params,'$.chName')AS ch_name,\n"
            + "  get_json_object(extra_params,'$.companyName')AS company_name,\n"
            + "  get_json_object(extra_params,'$.posName')AS pos_name,\n"
            + "  get_json_object(extra_params,'$.userName')AS user_name\n"
            + "FROM pg_source_ods_andon_pv\n"
            + "WHERE first_module IN ('antool','服务360')\n"
            + "  AND second_module = '交付作业' AND COALESCE(extra_params, '')  <> ''";

}
