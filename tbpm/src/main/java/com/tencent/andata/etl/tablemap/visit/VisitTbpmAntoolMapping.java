package com.tencent.andata.etl.tablemap.visit;

public class VisitTbpmAntoolMapping {

    public static String mysqlVisitTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_HI_PROCINST\",\n"
            + "        \"fTable\":\"mysql_source_visit_ACT_HI_PROCINST\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_GE_BYTEARRAY\",\n"
            + "        \"fTable\":\"mysql_source_visit_ACT_GE_BYTEARRAY\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_HI_VARINST\",\n"
            + "        \"fTable\":\"mysql_source_visit_ACT_HI_VARINST\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_RU_TASK\",\n"
            + "        \"fTable\":\"mysql_source_visit_ACT_RU_TASK\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_task_log\",\n"
            + "        \"fTable\":\"mysql_source_visit_t_task_log\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_HI_TASKINST\",\n"
            + "        \"fTable\":\"mysql_source_visit_ACT_HI_TASKINST\"\n"
            + "    }\n"
            + "]";

    public static final String VISIT_PG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_visit_var_inst\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_visit_var_inst\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_visit_ru_task\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_visit_ru_task\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_visit_proc_inst\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_visit_proc_inst\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_visit_t_task_log\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_visit_t_task_log\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_visit_task_inst_rt\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_visit_task_inst_rt\"\n"
            + "    }\n"
            + "]";

}
