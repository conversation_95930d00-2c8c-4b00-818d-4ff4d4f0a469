package com.tencent.andata.etl.tablemap.maintenance;

public class MaintenanceActivationTbpmMapping {

    public static String mysqlMaintenanceActivationTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_HI_PROCINST\",\n"
            + "        \"fTable\":\"mysql_source_maintenance_activation_ACT_HI_PROCINST\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_GE_BYTEARRAY\",\n"
            + "        \"fTable\":\"mysql_source_maintenance_activation_ACT_GE_BYTEARRAY\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_HI_VARINST\",\n"
            + "        \"fTable\":\"mysql_source_maintenance_activation_ACT_HI_VARINST\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_RU_TASK\",\n"
            + "        \"fTable\":\"mysql_source_maintenance_activation_ACT_RU_TASK\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"ACT_HI_TASKINST\",\n"
            + "        \"fTable\":\"mysql_source_maintenance_activation_ACT_HI_TASKINST\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_task_log\",\n"
            + "        \"fTable\":\"mysql_source_maintenance_activation_t_task_log\"\n"
            + "    }\n"
            + "]";

    public static final String MAINTENANCE_ACTIVATION_PG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_maintenance_activation_var_inst\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_maintenance_activation_var_inst\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_maintenance_activation_ru_task\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_maintenance_activation_ru_task\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_maintenance_activation_proc_inst\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_maintenance_activation_proc_inst\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_maintenance_activation_task_inst_rt\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_maintenance_activation_task_inst_rt\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_maintenance_activation_t_task_log\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_maintenance_activation_t_task_log\"\n"
            + "    }\n"
            + "]";

}
