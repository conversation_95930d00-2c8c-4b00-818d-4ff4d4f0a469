package com.tencent.andata.etl.dwd.visit;


import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.CDCTableBuilderStrategy;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import java.util.Properties;
import static com.tencent.andata.etl.sql.visit.VisitAntoolSql.QUERY_DWD_VISIT_VAR_INFO_TABLE_SQL;
import static com.tencent.andata.etl.tablemap.visit.VisitAntoolMapping.VISIT_PG_TABLE_TO_FLINK_TABLE;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

@Builder
public class VisitAntoolInfo {

    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);
        // 出库PG表注册
        DatabaseConf dataWareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        ObjectMapper mapper = new ObjectMapper();
        // pgTable mapping to flinkTable
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(VISIT_PG_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                PGSQL,
                tEnv
        );

        // PG CDC ODS表注册
        DatabaseConf pvDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "antool"))
                .build();
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("pgsql_source_dwd_visit_var_inst")
                        .tableBuilderStrategy(
                                new CDCTableBuilderStrategy(
                                        "dwd_visit_var_inst",
                                        PGSQL,
                                        pvDBConf, ""
                                )
                        )
                        .processTimeFiled("process_time")
                        .build()
        );

        StatementSet stmtSet = flinkEnv.stmtSet();
        // dwd_visit_var_info
        tEnv.createTemporaryView("dwd_visit_var_info_view", tEnv.sqlQuery(QUERY_DWD_VISIT_VAR_INFO_TABLE_SQL));
        stmtSet.addInsertSql(insertIntoSql(
                        "dwd_visit_var_info_view",
                        "pgsql_sink_dwd_visit_var_info",
                        tEnv.from("pgsql_sink_dwd_visit_var_info"),
                        PGSQL));
    }
}
