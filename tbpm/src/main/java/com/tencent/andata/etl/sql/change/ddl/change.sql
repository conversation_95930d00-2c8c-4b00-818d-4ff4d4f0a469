-- pg: dwd_change_ctrl_table_rt
CREATE TABLE dwd_change_ctrl_table_rt
(
    primary_id                      TEXT PRIMARY KEY,
    id                              BIGINT,
    proc_inst_id                    TEXT,
    task_id                         TEXT,
    activity_id                     TEXT,
    cid                             INTEGER,
    ctrl_info                       TEXT,
    prepare_phase_items             TEXT,
    vertical_product                TEXT,
    tool_name                       TEXT,
    tool_version                    TEXT,
    target_product_version          TEXT,
    target_small_product_version    TEXT,
    deployment_order                TEXT,
    product_name                    TEXT,
    current_product_version         TEXT,
    create_time                     TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL,
    update_time                     TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL
);

-- create index
CREATE INDEX idx_dwd_change_ctrl_table_rt_proc_inst_id on dwd_change_ctrl_table_rt(proc_inst_id);

-- add comments
COMMENT ON TABLE dwd_change_ctrl_table_rt IS '变更控制表';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.proc_inst_id IS '流程实例id';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.task_id IS '任务id';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.ctrl_info IS '变更控制信息';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.prepare_phase_items IS '准备阶段项';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.vertical_product IS '垂直产品';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.tool_name IS '组件名称';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.tool_version IS '组件版本';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.target_product_version IS '目标产品版本';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.target_small_product_version IS '目标小版本号';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.deployment_order IS '部署顺序';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.product_name IS '产品名称';
COMMENT ON COLUMN dwd_change_ctrl_table_rt.current_product_version IS '现场产品版本';



CREATE TABLE dwd_change_proc_inst
(
    proc_inst_id               VARCHAR(255) PRIMARY KEY,
    proc_def_id                VARCHAR(255) DEFAULT '',
    start_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    end_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    duration                   BIGINT DEFAULT 0,
    start_user_id              VARCHAR(255) DEFAULT '',
    delete_reason              VARCHAR(4000) DEFAULT '',
    tenant_id                  INT DEFAULT 0,
    name                       VARCHAR(255) DEFAULT '',
    rev                        INT DEFAULT 0
);

CREATE TABLE dwd_change_operate_log_rt
(
    id                            BIGINT PRIMARY KEY,
    proc_inst_id                  VARCHAR(255) DEFAULT '',
    task_id                       VARCHAR(255) DEFAULT '',
    activity_id                   VARCHAR(255) DEFAULT '',
    create_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    update_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    action                        VARCHAR(255) DEFAULT '',
    operator                      VARCHAR(255) DEFAULT '',
    reason_class                  VARCHAR(255) DEFAULT '',
    comment                       TEXT,
    cid INT                       DEFAULT 0,
    remark                        TEXT,
    node                          VARCHAR(255) DEFAULT ''
);


CREATE TABLE dwd_change_project_info_rt
(
    id                           BIGINT PRIMARY KEY,
    proc_inst_id                 VARCHAR(255)  DEFAULT '',
    task_id                      VARCHAR(255) DEFAULT '',
    activity_id                  VARCHAR(255) DEFAULT '',
    create_time                  TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    update_time                  TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    ltc_no                       VARCHAR(255) DEFAULT '',
    ltc_name                     VARCHAR(255) DEFAULT '',
    customer_cid                 VARCHAR(255) DEFAULT '',
    customer_name                VARCHAR(255) DEFAULT '',
    jid                          VARCHAR(255) DEFAULT '',
    judian_name                  VARCHAR(255) DEFAULT '',
    server_provider              VARCHAR(255) DEFAULT '',
    region                       VARCHAR(255) DEFAULT '',
    department                   VARCHAR(255) DEFAULT '',
    project_trade                VARCHAR(255) DEFAULT '',
    j_status                     VARCHAR(255) DEFAULT '',
    submitter                    VARCHAR(255) DEFAULT '',
    additional_proposal          VARCHAR(255) DEFAULT '',
    customer_authorization       VARCHAR(255) DEFAULT '',
    technology_authorization     VARCHAR(255) DEFAULT '',
    manange_authorization        VARCHAR(255) DEFAULT '',
    executor                     VARCHAR(255) DEFAULT '',
    custom_acceptor              VARCHAR(255) DEFAULT '',
    channel                      INT DEFAULT 0,
    materials_rrl                TEXT,
    project_desc                 TEXT,
    source                       VARCHAR(255) DEFAULT '',
    ctype                        VARCHAR(255) DEFAULT '',
    emergencylevel               INT DEFAULT 0,
    priority                     INT DEFAULT 0,
    risk                         INT DEFAULT 0,
    impact                       INT DEFAULT 0,
    state                        INT DEFAULT 0,
    product_name                 VARCHAR(255) DEFAULT '',
    phase                        INT DEFAULT 0,
    reason_class                 VARCHAR(255) DEFAULT '',
    product_version              VARCHAR(255) DEFAULT '',
    change_id                    VARCHAR(255) DEFAULT '',
    groupid                      VARCHAR(255) DEFAULT ''
);

CREATE TABLE dwd_change_ru_task
(
    id                           VARCHAR(255) PRIMARY KEY,
    rev                          BIGINT DEFAULT 0,
    execution_id                 VARCHAR(255) DEFAULT '',
    proc_inst_id                 VARCHAR(255) DEFAULT '',
    proc_def_id                  VARCHAR(255) DEFAULT '',
    task_def_id                  VARCHAR(255) DEFAULT '',
    scope_id                     VARCHAR(255) DEFAULT '',
    sub_scope_id                 VARCHAR(255) DEFAULT '',
    scope_type                   VARCHAR(255) DEFAULT '',
    scope_definition_id          VARCHAR(255) DEFAULT '',
    propagated_stage_inst_id     VARCHAR(255) DEFAULT '',
    name                         VARCHAR(255) DEFAULT '',
    parent_task_id               VARCHAR(255) DEFAULT '',
    description                  VARCHAR(255) DEFAULT '',
    task_def_key                 VARCHAR(255) DEFAULT '',
    owner                        VARCHAR(255) DEFAULT '',
    assignee                     VARCHAR(255) DEFAULT '',
    delegation                   VARCHAR(255) DEFAULT '',
    priority                     BIGINT DEFAULT 0,
    create_time                  TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    due_date                     TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    category                     VARCHAR(255) DEFAULT '',
    suspension_state             BIGINT DEFAULT 0,
    tenant_id                    VARCHAR(255) DEFAULT '',
    form_key                     VARCHAR(255) DEFAULT '',
    claim_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    is_count_enabled             INT DEFAULT 0,
    var_count                    BIGINT DEFAULT 0,
    id_link_count                BIGINT DEFAULT 0,
    sub_task_count               BIGINT DEFAULT 0
);

CREATE TABLE dwd_change_task_inst_rt
(
    id                         VARCHAR(255) PRIMARY KEY,
    rev                        BIGINT DEFAULT 0,
    proc_def_id                VARCHAR(255) DEFAULT '',
    task_def_id                VARCHAR(255) DEFAULT '',
    task_def_key               VARCHAR(255) DEFAULT '',
    proc_inst_id               VARCHAR(255) DEFAULT '',
    execution_id               VARCHAR(255) DEFAULT '',
    scope_id                   VARCHAR(255) DEFAULT '',
    sub_scope_id               VARCHAR(255) DEFAULT '',
    scope_type                 VARCHAR(255) DEFAULT '',
    scope_definition_id        VARCHAR(255) DEFAULT '',
    propagated_stage_inst_id   VARCHAR(255) DEFAULT '',
    name                       VARCHAR(255) DEFAULT '',
    parent_task_id             VARCHAR(255) DEFAULT '',
    description                VARCHAR(4000) DEFAULT '',
    owner                      VARCHAR(255) DEFAULT '',
    assignee                   VARCHAR(255) DEFAULT '',
    start_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    claim_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    end_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    duration                   BIGINT DEFAULT 0,
    delete_reason              VARCHAR(4000) DEFAULT '',
    priority                   BIGINT DEFAULT 0,
    due_date                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    form_key                   VARCHAR(255) DEFAULT '',
    category                   VARCHAR(255) DEFAULT '',
    tenant_id                  VARCHAR(255) DEFAULT '',
    last_updated_time          TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL
);

CREATE TABLE dwd_change_var_info
(
    proc_inst_id                VARCHAR(255) PRIMARY KEY ,
    record_update_time          TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    change_id                   VARCHAR(1024) DEFAULT '',
    uin                         VARCHAR(1024) DEFAULT '',
    j_stage                     VARCHAR(1024) DEFAULT '',
    is_verified                 VARCHAR(1024) DEFAULT '',
    unverified_reason           TEXT,
    inspect_report_1            TEXT,
    inspect_report_2            TEXT,
    inspect_report_1_info       TEXT,
    inspect_report_2_info       TEXT,
    product_name                TEXT,
    product_list_second         VARCHAR(1024) DEFAULT '',
    channel                     VARCHAR(1024) DEFAULT '',
    state                       VARCHAR(1024) DEFAULT '',
    source                      VARCHAR(1024) DEFAULT '',
    title                       VARCHAR(2048) DEFAULT '',
    c_type                      VARCHAR(1024) DEFAULT '',
    tapd_url                    TEXT,
    materials_url               TEXT,
    emergency_level             VARCHAR(1024) DEFAULT '',
    priority                    VARCHAR(1024) DEFAULT '',
    risk                        VARCHAR(1024) DEFAULT '',
    impact                      VARCHAR(1024) DEFAULT '',
    key_component               VARCHAR(1024) DEFAULT '',
    impact_scope                VARCHAR(1024) DEFAULT '' ,
    request_impact              VARCHAR(1024) DEFAULT ''  ,
    net_break                   VARCHAR(1024) DEFAULT ''   ,
    business_break              VARCHAR(1024) DEFAULT '' ,
    description                 TEXT,
    initiator                   VARCHAR(1024) DEFAULT '' ,
    plan_range                  VARCHAR(1024),
    real_range                  VARCHAR(1024),
    authorization_credential    TEXT,
    plan_execute_corp           VARCHAR(1024) DEFAULT '',
    plan_verify_corp            VARCHAR(1024) DEFAULT '',
    plan_start                  VARCHAR(1024) DEFAULT '',
    plan_end                    VARCHAR(1024) DEFAULT '',
    real_execute_corp           VARCHAR(1024) DEFAULT '',
    real_verify_corp            VARCHAR(1024) DEFAULT '',
    real_start                  VARCHAR(1024) DEFAULT '',
    real_end                    VARCHAR(1024) DEFAULT '',
    plan_execute_corp_company   VARCHAR(1024) DEFAULT '',
    plan_verify_corp_company    VARCHAR(1024) DEFAULT '',
    real_execute_corp_company   VARCHAR(1024) DEFAULT '',
    real_verify_corp_company    VARCHAR(1024) DEFAULT '',
    deploy_mode                 VARCHAR(1024) DEFAULT ''
);


alter table dwd_change_var_info
    add column is_invalid VARCHAR(1024) DEFAULT '' ;
comment on column dwd_change_var_info.is_invalid is '变更单是否有效';

alter table dwd_change_var_info
    add column phase varchar(1024)  default '';
comment on column dwd_change_var_info.phase is '阶段';


CREATE TABLE dim_antool_t_users_rt
(
    id                         TEXT PRIMARY KEY,
    company_id                 BIGINT DEFAULT 0,
    user_id                    VARCHAR(1024) DEFAULT '',
    user_name                  VARCHAR(1024) DEFAULT '',
    bind_user_id               VARCHAR(1024) DEFAULT '',
    phone                      VARCHAR(1024) DEFAULT '',
    depth                      VARCHAR(1024) DEFAULT '',
    is_active                  INT DEFAULT 0,
    is_valid                   INT DEFAULT 0,
    is_admin                   INT DEFAULT 0,
    last_login_time            TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    create_time                TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    creator                    VARCHAR(1024) DEFAULT '',
    modify_time                TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    modifier                   VARCHAR(1024) DEFAULT '',
    antool_token               VARCHAR(1024) DEFAULT '',
    lexiang_id                 VARCHAR(1024) DEFAULT '',
    antool_appid               VARCHAR(1024) DEFAULT '',
    antool_secret              VARCHAR(1024) DEFAULT '',
    status                     INT DEFAULT 0,
    open_user_id               VARCHAR(1024) DEFAULT ''
);

CREATE TABLE dim_antool_t_companies_rt
(
    id                          BIGINT PRIMARY KEY,
    name                        VARCHAR(255) DEFAULT '',
    short_name                  VARCHAR(255) DEFAULT '',
    phone                       VARCHAR(255) DEFAULT '',
    manager                     VARCHAR(255) DEFAULT '',
    uin                         VARCHAR(255) DEFAULT '',
    notice                      VARCHAR(255) DEFAULT '',
    wechat_group                VARCHAR(255) DEFAULT '',
    wework_group                VARCHAR(255) DEFAULT '',
    disabled                    INT DEFAULT 0,
    create_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    creator                     VARCHAR(255) DEFAULT '',
    modify_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    modifier                    VARCHAR(255) DEFAULT '',
    corp_id                     VARCHAR(255) DEFAULT '',
    company_uin                 VARCHAR(255) DEFAULT '',
    permanent_code              VARCHAR(255) DEFAULT '',
    agentid                     VARCHAR(255) DEFAULT '',
    company_info                TEXT,
    asp_company_name            VARCHAR(255) DEFAULT '',
    company_data_source         VARCHAR(255) DEFAULT '',
    open_corp_id                VARCHAR(255) DEFAULT ''
);


CREATE TABLE dwd_change_var_inst
(
    proc_inst_id         VARCHAR(255),
    var_key              VARCHAR(255) DEFAULT '',
    var_value            TEXT DEFAULT '',
    rev                  INT DEFAULT 0,
    CONSTRAINT pk_dwd_change_var_inst PRIMARY KEY (proc_inst_id, var_key)
);
create index index_dwd_change_var_inst_var_key on dwd_change_var_inst(var_key);


CREATE TABLE dwm_change_tapd_info
(
    proc_inst_id               VARCHAR(255),
    tapd_info                TEXT,
    tapd_url                 TEXT,
    bug_id              VARCHAR(255) DEFAULT '',
    CONSTRAINT pk_dwm_change_tapd_info PRIMARY KEY (proc_inst_id, bug_id)
);


CREATE TABLE dwm_change_vertical_product_info
(
    proc_inst_id               VARCHAR(255),
    product_list_second                TEXT,
    vertical_product              VARCHAR(255) DEFAULT '',
    CONSTRAINT pk_dwm_change_vertical_product_info PRIMARY KEY (proc_inst_id, vertical_product)
);


CREATE INDEX index_dwd_change_var_info_uin ON dwd_change_var_info(uin);
CREATE INDEX index_dwd_change_var_info_priority ON dwd_change_var_info(priority);
CREATE INDEX index_dwd_change_var_info_initiator ON dwd_change_var_info(initiator);
CREATE INDEX index_dwd_change_ru_task_proc_id ON dwd_change_ru_task (proc_inst_id);
CREATE INDEX index_dwd_change_ru_task_assignee ON dwd_change_ru_task (assignee);
CREATE INDEX index_dwd_change_ru_task_name ON dwd_change_ru_task (name);
CREATE INDEX index_dim_antool_t_users_rt_user_id ON dim_antool_t_users_rt(user_id);
CREATE INDEX index_dwd_change_task_inst_rt_assignee ON dwd_change_task_inst_rt(assignee);
CREATE INDEX index_dwd_change_task_inst_rt_end_time ON dwd_change_task_inst_rt(end_time);
CREATE INDEX index_dwd_change_task_inst_rt_proc_id ON dwd_change_task_inst_rt(proc_inst_id);
CREATE INDEX index_dwd_change_var_info_product_name ON dwd_change_var_info(product_name);
CREATE INDEX index_dwd_change_var_info_change_id ON dwd_change_var_info(change_id);
CREATE INDEX index_dwd_change_project_info_rt_name ON dwd_change_project_info_rt(ltc_name);
CREATE INDEX index_dwd_change_project_info_rt_ltc_no ON dwd_change_project_info_rt(ltc_no);
CREATE UNIQUE INDEX index_dwd_change_project_info_rt_proc_id ON dwd_change_project_info_rt(proc_inst_id);
CREATE INDEX index_dwd_change_project_info_rt_server_provider ON dwd_change_project_info_rt(server_provider);

ALTER TABLE public.dwd_change_var_inst REPLICA IDENTITY FULL;


CREATE OR REPLACE VIEW dwd_change_ru_task_view AS
SELECT task.id,
       task.rev,
       task.execution_id,
       task.proc_inst_id,
       task.proc_def_id,
       task.task_def_id,
       task.scope_id,
       task.sub_scope_id,
       task.scope_type,
       task.scope_definition_id,
       task.propagated_stage_inst_id,
       task.name,
       task.parent_task_id,
       task.description,
       task.task_def_key,
       task.owner,
       task.assignee,
       task.delegation,
       task.priority,
       task.create_time,
       task.due_date,
       task.category,
       task.suspension_state,
       task.tenant_id,
       task.form_key,
       task.claim_time,
       task.is_count_enabled,
       task.var_count,
       task.id_link_count,
       task.sub_task_count,
       u.id AS uid,
       u.user_name
FROM dwd_change_ru_task task
LEFT JOIN dim_antool_t_users_rt u ON task.assignee::text = u.id;




CREATE OR REPLACE VIEW dwd_change_base_info_view AS
SELECT t.proc_inst_id,
       t.create_time,
       t.end_time,
       t.ltc_no,
       t.ltc_name,
       t.customer_name,
       t.customer_cid,
       t.judian_name,
       t.jid,
       t.server_provider,
       t.region,
       t.department,
       t.project_trade,
       t.j_status,
       t.product_version,
       t.current_node_name,
       t.groupid,
       CASE
           WHEN t3.vertical_product IS NOT NULL THEN t3.vertical_product
           ELSE replace(t1.vertical_product::text, '"'::text, ''::text)
           END AS vertical_product,
       t3.tool_name,
       t3.tool_version,
       t3.target_product_version,
       t3.target_small_product_version,
       t3.deployment_order,
       t3.current_product_version,
       t.change_id,
       t.uin,
       t.j_stage,
       t.is_verified,
       t.unverified_reason,
       t.inspect_report_1,
       t.inspect_report_2,
       t.inspect_report_1_info,
       t.inspect_report_2_info,
       t.product_name,
       t.product_list_second,
       t.channel,
       t.state,
       t.source,
       t.title,
       t.c_type,
       t.tapd_url,
       t.materials_url,
       t.emergency_level,
       t.priority,
       t.risk,
       t.impact,
       t.key_component,
       t.impact_scope,
       t.request_impact,
       t.net_break,
       t.business_break,
       t.description,
       t.initiator,
       t.plan_range,
       t.real_range,
       t.authorization_credential,
       t.plan_execute_corp,
       t.plan_verify_corp,
       t.plan_start,
       t.plan_end,
       t.real_execute_corp,
       t.real_verify_corp,
       t.real_start,
       t.real_end,
       t.plan_execute_corp_company,
       t.plan_verify_corp_company,
       t.real_execute_corp_company,
       t.real_verify_corp_company,
       t.deploy_mode,
       t2.tapd_info,
       t2.bug_id,
       t.priority_num,
       t.phase,
       t.current_task_def_key AS task_def_key,
       t.current_assign AS current_node_user_name_array,
       t.current_assign_rtx AS current_node_user_name_v2_array,
       t.vertical_product_array,
       t.current_product_version_array,
       t.target_small_product_version_array
FROM dwm_change_base_info t
LEFT JOIN dwm_change_vertical_product_info t1 ON t.proc_inst_id = t1.proc_inst_id
LEFT JOIN dwm_change_tapd_info t2 ON t.proc_inst_id = t2.proc_inst_id
LEFT JOIN dwd_change_ctrl_table_rt t3 ON t.proc_inst_id::text = t3.proc_inst_id;




-- add by camille
-- 2024-03-28 15:18:17
CREATE TABLE dwm_change_ticket_url
(
    proc_inst_id               VARCHAR(255),
    ticket_url_list            TEXT,
    ticket_url                 TEXT,
    ticket_id                  VARCHAR(255) DEFAULT '',
    CONSTRAINT pk_dwm_change_ticket_url PRIMARY KEY (proc_inst_id, ticket_url)
);

alter table dwd_change_var_info
    add column trade VARCHAR(1024) DEFAULT '' ,
    add column ticket_url_list text ;
comment on column dwd_change_var_info.trade is '归属行业';
comment on column dwd_change_var_info.ticket_url_list is '事件单链接';