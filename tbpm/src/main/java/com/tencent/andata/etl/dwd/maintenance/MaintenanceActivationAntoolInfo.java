package com.tencent.andata.etl.dwd.maintenance;


import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.CDCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import com.tencent.andata.etl.tablemap.maintenance.MaintenanceActivationAntoolMapping;
import java.util.Properties;
import com.tencent.andata.etl.sql.maintenance.MaintenanceActivationAntoolSql;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

@Builder
public class MaintenanceActivationAntoolInfo {

    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        // 出库PG表注册
        DatabaseConf dataWareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        ObjectMapper mapper = new ObjectMapper();
        // pgTable mapping to flinkTable
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(MaintenanceActivationAntoolMapping.MAINTENANCE_ACTIVATION_PG_TABLE_TO_FLINK_TABLE,
                        ArrayNode.class), PGSQL, tEnv
        );

        // PG CDC ODS表注册
        DatabaseConf pvDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "antool"))
                .build();
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("pgsql_source_dwd_maintenance_activation_var_inst")
                        .tableBuilderStrategy(
                                new CDCTableBuilderStrategy(
                                        "dwd_maintenance_activation_var_inst",
                                        PGSQL,
                                        pvDBConf, ""
                                )
                        )
                        .processTimeFiled("process_time")
                        .build()
        );

        // dwd_maintenance_activation_var_info
        tEnv.createTemporaryView("dwd_maintenance_activation_var_info_view",
                tEnv.sqlQuery(MaintenanceActivationAntoolSql.QUERY_DWD_MAINTENANCE_ACTIVATION_VAR_INFO_TABLE_SQL));
        // dwd_maintenance_activation_judian_info
        tEnv.createTemporaryView("dwd_maintenance_activation_judian_info_view",
                tEnv.sqlQuery(MaintenanceActivationAntoolSql.QUERY_DWD_MAINTENANCE_ACTIVATION_JUDIAN_INFO_TABLE_SQL));

        StatementSet stmtSet = flinkEnv.stmtSet();

        stmtSet.addInsertSql(insertIntoSql(
                        "dwd_maintenance_activation_var_info_view",
                        "pgsql_sink_dwd_maintenance_activation_var_info",
                        tEnv.from("pgsql_sink_dwd_maintenance_activation_var_info"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "dwd_maintenance_activation_judian_info_view",
                        "pgsql_sink_dwd_maintenance_activation_judian_info",
                        tEnv.from("pgsql_sink_dwd_maintenance_activation_judian_info"),
                        PGSQL));
    }
}
