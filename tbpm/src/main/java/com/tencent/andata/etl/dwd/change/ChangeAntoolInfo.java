package com.tencent.andata.etl.dwd.change;

import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.CDCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import java.util.Properties;
import static com.tencent.andata.etl.tablemap.change.ChangeAntoolMapping.PG_TABLE_TO_FLINK_TABLE;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.etl.sql.change.ChangeAntoolSql.QUERY_DWD_CHANGE_VAR_INFO_TABLE_SQL;
import static com.tencent.andata.etl.sql.change.ChangeAntoolSql.QUERY_DWM_CHANGE_TAPD_INFO_TABLE_SQL;
import static com.tencent.andata.etl.sql.change.ChangeAntoolSql.QUERY_DWM_CHANGE_VERTICAL_PRODUCT_TABLE_SQL;
import static com.tencent.andata.etl.sql.change.ChangeAntoolSql.QUERY_DWM_CHANGE_TICKET_URL_TABLE_SQL;

@Builder
public class ChangeAntoolInfo {

    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        ObjectMapper mapper = new ObjectMapper();
        // 出库PG表注册
        DatabaseConf dataWareDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();
        // pgTable mapping to flinkTable
        TableUtils.rdbTable2FlinkTable(
                dataWareDBConf,
                mapper.readValue(PG_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                PGSQL,
                tEnv
        );
        // PG CDC ODS表注册
        DatabaseConf pvDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "antool"))
                .build();
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("pgsql_source_dwd_change_var_inst")
                        .tableBuilderStrategy(
                                new CDCTableBuilderStrategy(
                                        "dwd_change_var_inst",
                                        PGSQL,
                                        pvDBConf, ""
                                )
                        )
                        .processTimeFiled("process_time")
                        .build()
        );
        // dwd_change_var_info
        tEnv.createTemporaryView("dwd_change_var_info_view", tEnv.sqlQuery(QUERY_DWD_CHANGE_VAR_INFO_TABLE_SQL));
        // dwm_change_tapd_info
        tEnv.createTemporaryView("dwm_change_tapd_info_view", tEnv.sqlQuery(QUERY_DWM_CHANGE_TAPD_INFO_TABLE_SQL));
        // dwm_change_vertical_product_info
        tEnv.createTemporaryView("dwm_change_vertical_product_info_view",
                tEnv.sqlQuery(QUERY_DWM_CHANGE_VERTICAL_PRODUCT_TABLE_SQL));
        // dwm_change_ticket_url
        tEnv.createTemporaryView("dwm_change_ticket_url_view", tEnv.sqlQuery(QUERY_DWM_CHANGE_TICKET_URL_TABLE_SQL));

        StatementSet stmtSet = flinkEnv.stmtSet();

        stmtSet.addInsertSql(insertIntoSql(
                        "dwd_change_var_info_view",
                        "pgsql_sink_dwd_change_var_info",
                        tEnv.from("pgsql_sink_dwd_change_var_info"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "dwm_change_tapd_info_view",
                        "pgsql_sink_dwm_change_tapd_info",
                        tEnv.from("pgsql_sink_dwm_change_tapd_info"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "dwm_change_ticket_url_view",
                        "pgsql_sink_dwm_change_ticket_url",
                        tEnv.from("pgsql_sink_dwm_change_ticket_url"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "dwm_change_vertical_product_info_view",
                        "pgsql_sink_dwm_change_vertical_product_info",
                        tEnv.from("pgsql_sink_dwm_change_vertical_product_info"),
                        PGSQL));
    }
}
