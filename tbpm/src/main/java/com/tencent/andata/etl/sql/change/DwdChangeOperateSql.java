package com.tencent.andata.etl.sql.change;

public class DwdChangeOperateSql {

    public static String QUERY_69_CHANGE_OPERATE_LOG_SQL = ""
            + "SELECT \n"
            + "  CAST(id_ AS BIGINT) AS id,\n"
            + "  proc_inst_id_ AS proc_inst_id,\n"
            + "  task_id_ AS task_id,\n"
            + "  activity_id_ AS activity_id,\n"
            + "  `create_time_` AS create_time,\n"
            + "  `update_time_` AS update_time,\n"
            + "  `Action` AS `action`,\n"
            + "  `Operator` AS operator,\n"
            + "  `ReasonClass` AS reason_class,\n"
            + "  `Comment` AS `comment`,\n"
            + "  CAST(`Cid` AS BIGINT) AS cid,\n"
            + "  `Remark` AS `remark`,\n"
            + "  `Node` AS `node`\n"
            + "FROM mysql_source_69_changeOperateLog /*+ OPTIONS('server-id'='1001-1201') */";


    public static String QUERY_69_CHANGE_INFO_SQL = ""
            + "SELECT \n"
            + "  CAST(id_ AS BIGINT) AS id,\n"
            + "  proc_inst_id_ AS proc_inst_id,\n"
            + "  task_id_ AS task_id,\n"
            + "  activity_id_ AS activity_id,\n"
            + "  `create_time_` AS create_time,\n"
            + "  `update_time_` AS update_time,\n"
            + "  `LtcNo` AS `ltc_no`,\n"
            + "  `LtcName` AS ltc_name,\n"
            + "  `CustomerCid` AS customer_cid,\n"
            + "  `CustomerName` AS `customer_name`,\n"
            + "  `Jid` AS `jid`,\n"
            + "  `JudianName` AS `judian_name`,\n"
            + "  `ServerProvider` AS `server_provider`,\n"
            + "  `Region` AS `region`,\n"
            + "  `Department` AS `department`,\n"
            + "  `ProjectTrade` AS `project_trade`,\n"
            + "  `JStatus` AS `j_status`,\n"
            + "  `Submitter` AS `submitter`,\n"
            + "  `AdditionalProposal` AS `additional_proposal`,\n"
            + "  `CustomerAuthorization` AS `customer_authorization`,\n"
            + "  `TechnologyAuthorization` AS `technology_authorization`,\n"
            + "  `ManangeAuthorization` AS `manange_authorization`,\n"
            + "  `Executor` AS `executor`,\n"
            + "  `CustomAcceptor` AS `custom_acceptor`,\n"
            + "  CAST(`Channel` AS BIGINT) AS channel,\n"
            + "  `MaterialsUrl` AS `materials_rrl`,\n"
            + "  `Desc` AS `project_desc`,\n"
            + "  `Source` AS `source`,\n"
            + "  `CType` AS `ctype`,\n"
            + "  CAST(`EmergencyLevel` AS BIGINT) AS emergencylevel,\n"
            + "  CAST(`Priority` AS BIGINT) AS priority,\n"
            + "  CAST(`Risk` AS BIGINT) AS risk,\n"
            + "  CAST(`Impact` AS BIGINT) AS impact,\n"
            + "  CAST(`State` AS BIGINT) AS state,\n"
            + "  `ProductName` AS `product_name`,\n"
            + "  CAST(`Phase` AS BIGINT) AS phase,\n"
            + "  `ReasonClass` AS `reason_class`,\n"
            + "  `ProductVersion` AS `product_version`,\n"
            + "  `ChangeID` AS `change_id`,\n"
            + "  `GroupId` AS `groupid`\n"
            + "FROM mysql_source_69_changeinfo /*+ OPTIONS('server-id'='1202-1302') */";


    public static String QUERY_69_CHANGE_CTRL_TABLE_SQL = ""
            + "SELECT \n"
            + "  CAST(id_ AS BIGINT) AS id,\n"
            + "  proc_inst_id_ AS proc_inst_id,\n"
            + "  task_id_ AS task_id,\n"
            + "  activity_id_ AS activity_id,\n"
            + "  `create_time_` AS create_time,\n"
            + "  `update_time_` AS update_time,\n"
            + "  CAST(Cid AS BIGINT) AS cid,\n"
            + "  `Content` AS content\n"
            + "FROM mysql_source_69_changeCtrlTable /*+ OPTIONS('server-id'='1303-1403') */";


    public static String QUERY_CHANGE_CTRL_TABLE_SQL = ""
            + "WITH t1 AS (\n"
            + "    SELECT \n"
            + "        CAST(`id_` AS BIGINT) AS id,\n"
            + "        `proc_inst_id_` AS proc_inst_id,\n"
            + "        `task_id_` AS task_id,\n"
            + "        `activity_id_` AS activity_id,\n"
            + "        `create_time_` AS create_time,\n"
            + "        `update_time_` AS update_time,\n"
            + "        CAST(`Cid` AS BIGINT) AS cid,\n"
            + "        x.`spell`,\n"
            + "        MD5(x.`spell`) AS index\n"
            + "    FROM mysql_source_69_changeCtrlTable /*+ OPTIONS('server-id'='1404-1504') */\n"
            + "    CROSS JOIN UNNEST(json_list_to_array(Content)) AS x (spell)\n"
            + "),\n"
            + "t2 AS(\n"
            + "    SELECT\n"
            + "        t1.id,\n"
            + "        t1.proc_inst_id,\n"
            + "        t1.task_id,\n"
            + "        t1.activity_id,\n"
            + "        t1.create_time,\n"
            + "        t1.update_time,\n"
            + "        t1.cid,\n"
            + "        t1.spell AS content_info,\n"
            + "        t1.index,\n"
            + "        y.phase_item\n"
            + "    FROM t1\n"
            + "    CROSS JOIN UNNEST(json_list_to_array(get_json_object(t1.spell, '$.StartPhaseItems'))) "
            + "    AS y (phase_item)\n"
            + "),\n"
            + "t3 AS (\n"
            + "    SELECT \n"
            + "        CAST(t2.id AS STRING) || '-' || CAST(t2.index AS STRING) AS primary_id,\n"
            + "        t2.proc_inst_id,\n"
            + "        t2.id,\n"
            + "        t2.task_id,\n"
            + "        t2.activity_id,\n"
            + "        t2.cid,\n"
            + "        t2.content_info AS ctrl_info,\n"
            + "        t2.create_time,\n"
            + "        t2.update_time,\n"
            + "        get_json_object(t2.content_info,'$.PreparePhaseItems') AS prepare_phase_items,\n"
            + "        CASE\n"
            + "            WHEN get_json_object(t2.phase_item,'$.BaseInfo') = '垂直产品'\n"
            + "                THEN get_json_object(t2.phase_item,'$.Detail')\n"
            + "            ELSE ''\n"
            + "        END AS vertical_product,\n"
            + "        CASE\n"
            + "            WHEN get_json_object(t2.phase_item,'$.BaseInfo') = '组件名称'\n"
            + "                THEN get_json_object(t2.phase_item,'$.Detail')\n"
            + "            ELSE ''\n"
            + "        END AS tool_name,\n"
            + "        CASE\n"
            + "            WHEN get_json_object(t2.phase_item,'$.BaseInfo') = '组件版本'\n"
            + "                THEN get_json_object(t2.phase_item,'$.Detail')\n"
            + "            ELSE ''\n"
            + "        END AS tool_version,\n"
            + "        CASE\n"
            + "            WHEN get_json_object(t2.phase_item,'$.BaseInfo') = '目标产品版本'\n"
            + "                THEN get_json_object(t2.phase_item,'$.Detail')\n"
            + "            ELSE ''\n"
            + "        END AS target_product_version,\n"
            + "        CASE\n"
            + "            WHEN get_json_object(t2.phase_item,'$.BaseInfo') = '目标小版本号'\n"
            + "                THEN get_json_object(t2.phase_item,'$.Detail')\n"
            + "            ELSE ''\n"
            + "        END AS target_small_product_version,\n"
            + "        CASE\n"
            + "            WHEN get_json_object(t2.phase_item,'$.BaseInfo') = '部署顺序'\n"
            + "                THEN get_json_object(t2.phase_item,'$.Detail')\n"
            + "            ELSE ''\n"
            + "        END AS deployment_order,\n"
            + "        CASE\n"
            + "            WHEN get_json_object(t2.phase_item,'$.BaseInfo') = '产品名称'\n"
            + "                THEN get_json_object(t2.phase_item,'$.Detail')\n"
            + "            ELSE ''\n"
            + "        END AS product_name,\n"
            + "        CASE\n"
            + "            WHEN get_json_object(t2.phase_item,'$.BaseInfo') = '现场产品版本'\n"
            + "                THEN get_json_object(t2.phase_item,'$.Detail')\n"
            + "            ELSE ''\n"
            + "        END AS current_product_version\n"
            + "    FROM t2\n"
            + ")\n"
            + "SELECT \n"
            + "    t3.primary_id,\n"
            + "    MAX(t3.id) AS id,\n"
            + "    MAX(t3.proc_inst_id) AS proc_inst_id,\n"
            + "    MAX(t3.task_id) AS task_id,\n"
            + "    MAX(t3.activity_id) AS activity_id,\n"
            + "    MAX(t3.cid) AS cid,\n"
            + "    MAX(t3.ctrl_info) AS ctrl_info,\n"
            + "    MAX(t3.prepare_phase_items) AS prepare_phase_items,\n"
            + "    MAX(t3.vertical_product) AS vertical_product,\n"
            + "    MAX(t3.tool_name) AS tool_name,\n"
            + "    MAX(t3.tool_version) AS tool_version,\n"
            + "    MAX(t3.target_product_version) AS target_product_version,\n"
            + "    MAX(t3.target_small_product_version) AS target_small_product_version,\n"
            + "    MAX(t3.deployment_order) AS deployment_order,\n"
            + "    MAX(t3.product_name) AS product_name,\n"
            + "    MAX(t3.current_product_version) AS current_product_version,\n"
            + "    MAX(t3.create_time) AS create_time,\n"
            + "    MAX(t3.update_time) AS update_time\n"
            + "FROM t3\n"
            + "GROUP BY t3.primary_id";
}
