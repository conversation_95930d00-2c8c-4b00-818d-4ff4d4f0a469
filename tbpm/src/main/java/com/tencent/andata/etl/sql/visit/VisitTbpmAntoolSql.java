package com.tencent.andata.etl.sql.visit;

public class VisitTbpmAntoolSql {


    public static String QUERY_DWD_VISIT_RU_TASK_SQL = ""
            + "SELECT ID_ AS id,\n"
            + "       REV_ AS rev,\n"
            + "       EXECUTION_ID_ AS execution_id,\n"
            + "       PROC_INST_ID_ AS proc_inst_id,\n"
            + "       PROC_DEF_ID_ AS proc_def_id,\n"
            + "       TASK_DEF_ID_ AS task_def_id,\n"
            + "       SCOPE_ID_ AS scope_id,\n"
            + "       SUB_SCOPE_ID_ AS sub_scope_id,\n"
            + "       SCOPE_TYPE_ AS scope_type,\n"
            + "       SCOPE_DEFINITION_ID_ AS scope_definition_id,\n"
            + "       PROPAGATED_STAGE_INST_ID_ AS propagated_stage_inst_id,\n"
            + "       NAME_ AS name,\n"
            + "       PARENT_TASK_ID_ AS parent_task_id,\n"
            + "       DESCRIPTION_ AS description,\n"
            + "       TASK_DEF_KEY_ AS task_def_key,\n"
            + "       OWNER_ AS owner,\n"
            + "       ASSIGNEE_ AS assignee,\n"
            + "       DELEGATION_ AS delegation,\n"
            + "       PRIORITY_ AS priority,\n"
            + "       CREATE_TIME_ AS create_time,\n"
            + "       DUE_DATE_ AS due_date,\n"
            + "       CATEGORY_ AS category,\n"
            + "       SUSPENSION_STATE_ AS suspension_state,\n"
            + "       TENANT_ID_ AS tenant_id,\n"
            + "       FORM_KEY_ AS form_key,\n"
            + "       CLAIM_TIME_ AS claim_time,\n"
            + "       IS_COUNT_ENABLED_ AS is_count_enabled,\n"
            + "       VAR_COUNT_ AS var_count,\n"
            + "       ID_LINK_COUNT_ AS id_link_count,\n"
            + "       SUB_TASK_COUNT_ AS sub_task_count\n"
            + "FROM mysql_source_visit_ACT_RU_TASK /*+ OPTIONS('server-id'='6001-6100') */\n"
            + "WHERE TENANT_ID_='65'\n";



    public static String QUERY_DWD_VISIT_TASK_INST_SQL = ""
            + "SELECT ID_ AS id,\n"
            + "       REV_ AS rev,\n"
            + "       PROC_DEF_ID_ AS proc_def_id,\n"
            + "       TASK_DEF_ID_ AS task_def_id,\n"
            + "       TASK_DEF_KEY_ AS task_def_key,\n"
            + "       PROC_INST_ID_ AS proc_inst_id,\n"
            + "       EXECUTION_ID_ AS execution_id,\n"
            + "       SCOPE_ID_ AS scope_id,\n"
            + "       SUB_SCOPE_ID_ AS sub_scope_id,\n"
            + "       SCOPE_TYPE_ AS scope_type,\n"
            + "       SCOPE_DEFINITION_ID_ AS scope_definition_id,\n"
            + "       PROPAGATED_STAGE_INST_ID_ AS propagated_stage_inst_id,\n"
            + "       NAME_ AS name,\n"
            + "       PARENT_TASK_ID_ AS parent_task_id,\n"
            + "       DESCRIPTION_ AS description,\n"
            + "       OWNER_ AS owner,\n"
            + "       ASSIGNEE_ AS assignee,\n"
            + "       START_TIME_ AS start_time,\n"
            + "       CLAIM_TIME_ AS claim_time,\n"
            + "       END_TIME_ AS end_time,\n"
            + "       DURATION_ AS duration,\n"
            + "       DELETE_REASON_ AS delete_reason,\n"
            + "       PRIORITY_ AS priority,\n"
            + "       DUE_DATE_ AS due_date,\n"
            + "       FORM_KEY_ AS form_key,\n"
            + "       CATEGORY_ AS category,\n"
            + "       TENANT_ID_ AS tenant_id,\n"
            + "       LAST_UPDATED_TIME_\n AS last_updated_time\n"
            + "FROM mysql_source_visit_ACT_HI_TASKINST /*+ OPTIONS('server-id'='6101-6200') */\n"
            + "WHERE TENANT_ID_='65'\n";




    public static String QUERY_DWD_VISIT_PROC_INST_SQL = ""
            + "SELECT PROC_INST_ID_ AS proc_inst_id,\n"
            + "       PROC_DEF_ID_ AS proc_def_id,\n"
            + "       START_TIME_ AS start_time,\n"
            + "       END_TIME_ AS end_time,\n"
            + "       DURATION_ AS duration,\n"
            + "       START_USER_ID_ AS start_user_id,\n"
            + "       DELETE_REASON_ AS delete_reason,\n"
            + "       CAST(TENANT_ID_ AS INTEGER) AS tenant_id,\n"
            + "       NAME_ AS name,\n"
            + "       REV_ AS rev\n"
            + "FROM mysql_source_visit_ACT_HI_PROCINST /*+ OPTIONS('server-id'='6301-6400') */\n"
            + "WHERE TENANT_ID_='65'\n";



    public static String QUERY_DWD_VISIT_TBPM_ANTOOL_VAR_INST_SQL = ""
            + "WITH t1 AS (\n"
            + "    SELECT t2.ID_ AS id,\n"
            + "           t2.PROC_INST_ID_ AS proc_inst_id,\n"
            + "           t2.EXECUTION_ID_ AS execution_id,\n"
            + "           t2.TASK_ID_ AS task_id,\n"
            + "           t2.NAME_ AS name,\n"
            + "           t2.VAR_TYPE_ AS var_type,\n"
            + "           t2.SCOPE_ID_ AS scope_id,\n"
            + "           t2.SUB_SCOPE_ID_ AS sub_scope_id,\n"
            + "           t2.SCOPE_TYPE_ AS scope_type,\n"
            + "           t2.BYTEARRAY_ID_ AS bytearray_id,\n"
            + "           t2.DOUBLE_ AS `double`,\n"
            + "           t2.LONG_ AS `long`,\n"
            + "           t2.TEXT_ AS `text`,\n"
            + "           t2.TEXT2_ AS `text2`,\n"
            + "           t2.CREATE_TIME_ AS create_time,\n"
            + "           t2.LAST_UPDATED_TIME_ AS last_updated_time,\n"
            + "           t2.REV_ AS rev\n"
            + "    FROM mysql_source_visit_ACT_HI_VARINST /*+ OPTIONS('server-id'='6401-6500') */ t2\n"
            + "    INNER JOIN (\n"
            + "        SELECT PROC_INST_ID_\n"
            + "        FROM mysql_source_visit_ACT_HI_VARINST /*+ OPTIONS('server-id'='6501-6600') */\n"
            + "        WHERE NAME_ = 'visitId'\n"
            + "    ) t1 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_\n"
            + "    WHERE t2.NAME_ not like 'stage%'\n"
            + ")\n"
            + "SELECT\n"
            + "    t1.`id`,\n"
            + "    t1.`proc_inst_id`,\n"
            + "    t1.`execution_id`,\n"
            + "    t1.`task_id`,\n"
            + "    t1.`name`,\n"
            + "    t1.`var_type`,\n"
            + "    t1.`scope_id`,\n"
            + "    t1.`sub_scope_id`,\n"
            + "    t1.`scope_type`,\n"
            + "    t1.`bytearray_id`,\n"
            + "    t1.`double`,\n"
            + "    t1.`long`,\n"
            + "    t1.`text`,\n"
            + "    t1.`text2`,\n"
            + "    t3.`json`,\n"
            + "    t1.`create_time`,\n"
            + "    t1.`last_updated_time`,\n"
            + "    t1.`rev`\n"
            + "FROM t1\n"
            + "LEFT JOIN (\n"
            + "        SELECT `ID_`,\n"
            + "            udf_cast_jser_to_json(`BYTES_`, true) json \n"
            + "        FROM mysql_source_visit_ACT_GE_BYTEARRAY /*+ OPTIONS('server-id'='6601-6700') */\n"
            + ") t3 ON t1.`bytearray_id` = t3.`ID_`";


    public static String QUERY_DWD_VISIT_TBPM_ANTOOL_VAR_INST_INFO_SQL = ""
            + "SELECT \n"
            + "   proc_inst_id, \n"
            + "   name as var_key,\n"
            + "   udf_extract_tbpm_var_val(cast(var_type AS string), `double`, `long`, cast(text AS string), "
            + "   cast(text2 AS string), json) as var_value,\n"
            + "   rev\n"
            + "FROM dwd_visit_tbpm_antool_var_inst_view\n";



    public static String QUERY_DWD_VISIT_T_TASK_LOG_SQL = ""
            + "SELECT *\n"
            + "FROM mysql_source_visit_t_task_log /*+ OPTIONS('server-id'='6701-6800') */\n"
            + "WHERE tenant_id='65'\n";



    public static String QUERY_DWD_VISIT_ANDON_PV_SQL = ""
            + "SELECT\n"
            + "  value_of_primary_key,\n"
            + "  CAST(record_update_time AS TIMESTAMP) record_update_time,\n"
            + "  CAST(access_time AS TIMESTAMP) access_time,\n"
            + "  user AS user_id,\n"
            + "  company,\n"
            + "  first_module,\n"
            + "  second_module,\n"
            + "  channel,\n"
            + "  source_type,\n"
            + "  link,\n"
            + "  object_key,\n"
            + "  extra_params,\n"
            + "  get_json_object(extra_params,'$.id') AS id,\n"
            + "  get_json_object(extra_params,'$.type')AS task_type,\n"
            + "  get_json_object(extra_params,'$.chName')AS ch_name,\n"
            + "  get_json_object(extra_params,'$.companyName')AS company_name,\n"
            + "  get_json_object(extra_params,'$.posName')AS pos_name,\n"
            + "  get_json_object(extra_params,'$.userName')AS user_name\n"
            + "FROM pg_source_ods_andon_pv\n"
            + "WHERE first_module IN ('antool','服务360')\n"
            + "  AND second_module = '服务任务' AND COALESCE(extra_params, '')  <> ''";



    public static String QUERY_DIM_STAFF_ARCH_INFO_SQL = ""
            + "SELECT *\n"
            + "FROM iceberg_source_dim_staff_arch_info /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
            + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ ";
}
