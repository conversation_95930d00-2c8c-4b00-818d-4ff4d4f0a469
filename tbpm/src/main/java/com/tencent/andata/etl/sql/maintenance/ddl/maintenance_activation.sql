CREATE TABLE dwd_maintenance_activation_proc_inst
(
    proc_inst_id               VARCHAR(255) PRIMARY KEY,
    proc_def_id                VARCHAR(255) DEFAULT '',
    start_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    end_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    duration                   BIGINT DEFAULT 0,
    start_user_id              VARCHAR(255) DEFAULT '',
    delete_reason              VARCHAR(4000) DEFAULT '',
    tenant_id                  INT DEFAULT 0,
    name                       VARCHAR(255) DEFAULT '',
    rev                        INT DEFAULT 0
);

CREATE INDEX dwd_maintenance_activation_proc_inst_start_time ON dwd_maintenance_activation_proc_inst(start_time);

CREATE TABLE dwd_maintenance_activation_ru_task
(
    id                           VARCHAR(255) PRIMARY KEY,
    rev                          BIGINT DEFAULT 0,
    execution_id                 VARCHAR(255) DEFAULT '',
    proc_inst_id                 VARCHAR(255) DEFAULT '',
    proc_def_id                  VARCHAR(255) DEFAULT '',
    task_def_id                  VARCHAR(255) DEFAULT '',
    scope_id                     VARCHAR(255) DEFAULT '',
    sub_scope_id                 VARCHAR(255) DEFAULT '',
    scope_type                   VARCHAR(255) DEFAULT '',
    scope_definition_id          VARCHAR(255) DEFAULT '',
    propagated_stage_inst_id     VARCHAR(255) DEFAULT '',
    name                         VARCHAR(255) DEFAULT '',
    parent_task_id               VARCHAR(255) DEFAULT '',
    description                  VARCHAR(255) DEFAULT '',
    task_def_key                 VARCHAR(255) DEFAULT '',
    owner                        VARCHAR(255) DEFAULT '',
    assignee                     VARCHAR(255) DEFAULT '',
    delegation                   VARCHAR(255) DEFAULT '',
    priority                     BIGINT DEFAULT 0,
    create_time                  TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    due_date                     TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    category                     VARCHAR(255) DEFAULT '',
    suspension_state             BIGINT DEFAULT 0,
    tenant_id                    VARCHAR(255) DEFAULT '',
    form_key                     VARCHAR(255) DEFAULT '',
    claim_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    is_count_enabled             INT DEFAULT 0,
    var_count                    BIGINT DEFAULT 0,
    id_link_count                BIGINT DEFAULT 0,
    sub_task_count               BIGINT DEFAULT 0
);

CREATE TABLE dwd_maintenance_activation_task_inst_rt
(
    id                         VARCHAR(255) PRIMARY KEY,
    rev                        BIGINT DEFAULT 0,
    proc_def_id                VARCHAR(255) DEFAULT '',
    task_def_id                VARCHAR(255) DEFAULT '',
    task_def_key               VARCHAR(255) DEFAULT '',
    proc_inst_id               VARCHAR(255) DEFAULT '',
    execution_id               VARCHAR(255) DEFAULT '',
    scope_id                   VARCHAR(255) DEFAULT '',
    sub_scope_id               VARCHAR(255) DEFAULT '',
    scope_type                 VARCHAR(255) DEFAULT '',
    scope_definition_id        VARCHAR(255) DEFAULT '',
    propagated_stage_inst_id   VARCHAR(255) DEFAULT '',
    name                       VARCHAR(255) DEFAULT '',
    parent_task_id             VARCHAR(255) DEFAULT '',
    description                VARCHAR(4000) DEFAULT '',
    owner                      VARCHAR(255) DEFAULT '',
    assignee                   VARCHAR(255) DEFAULT '',
    start_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    claim_time                 TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    end_time                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    duration                   BIGINT DEFAULT 0,
    delete_reason              VARCHAR(4000) DEFAULT '',
    priority                   BIGINT DEFAULT 0,
    due_date                   TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    form_key                   VARCHAR(255) DEFAULT '',
    category                   VARCHAR(255) DEFAULT '',
    tenant_id                  VARCHAR(255) DEFAULT '',
    last_updated_time          TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL
);


CREATE INDEX dwd_maintenance_activation_task_inst_rt_proc_id ON dwd_maintenance_activation_task_inst_rt(proc_inst_id);
CREATE INDEX dwd_maintenance_activation_task_inst_rt_assignee ON dwd_maintenance_activation_task_inst_rt(assignee);
CREATE INDEX dwd_maintenance_activation_task_inst_rt_end_time ON dwd_maintenance_activation_task_inst_rt(end_time);
CREATE INDEX dwd_maintenance_activation_task_inst_rt_last_updated_time ON dwd_maintenance_activation_task_inst_rt(last_updated_time);





CREATE TABLE dwd_maintenance_activation_var_info
(
    proc_inst_id varchar(1024)  PRIMARY KEY,
    record_update_time          TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    sid     varchar(1024)  default '',
    ltc_no    varchar(1024)  default '',
    judian_info         text default '',
    service_type		        varchar(1024)  default '',
    service_scene  varchar(1024)  default '',

    poid_list  text default '',
    judian_id  text default '',
    node_name  varchar(1024)  default '',


    product  varchar(1024)  default '',
    team_members  text default '',
    server_time_inner  varchar(1024)  default '',
    server_time_outer  varchar(1024)  default '',
    asp_uin  varchar(1024)  default '',
    asp_name varchar(1024)  default '',
    initiator varchar(1024)  default ''
);

CREATE INDEX dwd_maintenance_activation_var_info_poid_list ON dwd_maintenance_activation_var_info(poid_list);
CREATE INDEX dwd_maintenance_activation_var_info_judian_id ON dwd_maintenance_activation_var_info(judian_id);

CREATE INDEX index_dim_antool_t_users_rt_company_id ON dim_antool_t_users_rt(company_id);



CREATE TABLE dwd_maintenance_activation_judian_info
(
    proc_inst_id varchar(1024)  PRIMARY KEY,
    record_update_time          TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    customer_name  varchar(1024)  default '',
    customer_uin  varchar(1024)  default '',
    trade  varchar(1024)  default '',
    region  varchar(1024)  default '',
    project_name  varchar(1024)  default ''
);


CREATE TABLE dwd_maintenance_activation_var_inst
(
    proc_inst_id         VARCHAR(255),
    var_key              VARCHAR(255) DEFAULT '',
    var_value            TEXT DEFAULT '',
    rev                  INT DEFAULT 0,
    CONSTRAINT pk_dwd_maintenance_activation_var_inst PRIMARY KEY (proc_inst_id, var_key)
);
create index index_dwd_maintenance_activation_var_inst_var_key on dwd_maintenance_activation_var_inst(var_key);


CREATE TABLE dwd_maintenance_activation_t_task_log
(
    id                         BIGINT PRIMARY KEY,
    tenant_id                  VARCHAR(255) DEFAULT '',
    proc_inst_id               VARCHAR(255) DEFAULT '',
    event_type                 VARCHAR(255) DEFAULT '',
    task_id                    VARCHAR(255) DEFAULT '',
    task_def_key               VARCHAR(255) DEFAULT '',
    task_name                  VARCHAR(255) DEFAULT '',
    operator_id                VARCHAR(255) DEFAULT '',
    operator_company_name      VARCHAR(255) DEFAULT '',
    operator_time              TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    submit_action              VARCHAR(255) DEFAULT '',
    action                     VARCHAR(255) DEFAULT '',
    assignee                   VARCHAR(255) DEFAULT '',
    approval_comment           VARCHAR(255) DEFAULT '',
    update_content             text
);
CREATE INDEX index_dwd_maintenance_activation_t_task_log_proc_id ON dwd_maintenance_activation_t_task_log (proc_inst_id);
CREATE INDEX index_dwd_maintenance_activation_t_task_log_action ON dwd_maintenance_activation_t_task_log (action);



ALTER TABLE public.dwd_maintenance_activation_var_inst REPLICA IDENTITY FULL;