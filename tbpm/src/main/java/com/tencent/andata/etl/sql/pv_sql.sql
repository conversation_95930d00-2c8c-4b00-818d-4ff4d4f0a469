CREATE TABLE dwd_sop_andon_pv
(
    value_of_primary_key       VARCHAR(255) PRIMARY KEY,
    record_update_time         TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    access_time                TIMESTAMP(6) WITHOUT TIME ZONE DEFAULT NULL,
    user_id                    VARCHAR(255) DEFAULT '',
    company                    BIGINT DEFAULT 0,
    first_module               VARCHAR(255) DEFAULT '',
    second_module              VARCHAR(255) DEFAULT '',
    channel                    VARCHAR(255) DEFAULT '',
    source_type                VARCHAR(255) DEFAULT '',
    link                       VARCHAR(1024) DEFAULT '',
    object_key                 VARCHAR(255) DEFAULT '',
    extra_params               VARCHAR(1024) DEFAULT '',
    id                         VARCHAR(1024) DEFAULT '',
    task_type                  int DEFAULT 0
);
CREATE INDEX index_dwd_sop_andon_pv_id_task_type ON dwd_sop_andon_pv (id,task_type,company);
CREATE INDEX index_dwd_sop_andon_pv_user_id ON dwd_sop_andon_pv (user_id);
CREATE INDEX index_dwd_sop_andon_pv_access_time ON dwd_sop_andon_pv (access_time);

GRANT SELECT ON dwd_sop_andon_pv TO andata_reader;





CREATE OR REPLACE VIEW dwd_sop_andon_pv_view AS
SELECT
    t.*,
    CASE WHEN COALESCE(t1.user_name, '') != '' THEN t1.user_name ELSE t.user_id END AS user_name,
    CASE WHEN t.company > 0 THEN t3.name ELSE t2.pos_name END AS company_name,
    --t2.pos_name AS company_name,
    CASE WHEN t.company > 0 OR t.user_id LIKE 'v\_%' OR t.user_id LIKE 'p\_%' THEN 2 ELSE 1 END AS is_regular
FROM dwd_sop_andon_pv t
LEFT JOIN dim_antool_t_users_rt t1 ON t.user_id = t1.user_id
LEFT JOIN dim_staff_arch_view t2 ON t.user_id = t2.eng_name
LEFT JOIN dim_antool_t_companies_rt t3 ON t.company = t3.id;


GRANT SELECT ON dwd_sop_andon_pv_view TO andata_reader;








alter table dwd_sop_andon_pv
    add column ch_name varchar(1024)  default '',
    add column company_name varchar(1024)  default '',
    add column pos_name varchar(1024)  default '',
    add column user_name varchar(1024)  default '';
comment on column dwd_sop_andon_pv.ch_name is '腾讯中文名';
comment on column dwd_sop_andon_pv.company_name is '公司名';
comment on column dwd_sop_andon_pv.pos_name is '组织架构';
comment on column dwd_sop_andon_pv.user_name is '中文名';


alter table dwd_visit_andon_pv
    add column ch_name varchar(1024)  default '',
    add column company_name varchar(1024)  default '',
    add column pos_name varchar(1024)  default '',
    add column user_name varchar(1024)  default '';
comment on column dwd_visit_andon_pv.ch_name is '腾讯中文名';
comment on column dwd_visit_andon_pv.company_name is '公司名';
comment on column dwd_visit_andon_pv.pos_name is '组织架构';
comment on column dwd_visit_andon_pv.user_name is '中文名';



