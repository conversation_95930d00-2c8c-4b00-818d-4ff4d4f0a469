package com.tencent.andata.etl.tablemap.visit;

public class VisitRecordInfoMapping {

    public static final String VISIT_PG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_visit_andon_pv\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_visit_andon_pv\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_sop_andon_pv\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_sop_andon_pv\"\n"
            + "    }\n"
            + "]";

}
