package com.tencent.andata.etl;

import com.tencent.andata.etl.dwd.change.ChangeAntoolInfo;
import com.tencent.andata.etl.dwd.change.DwdChangeInfo;
import com.tencent.andata.etl.dwd.maintenance.MaintenanceActivationAntoolInfo;
import com.tencent.andata.etl.dwd.sync.ChangeTbpmInfo;
import com.tencent.andata.etl.dwd.sync.DwdVisitRecordInfo;
import com.tencent.andata.etl.dwd.sync.MaintenanceActivationTbpmInfo;
import com.tencent.andata.etl.dwd.sync.VisitTbpmInfo;
import com.tencent.andata.etl.dwd.sync.common.AntoolAccount;
import com.tencent.andata.etl.dwd.sync.common.DimStaffArchInfo;
import com.tencent.andata.etl.dwd.visit.VisitAntoolInfo;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.hiveudf.JsonListToArray;
import com.tencent.andata.utils.udf.GetBugIdUdf;
import com.tencent.andata.utils.udf.StringArrayExplode;
import com.tencent.andata.utils.hiveudf.transformer.UDFCastJserToJson;
import com.tencent.andata.utils.hiveudf.transformer.UDFExtractTBPMVarValueByType;
import com.tencent.andata.utils.udf.MaxValBySeq;
import com.tencent.andata.utils.udf.StringArrayExplodeJson;
import com.tencent.andata.utils.udf.GetUrlParamUdf;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

public class Application {
    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    /**
     * 在一个main方法中运行多个flink etl 任务
     *
     * @param args args[0] = iceberg db name, args[1] = pg db name
     */
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        final IcebergCatalogReader catalog = new IcebergCatalogReader();

        // table env config
        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();
        // 设置flink应用程序名称
        configuration.setString("pipeline.name", "Tbpm Statistic Application");

        //开启微批模式
        configuration.setString("table.exec.mini-batch.size", "5000");
        configuration.setString("table.exec.mini-batch.enabled", "true");
        configuration.setString("table.exec.mini-batch.allow-latency", "5 s");
        configuration.setString("execution.runtime-mode", "streaming");
        configuration.setString("execution.checkpointing.interval", "30s");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("table.dynamic-table-options.enabled", "true");
        // enable two-phase, i.e. local-global aggregation
        configuration.setString("table.optimizer.agg-phase-strategy", "TWO_PHASE");
        // 关闭iceberg source的自动推断并行度
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");
        // 优化join性能
        configuration.setString("table.optimizer.join-reorder-enabled", "true");
        configuration.setString("table.optimizer.reuse-source-enabled", "true");
        configuration.setString("table.exec.disabled-operators", "NestedLoopJoin");
        configuration.setString("table.exec.simplify-operator-name-enabled", "false");
        configuration.setString("table.optimizer.join.broadcast-threshold", "268435456");
        configuration.setString("table.optimizer.reuse-sub-plan-enabled", "true");
        configuration.setString("table.optimizer.multiple-input-enabled", "true");
        configuration.setString("table.optimizer.source.aggregate-pushdown-enabled", "true");
        configuration.setString("table.exec.legacy-cast-behaviour", "enabled");

        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        // get iceberg db name and pg db name from args
        String icebergDbName = parameterTool.get("icebergDbName");
        String pgDbName = parameterTool.get("pgDbName");
        // 注册自定义udf
        flinkEnv.streamTEnv()
                .createTemporaryFunction("maxValBySeq", MaxValBySeq.class);
        flinkEnv.hiveModuleV2()
                .registryHiveUDF("udf_cast_jser_to_json", UDFCastJserToJson.class.getName());
        flinkEnv.hiveModuleV2()
                .registryHiveUDF("udf_extract_tbpm_var_val", UDFExtractTBPMVarValueByType.class.getName());
        // 注册hive udf
        flinkEnv.hiveModuleV2()
                .registryHiveUDF("json_list_to_array", JsonListToArray.class.getName());
        flinkEnv.streamTEnv().registerFunction("string_array_explode", new StringArrayExplode());
        flinkEnv.streamTEnv().registerFunction("string_array_explode_jsonstr", new StringArrayExplodeJson());
        flinkEnv.streamTEnv().registerFunction("get_bugid_udf", new GetBugIdUdf());
        flinkEnv.streamTEnv().registerFunction("get_url_param_udf", new GetUrlParamUdf());
        String subApplicationStr = parameterTool.get("subApplication");
        List<SubApplication> subAppList = Arrays.stream(subApplicationStr.split(","))
                .map(SubApplication::valueOf)
                .collect(Collectors.toList());

        // instantiate the DWD ETL
        List<Object> appList = new ArrayList<>();

        if (subAppList.contains(SubApplication.TBPM)) {
            // 设置flink应用程序名称
            configuration.setString("pipeline.name", "Tbpm Statistic Application");
            // common
            appList.add(AntoolAccount.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
            appList.add(DwdVisitRecordInfo.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
            //appList.add(DimStaffArchInfo.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
            appList.add(VisitTbpmInfo.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
            appList.add(MaintenanceActivationTbpmInfo.builder().icebergDbName(icebergDbName).
                    pgDbName(pgDbName).build());
            appList.add(ChangeTbpmInfo.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
        } else if (subAppList.contains(SubApplication.VISIT)) {
            configuration.setString("pipeline.name", "Visit Statistic Application");
            // visit
            appList.add(VisitAntoolInfo.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
        } else if (subAppList.contains(SubApplication.MAINTENANCE)) {
            configuration.setString("pipeline.name", "Maintenance Statistic Data Application");
            // 维保激活
            appList.add(MaintenanceActivationAntoolInfo.builder().icebergDbName(icebergDbName).
                    pgDbName(pgDbName).build());
        } else if (subAppList.contains(SubApplication.CHANGE)) {
            configuration.setString("pipeline.name", "change Statistic Data Application");
            // change
            appList.add(ChangeAntoolInfo.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
            appList.add(DwdChangeInfo.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());
        }

        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", flinkEnv, catalog)));

        // execute the sql statements
        flinkEnv.stmtSet().execute();
        flinkEnv.env().execute("Tbpm Application");
    }


    enum SubApplication {
        TBPM,
        VISIT,
        MAINTENANCE,
        CHANGE
    }
}
