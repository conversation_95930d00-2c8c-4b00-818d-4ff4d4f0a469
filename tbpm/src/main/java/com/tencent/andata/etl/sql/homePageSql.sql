-- 操作流水表
CREATE OR REPLACE VIEW dwd_home_page_operation_view AS
select
'维保激活' as data_type,
b.proc_inst_id,
a.sid as service_id,
a.customer_name,
a.current_node_name,
TO_CHAR(b.operator_time, 'YYYY-MM-DD HH24:MI:SS') as operator_time,
b.operator_id as operator,
a.current_assign,
'' as parent_visit_id
from (
    select
        proc_inst_id,
        max(sid) as sid,
        max(customer_name) as customer_name,
        max(current_node_name) as current_node_name,
        string_agg(distinct current_assign::text,';') AS current_assign
    from dwd_maintenance_activation_task_view group by proc_inst_id
) as a
inner join (
    select
    *
    ,row_number() OVER (PARTITION BY proc_inst_id,operator_id ORDER BY operator_time DESC) AS rn
    from dwd_maintenance_activation_t_task_log
    where operator_time>=CURRENT_DATE - INTERVAL '7 days'
) as b on a.proc_inst_id=b.proc_inst_id
where b.rn=1
union all
select
'软件更新' as data_type,
b.proc_inst_id,
a.change_id as service_id,
a.customer_name,
a.current_node_name,
TO_CHAR(b.create_time, 'YYYY-MM-DD HH24:MI:SS') as operator_time,
b.operator,
a.current_node_assignee as current_assign,
'' as parent_visit_id
from (SELECT
    t.proc_inst_id,
    max(t.change_id) as change_id,
    max(t.customer_name) as customer_name,
    max(t1.name) AS current_node_name,
    string_agg(distinct CASE WHEN t3.user_name IS NOT NULL AND t5.name IS NOT NULL THEN concat(t3.user_name,'(',t5.name,')') WHEN t3.user_name IS NOT NULL THEN t3.user_name ELSE t1.assignee END::text,';') AS current_node_assignee
FROM dwd_change_project_info_rt as t
LEFT JOIN dwd_change_ru_task t1 ON t.proc_inst_id = t1.proc_inst_id
LEFT JOIN dim_antool_t_users_rt t3 ON t1.assignee = t3.id
LEFT JOIN dim_antool_t_companies_rt t5 ON t3.company_id = t5.id
group by t.proc_inst_id) as a
inner join (
    select
    *
    ,row_number() OVER (PARTITION BY proc_inst_id,operator ORDER BY create_time DESC) AS rn
    from dwd_change_operate_log_rt
    where create_time>=CURRENT_DATE - INTERVAL '7 days'
) as b on a.proc_inst_id=b.proc_inst_id
where b.rn=1

union all

select
'服务主任务' as data_type,
b.proc_inst_id,
a.visit_id as service_id,
a.customer_name,
a.current_node_name,
TO_CHAR(b.operator_time, 'YYYY-MM-DD HH24:MI:SS') as operator_time,
b.operator_id as operator,
a.current_assign,
'' as parent_visit_id
from (select
    proc_inst_id,
    max(visit_id) as visit_id,
    max(customer_name) as customer_name,
    max(current_node_name) as current_node_name,
    max(current_assign) as current_assign
    from dwd_visit_task_info_view group by proc_inst_id
    ) as a
inner join (
    select
    *
    ,row_number() OVER (PARTITION BY proc_inst_id,operator_id ORDER BY operator_time DESC) AS rn
    from dwd_visit_t_task_log
    where operator_time>=CURRENT_DATE - INTERVAL '7 days'
) as b on a.proc_inst_id=b.proc_inst_id
where b.rn=1


union all
select
 data_type,
proc_inst_id,
service_id,
customer_name,
current_node_name,
operator_time,
operator,
current_assign,
parent_visit_id
 from(
select
data_type,
proc_inst_id,
service_id,
customer_name,
current_node_name,
operator_time,
current_assign as operator,
current_assign,
parent_visit_id
,row_number() OVER (PARTITION BY service_id,current_assign ORDER BY operator_time desc )AS rn
from(
     select
        '服务子任务' as data_type,
        proc_inst_id,
        visit_id AS parent_visit_id,
        (json_array_elements(sub_task_list::json)->>'subTaskId')::TEXT AS  service_id,
        (json_array_elements(sub_task_list::json)->>'customerName')::TEXT AS customer_name,
         '' as current_node_name,
        (json_array_elements((json_array_elements(sub_task_list::json)->>'logs')::json)->>'operatorTime')::TEXT AS operator_time,
        (json_array_elements((json_array_elements(sub_task_list::json)->>'logs')::json)->>'operator')::TEXT AS current_assign
     from dwd_visit_var_info WHERE sub_task_list is not null and sub_task_list !=''
) as a
where operator_time>=TO_CHAR(CURRENT_DATE - INTERVAL '7 days', 'YYYY-MM-DD HH24:MI:SS')
) as t
where rn=1
;




-- 流入表
CREATE OR REPLACE VIEW dwd_home_page_inflow_view AS
select
'维保激活' as data_type,
proc_inst_id,
sid as service_id,
customer_name,
current_node_name,
TO_CHAR(task_start_time, 'YYYY-MM-DD HH24:MI:SS') as inflow_time,
task_assignee as current_assign,
'' as parent_visit_id
from dwd_maintenance_activation_task_view where task_end_time is null and current_node_name<>'结束'

union all
SELECT
     '软件更新' as data_type,
    t.proc_inst_id,
    t1.change_id as service_id,
    t1.customer_name as customer_name,
    t2.name AS current_node_name,
    TO_CHAR(t2.start_time, 'YYYY-MM-DD HH24:MI:SS') as inflow_time,
    t2.assignee AS current_assign,
'' as parent_visit_id
    FROM dwd_change_proc_inst t
    LEFT JOIN dwd_change_project_info_rt t1 ON t.proc_inst_id = t1.proc_inst_id
LEFT JOIN dwd_change_task_inst_rt t2 ON t.proc_inst_id = t2.proc_inst_id
      where t2.end_time is null
union all

select
'服务主任务' as data_type,
proc_inst_id,
visit_id as service_id,
customer_name,
current_node_name,
TO_CHAR(task_start_time, 'YYYY-MM-DD HH24:MI:SS') as inflow_time,
task_assignee as current_assign,
'' as parent_visit_id
from dwd_visit_task_view
where task_end_time is null

union all
select
data_type,
proc_inst_id,
service_id,
customer_name,
current_node_name,
inflow_time,
current_assign,
parent_visit_id
from (
select
data_type,
proc_inst_id,
service_id,
customer_name,
current_node_name,
operator_time as inflow_time,
current_assign,
parent_visit_id
,row_number() OVER (PARTITION BY service_id ORDER BY operator_time DESC) AS rn
from(
     select
        '服务子任务' as data_type,
        proc_inst_id,
        visit_id AS parent_visit_id,
        (json_array_elements(sub_task_list::json)->>'subTaskId')::TEXT AS  service_id,
        (json_array_elements(sub_task_list::json)->>'customerName')::TEXT AS customer_name,
        (json_array_elements(sub_task_list::json)->>'handler')::TEXT AS current_assign,
    (json_array_elements(sub_task_list::json)->>'status')::int AS status,
         '' as current_node_name,
        (json_array_elements((json_array_elements(sub_task_list::json)->>'logs')::json)->>'operatorTime')::TEXT AS operator_time,
        (json_array_elements((json_array_elements(sub_task_list::json)->>'logs')::json)->>'operator')::TEXT AS operator
    from dwd_visit_var_info WHERE sub_task_list is not null and sub_task_list !=''
) as a
where status in (1,4)
) as t
where rn=1
;


-- 已办表
CREATE OR REPLACE VIEW dwd_home_page_done_view AS
select
'维保激活' as data_type,
proc_inst_id,
sid as service_id,
customer_name,
current_node_name,
TO_CHAR(task_end_time, 'YYYY-MM-DD HH24:MI:SS') as end_time,
current_assign,
task_assignee
from dwd_maintenance_activation_task_view where task_end_time is not null

union all
SELECT
     '软件更新' as data_type,
    t.proc_inst_id,
    t1.change_id as service_id,
    t1.customer_name as customer_name,
    t2.name AS current_node_name,
    TO_CHAR(t2.end_time, 'YYYY-MM-DD HH24:MI:SS') as end_time,
    CASE WHEN t3.user_name IS NOT NULL AND t5.name IS NOT NULL THEN concat(t3.user_name,'(',t5.name,')') WHEN t3.user_name IS NOT NULL THEN t3.user_name ELSE t2.assignee END AS current_assign,
    t2.assignee as task_assignee
    FROM dwd_change_proc_inst t
    LEFT JOIN dwd_change_project_info_rt t1 ON t.proc_inst_id = t1.proc_inst_id
LEFT JOIN dwd_change_task_inst_rt t2 ON t.proc_inst_id = t2.proc_inst_id
LEFT JOIN dim_antool_t_users_rt t3 ON t2.assignee = t3.id
LEFT JOIN dim_antool_t_companies_rt t5 ON t3.company_id = t5.id
      where t2.end_time is not null
union all

select
'服务主任务' as data_type,
proc_inst_id,
visit_id as service_id,
customer_name,
current_node_name,
TO_CHAR(task_end_time, 'YYYY-MM-DD HH24:MI:SS') as end_time,
current_assign,
task_assignee
from dwd_visit_task_view
where task_end_time is not null

union all
select
data_type,
proc_inst_id,
service_id,
customer_name,
current_node_name,
operator_time as end_time,
current_assign,
operator as task_assignee
from(
     select
        '服务子任务' as data_type,
        proc_inst_id,
        (json_array_elements(sub_task_list::json)->>'subTaskId')::TEXT AS  service_id,
        (json_array_elements(sub_task_list::json)->>'customerName')::TEXT AS customer_name,
        (json_array_elements(sub_task_list::json)->>'handler')::TEXT AS current_assign,
    (json_array_elements(sub_task_list::json)->>'status')::int AS status,
         '' as current_node_name,
        (json_array_elements((json_array_elements(sub_task_list::json)->>'logs')::json)->>'operatorTime')::TEXT AS operator_time,
        (json_array_elements((json_array_elements(sub_task_list::json)->>'logs')::json)->>'operator')::TEXT AS operator
    from dwd_visit_var_info WHERE sub_task_list is not null and sub_task_list !=''
) as a
;


-- 概况
CREATE OR REPLACE VIEW dwd_home_page_all_view AS
select
'维保激活' as data_type,
proc_inst_id,
sid as service_id,
TO_CHAR(create_time, 'YYYY-MM-DD HH24:MI:SS') as create_time,
customer_name,
current_node_name,
TO_CHAR(task_start_time, 'YYYY-MM-DD HH24:MI:SS') as task_start_time,
TO_CHAR(task_end_time, 'YYYY-MM-DD HH24:MI:SS') as task_end_time,
current_assign,
task_assignee,
initiator
from dwd_maintenance_activation_task_view

union all
SELECT
     '软件更新' as data_type,
    t.proc_inst_id,
    t1.change_id as service_id,
    TO_CHAR(t.start_time, 'YYYY-MM-DD HH24:MI:SS') as create_time,
    t1.customer_name as customer_name,
    t2.name AS current_node_name,
    TO_CHAR(t2.start_time, 'YYYY-MM-DD HH24:MI:SS') as task_start_time,
    TO_CHAR(t2.end_time, 'YYYY-MM-DD HH24:MI:SS') as task_end_time,
    CASE WHEN t3.user_name IS NOT NULL AND t5.name IS NOT NULL THEN concat(t3.user_name,'(',t5.name,')') WHEN t3.user_name IS NOT NULL THEN t3.user_name ELSE t2.assignee END AS current_assign,
    t2.assignee as task_assignee,
    t4.initiator
    FROM dwd_change_proc_inst t
    LEFT JOIN dwd_change_project_info_rt t1 ON t.proc_inst_id = t1.proc_inst_id
LEFT JOIN dwd_change_task_inst_rt t2 ON t.proc_inst_id = t2.proc_inst_id
left join dwd_change_var_info as t4 on t.proc_inst_id = t4.proc_inst_id and t4.is_invalid IS NULL
LEFT JOIN dim_antool_t_users_rt t3 ON t2.assignee = t3.id
LEFT JOIN dim_antool_t_companies_rt t5 ON t3.company_id = t5.id

union all

select
'服务主任务' as data_type,
proc_inst_id,
visit_id as service_id,
TO_CHAR(create_time, 'YYYY-MM-DD HH24:MI:SS') as create_time,
customer_name,
current_node_name,
TO_CHAR(task_start_time, 'YYYY-MM-DD HH24:MI:SS') as task_start_time,
TO_CHAR(task_end_time, 'YYYY-MM-DD HH24:MI:SS') as task_end_time,
current_assign,
task_assignee,
initiator
from dwd_visit_task_view


union all
select
data_type,
proc_inst_id,
service_id,
create_time,
customer_name,
current_node_name,
case when status in (1,4) then FIRST_VALUE(operator_time) OVER (PARTITION BY service_id ORDER BY operator_time desc) else operator_time end as task_start_time,
operator_time as task_end_time,
current_assign,
operator as task_assignee,
initiator
from(
     select
        '服务子任务' as data_type,
        proc_inst_id,
        (json_array_elements(sub_task_list::json)->>'subTaskId')::TEXT AS  service_id,
        (json_array_elements(sub_task_list::json)->>'customerName')::TEXT AS customer_name,
        (json_array_elements(sub_task_list::json)->>'creator')::TEXT AS initiator,
        (json_array_elements(sub_task_list::json)->>'createTime')::TEXT AS create_time,
        (json_array_elements(sub_task_list::json)->>'handler')::TEXT AS current_assign,
    (json_array_elements(sub_task_list::json)->>'status')::int AS status,
         '' as current_node_name,
        (json_array_elements((json_array_elements(sub_task_list::json)->>'logs')::json)->>'operatorTime')::TEXT AS operator_time,
        (json_array_elements((json_array_elements(sub_task_list::json)->>'logs')::json)->>'operator')::TEXT AS operator
           from dwd_visit_var_info WHERE sub_task_list is not null and sub_task_list !=''
) as a
;