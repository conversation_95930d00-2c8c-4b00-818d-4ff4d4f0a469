package com.tencent.andata.etl.dwd.sync;

import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.CDCTableBuilderStrategy;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import java.util.Properties;
import static com.tencent.andata.etl.sql.visit.VisitTbpmAntoolSql.QUERY_DWD_VISIT_ANDON_PV_SQL;
import static com.tencent.andata.etl.tablemap.visit.VisitRecordInfoMapping.VISIT_PG_TABLE_TO_FLINK_TABLE;
import static com.tencent.andata.etl.sql.PvRecordSql.QUERY_DWD_SOP_PV_SQL;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;


@Builder
public class DwdVisitRecordInfo {

    private final String icebergDbName;
    private final String pgDbName;


    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        StreamTableEnvironment tableEnv = flinkEnv.streamTEnv();
        // PG CDC ODS表注册
        DatabaseConf pvDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware_ods"))
                .build();
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("pg_source_ods_andon_pv")
                        .tableBuilderStrategy(
                                new CDCTableBuilderStrategy(
                                        "ods_andon_pv",
                                        PGSQL,
                                        pvDBConf, ""
                                )
                        )
                        .processTimeFiled("process_time")
                        .build()
        );

        // pgTable mapping to flinkTable
        ObjectMapper mapper = new ObjectMapper();
        TableUtils.rdbTable2FlinkTable(
                pgDBConf,
                mapper.readValue(VISIT_PG_TABLE_TO_FLINK_TABLE, ArrayNode.class),
                PGSQL,
                tableEnv
        );

        StatementSet stmtSet = flinkEnv.stmtSet();
        // 创建flink临时表
        flinkEnv.streamTEnv().createTemporaryView(
                "dwd_visit_andon_pv_view",
                flinkEnv.streamTEnv().sqlQuery(QUERY_DWD_VISIT_ANDON_PV_SQL));

        // 创建flink临时表
        flinkEnv.streamTEnv().createTemporaryView(
                "dwd_sop_andon_pv_view",
                flinkEnv.streamTEnv().sqlQuery(QUERY_DWD_SOP_PV_SQL));

        // insert
        stmtSet.addInsertSql(insertIntoSql(
                "dwd_visit_andon_pv_view",
                "pgsql_sink_dwd_visit_andon_pv",
                tableEnv.from("pgsql_sink_dwd_visit_andon_pv"),
                PGSQL)).
                addInsertSql(insertIntoSql(
                "dwd_sop_andon_pv_view",
                "pgsql_sink_dwd_sop_andon_pv",
                tableEnv.from("pgsql_sink_dwd_sop_andon_pv"),
                PGSQL));


    }
}
