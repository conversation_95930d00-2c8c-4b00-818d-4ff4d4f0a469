package com.tencent.andata.etl.tablemap.change;

public class ChangeAntoolMapping {



    public static final String PG_TABLE_TO_FLINK_TABLE = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_change_var_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_change_var_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwm_change_tapd_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dwm_change_tapd_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwm_change_ticket_url\",\n"
            + "        \"fTable\": \"pgsql_sink_dwm_change_ticket_url\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwm_change_vertical_product_info\",\n"
            + "        \"fTable\": \"pgsql_sink_dwm_change_vertical_product_info\"\n"
            + "    }\n"
            + "]";

}
