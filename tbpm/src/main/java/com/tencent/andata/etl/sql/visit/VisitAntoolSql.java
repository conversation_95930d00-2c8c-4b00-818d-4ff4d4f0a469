package com.tencent.andata.etl.sql.visit;

public class VisitAntoolSql {


    public static String QUERY_DWD_VISIT_VAR_INFO_TABLE_SQL = ""
            + "SELECT \n"
            + "         proc_inst_id, \n"
            + "         CAST('1970-01-01 08:00:00' AS TIMESTAMP) AS record_update_time,\n"
            + "         MAX(visit_id) AS visit_id,\n"
            + "         MAX(current_activity) AS current_activity,\n"
            + "         MAX(visit_state) AS visit_state,\n"
            + "         MAX(ltc_no) AS ltc_no,\n"
            + "         MAX(creator) AS creator,\n"

            + "         MAX(current_assign) AS current_assign,\n"
            + "         MAX(project_name) AS project_name,\n"
            + "         MAX(customer_name) AS customer_name,\n"
            + "         MAX(customer_uin) AS customer_uin,\n"
            + "         MAX(trade) AS trade,\n"

            + "         MAX(spmo) AS spmo,\n"
            + "         MAX(pms_pre_salesa_id) AS pms_pre_salesa_id,\n"
            + "         MAX(sales_manager_id) AS sales_manager_id,\n"
            + "         MAX(deliver_pm_name) AS deliver_pm_name,\n"
            + "         MAX(visit_product) AS visit_product,\n"

            + "         MAX(visit_type) AS visit_type,\n"
            + "         MAX(visit_category) AS visit_category,\n"
            + "         MAX(plan_participants) AS plan_participants,\n"
            //+ "         CASE WHEN MAX(plan_participants) IS NOT NULL THEN MAX(plan_participants) "
            //+ "         ELSE MAX(new_plan_participants) END AS plan_participants,\n"
            + "         MAX(plan_visit_time) AS plan_visit_time,\n"

            + "         MAX(visit_area) AS visit_area,\n"
            + "         MAX(visit_resion) AS visit_resion,\n"
            + "         MAX(activity_kubxnu) AS activity_kubxnu,\n"
            + "         MAX(activity_lwcifu) AS activity_lwcifu,\n"
            + "         MAX(activity_t6ha6u) AS activity_t6ha6u,\n"

            + "         MAX(activity_lzhkr) AS activity_lzhkr,\n"
            + "         MAX(meeting_topic) AS meeting_topic,\n"
            + "         MAX(meeting_agenda) AS meeting_agenda,\n"
            + "         MAX(meeting_time) AS meeting_time,\n"
            + "         MAX(meeting_address) AS meeting_address,\n"
            + "         MAX(customer_participants) AS customer_participants,\n"

            + "         MAX(tencent_participants) AS tencent_participants,\n"
            //+ "         CASE WHEN MAX(tencent_participants) IS NOT NULL THEN MAX(tencent_participants) "
            //+ "         ELSE MAX(new_tencent_participants) END AS tencent_participants,\n"
            + "         MAX(table_visit_id) AS table_visit_id,\n"
            + "         MAX(custom) AS custom,\n"
            + "         MAX(problem) AS problem,\n"
            + "         MAX(email_type) AS email_type,\n"
            + "         MAX(formal_email1) AS formal_email1,\n"
            + "         MAX(formal_email3) AS formal_email3,\n"

            + "         MAX(formal_email2) AS formal_email2,\n"
            + "         MAX(formal_email4) AS formal_email4,\n"
            + "         MAX(test_email) AS test_email,\n"
            + "         MAX(region) AS region,\n"
            + "         MAX(server_provider) AS server_provider,\n"
            + "         MAX(url) AS url,\n"
            + "         MAX(visit_second_type) AS visit_second_type,\n"


            + "         MAX(to_do_list) AS to_do_list,\n"
            + "         MAX(jielun) AS jielun,\n"
            + "         MAX(sub_task_list) AS sub_task_list,\n"
            + "         MAX(associated_visits) AS associated_visits,\n"

            + "         MAX(initiator) AS initiator,\n"
            + "         MAX(visit_order_type) AS visit_order_type,\n"
            + "         MAX(asp_uin) AS asp_uin,\n"
            + "         MAX(uins) AS uins,\n"

            + "         MAX(sensitive_reason) AS sensitive_reason,\n"
            + "         MAX(app_id) AS app_id,\n"
            + "         MAX(log_id) AS log_id,\n"
            + "         MAX(task_desc) AS task_desc,\n"
            + "         MAX(chat_id) AS chat_id,\n"
            + "         MAX(big_title) AS big_title,\n"
            //+ "         CASE WHEN MAX(big_title) IS NOT NULL THEN MAX(big_title) ELSE MAX(title) END AS big_title,\n"
            + "         MAX(submit_action) AS submit_action,\n"

            + "         MAX(what_task) AS what_task,\n"
            + "         MAX(meeting_location) AS meeting_location,\n"
            + "         MAX(meeting_when) AS meeting_when,\n"
            + "         MAX(customer_people) AS customer_people,\n"
            + "         MAX(tencent_people) AS tencent_people,\n"
            + "         MAX(meeting_title) AS meeting_title,\n"
            + "         MAX(meeting_what) AS meeting_what,\n"
            + "         MAX(file_upload) AS file_upload,\n"

            + "         MAX(meeting_file) AS meeting_file,\n"
            + "         MAX(asp_meeting_result) AS asp_meeting_result,\n"
            + "         MAX(new_tencent_participants) AS new_tencent_participants,\n"
            + "         MAX(title) AS title,\n"
            + "         MAX(new_plan_participants) AS new_plan_participants,\n"
            + "         MAX(email_content) AS email_content,\n"

            + "         MAX(source_channel) AS source_channel,\n"
            + "         MAX(opportunity_id) AS opportunity_id,\n"
            + "         MAX(task_source) AS task_source,\n"
            //+ "         MAX(plan_visit_end_time) AS plan_visit_end_time\n"
            + "         CAST(MAX(plan_visit_end_time) AS DATE) AS plan_visit_end_time, \n"
            + "         MAX(official_show) AS official_show, \n"
            + "         MAX(has_send) AS has_send \n"

            + "FROM (\n"
            + "     SELECT proc_inst_id,\n"
            + "            CASE WHEN var_key='visitId' THEN var_value ELSE NULL  END AS visit_id,\n"
            + "            CASE WHEN var_key='currentActivity' THEN var_value ELSE NULL  END AS current_activity,\n"
            + "            CASE WHEN var_key='visitState' THEN var_value ELSE NULL  END AS visit_state,\n"
            + "            CASE WHEN var_key='ltcNo' THEN var_value ELSE NULL  END AS ltc_no,\n"
            + "            CASE WHEN var_key='creator' THEN var_value ELSE NULL  END AS creator,\n"

            + "            CASE WHEN var_key='currentAssign' THEN var_value ELSE NULL  END AS current_assign,\n"
            + "            CASE WHEN var_key='ProjectName' THEN var_value ELSE NULL  END AS project_name,\n"
            + "            CASE WHEN var_key='CustomerName' THEN var_value ELSE NULL  END AS customer_name,\n"
            + "            CASE WHEN var_key='CustomerUin' THEN var_value ELSE NULL  END AS customer_uin,\n"
            + "            CASE WHEN var_key='Trade' THEN var_value ELSE NULL  END AS trade,\n"

            + "            CASE WHEN var_key='SPMO' THEN var_value ELSE NULL  END AS spmo,\n"
            + "            CASE WHEN var_key='PmsPreSaleSAId' THEN var_value ELSE NULL  END AS pms_pre_salesa_id,\n"
            + "            CASE WHEN var_key='SalesManagerId' THEN var_value ELSE NULL  END AS sales_manager_id,\n"
            + "            CASE WHEN var_key='DeliverPMName' THEN var_value ELSE NULL  END AS deliver_pm_name,\n"
            + "            CASE WHEN var_key='visitProduct' THEN var_value ELSE NULL  END AS visit_product,\n"

            + "            CASE WHEN var_key='visitType' THEN var_value ELSE NULL  END AS visit_type,\n"
            + "            CASE WHEN var_key='visitCategory' THEN var_value ELSE NULL  END AS visit_category,\n"
            + "            CASE WHEN var_key='planParticipants' THEN var_value ELSE NULL  END AS plan_participants,\n"
            + "            CASE WHEN var_key='planVisitTime' AND var_value <> '' THEN var_value ELSE NULL  END "
            + "            AS plan_visit_time,\n"

            + "            CASE WHEN var_key='visitArea' THEN var_value ELSE NULL  END AS visit_area,\n"
            + "            CASE WHEN var_key='visitResion' THEN var_value ELSE NULL  END AS visit_resion,\n"
            + "            CASE WHEN var_key='Activity_0kubxnu' THEN var_value ELSE NULL  END AS activity_kubxnu,\n"
            + "            CASE WHEN var_key='Activity_1lwcifu' THEN var_value ELSE NULL  END AS activity_lwcifu,\n"
            + "            CASE WHEN var_key='Activity_0t6ha6u' THEN var_value ELSE NULL  END AS activity_t6ha6u,\n"

            + "            CASE WHEN var_key='Activity_10lzhkr' THEN var_value ELSE NULL  END AS activity_lzhkr,\n"
            + "            CASE WHEN var_key='meetingTopic' THEN var_value ELSE NULL  END AS meeting_topic,\n"
            + "            CASE WHEN var_key='meetingAgenda' THEN var_value ELSE NULL  END AS meeting_agenda,\n"
            + "            CASE WHEN var_key='meetingTime' AND var_value <> '' THEN var_value ELSE NULL  END "
            + "            AS meeting_time,\n"
            + "            CASE WHEN var_key='meetingAddress' THEN var_value ELSE NULL  END AS meeting_address,\n"
            + "            CASE WHEN var_key='customerParticipants' THEN var_value ELSE NULL  END "
            + "            AS customer_participants,\n"

            + "            CASE WHEN var_key='tencentParticipants' THEN var_value ELSE NULL  END "
            + "            AS tencent_participants,\n"
            + "            CASE WHEN var_key='tableVisitId' THEN var_value ELSE NULL  END AS table_visit_id,\n"
            + "            CASE WHEN var_key='custom' THEN var_value ELSE NULL  END AS custom,\n"
            + "            CASE WHEN var_key='problem' THEN var_value ELSE NULL  END AS problem,\n"
            + "            CASE WHEN var_key='emailType' THEN var_value ELSE NULL  END AS email_type,\n"
            + "            CASE WHEN var_key='formalEmail1' THEN var_value ELSE NULL  END AS formal_email1,\n"
            + "            CASE WHEN var_key='formalEmail3' THEN var_value ELSE NULL  END AS formal_email3,\n"

            + "            CASE WHEN var_key='formalEmail2' THEN var_value ELSE NULL  END AS formal_email2,\n"
            + "            CASE WHEN var_key='formalEmail4' THEN var_value ELSE NULL  END AS formal_email4,\n"
            + "            CASE WHEN var_key='testEmail' THEN var_value ELSE NULL  END AS test_email,\n"
            + "            CASE WHEN var_key='Region' THEN var_value ELSE NULL  END AS region,\n"
            + "            CASE WHEN var_key='ServerProvider' THEN var_value ELSE NULL  END AS server_provider,\n"
            + "            CASE WHEN var_key='url' THEN var_value ELSE NULL  END AS url,\n"
            + "            CASE WHEN var_key='visitSecondType' THEN var_value ELSE NULL  END AS visit_second_type,\n"

            + "            CASE WHEN var_key='toDoList' THEN var_value ELSE NULL  END AS to_do_list,\n"
            + "            CASE WHEN var_key='jielun' THEN var_value ELSE NULL  END AS jielun,\n"
            + "            CASE WHEN var_key='subTaskList' THEN var_value ELSE NULL  END AS sub_task_list,\n"
            + "            CASE WHEN var_key='associatedVisits' THEN var_value ELSE NULL  END AS associated_visits,\n"

            + "            CASE WHEN var_key='initiator' THEN var_value ELSE NULL  END AS initiator,\n"
            + "            CASE WHEN var_key='visit_order_type' THEN var_value ELSE NULL  END AS visit_order_type,\n"
            + "            CASE WHEN var_key='aspUin' THEN var_value ELSE NULL  END AS asp_uin,\n"
            + "            CASE WHEN var_key='uins' THEN var_value ELSE NULL  END AS uins,\n"


            + "            CASE WHEN var_key='sensitiveReason' THEN var_value ELSE NULL  END AS sensitive_reason,\n"
            + "            CASE WHEN var_key='APPID' THEN var_value ELSE NULL  END AS app_id,\n"
            + "            CASE WHEN var_key='logId' THEN var_value ELSE NULL  END AS log_id,\n"
            + "            CASE WHEN var_key='taskDesc' THEN var_value ELSE NULL  END AS task_desc,\n"
            + "            CASE WHEN var_key='chatId' THEN var_value ELSE NULL  END AS chat_id,\n"
            + "            CASE WHEN var_key='BigTitle' THEN var_value ELSE NULL  END AS big_title,\n"
            + "            CASE WHEN var_key='submit_action' THEN var_value ELSE NULL  END AS submit_action,\n"

            + "            CASE WHEN var_key='whatTask' THEN var_value ELSE NULL  END AS what_task,\n"
            + "            CASE WHEN var_key='meetingLocation' THEN var_value ELSE NULL  END AS meeting_location,\n"
            + "            CASE WHEN var_key='meetingWhen' THEN var_value ELSE NULL  END AS meeting_when,\n"
            + "            CASE WHEN var_key='customerPeople' THEN var_value ELSE NULL  END AS customer_people,\n"
            + "            CASE WHEN var_key='tencentPeople' THEN var_value ELSE NULL  END AS tencent_people,\n"
            + "            CASE WHEN var_key='meetingTitle' THEN var_value ELSE NULL  END AS meeting_title,\n"
            + "            CASE WHEN var_key='meetingWhat' THEN var_value ELSE NULL  END AS meeting_what,\n"
            + "            CASE WHEN var_key='fileUpload' THEN var_value ELSE NULL  END AS file_upload,\n"

            + "            CASE WHEN var_key='title' THEN var_value ELSE NULL  END AS title,\n"
            + "            CASE WHEN var_key='newTencentParticipants' THEN var_value ELSE NULL "
            + "            END AS new_tencent_participants,\n"
            + "            CASE WHEN var_key='meetingFile' THEN var_value ELSE NULL  END AS meeting_file,\n"
            + "            CASE WHEN var_key='newPlanParticipants' THEN var_value ELSE NULL "
            + "            END AS new_plan_participants,\n"
            + "            CASE WHEN var_key='aspMeetingResult' THEN var_value ELSE NULL  END AS asp_meeting_result,\n"
            + "            CASE WHEN var_key='emailContent' THEN var_value ELSE NULL  END AS email_content,\n"

            + "            CASE WHEN var_key='sourceChannel' THEN var_value ELSE NULL  END AS source_channel,\n"
            + "            CASE WHEN var_key='opportunityID' THEN var_value ELSE NULL  END AS opportunity_id,\n"
            + "            CASE WHEN var_key='taskSource' THEN var_value ELSE NULL  END AS task_source,\n"
            + "            CASE WHEN var_key='planVisitEndTime' THEN var_value ELSE NULL  END AS plan_visit_end_time,\n"
            + "            CASE WHEN var_key='OfficialShow' THEN var_value ELSE NULL  END AS official_show,\n"
            + "            CASE WHEN var_key='hasSend' THEN var_value ELSE NULL  END AS has_send\n"

            + "         FROM pgsql_source_dwd_visit_var_inst\n"
            + ") AS t\n"
            + "GROUP BY proc_inst_id\n";

}