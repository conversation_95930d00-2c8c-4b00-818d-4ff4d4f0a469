package com.tencent.andata.etl.dwd;

import com.tencent.andata.etl.sql.DwdJuDianTableMapping;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;

import java.util.Properties;
import java.util.stream.Collectors;

import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;

@Builder
public class DwdJuDianInfo {

    private final String icebergDbName;
    private final String pgDbName;


    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "judian"))
                .build();

        ObjectMapper mapper = new ObjectMapper();
        ArrayNode tableMapping = mapper.readValue(DwdJuDianTableMapping.icebergTable2FlinkTable, ArrayNode.class);
        ArrayNode rdbTableMapping = mapper.readValue(DwdJuDianTableMapping.rdbTable2FlinkTable, ArrayNode.class);

        // rdbTable mapping to flinkTable
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, rdbTableMapping, MYSQL, tEnv);
        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(this.icebergDbName, tableMapping, tEnv, catalog);

        // pg
        DatabaseConf pgAndonDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName.split(",")[0]))
                .build();
        DatabaseConf pgDatawareOdsDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName.split(",")[1]))
                .build();

        // pgTable mapping to flinkTable
        TableUtils.rdbTable2FlinkTable(
                pgAndonDBConf,
                mapper.readValue(DwdJuDianTableMapping.andonTable2FlinkTable, ArrayNode.class),
                PGSQL,
                tEnv
        );

        TableUtils.rdbTable2FlinkTable(
                pgDatawareOdsDBConf,
                mapper.readValue(DwdJuDianTableMapping.odsTable2FlinkTable, ArrayNode.class),
                PGSQL,
                tEnv
        );

        StatementSet stmtSet = flinkEnv.stmtSet();

        for (JsonNode tableNode : rdbTableMapping) {

            String view = "mysql_".concat(tableNode.get("rdbTable").asText()).concat("_view");
            Table table = tEnv.sqlQuery("select * from " + tableNode.get("fTable").asText()
                    + " /*+ OPTIONS('server-time-zone'='Asia/Shanghai',"
                    + "'server-id'='" + Math.abs(tableNode.get("rdbTable").asText().hashCode()) + "') */");

            tEnv.createTemporaryView(view, table);
            String coloms = table.getResolvedSchema()
                    .getColumns()
                    .stream()
                    .map(s -> "`" + s.getName() + "`" + " AS " + "`" + s.getName().toLowerCase() + "`")
                    .collect(Collectors.joining(","));

            String viewName = "pg_".concat(tableNode.get("rdbTable").asText()).concat("_view");

            tEnv.createTemporaryView(viewName, tEnv.sqlQuery("select " + coloms + " from " + view));

            String pgTableName = tableNode.get("rdbTable").asText();
            if (DwdJuDianTableMapping.andonTable2FlinkTable.contains("\"rdbTable\": \"" + pgTableName + "\"")
                    || DwdJuDianTableMapping.odsTable2FlinkTable.contains("\"rdbTable\": \"" + pgTableName + "\"")) {
                // pg
                stmtSet.addInsertSql(
                        insertIntoSql(
                                viewName, "pg_".concat(pgTableName), tEnv.from("pg_".concat(pgTableName)), PGSQL));
            }
            // Iceberg
            stmtSet.addInsertSql(TableUtils.insertIntoSql(
                            viewName,
                            "iceberg_sink_ods_".concat(pgTableName),
                            tEnv.from("iceberg_sink_ods_".concat(pgTableName)),
                            ICEBERG
                            ));
        }
    }
}