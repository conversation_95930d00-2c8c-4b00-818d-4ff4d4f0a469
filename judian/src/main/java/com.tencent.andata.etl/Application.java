package com.tencent.andata.etl;


import com.tencent.andata.etl.dwd.DwdJuDianInfo;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class Application {

    Logger logger = LoggerFactory.getLogger(Application.class);

    /**
     * 在一个main方法中运行多个flink etl 任务
     *
     * @args args[0] = iceberg db name
     * @args args[1] = pg db name
     */
    public static void main(String[] args) throws Exception {
        FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        IcebergCatalogReader catalog = new IcebergCatalogReader();

        // table env config
        Configuration configuration = flinkEnv.streamTEnv().getConfig().getConfiguration();
        // 设置flink应用名称
        configuration.setString("pipeline.name", "JuDian Application");
        configuration.setString("execution.runtime-mode", "streaming");
        configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
        configuration.setString("table.dynamic-table-options.enabled", "true");
        //configuration.setString("table.exec.mini-batch.enabled", "true");
        //configuration.setString("table.exec.mini-batch.allow-latency", "30 min");
        //configuration.setString("table.exec.mini-batch.size", "1000");
        configuration.setString("table.exec.resource.default-parallelism", "1");
        configuration.setString("table.exec.iceberg.infer-source-parallelism", "false");
        configuration.setString("execution.checkpointing.interval", "3s");

        // get iceberg db name and pg db name from args
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        String icebergDbName = parameterTool.get("icebergDbName");
        String pgDbName = parameterTool.get("pgDbName");

        // instantiate the DWD ETL
        List<Object> appList = new ArrayList<>();
        appList.add(DwdJuDianInfo.builder().icebergDbName(icebergDbName).pgDbName(pgDbName).build());

        // 遍历appList，调用run方法
        for (Object app : appList) {
            app.getClass().getMethod("run",
                                     FlinkEnv.class,
                                     IcebergCatalogReader.class
            ).invoke(app, flinkEnv, catalog);
        }

        // execute the sql statements
        flinkEnv.stmtSet().execute();
        flinkEnv.getStreamExecutionEnvironment().disableOperatorChaining();
        flinkEnv.env().execute("Problem Application");
    }
}