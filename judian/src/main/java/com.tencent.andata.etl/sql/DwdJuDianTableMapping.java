package com.tencent.andata.etl.sql;

public class DwdJuDianTableMapping {

    public static String rdbTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_projects\",\n"
            + "        \"fTable\": \"mysql_source_t_projects\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_project_judians\",\n"
            + "        \"fTable\": \"mysql_source_t_project_judians\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_judian_info\",\n"
            + "        \"fTable\": \"mysql_source_t_judian_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_product_item\",\n"
            + "        \"fTable\": \"mysql_source_t_product_item\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_staff_info\",\n"
            + "        \"fTable\": \"mysql_source_t_staff_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_staff_judian_relation\",\n"
            + "        \"fTable\": \"mysql_source_t_staff_judian_relation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_log_info\",\n"
            + "        \"fTable\": \"mysql_source_t_log_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_customer_info\",\n"
            + "        \"fTable\": \"mysql_source_t_customer_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_order_info\",\n"
            + "        \"fTable\": \"mysql_source_t_order_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_judian_location_info\",\n"
            + "        \"fTable\": \"mysql_source_t_judian_location_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_component\",\n"
            + "        \"fTable\": \"mysql_source_t_component\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_component_version\",\n"
            + "        \"fTable\": \"mysql_source_t_component_version\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_judian_component\",\n"
            + "        \"fTable\": \"mysql_source_t_judian_component\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_product_info\",\n"
            + "        \"pgTable\": \"t_product_info\",\n"
            + "        \"pgDB\": \"andon\",\n"
            + "        \"fTable\": \"mysql_source_t_product_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_sub_product_info\",\n"
            + "        \"pgTable\": \"t_sub_product_info\",\n"
            + "        \"pgDB\": \"andon\",\n"
            + "        \"fTable\": \"mysql_source_t_sub_product_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_material_info\",\n"
            + "        \"fTable\": \"mysql_source_t_material_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_service_time_info\",\n"
            + "        \"fTable\": \"mysql_source_t_service_time_info\"\n"
            + "    }\n"
            + "]";

    public static String andonTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_projects\",\n"
            + "        \"fTable\": \"pg_t_projects\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_judian_info\",\n"
            + "        \"fTable\": \"pg_t_judian_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_product_item\",\n"
            + "        \"fTable\": \"pg_t_product_item\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_staff_info\",\n"
            + "        \"fTable\": \"pg_t_staff_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_staff_judian_relation\",\n"
            + "        \"fTable\": \"pg_t_staff_judian_relation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_customer_info\",\n"
            + "        \"fTable\": \"pg_t_customer_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_judian_location_info\",\n"
            + "        \"fTable\": \"pg_t_judian_location_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_component\",\n"
            + "        \"fTable\": \"pg_t_component\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_component_version\",\n"
            + "        \"fTable\": \"pg_t_component_version\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_judian_component\",\n"
            + "        \"fTable\": \"pg_t_judian_component\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_product_info\",\n"
            + "        \"fTable\": \"pg_t_product_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_sub_product_info\",\n"
            + "        \"fTable\": \"pg_t_sub_product_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_material_info\",\n"
            + "        \"fTable\": \"pg_t_material_info\"\n"
            + "    }\n"
            + "]";
    public static String odsTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_project_judians\",\n"
            + "        \"fTable\": \"pg_t_project_judians\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_log_info\",\n"
            + "        \"fTable\": \"pg_t_log_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_order_info\",\n"
            + "        \"fTable\": \"pg_t_order_info\"\n"
            + "    }\n"
            + "]";

    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_customer_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_customer_info\",\n"
            + "        \"primaryKey\": \"customer_cid\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_projects\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_projects\",\n"
            + "        \"primaryKey\": \"project_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_project_judians\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_project_judians\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_judian_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_judian_info\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_product_item\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_product_item\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_staff_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_staff_info\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_staff_judian_relation\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_staff_judian_relation\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_log_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_log_info\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_order_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_order_info\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_judian_location_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_judian_location_info\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_component\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_component\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_component_version\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_component_version\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_judian_component\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_judian_component\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_product_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_product_info\",\n"
            + "        \"primaryKey\": \"product_id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_sub_product_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_sub_product_info\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_material_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_material_info\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\": \"ods_t_service_time_info\",\n"
            + "        \"fTable\": \"iceberg_sink_ods_t_service_time_info\",\n"
            + "        \"primaryKey\": \"id\"\n"
            + "    }\n"
            + "]";



}
