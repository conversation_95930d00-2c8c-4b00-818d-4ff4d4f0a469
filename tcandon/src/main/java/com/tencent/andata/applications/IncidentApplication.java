package com.tencent.andata.applications;

import com.tencent.andata.sql.TicketSql;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.CDCTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;

import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import lombok.Builder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.Properties;

@Builder
public class IncidentApplication {
    public String hiveDbName;
    private static final String SINK_DB_GROUP = "sink.database.pgsql.dataware";
    private static final String SOURCE_DB_GROUP = "cdc.database.mysql.d_incident";

    // PG表
    private static final String PG_ODS_TICKET_TABLE = "ods_incidents";
    private static final String PG_DWD_TICKET_TABLE = "dwd_incident_ticket_base_info";
    private static final String PG_ODS_INCIDENT_OPERATION_TABLE = "ods_incident_operations";
    private static final String PG_DWD_INCIDENT_OPERATION_TABLE = "dwd_incident_operation";


    // Flink 表
    private static final String F_PG_ODS_TICKET_TABLE = "f_pg_ods_incidents";
    private static final String F_PG_DWD_TICKET_TABLE = "f_pg_dwd_incident_ticket_base_info";
    private static final String F_PG_DWM_TICKET_TABLE = "f_pg_dwm_incident_statistic";
    private static final String F_PG_ODS_INCIDENT_OPERATION_TABLE = "f_pg_ods_incident_operations";
    private static final String F_PG_DWD_INCIDENT_OPERATION_TABLE = "f_pg_dwd_incident_operation";
    private static final String F_MYSQL_TICKET_TABLE = "f_incidents";
    private static final String F_MYSQL_INCIDENT_OPERATION_TABLE = "f_incident_operations";
    private static final String F_DWD_INCIDENT_OPERATION_TABLE = "f_dwd_incident_operation";
    private static final String F_INDICATOR_TABLE = "indicator_table";
    private static final String F_DWD_TICKET_TABLE = "dwd_incident";
    private static final String F_ICE_DWD_TICKET_TABLE = "f_ice_dwd_incident_base_info";
    private static final String F_ICE_DWD_OPERATION_TABLE = "f_ice_dwd_incident_operation";

    // Mysql表
    private static final String MYSQL_TICKET_TABLE = "incidents";
    private static final String MYSQL_INCIDENT_OPERATION_TABLE = "incident_operations";

    // Iceberg 表
    private static String ICEBERG_DWD_TICKET_TABLE = "dwd_incident_base_info";
    private static String ICEBERG_DWD_OPERATION_TABLE = "dwd_incident_operation";

    /**
     * run
     *
     * @param flinkEnv
     * @throws Exception
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);
        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(SINK_DB_GROUP)
                .build();
        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(SOURCE_DB_GROUP)
                .build();
        // 注册Ice
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(F_ICE_DWD_TICKET_TABLE)
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(
                                        catalog.getTableInstance(
                                                hiveDbName,
                                                ICEBERG_DWD_TICKET_TABLE
                                        )
                                ).primaryKeyList(
                                        new String[]{"id"}
                                )
                        )
                        .build()
        );
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(F_ICE_DWD_OPERATION_TABLE)
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(
                                        catalog.getTableInstance(
                                                hiveDbName,
                                                ICEBERG_DWD_OPERATION_TABLE
                                        )
                                ).primaryKeyList(
                                        new String[]{"id"}
                                )
                        )
                        .build()
        );
        // 注册DWD表
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(F_PG_DWD_TICKET_TABLE)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        PG_DWD_TICKET_TABLE,
                                        DatabaseEnum.PGSQL,
                                        pgsqlDBConf
                                )
                        )
                        .build()
        );
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(F_PG_DWD_INCIDENT_OPERATION_TABLE)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        PG_DWD_INCIDENT_OPERATION_TABLE,
                                        DatabaseEnum.PGSQL,
                                        pgsqlDBConf
                                )
                        )
                        .build()
        );
        // 注册PG ODS表
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(F_PG_ODS_TICKET_TABLE)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        PG_ODS_TICKET_TABLE,
                                        DatabaseEnum.PGSQL,
                                        pgsqlDBConf
                                )
                        )
                        .build()
        );
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(F_PG_ODS_INCIDENT_OPERATION_TABLE)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        PG_ODS_INCIDENT_OPERATION_TABLE,
                                        DatabaseEnum.PGSQL,
                                        pgsqlDBConf
                                )
                        )
                        .build()
        );

        String mysqlNodeMap = ""
                + "[\n"
                + "    {\n"
                + "        \"rdbTable\":\"incidents\",\n"
                + "        \"fTable\":\"f_incidents\"\n"
                + "    },\n"
                + "    {\n"
                + "        \"rdbTable\":\"incident_operations\",\n"
                + "        \"fTable\":\"f_incident_operations\"\n"
                + "    }\n"
                + "]";
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode mysqlNodes = mapper.readValue(mysqlNodeMap, ArrayNode.class);
        buildSourceView(mysqlDBConf, flinkEnv, mysqlNodes, DatabaseEnum.MYSQL, parameterTool,true);

        // 工单流水DWD表
        tEnv.createTemporaryView(
                F_DWD_INCIDENT_OPERATION_TABLE,
                tEnv.sqlQuery(
                        String.format(TicketSql.DWD_INCIDENT_OPERATION_SQL, F_MYSQL_INCIDENT_OPERATION_TABLE)
                )
        );
        final Table odsOperationTable = tEnv.from(F_MYSQL_INCIDENT_OPERATION_TABLE);
        // DWD工单详情表数据
        tEnv.createTemporaryView(
                F_DWD_TICKET_TABLE,
                tEnv.sqlQuery(
                        String.format(TicketSql.DWD_TICKET_SQL, F_MYSQL_TICKET_TABLE)
                )
        );
        StatementSet sts = flinkEnv.stmtSet();
        // 出库PG
        sts.addInsertSql(insertIntoSql(
                F_MYSQL_TICKET_TABLE,
                F_PG_ODS_TICKET_TABLE,
                tEnv.from(F_PG_ODS_TICKET_TABLE),
                PGSQL
        )).addInsertSql(insertIntoSql(
                F_MYSQL_INCIDENT_OPERATION_TABLE,
                F_PG_ODS_INCIDENT_OPERATION_TABLE,
                tEnv.from(F_PG_ODS_INCIDENT_OPERATION_TABLE),
                PGSQL
        )).addInsertSql(insertIntoSql(
                F_DWD_TICKET_TABLE,
                F_PG_DWD_TICKET_TABLE,
                tEnv.from(F_PG_DWD_TICKET_TABLE),
                PGSQL
        )).addInsertSql(insertIntoSql(
                F_DWD_INCIDENT_OPERATION_TABLE,
                F_PG_DWD_INCIDENT_OPERATION_TABLE,
                tEnv.from(F_PG_DWD_INCIDENT_OPERATION_TABLE),
                PGSQL
        )).addInsertSql(insertIntoSql(
                F_DWD_INCIDENT_OPERATION_TABLE,
                F_ICE_DWD_OPERATION_TABLE,
                tEnv.from(F_ICE_DWD_OPERATION_TABLE),
                ICEBERG
        )).addInsertSql(insertIntoSql(
                F_DWD_TICKET_TABLE,
                F_ICE_DWD_TICKET_TABLE,
                tEnv.from(F_ICE_DWD_TICKET_TABLE),
                ICEBERG
        ));

    }
}