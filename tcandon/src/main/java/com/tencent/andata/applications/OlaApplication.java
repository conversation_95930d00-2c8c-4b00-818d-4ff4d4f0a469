package com.tencent.andata.applications;


import com.tencent.andata.sql.OLASql;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;

import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import lombok.Builder;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.OutputTag;

import java.util.HashMap;
import java.util.Properties;

@Builder
public class OlaApplication {
    public String hiveDbName;
    private static final String SINK_DB_GROUP = "sink.database.pgsql.dataware";
    private static final String SOURCE_DB_GROUP = "cdc.database.mysql.d_record";
    // PG表
    private static final String PG_ODS_OLA_TABLE = "ods_c_ola_record";
    private static final String PG_DWD_OLA_TABLE = "dwd_incident_ola_record";
    // Mysql表
    private static final String MYSQL_OLA_TABLE = "c_ola_record";
    // Flink 表
    private static final String F_PG_ODS_OLA_TABLE = "pg_ods_c_ola_record";
    private static final String F_PG_DWD_OLA_TABLE = "pg_dwd_incident_ola_record";
    private static final String F_MYSQL_OLA_TABLE = "f_c_ola_record";
    public static final String F_DWD_OLA_TABLE = "f_dwd_ola";
    private static final String F_ICE_OLA_TABLE = "f_ice_ola";

    // Iceberg表
    private static String ICEBERG_OLA_TABLE = "dwd_incident_ola_record";

    /**
     * run
     *
     * @param flinkEnv
     * @throws Exception
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);
        // Sink PG Conf
        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(SINK_DB_GROUP)
                .build();
        // Source Mysql Conf
        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(SOURCE_DB_GROUP)
                .build();
        // 注册Ice
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(F_ICE_OLA_TABLE)
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(
                                        catalog.getTableInstance(
                                                hiveDbName,
                                                ICEBERG_OLA_TABLE
                                        )
                                ).primaryKeyList(
                                        new String[]{"id"}
                                )
                        )
                        .build()
        );
        // 注册PG表
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(F_PG_ODS_OLA_TABLE)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        PG_ODS_OLA_TABLE,
                                        DatabaseEnum.PGSQL,
                                        pgsqlDBConf
                                )
                        )
                        .build()
        );
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(F_PG_DWD_OLA_TABLE)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        PG_DWD_OLA_TABLE,
                                        DatabaseEnum.PGSQL,
                                        pgsqlDBConf
                                )
                        )
                        .build()
        );
        String mysqlNodeMap = ""
                + "[\n"
                + "    {\n"
                + "        \"rdbTable\":\"c_ola_record\",\n"
                + "        \"fTable\":\"f_c_ola_record\"\n"
                + "    }\n"
                + "]";
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode mysqlNodes = mapper.readValue(mysqlNodeMap, ArrayNode.class);

        buildSourceView(mysqlDBConf, flinkEnv, mysqlNodes, DatabaseEnum.MYSQL, parameterTool,true);

        // DWD OLA计算
        Table dwdOlaTable = tEnv.sqlQuery(
                String.format(OLASql.DWD_INCIDENT_OLA_SQL, F_MYSQL_OLA_TABLE)
        );
        tEnv.createTemporaryView(F_DWD_OLA_TABLE, dwdOlaTable);
        StatementSet sts = flinkEnv.stmtSet();
        // 出库PG
        sts.addInsertSql(insertIntoSql(
                F_MYSQL_OLA_TABLE,
                F_PG_ODS_OLA_TABLE,
                tEnv.from(F_PG_ODS_OLA_TABLE),
                PGSQL
        )).addInsertSql(insertIntoSql(
                F_DWD_OLA_TABLE,
                F_PG_DWD_OLA_TABLE,
                tEnv.from(F_PG_DWD_OLA_TABLE),
                PGSQL
        )).addInsertSql(insertIntoSql(
                F_DWD_OLA_TABLE,
                F_ICE_OLA_TABLE,
                tEnv.from(F_ICE_OLA_TABLE),
                ICEBERG
        ));

    }
}