package com.tencent.andata;

import com.tencent.andata.applications.IncidentApplication;
import com.tencent.andata.applications.OlaApplication;
import com.tencent.andata.utils.ExceptionWrapperUtil;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.udf.LongTimestampTransform;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class Application {

    /**
     * main
     *
     * @param args 参数列表
     * @throws Exception 异常
     */
    public static void main(String[] args) throws Exception {
        FlinkEnvUtils.FlinkEnv flinkEnv = FlinkEnvUtils.getStreamTableEnv(args);
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        tEnv.createFunction("long_to_sql_timestamp", LongTimestampTransform.LongToSqlTimestamp.class);
        final String hiveDbName = rainbowUtils.getStringValue("conf", "ods_database");
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        // 需要执行的应用
        List<Object> appList = new ArrayList<>();
        appList.add(OlaApplication.builder().hiveDbName(hiveDbName).build());
        appList.add(IncidentApplication.builder().hiveDbName(hiveDbName).build());
        // 遍历appList，调用run方法
        appList.forEach(ExceptionWrapperUtil.consumer(app -> MethodUtils.invokeMethod(
                app,
                "run",
                flinkEnv,
                new IcebergCatalogReader(),
                parameterTool
        )));
        StatementSet sts = flinkEnv.stmtSet();
        sts.execute();

        StreamExecutionEnvironment env = flinkEnv.env();
        env.setParallelism(Integer.parseInt(parameterTool.get("parallelism", "2")));
        env.disableOperatorChaining();
        env.execute("Incident Data Process");
    }

}