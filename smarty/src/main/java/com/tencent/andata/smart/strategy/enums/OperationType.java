package com.tencent.andata.smart.strategy.enums;

import io.vavr.collection.List;

public enum OperationType {

    UNKNOWN(0, "其它"),

    CREATE(1, "建单"),

    RETRIEVE(2, "进入详情"),

    PULL(3, "认领"),

    URGE(4, "催单"),

    TRANSFER(5, "转单"),

    DISPATCH(6, "派单"),

    SEPARATE(7, "拆单"),

    REPLY(8, "回复"),

    CUSTOMER_REPLY(9, "客户回复"),

    TRANSFER_RESPONSIBLE(10, "交接"),

    WAIT_CUSTOMER_ADD_INFO(11, "待客户补充"),

    CLOSE_APPLICATION(12, "申请结单"),

    AGREE_CLOSE_APPLICATION(13, "同意并继续处理"),

    DISAGREE_CLOSE_APPLICATION(14, "不同意申请结单"),

    WAIT_CUSTOMER_CLOSE(15, "待客户确认结单"),

    CLOSE(16, "结单"),

    CANCEL(17, "撤销"),

    DELETE(18, "删除"),

    EDIT(19, "编辑"),

    COMMENT(21, "评论"),

    REFUSE(22, "拒单"),

    REPORT_FAILURE(23, "上报故障"),

    BATCH_REPLY_FAILURE(24, "故障单批量回复"),

    VNC_AUTH_REQUEST(25, "VNC授权"),

    VNC_VIEW_URL(26, "VNC查看链接"),

    APPRAISE(27, "评价"),

    ADD_INFO_APPLICATION(28, "申请补充或待复现"),

    TRANSFER_TAPD_BUG(29, "转TAPD Bug"),

    SET_DELAY_DISPATCH(30, "设置延迟派单"),

    COMPLAINT(31, "投诉"),

    TRANSFER_TAPD_STORY(32, "转需求"),

    PULL_RESPONSIBLE(33, "认领客户代表"),

    TRANSFER_POINT_STONE(34, "转点石"),

    TO_CONFIRM_RESTORE(35, "待确认业务恢复"),

    RESTORED(36, "已恢复分析根因"),

    RESET_STATUS(37, "重新打开"),

    NOT_RESTORED(38, "未恢复"),

    TRANSFER_TAPD(39, "转TAPD"),

    AGREE_ADD_INFO_APPLICATION(40, "同意申请补充"),

    DISAGREE_ADD_INFO_APPLICATION(41, "不同意申请补充"),

    PENDING_CHANGE(42, "待变更(需出包修复)"),

    PULL_INCIDENT_MANAGER(43, "认领事件经理"),

    EFFECTIVE_CALL(44, "有效外呼"),

    NONEFFECTIVE_CALL(45, "无效外呼"),

    CHANGE_TICKET_RELATION(46, "关联变更单"),

    NOT_RESTORED_ANALYSIS(47, "待查根因"),

    INSURE_ARCHIVE(48, "确认归档"),

    WITH_DRAW(49, "撤回"),

    BUILD_GROUP_CHAT(50, "拉群"),

    INCIDENT_MANAGER_ASSIGN(51, "事件经理派单"),

    PROGRESS_UPDATE(52, "更新"),

    PROGRESS_REPORT(53, "报备"),

    SYSTEM_REPLY(54, "系统回复"),

    FSM_UPDATE_PLAN_COMPLETE_TIME(55, "修改预计完成时间"),

    AUTO_DIAGNOSE(56, "自动诊断"),

    DIAGNOSE_FAILED(57, "诊断失败"),

    DIAGNOSE_SUCCESS(58, "诊断成功"),

    DIAGNOSE_ARTICLE(59, "精选"),

    DIAGNOSE_MANUAL(60, "手动诊断"),

    REFUSE_TEG_FIRST_TIME(61, "拒单"),

    WAIT_CUSTOMER_VALIDATION(62, "待客户实施或验证"),

    WAIT_PACKAGED(63, "待出包");

    private final int code;
    private final String description;

    OperationType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return description;
    }

    public static OperationType of(int code) {
        return List.of(values())
                .filter(t -> t.code == code)
                .getOrElseThrow(() -> new IllegalArgumentException("Unknown operation type: " + code));
    }
}