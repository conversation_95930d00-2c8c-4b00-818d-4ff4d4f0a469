package com.tencent.andata.smart.utils.splice.impl;


import static com.tencent.andata.smart.enums.OperatorType.CUSTOMER;
import static com.tencent.andata.smart.enums.OperatorType.STAFF;
import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;

import com.tencent.andata.smart.enums.PostType;
import com.tencent.andata.smart.enums.WebIMOperationType;
import com.tencent.andata.smart.lookup.QuestionTemplateQuery;
import com.tencent.andata.smart.strategy.Strategy;
import com.tencent.andata.smart.strategy.enums.Scene;
import com.tencent.andata.smart.utils.DutyInfoUtils;
import com.tencent.andata.smart.utils.StaffInfoUtils;
import com.tencent.andata.smart.utils.util.ContentClean;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.collection.List;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.Builder;
import lombok.Value;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class WebIMOperationQASplice extends WebIMOperationRCSplice {

    private static final List<WebIMOperationType> TRIGGER_ACTION_OPERATIONS = List.of(
            WebIMOperationType.ASSIGN,
            WebIMOperationType.COMPLAIN,
            WebIMOperationType.APPLY_FINISH,
            WebIMOperationType.USER_ACTIVELY_FINISH
    );

    private static final QuestionTemplateQuery dbQuery = new QuestionTemplateQuery();

    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return item.map(this::buildReplyMessage).get().getOrElse("") + "\n";
    }

    private Option<String> buildReplyMessage(ElementContext<JsonNode> item) {
        return Option.of(item)
                .map(this::createMessageComponents)
                .map(this::formatMessage);
    }

    /*
     * 创建消息对组件
     */
    private Tuple2<MessageComponent, MessageComponent> createMessageComponents(ElementContext<JsonNode> item) {
        return Tuple.of(buildMessageComponent(item.getPrevious()), buildMessageComponent(item.getCurrent()));
    }

    /*
     * 创建消息组件
     */
    private MessageComponent buildMessageComponent(JsonNode item) {
        return Option.of(item)
                .map(i ->
                        Try.of(() ->
                                MessageComponent
                                        .builder()
                                        .role(getRole(i))
                                        .staffInfo(getStaffInfo(i))
                                        .time(formatTime(i, Scene.WebIM))
                                        .rpcName(getOperationType(i).getOrNull())
                                        .conversationId(i.get("conversation_id").asText())
                                        .replyContent(extractReplyContent(i).getOrElse(""))
                                        .valueOfPrimaryKey(i.get("value_of_primary_key").asText())
                                        .build()
                        ).getOrElse(MessageComponent.builder().build()))
                .getOrElse(MessageComponent.builder().build());
    }

    /*

     */
    private String formatMessage(Tuple2<MessageComponent, MessageComponent> comps) {
        switch (WebIMOperationType.fromCode(comps._2.rpcName).get()) {
            case ASSIGN:
            case COMPLAIN:
            case APPLY_FINISH:
            case USER_ACTIVELY_FINISH:
                return triggerAction(comps);
            case SEND_USER_MSG:
            case SEND_ZX_MSG:
                return handleReplay(comps).getOrElse("");
            case CREATE:
                return handleCreate(comps._2).getOrElse("");
            default:
                return null;
        }
    }

    /*
     * 处理触发动作，拼接触发动作信息
     */
    private String triggerAction(Tuple2<MessageComponent, MessageComponent> comps) {

        Option<WebIMOperationType> action = Option
                .of(comps._2.rpcName)
                .map(WebIMOperationType::fromCode)
                .filter(operationType -> TRIGGER_ACTION_OPERATIONS.contains(operationType.getOrNull()))
                .get();

        return Option.of(action)
                .map(type -> Match(type.getOrNull())
                        .<String>of(
                                Case($(WebIMOperationType.ASSIGN), () -> handleAssign(comps).getOrNull()),
                                Case($(WebIMOperationType.COMPLAIN), () -> handleComplain(comps._2).getOrNull()),
                                Case($(WebIMOperationType.APPLY_FINISH), () -> handleApplyFinish(comps._2).getOrNull()),
                                Case($(WebIMOperationType.USER_ACTIVELY_FINISH), () -> handleUserFinish(comps._2).getOrNull()),
                                Case($(), () -> null)
                        )
                ).getOrNull();
    }


    /*
     * 获取坐席角色信息
     */
    private String getRole(JsonNode item) {
        return Option.of(item.get("rpc_name").asText())
                .map(rpcName -> Match(WebIMOperationType.fromCode(rpcName).getOrNull())
                        .<String>of(
                                Case($(WebIMOperationType.CREATE), () -> CUSTOMER.getDesc()), // 建单
                                Case($(WebIMOperationType.ASSIGN), () -> getStaffInfo(item)), // 转单
                                Case($(WebIMOperationType.COMPLAIN), () -> CUSTOMER.getDesc()), // 投诉
                                Case($(WebIMOperationType.SEND_ZX_MSG), () -> getStaffInfo(item)), // 坐席回复
                                Case($(WebIMOperationType.SEND_USER_MSG), () -> CUSTOMER.getDesc()), // 客户回复
                                Case($(WebIMOperationType.APPLY_FINISH), () -> getStaffInfo(item)), // 待客户确认结单
                                Case($(WebIMOperationType.USER_ACTIVELY_FINISH), () -> CUSTOMER.getDesc()), // 客户侧结束会话
                                Case($(), () -> getStaffInfo(item))
                        )
                ).getOrNull();
    }

    /*
     * 格式化坐席角色信息
     */
    private String getStaffInfo(JsonNode item) {
        Option<String> baseInfo = PostType
                .getDesc(item.get("post").asText())
                .map(post -> String.format("%s %s %s",
                        STAFF.getDesc(),
                        post,
                        StaffInfoUtils.getUserName(item.get("current_staff").asText()))
                );

        String dutyInfo = DutyInfoUtils.getDutyName(item.get("fact_assign").asInt());

        return Option.when(baseInfo.isDefined() && dutyInfo != null, String.format("%s （%s）", baseInfo.get(), dutyInfo))
                .getOrElse(baseInfo.getOrNull());
    }

    /*
     * 处理转单信息
     */
    private Option<String> handleAssign(Tuple2<MessageComponent, MessageComponent> comps) {
        return Option.of(comps._1)
                .filter(c -> !StringUtils.isBlank(comps._1.staffInfo))
                .filter(c -> !comps._1.staffInfo.contains("坐席 其它"))
                .filter(c -> !StringUtils.equals(comps._1.staffInfo, comps._2.staffInfo))
                .map(c -> String.format("%s %s触发动作：转单，将会话转移给 %s", comps._2.time, comps._1.staffInfo, comps._2.staffInfo));
    }

    /*
     * 处理投诉信息
     */
    private Option<String> handleComplain(MessageComponent message) {
        return Option.of(String.format("%s %s触发动作：投诉", message.time, message.role));
    }

    /*
     * 处理自动结单信息
     */
    private Option<String> handleAutoFinish(MessageComponent message) {
        return Option.of(String.format("%s 超过3天系统自动结单", message.time));
    }

    /*
     * 处理待客户确认结单信息
     */
    private Option<String> handleApplyFinish(MessageComponent message) {
        return Option.of(String.format("%s %s触发动作：待客户确认结单", message.time, message.role));
    }

    /*
     * 处理建单信息
     * 获取建单模板数据
     */
    private Option<String> handleCreate(MessageComponent message) {
        // Only process CREATE if not already processed
        String content = Match(message.replyContent.contains("提单信息"))
                .of(
                        Case($(false), () -> getTicketTemplate(message)),
                        Case($(true), () -> message.replyContent.replace("提单信息", "客户回复")),
                        Case($(), () -> null)
                );

        // 建单模板数据不能为空
        if (StringUtils.isBlank(content)) {
            return Option.none();
        }

        return Option.of(String.format("%s %s", message.time, ContentClean.UserContentClean(content)));
    }

    /*
     * 从dbQuery中获取提单模板数据
     */
    private String getTicketTemplate(MessageComponent message) {
        return dbQuery.getTicketTemplate(message.conversationId)
                .filter(t -> !StringUtils.isBlank(t))
                .map(t -> "客户回复：" + t)
                .getOrElse("");
    }

    /*
     * 处理客户/客服回复信息
     */
    private Option<String> handleReplay(Tuple2<MessageComponent, MessageComponent> comps) {
        if (StringUtils.isEmpty(comps._2.replyContent)) {
            return null;
        }
        return Option.of(String.format("%s %s回复：%s", comps._2.time, comps._2.role, comps._2.replyContent));
    }

    /*
     * 处理客户侧结束会话信息
     */
    private Option<String> handleUserFinish(MessageComponent message) {
        return Option.of(String.format("%s %s触发动作：结单", message.time, message.role));
    }

    @Value
    @Builder
    private static class MessageComponent {

        String role;
        String time;
        String rpcName;
        String staffInfo;
        String replyContent;
        String conversationId;
        String valueOfPrimaryKey;
    }
}