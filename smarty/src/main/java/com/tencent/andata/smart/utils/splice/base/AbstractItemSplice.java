package com.tencent.andata.smart.utils.splice.base;

import static com.tencent.andata.utils.TimeUtil.getEpochMilli;
import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.strategy.Strategy;
import com.tencent.andata.smart.strategy.enums.Scene;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import com.tencent.andata.smart.utils.util.ContentClean;
import com.tencent.andata.smart.utils.splice.ItemSplice;
import io.vavr.API;
import io.vavr.Predicates;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import io.vavr.control.Try;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

/**
 * ItemSplice抽象基类,实现通用逻辑
 */
public abstract class AbstractItemSplice implements ItemSplice {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    protected static final Map<String, Scene> sceneMap = HashMap.ofEntries(
            API.Tuple("ticket_operation", Scene.Ticket),
            API.Tuple("webim", Scene.WebIM),
            API.Tuple("group", Scene.Group),
            API.Tuple("c2000", Scene.Group)
    );

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final FlinkLog logger = FlinkLog.getInstance();

    @Override
    public Option<String> splice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return Try.of(() -> doSplice(item, strategy))
                  .filter(content -> !isNullContent(content))
                  .toOption();
    }

    /**
     * 子类实现具体的拼接逻辑
     */
    protected abstract String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy);

    /**
     * 清理内容
     */
    protected String cleanContent(String content) {
        return ContentClean.UserContentClean(content);
    }

    /**
     * 判断内容是否为空
     */
    protected static boolean isNullContent(String content) {
        if (content == null) {
            return true;
        }
        content = content.trim();
        return content.equalsIgnoreCase("null") || StringUtils.isEmpty(content);
    }

    /**
     * 格式化WebIM时间
     */
    protected static String formatTime(JsonNode item, Scene scene) {
        String timeField = getTime(item, scene);

        return String.format("%s %s", timeField.substring(5, 10), timeField.substring(11, 16));
    }

    protected static Option<String> getOperationType(JsonNode item) {
        String dataType = item.get("data_type").asText();
        return API.Match(sceneMap.get(dataType).getOrNull())
                  .of(
                          Case($(Scene.Ticket), () -> Option.of(item.get("operation_type").asText())),
                          Case($(Scene.WebIM), () -> Option.of(item.get("rpc_name").asText())),
                          Case($(Scene.Group), () -> Option.of("")),
                          Case($(), Option::none)
                  );
    }

    /**
     * 格式化时间戳
     */
    private static String formatTimeStamp(Long timeField) {
        return Instant
                .ofEpochMilli(timeField)
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime()
                .format(FORMATTER);
    }

    protected Option<JsonNode> getFirstData(Option<Strategy> strategy) {
        return strategy
                .filter(s -> s.trigger != null && s.trigger.data != null && s.trigger.data.length > 0)
                .flatMap(s -> Option.of(s.trigger.data[0]))
                .flatMap(this::parseTriggerData)
                .onEmpty(() -> logger.error("[AbstractItemSplice] Failed to parse first data from strategy: " + strategy));
    }

    private Option<JsonNode> parseTriggerData(Object data) {
        return Match(data).of(
                Case($(Predicates.instanceOf(JsonNode.class)), Option::of),
                Case($(), d -> Try.of(() -> MAPPER.readTree(d.toString()))
                                  .onFailure(e -> logger.error("Failed to parse data element: " + data + " errMsg:" + e))
                                  .toOption())
        );
    }

    private long getTriggeredTimestamp(Option<Strategy> strategy) {
        return getFirstData(strategy)
                .map(data -> getTime(data, strategy.get().scene))
                .map(x -> getEpochMilli(x))
                .getOrElse(0L);
    }

    private long getCurrentItemTimestamp(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return getEpochMilli(getTime(item.get().getCurrent(), strategy.get().scene));
    }

    // 判断当前item是否在getTriggeredTimestamp之前
    protected boolean isBeforeTriggeredTimestamp(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return getCurrentItemTimestamp(item, strategy) <= getTriggeredTimestamp(strategy);
    }

    // 获取时间字段
    private static String getTime(JsonNode item, Scene scene) {
        return API.Match(scene)
                  .of(
                          Case($(Scene.Ticket), () -> item.get("operate_time").asText()),
                          Case($(Scene.WebIM), () -> item.get("record_update_time").asText()),
                          Case($(Scene.Group), () -> formatTimeStamp(item.get("msg_time").asLong())),
                          Case($(Scene.C2000), () -> formatTimeStamp(item.get("msg_time").asLong())),
                          Case($(), () -> {throw new IllegalArgumentException("Unsupported scene: " + scene);})
                  );
    }
}