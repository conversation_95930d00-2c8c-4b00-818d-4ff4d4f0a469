package com.tencent.andata.smart.utils.splice.impl;

import com.tencent.andata.smart.strategy.Strategy;
import com.tencent.andata.smart.strategy.enums.Scene;
import com.tencent.andata.smart.utils.splice.base.AbstractItemSplice;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.Function1;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.Builder;
import lombok.Value;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * 群风控拼接实现
 */
public class GroupMsgRCSplice extends AbstractItemSplice {

    protected static final String CUSTOMER_TYPE = "客户";
    protected static final String ROBOT_TYPE = "机器人";
    protected static final String STAFF_TYPE = "客服";

    private static final Map<String, Function1<Option<String>, String>> ROLE_FORMATTERS = HashMap.of(
            CUSTOMER_TYPE, name -> CUSTOMER_TYPE,
            ROBOT_TYPE, name -> "坐席",
            STAFF_TYPE, name -> name.map(n -> "坐席 " + n).getOrElse("坐席")
    );

    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return item.filter(i -> isBeforeTriggeredTimestamp(item, strategy))
                .flatMap(node -> buildMessageComponent(item).map(this::formatMessage))
                .getOrNull();
    }

    protected Option<MessageComponent> buildMessageComponent(Option<ElementContext<JsonNode>> node) {
        return node.map(ElementContext::getCurrent)
                .map(item ->
                        Try.of(() -> MessageComponent
                                .builder()
                                .time(formatTime(item, Scene.Group))
                                .role(getRole(item))
                                .content(extractReplyContent(item))
                                .build()
                        ).getOrNull())
                .filter(c -> c.getContent().isDefined());
    }

    protected String formatMessage(MessageComponent components) {
        return String.format("%s回复：%s\n",
                components.role,
                components.content.get());
    }

    protected String getRole(JsonNode item) {
        return Option.of(item)
                .flatMap(node -> extractNodeText(node, "sender_type"))
                .filter(r -> !r.equals(ROBOT_TYPE))
                // 群风控，不需要机器人回复的内容，但是需要将“客服”转换为“坐席”
                .map(r -> r.equals(STAFF_TYPE) ? STAFF_TYPE : CUSTOMER_TYPE)
                .getOrElse("坐席");
    }

    protected Option<String> extractNodeText(JsonNode node, String fieldName) {
        return Option.of(node.get(fieldName))
                .map(JsonNode::asText);
    }

    protected Option<String> extractReplyContent(JsonNode node) {
        return Option.of(node)
                .flatMap(n -> extractNodeText(n, "display_content"))
                .map(this::cleanContent)
                .filter(content -> !isNullContent(content));
    }

    @Value
    @Builder
    protected static class MessageComponent {

        public String time;
        public String role;
        public Option<String> content;
    }
}