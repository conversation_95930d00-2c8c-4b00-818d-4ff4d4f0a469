package com.tencent.andata.smart.strategy.enums;

import io.vavr.collection.List;

public enum TicketServiceChannel {

    IMCC(1, "IMCC", "IMCC"),

    CALLCENTER(2, "CALLCENTER", "CallCenter"),

    MC(3, "MC", "MC"),

    RTX(4, "RTX", "腾讯云小助手"),

    MAIL(5, "MAIL", "邮件"),

    MOBIL_PHONE(6, "MOBIL_PHONE", "备勤手机"),

    QQ_GROUP(7, "QQ_GROUP", "QQ群"),

    BBS(9, "BBS", "官网论坛"),

    RETURN_VISIT(10, "RETURN_VISIT", "回访"),

    PUBLIC_SENTIMENT(11, "PUBLIC_SENTIMENT", "舆情"),

    COMPLAINT(12, "COMPLAINT", "投诉/建议"),

    UNSATISFIED(13, "UNSATISFIED", "不满意"),

    DUTY(14, "DUTY", "duty(工单告警)"),

    XINGYUNCBS(15, "XINGYUNCBS", "母机故障"),

    FAULT_NOTICE(16, "FAULT_NOTICE", "故障通知"),

    CUSTOMER_AUTHORIZATION(17, "CUSTOMER_AUTHORIZATION", "客户授权"),

    BIG_CUSTOMER_QUESTION(18, "BIG_CUSTOMER_QUESTION", "大客户问题"),

    QQ_GROUP_AUTO(19, "QQ_GROUP_AUTO", "QQ群自动录单"),

    MC_VIP_ASK_CALLBACK(20, "MC_VIP_ASK_CALLBACK", "预约回访"),

    QQGROUP_MSG_MONITOR(21, "QQGROUP_MSG_MONITOR", "大客户工作台"),

    SC_TICKET(22, "SC_TICKET", "管局指令通知"),

    ORACLE(23, "ORACLE", "Oracle"),

    SPECIAL_SERVICE(24, "SPECIAL_SERVICE", "专项服务组"),

    QQGROUP_BEIAN(25, "QQGROUP_BEIAN", "大客户备案支持"),

    BEIAN(26, "BEIAN", "备案专项支持"),

    KA(27, "KA", "大客户群"),

    P_CLOUD(28, "P_CLOUD", "私有化售后"),

    P_CLOUD_INSALES(29, "P_CLOUD_INSALES", "私有化售中/POC测试"),

    TENCENT_ON_QCLOUD(30, "TENCENT_ON_QCLOUD", "内部服务台"),

    CLOUD_ARCHITECTURE_PLATFORM(31, "CLOUD_ARCHITECTURE_PLATFORM", "云架平"),

    TKE_CONTAINER_OR_BEIAN(32, "TKE_CONTAINER_OR_BEIAN", "内部建单(TKE容器/备案等)"),

    SAFE_P_CLOUD(33, "SAFE_P_CLOUD", "安全内部建单"),

    PUBLIC_CLOUD_SALE(34, "PUBLIC_CLOUD_SALE", "公有云售中"),

    CSIG_KNOWLEDGE_PLATFORM(35, "CSIG_KNOWLEDGE_PLATFORM", "CSIG知识平台"),

    AMP_MONITOR(36, "AMP_MONITOR", "AMP监控"),

    SAAS_PRODUCT(37, "SAAS_PRODUCT", "SaaS产品"),

    REPORTING_PLATFORM(38, "REPORTING_PLATFORM", "举报平台"),

    MC_CONVERSATION(39, "MC_CONVERSATION", "在线售后"),

    P_CLOUD_COMPANY(40, "P_CLOUD_COMPANY", "私有化售后(供应商)"),

    P_CLOUD_INSALES_COMPANY(41, "P_CLOUD_INSALES_COMPANY", "私有化售中/POC测试(供应商)"),

    P_CLOUD_ASP(42, "P_CLOUD_ASP", "私有云ASP"),

    E_GOVERNMENT_CLOUD(43, "E_GOVERNMENT_CLOUD", "政务云"),

    QIDIAN_BOSS(44, "QIDIAN_BOSS", "企点BOSS"),

    OMP(45, "OMP", "经营平台"),

    WECHAT_PAY_MLT(46, "WECHAT_PAY_MLT", "微信支付中长尾商户"),

    WECHAT_PAY_KA(47, "WECHAT_PAY_KA", "微信支付KA商户"),

    CONVERSATION_PRESALE(48, "CONVERSATION_PRESALE", "在线售前"),

    CONVERSATION_TENCENT_MEETING(49, "CONVERSATION_TENCENT_MEETING", "腾讯会议(售后)"),

    CONVERSATION_TENCENT_MEETING_PRESALE(50, "CONVERSATION_TENCENT_MEETING_PRESALE", "腾讯会议(售前)"),

    PARTNER_SUPPORT(51, "PARTNER_SUPPORT", "合作伙伴支持"),

    WECHAT_FACE_PAY_KA(52, "WECHAT_FACE_PAY_KA", "微信支付刷脸KA商户群"),

    CONVERSATION_MEETING_SECRETARY(53, "CONVERSATION_MEETING_SECRETARY", "腾讯会议小秘书"),

    FSM(60, "FSM", "服务请求"),

    XINGYUN_ALERT(61, "XINGYUN_ALERT", "星云平台告警"),

    TRTC(62, "TRTC", "终端专家服务"),

    CLOUD_PRODUCT_SUPPORT_ASSISTANT(63, "CLOUD_PRODUCT_SUPPORT_ASSISTANT", "云产品技术支持助手"),

    SELFBUILT_FOR_SECURITY(64, "SELFBUILT_FOR_SECURITY", "安全行业自建"),

    QIDIAN_BIG_CUSTOMER(65, "QIDIAN_BIG_CUSTOMER", "企点大客户通道"),

    PARTNER_CPT(66, "PARTNER_CPT", "伙伴CPT"),

    P_CLOUD_CONVERSATION(69, "P_CLOUD_CONVERSATION", "私有云在线"),

    QIDIAN_P_CLOUD(72, "QIDIAN_P_CLOUD", "企点私有化"),

    QIDIAN_P_CLOUD_INSALES(73, "QIDIAN_P_CLOUD_INSALES", "企点私有化(售中)"),

    SAFE_P_CLOUD_PRIVATE(74, "SAFE_P_CLOUD_PRIVATE", "安全内部建单-私有云"),

    WECHAT_CHANNELS_SHOP(75, "WECHAT_CHANNELS_SHOP", "视频号小店在线"),

    WECHAT_CHANNELS_SHOP_KA(76, "WECHAT_CHANNELS_SHOP_KA", "视频号小店KA群");

    private final int id;
    private final String code;
    private final String description;

    TicketServiceChannel(int id, String code, String description) {
        this.id = id;
        this.code = code;
        this.description = description;
    }

    public int getId() {
        return id;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static TicketServiceChannel fromId(int id) {
        return List.of(values())
                .filter(t -> t.id == id)
                .getOrElseThrow(() -> new IllegalArgumentException("Unknown target status: " + id));
    }
}