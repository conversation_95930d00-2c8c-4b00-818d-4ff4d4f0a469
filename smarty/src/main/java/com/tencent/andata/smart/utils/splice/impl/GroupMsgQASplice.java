package com.tencent.andata.smart.utils.splice.impl;

import com.tencent.andata.smart.strategy.Strategy;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.Function1;
import io.vavr.Tuple;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * 群质检拼接实现
 */
public class GroupMsgQASplice extends GroupMsgRCSplice {

    private static final Map<String, Function1<Option<String>, String>> ROLE_FORMATTERS = HashMap.of(
            CUSTOMER_TYPE, name -> name.map(n -> "客户 " + n).getOrElse("客户"),
            STAFF_TYPE, name -> name.map(n -> "坐席 " + n).getOrElse("坐席"),
            ROBOT_TYPE, name -> "坐席"
    );

    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return item.flatMap(node -> buildMessageComponent(item).map(this::formatMessage))
                .getOrNull();
    }

    @Override
    protected String formatMessage(MessageComponent components) {
        return String.format("\n%s %s回复：%s",
                components.time,
                components.role,
                components.content.get());
    }

    @Override
    protected String getRole(JsonNode item) {
        return Option.of(item)
                .map(node -> Tuple.of(extractNodeText(node, "sender_type"), extractSenderName(node)))
                .flatMap(tuple ->
                        tuple._1.flatMap(ROLE_FORMATTERS::get)
                                .map(formatter -> formatter.apply(tuple._2))
                )
                .getOrElse("坐席");
    }

    private Option<String> extractSenderName(JsonNode node) {
        Option<String> cleanedSenderName = extractNodeText(node, "sender_name")
                .map(this::cleanName);
        
        return extractNodeText(node, "rtx")
                .filter(StringUtils::isNotEmpty)
                .map(rtx -> String.format("%s(%s)", rtx, cleanedSenderName.getOrElse("")))
                .orElse(cleanedSenderName);
    }

    private String cleanName(String name) {
        return name.replaceAll("\\(.*?\\)", "").trim();
    }
}