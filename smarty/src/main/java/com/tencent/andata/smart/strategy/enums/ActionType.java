package com.tencent.andata.smart.strategy.enums;

public enum ActionType {
    URGE("4", "催单"),
    TRANSFER("5", "转单"),
    COMPLAINT("31", "投诉"),
    WAIT_CONFIRM("15", "待客户确认结单");

    private final String code;
    private final String desc;

    ActionType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() { return code; }
    public String getDesc() { return desc; }

    public static String getDesc(String code) {
        for (ActionType type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }
}