CREATE TABLE dwd_demand_story_base_info
(
    `id`                            BIGINT DEFAULT '0'     COMMENT '唯一性主键'
    ,`dwd_create_time`              DATETIME              COMMENT '创建时间'
    ,`title`                        STRING                 COMMENT  '标题'
    ,`background`                   STRING                 COMMENT  '背景信息'
    ,`desc`                         STRING                 COMMENT  '描述信息'
    ,`product`                      STRING                 COMMENT  '产品信息'
    ,`product_name`                 STRING                 COMMENT  '产品名称'
    ,`product_center`               STRING                 COMMENT  '产品中心'
    ,`service_first_scene`          STRING                 COMMENT  '产品信息一级id'
    ,`service_second_scene`         STRING                 COMMENT  '产品信息二级id'
    ,`status`                       STRING                 COMMENT  '需求单状态,1:初审,2:初审被拒,3:产品评估中,4:产品已采纳,5:暂不实现,6:产品未采纳，进行初审,7:产品未采纳，发起人补充材料,8:重复,9:已实现,10:已结单,11:被关闭'
    ,`current_operator`             STRING                 COMMENT  '当前处理人'
    ,`create_by`                    STRING                 COMMENT  '创建人'
    ,`create_at`                    DATETIME               COMMENT  '创建时间'
    ,`update_by`                    STRING                 COMMENT  '最近修改人'
    ,`update_at`                    DATETIME               COMMENT  '最近修改时间'
    ,`demand_manager`               STRING                 COMMENT  '需求经理'
    ,`develop_man`                  STRING                 COMMENT  '产研接口人'
    ,`kano_score`                   BIGINT                 COMMENT  'kano积分'
    ,`product_advises`              STRING                 COMMENT  '产品建议'
    ,`product_rtx`                  STRING                 COMMENT  '参与沟通产研的rtx'
    ,`dst_story_id`                 BIGINT                 COMMENT  '标记为重复时，被关联的单号'
    ,`tapd_id`                      STRING                 COMMENT  'tapd单号'
    ,`special`                      BIGINT                 COMMENT  '1-是个性化需求; 0-否个性化需求'
    ,`instance_id`                  STRING                 COMMENT  'tbpm流程实例ID'
    ,`online_time`                  DATETIME               COMMENT  '上线时间'
    ,`current_node`                 STRING                 COMMENT  'tbpm上当前的处理节点id'
    ,`channel`                      BIGINT                 COMMENT  '被哪个渠道关联，No = 0; // 未知 ServiceRequest = 1;// 服务请求 EventItem = 2;// 事件单 UserVoc = 3;// 用户之声 ProductExperience = 4; // 产品体验 AnalysisOfCompetingProducts = 5;// 竞品分析 DevelopersGroup = 6;// 开发者群 SurveyQuestionnaire = 7;// 调研问卷'
    ,`tapd_workspaceid`             STRING                 COMMENT  '使用的哪个tapd池'
    ,`create_by_id`                 STRING                 COMMENT  '创建者的andon用户id'
    ,`tapd_completed_states`        STRING                 COMMENT  'tapd结束状态'
    ,`is_import`                    BIGINT                 COMMENT  '是否导入'
    ,`is_deleted`                   BIGINT                 COMMENT  '是否删除'
    ,`exp_tapd_id`                  STRING                 COMMENT  '体验单号'
    ,`wxgroup_id`                   STRING                 COMMENT  '微信群id'
    ,`is_allies_support`            BIGINT                 COMMENT  '友商是否支持'
    ,`estimated_launch_time`        DATETIME               COMMENT  '预计上线时间'
    ,`is_custom_tapd`               BIGINT                 COMMENT  '是否绑定TAPD'
    ,`kano_type`                    BIGINT                 COMMENT  'kano记分类型'
    ,`sla_time`                     BIGINT                 COMMENT  'SLA时长'
    ,`cloud_type`                   BIGINT                 COMMENT  '云类型, 1:公有云，2:线下交付项目'
    ,`ltc_name`                     STRING                 COMMENT  '项目名称'
    ,`customer_name`                STRING                 COMMENT  '客户名称'
    ,`category`                     BIGINT                 COMMENT  '需求分类'
    ,`priority_level`               STRING                 COMMENT  '蜕变等级'
    ,`story_type`                   BIGINT                 COMMENT  '需求类型'
    ,`creator_arch`                 STRING                 COMMENT  '创建人组织架构'
) ENGINE=OLAP
PRIMARY KEY(`id`)
COMMENT "需求单基础信息表"
DISTRIBUTED BY HASH(`id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "id",
    "enable_persistent_index" = "true"
);

-- add camillemli 2023-09-21
ALTER TABLE dwd_demand_story_base_info
    ADD COLUMN (grade int COMMENT '优先级');


-- add cesarezhang 2023-12-18
ALTER TABLE dwd_demand_story_base_info
    ADD COLUMN (url int COMMENT 'url');