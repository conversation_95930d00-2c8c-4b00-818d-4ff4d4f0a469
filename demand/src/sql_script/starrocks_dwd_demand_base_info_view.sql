CREATE VIEW IF NOT EXISTS dwd_demand_base_info_view AS
SELECT
    t1.id,
    t1.title,
    t1.background,
    t1.`desc`,
    CAST(CONCAT(t2.service_scene_level1_name, '/', t2.service_scene_level2_name) AS varchar(65533)) AS product,
    t1.service_first_scene,
    t1.service_second_scene,
    t1.status,
    t1.current_operator,
    t1.create_by,
    t1.create_at,
    t1.update_by,
    t1.update_at,
    t1.demand_manager,
    t1.develop_man,
    t1.kano_score,
    t1.product_advises,
    t1.product_rtx,
    t1.dst_story_id,
    t1.tapd_id,
    t1.special,
    t1.instance_id,
    t1.online_time,
    t1.current_node,
    t1.channel,
    t1.tapd_workspaceid,
    t1.create_by_id,
    t1.tapd_completed_states,
    t1.is_import,
    t1.is_deleted,
    t1.exp_tapd_id,
    t1.wxgroup_id,
    t1.is_allies_support,
    t1.estimated_launch_time,
    t1.is_custom_tapd,
    t1.kano_type,
    t1.cloud_type,
    t1.ltc_name,
    t1.customer_name,
    t1.category,
    t1.priority_level,
    t1.story_type,
    t1.creator_arch,
    sla_time,
    t1.grade
FROM dwd_demand_story_base_info t1
         LEFT JOIN (
    SELECT
        service_scene_level2_id,
        MAX(service_scene_level1_name) AS service_scene_level1_name,
        MAX(service_scene_level2_name) AS service_scene_level2_name
    FROM dim_service_scenes
    GROUP BY service_scene_level2_id
) AS t2 ON t1.service_second_scene = t2.service_scene_level2_id;
