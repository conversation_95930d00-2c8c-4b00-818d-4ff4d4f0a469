CREATE TABLE dws_chatgpt_session
(
    `user`          STRING COMMENT '提问人（rtx）',
    `question_time` STRING COMMENT '首次提问时间',
    `session_id`    STRING COMMENT '会话id',
    `has_copy`      INT COMMENT '是否复制 1:已复制 0:未复制',
    `is_liked`      INT COMMENT '是否点赞 0:否 1:是',
    `is_disliked`   INT COMMENT '是否点踩 0:否 1:是',
    `qa_count`      INT COMMENT '该会话下的问答数量',
    `source`        INT COMMENT '会话来源 1:工单',
    `source_id`     STRING COMMENT '来源id',
    `model`         STRING COMMENT '模型, youtu/glm'
) ENGINE=OLAP
PRIMARY KEY(`user`,`question_time`)
DISTRIBUTED BY HASH(`user`,`question_time`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
