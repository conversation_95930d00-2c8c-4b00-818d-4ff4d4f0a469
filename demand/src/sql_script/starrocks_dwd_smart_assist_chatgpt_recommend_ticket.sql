CREATE TABLE dwd_smart_assist_chatgpt_recommend_ticket(
    `value_of_primary_key` STRING COMMENT '主键',
    `event_time`           STRING COMMENT '点击时间',
    `question_id`          STRING COMMENT '问题ID',
    `model`                STRING COMMENT '模型：youtu/glm',
    `user`                 STRING COMMENT '点击人',
    `ticket_url`           STRING COMMENT '工单链接',
    `ticket_id`            BIGINT COMMENT '工单ID'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
