CREATE TABLE dws_copilot_tool_tag_ticket
(
    `value_of_primary_key`      STRING COMMENT '主键',
    `user`                      STRING COMMENT '使用人',
    `click_time`                STRING COMMENT '使用时间',
    `question_id`               STRING COMMENT 'GLM问答ID',
    `tool_id`                   STRING COMMENT '工具ID',
    `tool_name`                 STRING COMMENT '工具名称',
    `question`                  STRING COMMENT '使用问题',
    `answer`                    STRING COMMENT '工具返回内容',
    `qa_source`                 BIGINT COMMENT '来源：1: 企业号 2: 智能辅助-工单控制台 3: 智能辅助-webim控制台 4: 智能辅助-cc控制台, 5: 群工作台',
    `session_id`                STRING COMMENT '问答会话ID',
    `ticket_id`                 STRING COMMENT '工单ID',
    `service_channel`           STRING COMMENT '服务通道',
    `service_scene_level1_name` STRING COMMENT '工单一级归档',
    `service_scene_level2_name` STRING COMMENT '工单二级归档',
    `service_scene_level3_name` STRING COMMENT '工单三级归档',
    `service_scene_level4_name` STRING COMMENT '工单四级归档'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
