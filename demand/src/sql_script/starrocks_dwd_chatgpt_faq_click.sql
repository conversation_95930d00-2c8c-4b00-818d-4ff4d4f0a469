CREATE TABLE dwd_chatgpt_faq_click
(
    `value_of_primary_key` STRING COMMENT '主键',
    `user` STRING COMMENT '点击人',
    `click_time` DATETIME COMMENT '点击时间',
    `question_id` STRING COMMENT '问答ID',
    `model` STRING COMMENT '模型：glm/youtu',
    `content` STRING COMMENT '内容',
    `url` STRING COMMENT '链接',
    `from` BIGINT COMMENT '来源 1: 正文 2: 知识注入',
    `type` BIGINT COMMENT '类型 1. 官网文档 2. FAQ 3.知识库 4.腾讯会议 5.知识库 6.官网文档 7.iWiki'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
