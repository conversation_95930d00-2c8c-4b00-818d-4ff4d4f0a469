CREATE VIEW IF NOT EXISTS dwd_demand_relation_view AS
SELECT
    t1.id,
    t1.story_id,
    t1.channel,
    t1.item_id,
    t1.item_title,
    t1.create_by,
    t1.create_at,
    t1.uin,
    t1.customer_name,
    CAST(t2.sales_grade AS varchar(65533)) AS customer_class,
    t1.grade,
    t1.expected_time,
    t1.dst_story_id,
    t1.status,
    t1.way_by,
    CAST(
            CASE
                WHEN t2.service_providers = '腾讯云' THEN '大客户'
                WHEN t2.service_providers IS NULL THEN '中长尾客户'
                ELSE '腰部客户'
                END AS varchar(65533)
        ) AS customer_category,
    t1.create_by_id,
    t1.is_reverse_relation,
    t1.ltc_name,
    t1.trade,
    t1.ltc_no,
    t1.final_customer,
    t1.jid,
    t1.ltc_level,
    t3.create_at as story_create_at
FROM dwd_demand_relation t1
         LEFT JOIN (
    SELECT
        uin,
        MAX(service_providers) AS service_providers,
        MAX(sales_grade) AS sales_grade
    FROM dwd_key_customer_information
    GROUP BY uin
) AS t2 ON t1.uin = t2.uin
left join dwd_demand_story_base_info t3
on t1.story_id = t3.id