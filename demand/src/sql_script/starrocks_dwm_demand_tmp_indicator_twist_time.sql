CREATE TABLE dwm_demand_tmp_indicator_twist_time
(
    `story_id`                          BIGINT   COMMENT '需求单ID',
    `to_product_evaluating_at`          DATETIME COMMENT '产品评估中扭转时间',
    `to_product_accept_at`              DATETIME COMMENT '产品已采纳扭转时间',
    `first_evaluate_failed_at`          DATETIME COMMENT '初审未通过扭转时间',
    `to_repeat_demand_at`               DATETIME COMMENT '重复需求扭转时间',
    `to_review_repeat_demand_at`        DATETIME COMMENT '复审重复需求扭转时间',
    `to_reopen_at`                      DATETIME COMMENT '重新打开扭转时间',
    `to_not_implemented_yet_at`         DATETIME COMMENT '暂不实现扭转时间',
    `to_product_not_accept_at`          DATETIME COMMENT '产品未采纳扭转时间',
    `reverse_twist_at`                  DATETIME COMMENT '反向扭转时间',
    `to_closed_at`                      DATETIME COMMENT '已关闭扭转时间',
    `to_first_evalute_failed_closed_at` DATETIME COMMENT '已关闭-初审未通过扭转时间',
    `to_product_not_accept_closed_at`   DATETIME COMMENT '已关闭-产品未采纳扭转时间',
    `first_to_product_evaluate_at`      DATETIME COMMENT '首次扭转为产品评估中时间'
) ENGINE=OLAP
PRIMARY KEY(`story_id`)
DISTRIBUTED BY HASH(`story_id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
