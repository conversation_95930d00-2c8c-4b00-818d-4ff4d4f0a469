CREATE TABLE dwm_demand_tmp_indicator_base
(
    `story_id`                            BIGINT    COMMENT '需求单ID',
    `is_pass_first_audit`                 BIGINT    COMMENT '是否经过初审',
    `dst_story_id`                        BIGINT    COMMENT '重复需求单ID',
    `create_at`                           DATETIME  COMMENT '创建时间',
    `online_time`                         DATETIME  COMMENT '上线时间',
    `status`                              BIGINT    COMMENT '需求单状态',
    `first_evaluate_faield_reason`        STRING    COMMENT '初审拒绝原因',
    `review_faield_reason`                STRING    COMMENT '复审拒绝原因',
    `repeat_source_story_id_list`         STRING    COMMENT '标记重复来源的需求单ID列表',
    `is_delay`                            BIGINT    COMMENT '需求是否延期',
    `first_evaluate_faield_second_reason` STRING    COMMENT '初审失败二级拒绝原因',
    `review_faield_second_reason`         STRING    COMMENT '复审失败二级拒绝原因'
) ENGINE=OLAP
PRIMARY KEY(`story_id`)
DISTRIBUTED BY HASH(`story_id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
