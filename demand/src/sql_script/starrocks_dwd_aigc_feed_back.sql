CREATE TABLE dwd_aigc_feed_back(
    `id` INT COMMENT '',
    `feed_back_id` STRING COMMENT '反馈id',
    `question_id` STRING COMMENT '问题id',
    `answer_id` STRING COMMENT '回答id',
    `ticket_id` STRING COMMENT '工单id',
    `question` STRING COMMENT '问题',
    `answer` STRING COMMENT '回答',
    `feed_back_state` STRING COMMENT '反馈状态',
    `improve_state` STRING COMMENT '改进状态',
    `feed_back_type` STRING COMMENT '反馈类型',
    `model_type` STRING COMMENT '模型类型',
    `feed_back_user` STRING COMMENT '反馈人',
    `knowledge_scene_id` STRING COMMENT '知识归档id',
    `knowledge_scene_name` STRING COMMENT '知识归档',
    `improve_recommend_text` STRING COMMENT '改进建议',
    `right_answer` STRING COMMENT '正确回答',
    `injected_knowledge_info` STRING COMMENT '注入知识',
    `selected_knowledge_index` STRING COMMENT '知识索引',
    `update_time` DATETIME COMMENT '修改时间',
    `create_time` DATETIME COMMENT '创建时间'
) ENGINE=OLAP
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
