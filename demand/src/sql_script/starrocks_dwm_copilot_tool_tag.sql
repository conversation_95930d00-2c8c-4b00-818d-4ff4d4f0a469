CREATE TABLE dwm_copilot_tool_tag
(
    `value_of_primary_key` STRING COMMENT '主键',
    `user`                 STRING COMMENT '使用人',
    `click_time`           STRING COMMENT '使用时间',
    `question_id`          STRING COMMENT 'GLM问答ID',
    `tool_id`              STRING COMMENT '工具ID',
    `tool_name`            STRING COMMENT '工具名称',
    `question`             STRING COMMENT '使用问题',
    `answer`               STRING COMMENT '工具返回内容',
    `session_id`           STRING COMMENT '问答会话ID',
    `qa_source`            BIGINT COMMENT '来源：1: 企业号 2: 智能辅助-工单控制台 3: 智能辅助-webim控制台 4: 智能辅助-cc控制台, 5: 群工作台'
) ENGINE=OLAP
PRIMARY KEY(`value_of_primary_key`)
DISTRIBUTED BY HASH(`value_of_primary_key`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
