CREATE TABLE dwm_demand_indicator_view_snapshot
(
    `story_id`                             INT COMMENT '需求单ID',
    `to_product_evaluate_time`             DATETIME COMMENT '产品评估中扭转时间',
    `to_product_accept_time`               DATETIME COMMENT '产品已采纳扭转时间',
    `first_evaluate_failed_time`           DATETIME COMMENT '初审未通过扭转时间',
    `to_repeat_demand_time`                DATETIME COMMENT '重复需求(初审)扭转时间',
    `to_review_repeat_demand_time`         DATETIME COMMENT '重复需求(复审)扭转时间',
    `to_reopen_time`                       DATETIME COMMENT '重新打开扭转时间',
    `to_not_implemented_yet_time`          DATETIME COMMENT '暂不实现扭转时间',
    `to_product_not_accept_time`           DATETIME COMMENT '产品未采纳扭转时间',
    `is_pass_first_audit`                  INT COMMENT '是否经过初审',
    `reverse_twist_time`                   DATETIME COMMENT '逆向扭转时间',
    `dst_story_id`                         INT COMMENT '标记重复需求单ID',
    `create_at`                            DATETIME COMMENT '需求单创建时间',
    `online_time`                          DATETIME COMMENT '上线时间',
    `status`                               INT COMMENT '需求单状态',
    `gongxing_time`                        DATETIME COMMENT '成为共性需求时间',
    `is_gongxing`                          INT COMMENT '是否为共性需求',
    `data_date`                            STRING COMMENT '数据快照日期',
    `commit_duration_second`               BIGINT COMMENT '需求创建时长',
    `product_evaluate_duration_second`     BIGINT COMMENT '产品评估耗时',
    `demand_process_duration_second`       BIGINT COMMENT '需求处理时长',
    `first_evaluate_duration_second`       BIGINT COMMENT '初审评估时长',
    `product_process_duration_second`      BIGINT COMMENT '产品处理时长',
    `first_evaluate_close_duration_second` BIGINT COMMENT '初审关单耗时',
    `product_close_duration_second`        BIGINT COMMENT '产品关单耗时',
    `first_evaluate_faield_reason`         STRING COMMENT '初审拒绝原因',
    `review_faield_reason`                 STRING COMMENT '复审拒绝原因',
    `repeat_source_story_id_list`          STRING COMMENT '标记重复来源的需求单ID列表',
    `to_closed_at`                         DATETIME COMMENT '已关闭扭转时间',
    `is_delay`                             BIGINT COMMENT '需求是否延期',
    `first_evaluate_faield_second_reason`  STRING COMMENT '初审失败二级拒绝原因',
    `review_faield_second_reason`          STRING COMMENT '复审失败二级拒绝原因',
    `close_reason`                         STRING COMMENT '关单原因'
) ENGINE=OLAP
DISTRIBUTED BY HASH(`story_id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
