CREATE TABLE dwd_demand_relation
(
    `id`                    BIGINT DEFAULT '0'    COMMENT '唯一性主键'
    ,`dwd_create_time`       DATETIME              COMMENT '创建时间'
    ,`story_id`              BIGINT                 COMMENT '需求单id'
    ,`channel`               BIGINT                 COMMENT '被哪个需求关联,0未知；1服务请求；2 事件单；3用户之声；4 产品体验；5竞品分析；6开发者群；7调研问卷'
    ,`item_id`               STRING                 COMMENT '关联来源的唯一标识，比如工单id等'
    ,`item_title`            STRING                 COMMENT '单据标题'
    ,`create_by`             STRING                 COMMENT '操作人'
    ,`create_at`             DATETIME               COMMENT '关联时间'
    ,`uin`                   STRING                 COMMENT '客户uin'
    ,`customer_name`         STRING                 COMMENT '客户名称'
    ,`customer_class`        STRING                 COMMENT '客户等级'
    ,`grade`                 BIGINT                 COMMENT '重要性等级：1.一般 2.重要'
    ,`expected_time`         DATETIME               COMMENT '预期上线时间'
    ,`dst_story_id`          BIGINT                 COMMENT '源需求单被关联时，被关联到的需求单id，方便查询'
    ,`status`                BIGINT                 COMMENT '关联状态，0，正常，1：已解除关联'
    ,`way_by`                BIGINT                 COMMENT '创建方式，0，从其它渠道创建，1： 手动直接创建'
    ,`customer_category`     STRING                 COMMENT '客户分类，中长尾、大客户'
    ,`create_by_id`          STRING                 COMMENT '创建人的andon用户id'
    ,`is_reverse_relation`   BIGINT                 COMMENT '是否为反向关联，1:是反向关联'
    ,`ltc_name`              STRING                 COMMENT '项目名称'
    ,`trade`                 STRING                 COMMENT '所属行业'
    ,`ltc_no`                STRING                 COMMENT 'LTC ID'
    ,`final_customer`        STRING                 COMMENT '最终客户'
    ,`jid`                   BIGINT                 COMMENT '集团ID'
    ,`ltc_level`             STRING                 COMMENT '项目等级'
) ENGINE=OLAP
PRIMARY KEY(`id`)
COMMENT "需求关联信息"
DISTRIBUTED BY HASH(`id`)
PROPERTIES (
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "id",
    "enable_persistent_index" = "true"
);
