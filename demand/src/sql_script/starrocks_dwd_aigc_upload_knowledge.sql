CREATE TABLE dwd_aigc_upload_knowledge(
    `id` INT COMMENT '',
    `task_id` STRING COMMENT '任务id',
    `task_state` STRING COMMENT '任务状态',
    `err_msg` STRING COMMENT '错误信息',
    `knowledge_title` STRING COMMENT '知识标题',
    `knowledge_type` STRING COMMENT '知识类型',
    `upload_user` STRING COMMENT '上传人',
    `knowledge_url` STRING COMMENT '知识url',
    `knowledge_scene_ids` STRING COMMENT '知识归档id',
    `knowledge_scene_names` STRING COMMENT '知识归档名称',
    `update_time` DATETIME COMMENT '修改时间',
    `create_time` DATETIME COMMENT '创建时间',
    `knowledge_scene_search_ids` STRING COMMENT '知识归档搜索id'
) ENGINE=OLAP
PRIMARY KEY(`id`)
DISTRIBUTED BY HASH(`id`)
PROPERTIES ( "replication_num" = "3", "in_memory" = "false", "enable_persistent_index" = "true");
