create table dwd_voc_requirements(
    id bigint primary key ,
    prod_center text,
    prod_type   text,
    title   text,
    describe    text,
    images  text,
    status int,
    operate_user text,
    deleted     int,
    risk_scene  text,
    has_risk    int,
    uid text,
    user_type int,
    nickname    text,
    create_at timestamp,
    update_at timestamp,
    process_instance_id text,
    pic_paths   text,
    file_paths  text,
    prod_type_id    int,
    owner_uin   text,
    is_public   int,
    flag text,
    prod_center_id  int,
    prod_name_id int,
    prod_name   text,
    dmd_id  bigint,
    is_voc_repeat   bigint,
    requirement_type_name text,
    requirement_type_id int,
    pre_check_time  timestamp,
    prd_check_time  timestamp,
    implemented_time timestamp
)
