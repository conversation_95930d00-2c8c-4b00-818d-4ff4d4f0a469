drop view dwm_voc_statistic_view;
create  view dwm_voc_statistic_view as
(
select *,
       'https://cloud.tencent.com/voc/tasks/' || cast(requirement_id as text) as story_url,
       'https://andon.woa.com/user-voice-admin/demand/DemandDetails?ProcessInstanceID=' ||
       cast(requirement_id as text)                                           as voc_console_url,
       case
           when status = '已实现'
               then get_work_second_count(create_time, prod_complete_time)
           when status in ('预审中', '初审中', '产品评估中', '待实现', '暂不实现')
               then get_work_second_count(localtimestamp, create_time)
           when status = '产品未采纳'
               then get_work_second_count(prod_check_time, create_time)
           when status = '初审未通过'
               then get_work_second_count(review_time, create_time)
           when status = '预审未通过'
               then get_work_second_count(preview_time, create_time)
           else null
           end                                                                as commit_duration_second,
       case
           when prod_check_time is null and status = '产品评估中'
               then get_work_second_count(review_time, localtimestamp)
           when prod_check_time is not null
               then get_work_second_count(review_time, prod_check_time)
           else null
           end                                                                as product_evaluate_duration_second,
       case
           when preview_time is null and status = '预审中'
               then get_work_second_count(create_time, localtimestamp)
           when preview_time is not null
               then get_work_second_count(create_time, preview_time)
           else null
           end                                                                as pre_evaluate_duration_second,
       case
           when review_time is null and status = '初审中'
               then get_work_second_count(localtimestamp, preview_time)
           when review_time is not null
               then get_work_second_count(preview_time, review_time)
           end                                                                as first_evaluate_duration_second,
       case
           when status = '已实现'
               then get_work_second_count(prod_check_time, prod_complete_time)
           when status = '待实现'
               then get_work_second_count(prod_check_time, localtimestamp)
           else null
           end                                                                as product_process_duration_second
from
    -- 这里immutable的数据通过CDC来同步，但是Mutable的数据是上报的，切换不了，使用join关联
    (
        select
            t1.dwm_id,
            t1.dwm_is_valid,
            t1.value_of_primary_key,
            t1.dwm_create_time,
            t1.record_update_time,
            t1.requirement_id,
            case
                when t2.status = 0 then '待预审'
                when t2.status = 1 then '待初审（初审中）'
                when t2.status = 2 then '待产品评估（产品评估中）'
                when t2.status = 3 then '产品已采纳'
                when t2.status = 4 then '待实现'
                when t2.status = 5 then '暂不实现'
                when t2.status = 6 then '已实现'
                when t2.status = 7 then '预审未通过'
                when t2.status = 8 then '初审未通过'
                when t2.status = 9 then '产品未采纳'
                else t1.status
            end as status,
            t1.title,
            t1.prod_center,
            t1.prod_type,
            t1.create_time,
            t1.uin,
            t1.nickname,
            t1.main_uin,
            t1.preview_time,
            t1.review_time,
            t1.prod_check_time,
            t1.prod_complete_time,
            t1.prod_temp_complete_time,
            t1.current_processor,
            t1.comment_cnt,
            t1.like_cnt,
            t1.read_cnt,
            t1.collect_cnt,
            t1.prod_name,
            t1.dmd_id,
            t1.is_voc_repeat,
            t1.is_public,
            case
                when t1.requirement_type_id = 1 then '产品功能与建议'
                when t1.requirement_type_id = 2 then '文档问题'
                when t1.requirement_type_id = 3 then '其它'
                when t1.requirement_type_id = 4 then '体验建议'
                else t1.requirement_type_name
            end as requirement_type_name,
            t1.requirement_type_id,
            t1.source
        from dwm_voc_statistic t1
        left join dwd_voc_requirements t2
        on t1.requirement_id = t2.id
    ) t
)
