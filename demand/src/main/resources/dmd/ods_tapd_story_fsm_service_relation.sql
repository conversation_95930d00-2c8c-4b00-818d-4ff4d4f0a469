create table ods_tapd_story_fsm_service_relation
(
    ods_id               serial                                      not null primary key,
    ods_is_valid         boolean                        default true not null,
    value_of_primary_key text                                        not null,
    ods_create_time      timestamp(6) without time zone default (now() at time zone ('utc-8')),
    record_update_time   text                                        not null,

    id                   text,
    story_id             text,
    channel              text,
    item_id              text,
    item_title           text,
    create_by            text,
    create_at            text,
    uin                  text,
    customer_name        text,
    customer_class       text,
    grade                text,
    expected_time        text,
    dst_story_id         text,
    status               text
);

alter table ods_tapd_story_fsm_service_relation owner to andata;

create index idx_ods_tapd_story_fsm_service_relation_vpky on ods_tapd_story_fsm_service_relation(value_of_primary_key);
create index idx_ods_tapd_story_fsm_service_relation_ods_id on ods_tapd_story_fsm_service_relation(ods_id);
create index idx_ods_tapd_story_fsm_service_relation_record_update_time on ods_tapd_story_fsm_service_relation(record_update_time);


comment on table ods_tapd_story_fsm_service_relation is '需求单和其它业务的关联';

comment on column ods_tapd_story_fsm_service_relation.value_of_primary_key is '消息主键';
comment on column ods_tapd_story_fsm_service_relation.id is '唯一标记';
comment on column ods_tapd_story_fsm_service_relation.story_id is '需求单id';
comment on column ods_tapd_story_fsm_service_relation.channel is '被哪个需求关联,1:服务请求单';
comment on column ods_tapd_story_fsm_service_relation.item_id is '关联来源的唯一标识，比如工单id等';
comment on column ods_tapd_story_fsm_service_relation.item_title is '单据标题';
comment on column ods_tapd_story_fsm_service_relation.create_by is '操作人';
comment on column ods_tapd_story_fsm_service_relation.create_at is '关联时间';
comment on column ods_tapd_story_fsm_service_relation.uin is '客户uin';
comment on column ods_tapd_story_fsm_service_relation.customer_name is '客户名';
comment on column ods_tapd_story_fsm_service_relation.customer_class is '客户等级';
comment on column ods_tapd_story_fsm_service_relation.grade is '重要性等级';
comment on column ods_tapd_story_fsm_service_relation.expected_time is '预期上线时间';
comment on column ods_tapd_story_fsm_service_relation.dst_story_id is '源需求单被关联时，被关联到的需求单id，方便查询';
comment on column ods_tapd_story_fsm_service_relation.status is '关联状态，0，正常，1：已解除关联';

-- 增加字段(2021-12-28 jingzhenhe)
alter table ods_tapd_story_fsm_service_relation add column way_by text;
comment on column ods_tapd_story_fsm_service_relation.way_by is '创建方式，0，从其它渠道创建，1： 手动直接创建';

-- add by baymaxxu 2022-03-22
alter table ods_tapd_story_fsm_service_relation
    add column customer_category text,
    add column create_by_id text;
comment on column ods_tapd_story_fsm_service_relation.customer_category is '客户分类，中长尾、大客户';
comment on column ods_tapd_story_fsm_service_relation.create_by_id is '创建人的andon用户id';
