create table tdw_dws_demand_indicator_by_date(
    data_date               date,
    create_count            bigint,
    gongxing_count          bigint,
    mark_repeat_count       bigint,
    marked_repeat_count     bigint
);

comment on column tdw_dws_demand_indicator_by_date.data_date is '日期';
comment on column tdw_dws_demand_indicator_by_date.create_count is '创建的需求单数量';
comment on column tdw_dws_demand_indicator_by_date.gongxing_count is '成为共性需求的需求单数量';
comment on column tdw_dws_demand_indicator_by_date.mark_repeat_count is '标记为需求重复的数量';
comment on column tdw_dws_demand_indicator_by_date.marked_repeat_count is '被标记重复的需求单数量';


-- add by baymaxxu 2022-08-05
alter table tdw_dws_demand_indicator_by_date
    add column reverse_relation_count bigint,
    add column positive_relation_count bigint;
comment on column tdw_dws_demand_indicator_by_date.reverse_relation_count is '反向关联需求单数量';
comment on column tdw_dws_demand_indicator_by_date.positive_relation_count is '正向关联需求单数量';
