create table dwd_demand_frontend_access_record(
    "user"                    varchar(1024),
    value_of_primary_key    text,
    access_time             timestamp(6) without time zone,
    device                  varchar(1024),
    source                  varchar(1024),
    page                    varchar(1024),
    module                  varchar(1024)
);

comment on column dwd_demand_frontend_access_record.user is '访问人';
comment on column dwd_demand_frontend_access_record.access_time is '访问时间';
comment on column dwd_demand_frontend_access_record.device is '访问设备';
comment on column dwd_demand_frontend_access_record.source is '访问来源';
comment on column dwd_demand_frontend_access_record.page is '访问URL';
comment on column dwd_demand_frontend_access_record.module is '模块';
