-- 废弃
create table dwd_tapd_story_base_info
(
    dwd_id serial primary key,
    dwd_is_valid boolean not null default true,
    value_of_primary_key varchar(1024) not null default '',
    dwd_create_time timestamp(6) without time zone default (now() at time zone ('utc-8')),
    record_update_time timestamp(6) without time zone default (now() at time zone ('utc-8')),

    "id"                 bigint                         default 0,
    title                varchar(1024) not null         default '',
    background           varchar(1024) not null         default '',
    "desc"               text,
    product              varchar(1024) not null         default '',
    service_first_scene  bigint                         default 0,
    service_second_scene bigint                         default 0,
    status               bigint                         default 0,
    current_operator     varchar(1024) not null         default '',
    create_by            varchar(1024) not null         default '',
    create_at            timestamp(6) without time zone default null,
    update_by            varchar(1024) not null         default '',
    update_at            timestamp(6) without time zone default null,
    demand_manager       varchar(1024) not null         default '',
    develop_man          varchar(1024) not null         default '',
    kano_score           bigint                         default 0,
    product_advises      varchar(1024) not null         default '',
    product_rtx          varchar(1024) not null         default '',
    dst_story_id         varchar(1024) not null         default '',
    tapd_id              varchar(1024) not null         default '',
    special              bigint                         default 0,
    instance_id          varchar(1024) not null         default '',
    online_time          timestamp(6) without time zone default null,
    current_node         varchar(1024) not null         default '',
    channel              bigint                         default 0
);


alter table dwd_tapd_story_base_info owner to andata;

create index idx_dwd_tapd_story_base_info_vpky on dwd_tapd_story_base_info(value_of_primary_key);
create index idx_dwd_tapd_story_base_info_dwd_id on dwd_tapd_story_base_info(dwd_id);
create index idx_dwd_tapd_story_base_info_record_update_time on dwd_tapd_story_base_info(record_update_time);

comment on table dwd_tapd_story_base_info is '需求信息';

comment on column dwd_tapd_story_base_info.value_of_primary_key is '消息主键';
comment on column dwd_tapd_story_base_info.id is '唯一性主键';
comment on column dwd_tapd_story_base_info.title is '标题';
comment on column dwd_tapd_story_base_info.background is '背景信息';
comment on column dwd_tapd_story_base_info.desc is '描述信息';
comment on column dwd_tapd_story_base_info.product is '产品信息';
comment on column dwd_tapd_story_base_info.service_first_scene is '产品信息一级id';
comment on column dwd_tapd_story_base_info.service_second_scene is '产品信息二级id';
comment on column dwd_tapd_story_base_info.status is '需求单状态,1:初审,2:初审被拒,3:产品评估中,4:产品已采纳,5:暂不实现,6:产品未采纳，进行初审,7:产品未采纳，发起人补充材料,8:重复,9:已实现,10:已结单,11:被关闭';
comment on column dwd_tapd_story_base_info.current_operator is '当前处理人';
comment on column dwd_tapd_story_base_info.create_by is '创建人';
comment on column dwd_tapd_story_base_info.create_at is '创建时间';
comment on column dwd_tapd_story_base_info.update_by is '最近修改人';
comment on column dwd_tapd_story_base_info.update_at is '最近修改时间';
comment on column dwd_tapd_story_base_info.demand_manager is '需求经理';
comment on column dwd_tapd_story_base_info.develop_man is '产研接口人';
comment on column dwd_tapd_story_base_info.kano_score is 'kano积分';
comment on column dwd_tapd_story_base_info.product_advises is '产品建议';
comment on column dwd_tapd_story_base_info.product_rtx is '参与沟通产研的rtx';
comment on column dwd_tapd_story_base_info.dst_story_id is '标记为重复时，被关联的单号';
comment on column dwd_tapd_story_base_info.tapd_id is 'tapd单号';
comment on column dwd_tapd_story_base_info.special is '1:是个性化需求';
comment on column dwd_tapd_story_base_info.instance_id is 'tbpm流程实例ID';
comment on column dwd_tapd_story_base_info.online_time is '上线时间';
comment on column dwd_tapd_story_base_info.current_node is 'tbpm上当前的处理节点id';
comment on column dwd_tapd_story_base_info.channel is '被哪个渠道关联,1:服务请求单';

-- 21/12/28 新增字段 jingzhenhe
alter table dwd_tapd_story_base_info
    add column product_name varchar(1024) default '',
    add column product_center varchar(1024) default '';
comment on column dwd_tapd_story_base_info.product_name is '产品名称';
comment on column dwd_tapd_story_base_info.product_center is '产品中心';

-- add by baymaxxu 2022-03-22
alter table dwd_tapd_story_base_info
    add column tapd_workspaceid varchar(1024) default '',
    add column create_by_id varchar(1024) default '',
    add column tapd_completed_states varchar(1024) default '';

comment on column dwd_tapd_story_base_info.tapd_workspaceid is '使用的哪个tapd池';
comment on column dwd_tapd_story_base_info.create_by_id is '创建者的andon用户id';
comment on column dwd_tapd_story_base_info.tapd_completed_states is 'tapd结束状态';

-- add by jingzhenhe 2022-05-24
alter table dwd_tapd_story_base_info
    add column is_deleted bigint default 0;

-- edit column type
alter table dwd_tapd_story_base_info ALTER COLUMN background TYPE text;
