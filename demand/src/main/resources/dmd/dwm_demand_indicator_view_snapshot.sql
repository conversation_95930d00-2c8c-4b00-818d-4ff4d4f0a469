create table tdw_dwm_demand_indicator_view_snapshot(
    story_id                    int,
    to_product_evaluate_time    timestamp,
    to_product_accept_time      timestamp,
    first_evaluate_failed_time  timestamp,
    to_repeat_demand_time       timestamp,
    to_review_repeat_demand_time    timestamp,
    to_reopen_time              timestamp,
    to_not_implemented_yet_time timestamp,
    to_product_not_accept_time  timestamp,
    is_pass_first_audit         int,
    reverse_twist_time          timestamp,
    dst_story_id                int,
    create_at                   timestamp,
    online_time                 timestamp,
    status                      int,
    gongxing_time               timestamp,
    is_gongxing                 int,
    data_date                   date
);

alter table tdw_dwm_demand_indicator_view_snapshot
    add column current_operator text;

alter table tdw_dwm_demand_indicator_view_snapshot
    add column is_delay bigint;