-- 废弃
create table dwd_tapd_story_approval_history
(
    dwd_id               serial primary key,
    dwd_is_valid         boolean       not null         default true,
    value_of_primary_key varchar(1024) not null         default '',
    dwd_create_time      timestamp(6) without time zone default (now() at time zone ('utc-8')),
    record_update_time   timestamp(6) without time zone default (now() at time zone ('utc-8')),

    "id"                 bigint,
    story_id             bigint,
    story_status         bigint,
    action               bigint,
    opinion              varchar(1024)                  default '',
    reason               varchar(1024)                  default '',
    "desc"               varchar(1024)                  default '',
    out_words            text                           default '',
    create_by            varchar(1024)                  default '',
    create_at            timestamp(6) without time zone default null,
    status               bigint,
    remark               varchar(1024)                  default ''
);


alter table dwd_tapd_story_approval_history owner to andata;

create index idx_dwd_tapd_story_approval_history_vpky on dwd_tapd_story_approval_history(value_of_primary_key);
create index idx_dwd_tapd_story_approval_history_dwd_id on dwd_tapd_story_approval_history(dwd_id);
create index idx_dwd_tapd_story_approval_history_time on dwd_tapd_story_approval_history(record_update_time);

comment on table dwd_tapd_story_approval_history is '需求信息';

comment on column dwd_tapd_story_approval_history.value_of_primary_key is '消息主键';
comment on column dwd_tapd_story_approval_history.id is '唯一性主键';
comment on column dwd_tapd_story_approval_history.status is '1:用户重新提交了，之前审批清空';
comment on column dwd_tapd_story_approval_history.create_by is '操作人';
comment on column dwd_tapd_story_approval_history.create_at is '操作时间';
comment on column dwd_tapd_story_approval_history.story_id is '需求单id';
comment on column dwd_tapd_story_approval_history.story_status is '审批时的需求单状态：1:初审,2:初审被拒,3:产品评估中,4:产品已采纳,5:暂不实现,6:产品未采纳，进行初审,7:产品未采纳，发起人补充材料,8:重复,9:已实现,10:已结单,11:被关闭';
comment on column dwd_tapd_story_approval_history.action is '审批动作，1：采纳，2：驳回，3：重复标记';
comment on column dwd_tapd_story_approval_history.opinion is '审批意见';
comment on column dwd_tapd_story_approval_history.reason is '拒绝原因';
comment on column dwd_tapd_story_approval_history.desc is '拒绝原因描述';
comment on column dwd_tapd_story_approval_history.out_words is '对外话术';
comment on column dwd_tapd_story_approval_history.remark is '其它信息';

-- 增加字段(2021-12-28 jingzhenhe)
alter table dwd_tapd_story_approval_history add column curr_story_status int;
comment on column dwd_tapd_story_approval_history.curr_story_status is '操作后的需求单状态：1:初审,2:初审被拒,3:产品评估中,4:产品已采纳,5:暂不实现,6:产品未采纳，进行初审,7:产品未采纳，发起人补充材料,8:重复,9:已实现,10:已结单,11:被关闭';
