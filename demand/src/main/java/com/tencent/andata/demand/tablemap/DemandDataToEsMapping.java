package com.tencent.andata.demand.tablemap;

public class DemandDataToEsMapping {

    public static String pgTable2FTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_demand_base_info\",\n"
            + "        \"fTable\": \"pg_src_dwd_demand_base_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dim_demand_deploy\",\n"
            + "        \"fTable\": \"pg_src_dim_demand_deploy\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_rt_key_customer_information\",\n"
            + "        \"fTable\": \"pg_src_dwd_rt_key_customer_information\"\n"
            + "    },\n"
            // + "    {\n"
            // + "        \"rdbTable\": \"dwd_demand_approval_history\",\n"
            // + "        \"fTable\": \"pg_src_dwd_demand_approval_history\"\n"
            // + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_demand_tag_story_relation\",\n"
            + "        \"fTable\": \"pg_src_dwd_demand_tag_story_relation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_demand_relation\",\n"
            + "        \"fTable\": \"pg_src_dwd_demand_relation\"\n"
            + "    }\n"
            + "]";

    public static String pglkTable2FTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_demand_relation\",\n"
            + "        \"fTable\": \"pg_lk_dwd_demand_relation\"\n"
            + "    },\n"
            // + "    {\n"
            // + "        \"rdbTable\": \"dwd_demand_approval_history\",\n"
            // + "        \"fTable\": \"pg_lk_dwd_demand_approval_history\"\n"
            // + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_demand_tag_story_relation\",\n"
            + "        \"fTable\": \"pg_lk_dwd_demand_tag_story_relation\"\n"
            + "    }\n"
            + "]";

    public static String mysqlTable2FTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"comments\",\n"
            + "        \"fTable\": \"mysql_src_comments\"\n"
            + "    }\n"
            + "]";

    public static String mysqllkTable2FTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"comments\",\n"
            + "        \"fTable\": \"mysql_lk_comments\"\n"
            + "    }\n"
            + "]";
}
