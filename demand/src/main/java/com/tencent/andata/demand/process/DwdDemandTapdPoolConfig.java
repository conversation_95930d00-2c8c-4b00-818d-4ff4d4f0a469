package com.tencent.andata.demand.process;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.utils.*;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Properties;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.*;

public class DwdDemandTapdPoolConfig implements DemandProcessStrategy {
    Logger logger = LoggerFactory.getLogger(DwdDemandTapdPoolConfig.class);

    private final ConsumerDemandConfig config;

    public DwdDemandTapdPoolConfig(ConsumerDemandConfig config) throws IOException {
        this.config = config;
    }

    /**
     * 具体执行SQL
     *
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool paraTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        // 出库PG表注册
        String pgTableName = "dwd_demand_tapd_pool_config";
        String fPgTableName = "pg_dwd_demand_tapd_pool_config";
        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware"))
                .build();
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(fPgTableName)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        pgTableName,
                                        DatabaseEnum.PGSQL,
                                        pgDBConf
                                )
                        )
                        .build()
        );

        // iceberg表注册
        String icebergTapdTableName = "dwd_demand_tapd_pool_config";
        String fIceTapdTableName = "iceberg_dwd_demand_tapd_pool_config";
        org.apache.iceberg.Table configTableInstance = catalog.getTableInstance(
                config.databaseName,
                icebergTapdTableName
        );
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(fIceTapdTableName)
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(configTableInstance)
                                        .primaryKeyList(
                                                new String[]{"id"}
                                        )
                        )
                        .build()
        );
        StatementSet stmtSet = flinkEnv.stmtSet();
        // 数据出库
        stmtSet.addInsertSql(insertIntoSql(
                        "f_ods_demand_tapd_pool_config",
                        fPgTableName,
                        tEnv.from(fPgTableName),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "f_ods_demand_tapd_pool_config",
                        fIceTapdTableName,
                        tEnv.from(fIceTapdTableName),
                        ICEBERG));
    }
}