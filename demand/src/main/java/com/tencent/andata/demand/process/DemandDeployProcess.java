package com.tencent.andata.demand.process;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.demand.sqls.DemandDeployProcessSql;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.StarRocksTableBuilderStrategy;
import com.tencent.andata.utils.factory.JDBCTableDDLFactory;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Properties;

public class DemandDeployProcess  implements DemandProcessStrategy {
    Logger logger = LoggerFactory.getLogger(DemandDeployProcess.class);

    private final ConsumerDemandConfig config;

    public DemandDeployProcess(ConsumerDemandConfig config) throws IOException {
        this.config = config;
    }

    /**
     * 运行方法
     *
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    @Override
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool paraTool) throws Exception {
        StreamTableEnvironment tableEnv = flinkEnv.streamTEnv();
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        // 注册Iceberg
        org.apache.iceberg.Table tableInstance = catalog.getTableInstance(
                config.databaseName,
                "dwd_demand_update_history"
        );
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("f_ice_dwd_deploy_update_history")
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(tableInstance)
                                        .primaryKeyList(
                                                new String[]{"id"}
                                        )
                        )
                        .build()
        );


        // 注册360 PG Table
        DatabaseConf s360DbConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "s360"))
                .build();
        JDBCTableDDLFactory s360DemandDeployTableDDLFactory = new JDBCTableDDLFactory(
                "dim_demand_deploy",
                DatabaseEnum.PGSQL,
                s360DbConf
        );
        TableUtils.registerTable(
                tableEnv,
                s360DemandDeployTableDDLFactory.createTableDDL("s360_dim_demand_deploy")
        );
        // 注册数仓 PG Table
        DatabaseConf datawareDbConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware"))
                .build();
        JDBCTableDDLFactory datawareDemandDeployTableDDLFactory = new JDBCTableDDLFactory(
                "dim_demand_deploy",
                DatabaseEnum.PGSQL,
                datawareDbConf
        );
        TableUtils.registerTable(
                tableEnv,
                datawareDemandDeployTableDDLFactory.createTableDDL("pg_dim_demand_deploy")
        );
        JDBCTableDDLFactory pgDeployUpdateHistory = new JDBCTableDDLFactory(
                "dwd_demand_deploy_update_history",
                DatabaseEnum.PGSQL,
                datawareDbConf
        );
        TableUtils.registerTable(
                tableEnv,
                pgDeployUpdateHistory.createTableDDL("pg_dwd_demand_deploy_update_history")
        );
        // 注册starrocks表
        DatabaseConf rocksDbConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("f_sr_dim_demand_deploy")
                        .tableBuilderStrategy(
                                new StarRocksTableBuilderStrategy(
                                        "dim_demand_deploy",
                                        DatabaseEnum.ROCKS,
                                        rocksDbConf,
                                        8080
                                ).sinkParallelism("1")
                                        .primaryKeyList(new String[]{"id"})
                                        .property("sink.properties.format", "json")
                                        .property("sink.buffer-flush.max-rows", "64000")
                                        .property("sink.buffer-flush.interval-ms", "1000")
                                        .property("sink.buffer-flush.max-bytes", "67108864")
                                        .property("sink.properties.ignore_json_size", "true")
                                        .property("sink.properties.strip_outer_array", "true")
                        )
                        .build()
        );
        StatementSet sts = flinkEnv.stmtSet();
        // 流水数据处理
        Table dwdDeployUpdateHistoryTable = tableEnv.sqlQuery(
                String.format(
                        DemandDeployProcessSql.DEMAND_DEPLOY_UPDATE_HISTORY_PROCESS,
                        "t_deploy_update_history"
                )
        );
        tableEnv.createTemporaryView("f_dwd_deploy_update_history", dwdDeployUpdateHistoryTable);
        // 出库360
        sts.addInsertSql(
                String.format(
                        DemandDeployProcessSql.DEMAND_DEPLOY_SINK_TO_PG_SQL,
                        "s360_dim_demand_deploy",
                        "t_deploy"
                )
        );
        // 出库数仓
        sts.addInsertSql(
                String.format(
                        DemandDeployProcessSql.DEMAND_DEPLOY_SINK_TO_PG_SQL,
                        "pg_dim_demand_deploy",
                        "t_deploy"
                )
        ).addInsertSql(
                String.format(
                        DemandDeployProcessSql.DEMAND_DEPLOY_SINK_TO_PG_SQL,
                        "f_sr_dim_demand_deploy",
                        "t_deploy"
                )
        );
        sts.addInsertSql(insertIntoSql(
                        "f_dwd_deploy_update_history",
                        "pg_dwd_demand_deploy_update_history",
                        tableEnv.from("pg_dwd_demand_deploy_update_history"),
                        PGSQL
                )
        );
        // 出库Ice
        sts.addInsertSql(
                String.format(
                        DemandDeployProcessSql.DEMAND_DEPLOY_UPDATE_HISTORY_SINK_ICE,
                        "f_ice_dwd_deploy_update_history",
                        "f_dwd_deploy_update_history"
                )
        );
    }
}