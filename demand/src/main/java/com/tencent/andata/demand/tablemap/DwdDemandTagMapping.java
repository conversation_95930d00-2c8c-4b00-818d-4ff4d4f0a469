package com.tencent.andata.demand.tablemap;

public class DwdDemandTagMapping {
    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_demand_story_tag_relation\",\n"
            + "        \"fTable\":\"iceberg_dwd_demand_story_tag_relation\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_demand_tag_config\",\n"
            + "        \"fTable\":\"iceberg_dwd_demand_tag_config\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    }\n"
            + "]";

    public static String rdbTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_demand_tag_config\",\n"
            + "        \"fTable\": \"pg_dwd_demand_tag_config\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_demand_tag_story_relation\",\n"
            + "        \"fTable\": \"pg_dwd_demand_tag_story_relation\"\n"
            + "    }\n"
            + "]";

}
