package com.tencent.andata.demand.tablemap;

public class InitMapping {

    public static String pgToFlink = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\":\"dim_service_scenes\",\n"
            + "        \"fTable\":\"dim_service_scenes_flink_source_table\"\n"
            + "    }\n"
            + "]";
    public static String mysqlSourceTable2FTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_story\",\n"
            + "        \"fTable\": \"t_story\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_source_relation\",\n"
            + "        \"fTable\": \"t_source_relation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_approval_history\",\n"
            + "        \"fTable\": \"t_approval_history\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_story_enum\",\n"
            + "        \"fTable\": \"t_story_enum\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_deploy\",\n"
            + "        \"fTable\": \"t_deploy\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_deploy_update_history\",\n"
            + "        \"fTable\": \"t_deploy_update_history\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_tag_configs\",\n"
            + "        \"fTable\": \"demand_tag_config\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_tag_infos\",\n"
            + "        \"fTable\": \"demand_story_tag_relation\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_tapd_info\",\n"
            + "        \"fTable\": \"f_ods_demand_tapd_info\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_review\",\n"
            + "        \"fTable\": \"f_ods_t_review\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"t_tapd_pool_config\",\n"
            + "        \"fTable\": \"f_ods_demand_tapd_pool_config\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_field_item_config\",\n"
            + "        \"fTable\":\"t_field_item_config\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\":\"t_field_item_update_history\",\n"
            + "        \"fTable\":\"t_field_item_update_history\"\n"
            + "    }\n"
            + "]";
}