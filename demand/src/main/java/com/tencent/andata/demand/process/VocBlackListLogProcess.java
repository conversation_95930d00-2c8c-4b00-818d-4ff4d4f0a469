package com.tencent.andata.demand.process;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.demand.sqls.VocBlackListLogProcessSql;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.CDCTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.io.IOException;
import java.util.Properties;

//TODO 已经废弃，停掉观察一段时间
public class VocBlackListLogProcess implements DemandProcessStrategy {
    private ConsumerDemandConfig config;

    public VocBlackListLogProcess(ConsumerDemandConfig config) throws IOException {
        this.config = config;
    }

    /**
     * 具体执行SQL
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool paraTool) throws Exception {
        StreamTableEnvironment tableEnv = flinkEnv.streamTEnv();
        StatementSet sts = flinkEnv.stmtSet();
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        // 注册PG Table
        String pgTableName = "dwd_voc_black_list_log";
        String flinkPgTableName = "pg_voc_black_list_log";
        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware"))
                .build();
        JDBCTableBuilderStrategy blackListLogTableBuilderStrategy = new JDBCTableBuilderStrategy(
                pgTableName,
                DatabaseEnum.PGSQL,
                pgDBConf
        );
        String pgTableDDL = FlinkTableDDL
                .builder()
                .flinkTableName(flinkPgTableName)
                .tableBuilderStrategy(blackListLogTableBuilderStrategy)
                .build();
        TableUtils.registerTable(tableEnv, pgTableDDL);
        // 注册CDC表数据
        DatabaseConf vocDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName("cdc.database.mysql.voc")
                .build();
        String cdcDBTableName = "black_list_log";
        String cdcFlinkTableName = "cdc_black_list_log";
        CDCTableBuilderStrategy vocCdcTableBuilderStrategy = new CDCTableBuilderStrategy(
                cdcDBTableName,
                DatabaseEnum.MYSQL,
                vocDBConf,
                ""
        );
        String cdcTableDDL = FlinkTableDDL
                .builder()
                .flinkTableName(cdcFlinkTableName)
                .tableBuilderStrategy(vocCdcTableBuilderStrategy)
                .build();
        TableUtils.registerTable(tableEnv, cdcTableDDL);
        // Sink数据
        sts.addInsertSql(
                String.format(
                        VocBlackListLogProcessSql.SINK_TO_PG_SQL,
                        flinkPgTableName,
                        cdcFlinkTableName
                )
        );
    }
}