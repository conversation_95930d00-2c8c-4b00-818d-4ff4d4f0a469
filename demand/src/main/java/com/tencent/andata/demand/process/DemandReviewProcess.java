package com.tencent.andata.demand.process;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.demand.sqls.DemandReviewProcessSql;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Properties;

public class DemandReviewProcess implements DemandProcessStrategy {
    Logger logger = LoggerFactory.getLogger(DemandReviewProcess.class);

    private final ConsumerDemandConfig config;

    public DemandReviewProcess(ConsumerDemandConfig config) throws IOException {
        this.config = config;
    }

    /**
     * 具体执行SQL
     *
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    @Override
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool paraTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        // 出库PG表注册
        String pgTableName = "dwd_demand_review";
        String fPgTableName = "pg_dwd_demand_review";
        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware"))
                .build();
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(fPgTableName)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        pgTableName,
                                        DatabaseEnum.PGSQL,
                                        pgDBConf
                                )
                        )
                        .build()
        );

        // iceberg表注册
        String icebergReviewTableName = "dwd_demand_review";
        String fIceReviewTableName = "iceberg_dwd_demand_review";
        org.apache.iceberg.Table configTableInstance = catalog.getTableInstance(
                config.databaseName,
                icebergReviewTableName
        );
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(fIceReviewTableName)
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(configTableInstance)
                                        .primaryKeyList(
                                                new String[]{"id"}
                                        )
                        )
                        .build()
        );
        StatementSet stmtSet = flinkEnv.stmtSet();
        // 数据计算
        String flinkDwdReviewTableName = "f_dwd_demand_review";
        tEnv.createTemporaryView(
                flinkDwdReviewTableName,
                tEnv.sqlQuery(
                        String.format(
                                DemandReviewProcessSql.DWD_DEMAND_REVIEW_SQL,
                                "f_ods_t_review"
                        )
                )
        );

        // 数据出库
        stmtSet.addInsertSql(insertIntoSql(
                        flinkDwdReviewTableName,
                        fPgTableName,
                        tEnv.from(fPgTableName),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        flinkDwdReviewTableName,
                        fIceReviewTableName,
                        tEnv.from(fIceReviewTableName),
                        ICEBERG));
    }
}