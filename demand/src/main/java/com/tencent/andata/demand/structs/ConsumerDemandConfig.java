package com.tencent.andata.demand.structs;

import java.io.Serializable;

public class ConsumerDemandConfig implements Serializable {
    public String databaseName;
    public String icebergDemandBaseInfoTableName;
    public String sinkIcebergDemandBaseInfoTableName;
    public String icebergDemandRelationTableName;
    public String sinkIcebergDemandRelationTableName;
    public String icebergDemandApprovalHistoryTableName;
    public String sinkIcebergDemandApprovalHistoryTableName;
    public String sinkPgDemandBaseInfoTableName;
    public String sinkPgDemandRelationTableName;
    public String sinkPgDemandApprovalHistoryTableName;
    public String cdcDatabasePath;
    public String cdcDbName;
    public String cdcDmdBaseInfoTableName;
    public String cdcDmdBaseInfoPrimaryKeys;
    public String cdcDmdRelationTableName;
    public String cdcDmdRelationPrimaryKeys;
    public String cdcDmdApprovalHistoryTableName;
    public String cdcDmdApprovalHistoryPrimaryKeys;

    @Override
    public String toString() {
        return "ConsumerDemandConfig{"
            + "databaseName='" + databaseName + '\''
            + ", icebergDemandBaseInfoTableName='" + icebergDemandBaseInfoTableName + '\''
            + ", sinkIcebergDemandBaseInfoTableName='" + sinkIcebergDemandBaseInfoTableName + '\''
            + ", icebergDemandRelationTableName='" + icebergDemandRelationTableName + '\''
            + ", sinkIcebergDemandRelationTableName='" + sinkIcebergDemandRelationTableName + '\''
            + ", icebergDemandApprovalHistoryTableName='" + icebergDemandApprovalHistoryTableName + '\''
            + ", sinkIcebergDemandApprovalHistoryTableName='" + sinkIcebergDemandApprovalHistoryTableName + '\''
            + ", sinkPgDemandBaseInfoTableName='" + sinkPgDemandBaseInfoTableName + '\''
            + ", sinkPgDemandRelationTableName='" + sinkPgDemandRelationTableName + '\''
            + ", sinkPgDemandApprovalHistoryTableName='" + sinkPgDemandApprovalHistoryTableName + '\''
            + ", cdcDatabasePath='" + cdcDatabasePath + '\''
            + ", cdcDbName='" + cdcDbName + '\''
            + ", cdcDmdBaseInfoTableName='" + cdcDmdBaseInfoTableName + '\''
            + ", cdcDmdBaseInfoPrimaryKeys='" + cdcDmdBaseInfoPrimaryKeys + '\''
            + ", cdcDmdRelationTableName='" + cdcDmdRelationTableName + '\''
            + ", cdcDmdRelationPrimaryKeys='" + cdcDmdRelationPrimaryKeys + '\''
            + ", cdcDmdApprovalHistoryTableName='" + cdcDmdApprovalHistoryTableName + '\''
            + ", cdcDmdApprovalHistoryPrimaryKeys='" + cdcDmdApprovalHistoryPrimaryKeys + '\''
            + '}';
    }
}
