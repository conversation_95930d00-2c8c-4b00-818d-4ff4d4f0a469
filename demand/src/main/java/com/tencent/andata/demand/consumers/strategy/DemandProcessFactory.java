package com.tencent.andata.demand.consumers.strategy;

import com.tencent.andata.demand.init.InitApplication;
import com.tencent.andata.demand.process.*;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;

import java.util.HashMap;
import java.util.Map;

public class DemandProcessFactory {
    private static final Map<String, Class<? extends DemandProcessStrategy>> processMap = new HashMap<>();

    static {
        processMap.put("InitApplication", InitApplication.class);
        processMap.put("DwdDemandBaseInfo", DwdDemandBaseInfo.class);
        processMap.put("DwdDemandRelationProcess", DwdDemandRelationProcess.class);
        processMap.put("DwdDemandApprovalHistoryProcess", DwdDemandApprovalHistoryProcess.class);
        processMap.put("DemandStatusEnumProcess", DemandStatusEnumProcess.class);
        processMap.put("DemandDeployProcess", DemandDeployProcess.class);
        processMap.put("VocBlackListLogProcess", VocBlackListLogProcess.class);
        processMap.put("DwdDemandItemConf", DwdDemandItemConf.class);
        processMap.put("DwdDemandTagProcess", DwdDemandTagProcess.class);
        processMap.put("DwdDemandTapdInfo", DwdDemandTapdInfo.class);
        processMap.put("DemandReviewProcess", DemandReviewProcess.class);
        processMap.put("VocImmutableRequirementsProcess", VocImmutableRequirementsProcess.class);
        processMap.put("DwdDemandTapdPoolConfig", DwdDemandTapdPoolConfig.class);
        processMap.put("DemandS360KanbanProcess", DemandS360KanbanProcess.class);
    }

    public static DemandProcessStrategy getProcess(String subApplicationStr, ConsumerDemandConfig config) throws Exception {
        Class<? extends DemandProcessStrategy> processClass = processMap.get(subApplicationStr);
        if (processClass != null) {
            return processClass.getConstructor(ConsumerDemandConfig.class).newInstance(config);
        }
        throw new IllegalArgumentException("Invalid subApplicationStr: " + subApplicationStr);
    }
}