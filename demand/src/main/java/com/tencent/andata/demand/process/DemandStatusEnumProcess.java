package com.tencent.andata.demand.process;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.demand.sqls.DemandStatusEnumProcessSql;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.factory.JDBCTableDDLFactory;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Properties;

public class DemandStatusEnumProcess implements DemandProcessStrategy {
    Logger logger = LoggerFactory.getLogger(DwdDemandBaseInfo.class);

    private final ConsumerDemandConfig config;

    public DemandStatusEnumProcess(ConsumerDemandConfig config) throws IOException {
        this.config = config;
    }

    /**
     * 具体执行SQL
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    @Override
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog,ParameterTool paraTool) throws Exception {
        StreamTableEnvironment tableEnv = flinkEnv.streamTEnv();
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        // Sink Iceberg 的 Flink Table名
        final String icebergTableName = "ice_dim_demand_status_enum";
        // 注册Iceberg Table
        org.apache.iceberg.Table tableInstance = catalog.getTableInstance(
                config.databaseName,
                "dim_demand_status_enum"
        );
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(icebergTableName)
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(tableInstance)
                                        .primaryKeyList(
                                                new String[]{"id"}
                                        )
                        )
                        .build()
        );
        // 注册360 PG Table
        DatabaseConf s360DbConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "s360"))
                .build();
        JDBCTableDDLFactory s360DemandStatusTableDDLFactory = new JDBCTableDDLFactory(
                "dim_demand_status_enum",
                DatabaseEnum.PGSQL,
                s360DbConf
        );
        TableUtils.registerTable(
                tableEnv,
                s360DemandStatusTableDDLFactory.createTableDDL("s360_dim_demand_status_enum")
        );
        // 注册数仓 PG Table
        DatabaseConf datawareDbConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware"))
                .build();
        JDBCTableDDLFactory datawareDemandStatusTableDDLFactory = new JDBCTableDDLFactory(
                "dim_demand_status_enum",
                DatabaseEnum.PGSQL,
                datawareDbConf
        );
        TableUtils.registerTable(
                tableEnv,
                datawareDemandStatusTableDDLFactory.createTableDDL("pg_dim_demand_status_enum")
        );
        StatementSet sts = flinkEnv.stmtSet();
        // 出库360
        sts.addInsertSql(
                String.format(
                        DemandStatusEnumProcessSql.DEMAND_STATUS_ENUM_SINK_TO_PG_SQL,
                        "s360_dim_demand_status_enum",
                        "t_story_enum"
                )
        );
        // 出库数仓
        sts.addInsertSql(
                String.format(
                        DemandStatusEnumProcessSql.DEMAND_STATUS_ENUM_SINK_TO_PG_SQL,
                        "pg_dim_demand_status_enum",
                        "t_story_enum"
                )
        );
        //出库iceberg
        sts.addInsertSql(
                String.format(
                        DemandStatusEnumProcessSql.DEMAND_STATUS_ENUM_SINK_TO_ICEBERG,
                        icebergTableName,
                        "t_story_enum"
                )
        );
    }
}