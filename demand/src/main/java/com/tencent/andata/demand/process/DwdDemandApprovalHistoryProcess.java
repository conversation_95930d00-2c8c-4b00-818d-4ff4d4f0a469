package com.tencent.andata.demand.process;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.demand.sqls.DemandApprovalHistoryProcessSql;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.StarRocksTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.io.IOException;
import java.io.Serializable;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DwdDemandApprovalHistoryProcess implements Serializable, DemandProcessStrategy {
    Logger logger = LoggerFactory.getLogger(DwdDemandBaseInfo.class);

    private final ConsumerDemandConfig config;

    public DwdDemandApprovalHistoryProcess(ConsumerDemandConfig config) throws IOException {
        this.config = config;
    }

    /**
     * 具体执行SQL
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tableEnv = flinkEnv.streamTEnv();
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        String icebergTableName = "iceberg_" + config.sinkIcebergDemandApprovalHistoryTableName;
        String pgTableName = "pg_" + config.sinkPgDemandApprovalHistoryTableName;
        // 注册Iceberg Table
        org.apache.iceberg.Table tableInstance = catalog.getTableInstance(
                config.databaseName,
                config.sinkIcebergDemandApprovalHistoryTableName
        );
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(icebergTableName)
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(tableInstance)
                                        .primaryKeyList(
                                                new String[]{"id"}
                                        )
                        )
                        .build()
        );
        // 注册PG Table
        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware"))
                .build();

        DatabaseConf rocksDbConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(pgTableName)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        config.sinkPgDemandApprovalHistoryTableName,
                                        DatabaseEnum.PGSQL,
                                        pgDBConf
                                )
                        )
                        .build()
        );
        // 注册starrocks表
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("sr_dwd_demand_approval_history")
                        .tableBuilderStrategy(
                                new StarRocksTableBuilderStrategy(
                                        "dwd_demand_approval_history",
                                        DatabaseEnum.ROCKS,
                                        rocksDbConf,
                                        8080
                                ).sinkParallelism("1")
                                        .primaryKeyList(new String[]{"id"})
                                        .property("sink.properties.format", "json")
                                        .property("sink.buffer-flush.max-rows", "64000")
                                        .property("sink.buffer-flush.interval-ms", "1000")
                                        .property("sink.buffer-flush.max-bytes", "67108864")
                                        .property("sink.properties.ignore_json_size", "true")
                                        .property("sink.properties.strip_outer_array", "true")
                        )
                        .build()
        );
        // 执行数据处理
        Table dwdTable = tableEnv.sqlQuery(
                String.format(
                        DemandApprovalHistoryProcessSql.DMD_APPROVAL_HISTORY_PROCESS_SQL_TEMPLATE,
                        config.cdcDmdApprovalHistoryTableName
                )
        );
        // 创建原始数据视图
        tableEnv.createTemporaryView("dwd_demand_approval_history_raw", dwdTable);
        
        // 添加一个清洗视图，处理所有可能包含NULL字节的字段
        logger.info("添加数据清洗视图，处理NULL字节问题");
        String cleanSql = "SELECT \n" +
                "    dwd_create_time,\n" +
                "    value_of_primary_key,\n" +
                "    id,\n" +
                "    story_id,\n" +
                "    story_status,\n" +
                "    action,\n" +
                "    REGEXP_REPLACE(opinion, '[\\u0000]', '') as opinion,\n" +
                "    REGEXP_REPLACE(reason, '[\\u0000]', '') as reason,\n" +
                "    REGEXP_REPLACE(`desc`, '[\\u0000]', '') as `desc`,\n" +
                "    REGEXP_REPLACE(out_words, '[\\u0000]', '') as out_words,\n" +
                "    create_by,\n" +
                "    create_at,\n" +
                "    status,\n" +
                "    REGEXP_REPLACE(remark, '[\\u0000]', '') as remark,\n" +
                "    curr_story_status,\n" +
                "    REGEXP_REPLACE(approval_fields, '[\\u0000]', '') as approval_fields,\n" +
                "    REGEXP_REPLACE(second_reason, '[\\u0000]', '') as second_reason,\n" +
                "    REGEXP_REPLACE(cur_role, '[\\u0000]', '') as cur_role,\n" +
                "    replication_type,\n" +
                "    replication_story_ids,\n" +
                "    reason_id,\n" +
                "    second_reason_id\n" +
                "FROM dwd_demand_approval_history_raw";
        Table cleanedDwdTable = tableEnv.sqlQuery(cleanSql);
        
        // 使用清洗后的表创建最终视图
        tableEnv.createTemporaryView("dwd_demand_approval_history", cleanedDwdTable);
        
        StatementSet sts = flinkEnv.stmtSet();
        // 出库PG
        sts.addInsertSql(
                String.format(
                        DemandApprovalHistoryProcessSql.DEMAND_APPROVAL_HISTORY_SINK_TO_PG,
                        pgTableName,
                        "dwd_demand_approval_history"
                )
        );
        // 出库SR
        sts.addInsertSql(
                String.format(
                        DemandApprovalHistoryProcessSql.DEMAND_APPROVAL_HISTORY_SINK_TO_SR,
                        "sr_dwd_demand_approval_history",
                        "dwd_demand_approval_history"
                )
        );
        // 出库Iceberg
        sts.addInsertSql(
                String.format(
                        DemandApprovalHistoryProcessSql.DEMAND_APPROVAL_HISTORY_SINK_TO_ICEBERG,
                        icebergTableName,
                        "dwd_demand_approval_history"
                )
        );
    }
}