package com.tencent.andata.demand.process;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.demand.sqls.DemandS360KanbanProcessSql;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.factory.JDBCTableDDLFactory;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Serializable;
import java.util.Properties;

public class DemandS360KanbanProcess implements Serializable, DemandProcessStrategy {
    Logger logger = LoggerFactory.getLogger(DwdDemandBaseInfo.class);

    private ConsumerDemandConfig config;

    public DemandS360KanbanProcess(ConsumerDemandConfig config) throws IOException {
        this.config = config;
    }

    /**
     * 运行方法
     *
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool parameterTool) throws Exception {
        StreamTableEnvironment tableEnv = flinkEnv.streamTEnv();
        StatementSet sts = flinkEnv.stmtSet();
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbowUtils = new RainbowUtils(properties);
        // 注册PG Table
        String pgTableName = "pg_dwd_demand_kanban";
        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "s360"))
                .build();
        JDBCTableDDLFactory pgTableFactory = new JDBCTableDDLFactory(
                "dwd_demand_kanban",
                DatabaseEnum.PGSQL,
                pgDBConf
        );
        TableUtils.registerTable(tableEnv, pgTableFactory.createTableDDL(pgTableName));
        // Sink数据
        sts.addInsertSql(
                String.format(
                        DemandS360KanbanProcessSql.DEMAND_KANBAN_DATA_SINK_TO_PG,
                        pgTableName,
                        "t_source_relation",
                        "dwd_demand_base_info"
                )
        );
    }
}