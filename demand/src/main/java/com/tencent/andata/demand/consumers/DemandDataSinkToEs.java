package com.tencent.andata.demand.consumers;

import com.tencent.andata.demand.process.DemandDataToEsProcess;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.FlinkTableConf;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.udf.RemoveHtmlTags;
import com.tencent.andata.utils.udf.SafeTimestampConversion;
import java.util.concurrent.TimeUnit;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class DemandDataSinkToEs {

    public static void main(String[] args) throws Exception {
        FlinkEnv fEnv = FlinkEnvUtils.getStreamTableEnv(args);
        final StreamTableEnvironment tEnv = fEnv.streamTEnv();
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        fEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));

        final String pgDbName = parameterTool.get("pgDbName");
        // table env config
        Configuration configuration = new FlinkTableConf()
                .setEnv(tEnv)
                .setConf("pipeline.name", "DMD-dataSinkToEs")
                .build();

        // 注册 udf
        tEnv.createTemporaryFunction("REMOVE_HTML_TAG", RemoveHtmlTags.class);
        // 注册自定义函数，用于处理MySQL中的零值日期格式
        tEnv.createTemporaryFunction("SAFE_TIMESTAMP", SafeTimestampConversion.class);
        IcebergCatalogReader reader = new IcebergCatalogReader();
        
        // 直接实例化并调用run方法，避免使用反射
        DemandDataToEsProcess process = DemandDataToEsProcess.builder().pgDbName(pgDbName).build();
        process.run(fEnv, reader, parameterTool);
        
        fEnv.env().execute("DMD-dataSinkToEs");
    }
}