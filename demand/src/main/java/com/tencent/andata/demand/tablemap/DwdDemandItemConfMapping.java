package com.tencent.andata.demand.tablemap;

public class DwdDemandItemConfMapping {

    public static String icebergTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_demand_field_item_config\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_demand_field_item_config\",\n"
            + "        \"primaryKey\":\"value_of_primary_key\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"icebergTable\":\"dwd_demand_field_item_history\",\n"
            + "        \"fTable\":\"iceberg_sink_dwd_demand_field_item_update_history\",\n"
            + "        \"primaryKey\":\"id\"\n"
            + "    }\n"
            + "]";

    public static String pgsqlTable2FlinkTable = ""
            + "[\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_demand_field_item_config\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_demand_field_item_config\"\n"
            + "    },\n"
            + "    {\n"
            + "        \"rdbTable\": \"dwd_demand_field_item_history\",\n"
            + "        \"fTable\": \"pgsql_sink_dwd_demand_field_item_history\"\n"
            + "    }\n"
            + "]";

}
