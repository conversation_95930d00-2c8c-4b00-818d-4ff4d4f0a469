package com.tencent.andata.demand.process;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.demand.sqls.DwdDemandTagProcessSql;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.demand.tablemap.DwdDemandTagMapping;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Properties;

public class DwdDemandTagProcess implements DemandProcessStrategy {
    Logger logger = LoggerFactory.getLogger(DwdDemandTagProcess.class);

    private final ConsumerDemandConfig config;

    public DwdDemandTagProcess(ConsumerDemandConfig config) throws IOException {
        this.config = config;
    }

    /**
     * 具体执行SQL
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool paraTool) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", "dataware"))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // 出库PG表注册
        ArrayNode pgsqlTable2FlinkTableMap = mapper.readValue(
                DwdDemandTagMapping.rdbTable2FlinkTable,
                ArrayNode.class
        );
        TableUtils.rdbTable2FlinkTable(pgsqlDBConf, pgsqlTable2FlinkTableMap, PGSQL, tEnv);

        // iceberg表注册
        String icebergTagConfigName = "dwd_demand_tag_config";
        String fTagConfigName = "iceberg_dwd_demand_tag_config";
        String icebergTagRelationName = "dwd_demand_story_tag_relation";
        String fTagRelationName = "iceberg_dwd_demand_story_tag_relation";
        org.apache.iceberg.Table configTableInstance = catalog.getTableInstance(
                config.databaseName,
                icebergTagConfigName
        );
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(fTagConfigName)
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(configTableInstance)
                                        .primaryKeyList(
                                                new String[]{"id"}
                                        )
                        )
                        .build()
        );
        org.apache.iceberg.Table tagRelationTableInstance = catalog.getTableInstance(
                config.databaseName,
                icebergTagRelationName
        );
        TableUtils.registerTable(
                tEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(fTagRelationName)
                        .tableBuilderStrategy(
                                new IcebergTableBuilderStrategy(tagRelationTableInstance)
                                        .primaryKeyList(
                                                new String[]{"id"}
                                        )
                        )
                        .build()
        );

        StatementSet stmtSet = flinkEnv.stmtSet();

        // 数据计算
        tEnv.createTemporaryView(
                "dwd_demand_tag_config",
                tEnv.sqlQuery(
                        String.format(
                                DwdDemandTagProcessSql.DMD_TAG_CONFIG_SQL,
                                "demand_tag_config"
                        )
                )
        );
        tEnv.createTemporaryView(
                "dwd_demand_tag_story_relation",
                tEnv.sqlQuery(
                        String.format(
                                DwdDemandTagProcessSql.DMD_TAG_STORY_RELATION_SQL,
                                "demand_story_tag_relation"
                        )
                )
        );

        // 数据出库
        stmtSet.addInsertSql(insertIntoSql(
                        "dwd_demand_tag_config",
                        "pg_dwd_demand_tag_config",
                        tEnv.from("pg_dwd_demand_tag_config"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "dwd_demand_tag_story_relation",
                        "pg_dwd_demand_tag_story_relation",
                        tEnv.from("pg_dwd_demand_tag_story_relation"),
                        PGSQL))
                .addInsertSql(insertIntoSql(
                        "dwd_demand_tag_config",
                        "iceberg_dwd_demand_tag_config",
                        tEnv.from("iceberg_dwd_demand_tag_config"),
                        ICEBERG))
                .addInsertSql(insertIntoSql(
                        "dwd_demand_tag_story_relation",
                        "iceberg_dwd_demand_story_tag_relation",
                        tEnv.from("iceberg_dwd_demand_story_tag_relation"),
                        ICEBERG));
    }
}