package com.tencent.andata.demand.process;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.JDBCTableBuilderStrategy;
import com.tencent.andata.utils.ddl.strategy.StarRocksTableBuilderStrategy;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.table.api.StatementSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.tencent.andata.demand.sqls.DemandRelationProcessSql;
import com.tencent.andata.demand.structs.ConsumerDemandConfig;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.io.IOException;
import java.io.Serializable;
import java.util.Properties;

public class DwdDemandRelationProcess implements Serializable, DemandProcessStrategy {

    Logger logger = LoggerFactory.getLogger(DwdDemandBaseInfo.class);

    private final ConsumerDemandConfig config;

    public DwdDemandRelationProcess(ConsumerDemandConfig config) throws IOException {
        this.config = config;
    }

    /**
     * 具体执行SQL
     *
     * @param flinkEnv
     * @param catalog
     * @throws Exception
     */
    public void run(FlinkEnvUtils.FlinkEnv flinkEnv, IcebergCatalogReader catalog, ParameterTool paraTool) throws Exception {
        StreamTableEnvironment tableEnv = flinkEnv.streamTEnv();
        // 导入Rainbow密钥和环境
        Properties properties = PropertyUtils.loadProperties("env.properties");
        // rainbow初始化
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        String icebergTableName = "iceberg_" + config.sinkIcebergDemandRelationTableName;
        String pgTableName = "pg_" + config.sinkPgDemandRelationTableName;
        // 注册Iceberg Table
        org.apache.iceberg.Table relationIcebergTable = catalog.getTableInstance(
                config.databaseName,
                config.sinkIcebergDemandRelationTableName
        );
        IcebergTableBuilderStrategy relationIcebergTableBuilderStrategy = new IcebergTableBuilderStrategy(
                relationIcebergTable
        ).primaryKeyList(new String[]{"id"}).writeUpsertEnabled("true");
        String relationTableDDL = FlinkTableDDL.builder()
                .flinkTableName(icebergTableName)
                .tableBuilderStrategy(relationIcebergTableBuilderStrategy)
                .build();
        TableUtils.registerTable(
                tableEnv,
                relationTableDDL
        );
        // 注册PG Table
        DatabaseConf pgDBConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware"))
                .build();
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName(pgTableName)
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        config.sinkPgDemandRelationTableName,
                                        DatabaseEnum.PGSQL,
                                        pgDBConf
                                )
                        )
                        .build()
        );
        DatabaseConf s360BConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "s360"))
                .build();
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("f_s360_dwd_demand_relation")
                        .tableBuilderStrategy(
                                new JDBCTableBuilderStrategy(
                                        config.sinkPgDemandRelationTableName,
                                        DatabaseEnum.PGSQL,
                                        s360BConf
                                )
                        )
                        .build()
        );

        DatabaseConf rocksDbConf = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        // 注册starrocks表
        TableUtils.registerTable(
                tableEnv,
                FlinkTableDDL.builder()
                        .flinkTableName("starrocks_dwd_demand_relation")
                        .tableBuilderStrategy(
                                new StarRocksTableBuilderStrategy(
                                        "dwd_demand_relation",
                                        DatabaseEnum.ROCKS,
                                        rocksDbConf,
                                        8080
                                ).sinkParallelism("1")
                                        .primaryKeyList(new String[]{"id"})
                                        .property("sink.properties.format", "json")
                                        .property("sink.buffer-flush.max-rows", "64000")
                                        .property("sink.buffer-flush.interval-ms", "1000")
                                        .property("sink.buffer-flush.max-bytes", "67108864")
                                        .property("sink.properties.ignore_json_size", "true")
                                        .property("sink.properties.strip_outer_array", "true")
                        )
                        .build()
        );

        // 执行数据处理
        Table dwdTable = tableEnv.sqlQuery(
                String.format(
                        DemandRelationProcessSql.DMD_SOURCE_RELATION_PROCESS_SQL_TEMPLATE,
                        config.cdcDmdRelationTableName
                )
        );
        tableEnv.createTemporaryView("dwd_demand_relation", dwdTable);
        StatementSet sts = flinkEnv.stmtSet();
        // 出库PG
        sts.addInsertSql(
                String.format(
                        DemandRelationProcessSql.DEMAND_RELATION_SINK_TO_PG,
                        pgTableName,
                        "dwd_demand_relation"
                )
        );
        sts.addInsertSql(insertIntoSql(
                "dwd_demand_relation",
                "f_s360_dwd_demand_relation",
                tableEnv.from("f_s360_dwd_demand_relation"),
                PGSQL));
        // 出库Iceberg
        sts.addInsertSql(
                String.format(
                        DemandRelationProcessSql.DEMAND_RELATION_SINK_TO_ICEBERG,
                        icebergTableName,
                        "dwd_demand_relation"
                )
        );

        // 出库starrocks
        sts.addInsertSql(insertIntoSql(
                        "dwd_demand_relation",
                        "starrocks_dwd_demand_relation",
                        tableEnv.from("starrocks_dwd_demand_relation"),
                        ROCKS
                )
        );
    }
}