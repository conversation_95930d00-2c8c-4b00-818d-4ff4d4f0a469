package com.tencent.andata.demand.process;

import static com.tencent.andata.demand.sqls.DemandDataToEsSql.DEMAND_COMMENTS_LOOKUP_SQL;
import static com.tencent.andata.demand.sqls.DemandDataToEsSql.DWD_DEMAND_RELATION_LOOKUP_SQL;
import static com.tencent.andata.demand.sqls.DemandDataToEsSql.DWD_DEMAND_TAG_LOOKUP_SQL;
import static com.tencent.andata.demand.sqls.DemandDataToEsSql.DWS_DEMAND_BASE_INFO_SQL;
import static com.tencent.andata.demand.tablemap.DemandDataToEsMapping.mysqlTable2FTable;
import static com.tencent.andata.demand.tablemap.DemandDataToEsMapping.mysqllkTable2FTable;
import static com.tencent.andata.demand.tablemap.DemandDataToEsMapping.pgTable2FTable;
import static com.tencent.andata.demand.tablemap.DemandDataToEsMapping.pglkTable2FTable;
import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import com.tencent.andata.demand.consumers.strategy.DemandProcessStrategy;
import com.tencent.andata.utils.*;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.utils.struct.DbPurposeEnum;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.elasticsearch.sink.Elasticsearch7SinkBuilder;
import org.apache.flink.connector.elasticsearch.sink.ElasticsearchEmitter;
import org.apache.flink.connector.elasticsearch.sink.FlushBackoffType;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.http.HttpHost;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class DemandDataToEsProcess implements DemandProcessStrategy {

    private final String pgDbName;
    private final ObjectMapper mapper = new ObjectMapper();
    private static final Logger logger = LoggerFactory.getLogger(DemandDataToEsProcess.class);
    private static Set<String> VALID_KEYS = new HashSet<>(Arrays.asList("relation", "history", "tags", "ancomment"));


    /**
     * demand数据导入es
     *
     * @param fEnv flink运行环境工具类
     */
    @Override
    public void run(FlinkEnvUtils.FlinkEnv fEnv, IcebergCatalogReader catalogReader,ParameterTool paraTool) throws Exception {
        StreamTableEnvironment tEnv = fEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf pgDBCon = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", pgDbName))
                .build();

        DatabaseConf commDbCon = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils)
                .setGroupName("cdc.database.mysql.ancomment")
                .build();

        final ObjectMapper mapper = new ObjectMapper();
        ArrayNode pgTable2FlinkTable = mapper.readValue(pgTable2FTable, ArrayNode.class);
        ArrayNode mysqlTable2FlinkTable = mapper.readValue(mysqlTable2FTable, ArrayNode.class);
        buildSourceView(pgDBCon, fEnv, pgTable2FlinkTable, DatabaseEnum.PGSQL, paraTool);
        buildSourceView(commDbCon, fEnv, mysqlTable2FlinkTable, DatabaseEnum.MYSQL, paraTool);

        ArrayNode pglkTables = mapper.readValue(pglkTable2FTable, ArrayNode.class);
        ArrayNode mysqllkTables = mapper.readValue(mysqllkTable2FTable, ArrayNode.class);

        // 添加lookup表专用属性
        Properties lookupProps = new Properties();
        lookupProps.setProperty("lookup.cache.ttl", "60s");
        lookupProps.setProperty("lookup.max-retries", "3");
        lookupProps.setProperty("lookup.async", "true");
        lookupProps.setProperty("lookup.cache.max-rows", "1000");
        lookupProps.setProperty("jdbc.properties.zeroDateTimeBehavior", "convertToNull");

        // rdb table registe to flink table
        BuildFtables.rdbTable2FlinkTable(pgDBCon, pglkTables, PGSQL, tEnv, lookupProps, DbPurposeEnum.SINK);
        BuildFtables.rdbTable2FlinkTable(commDbCon, mysqllkTables, MYSQL, tEnv, lookupProps, DbPurposeEnum.SINK);

        Table dws_dmd_base_info = tEnv.sqlQuery(DWS_DEMAND_BASE_INFO_SQL);
        Table dwd_demand_comments = tEnv.sqlQuery(DEMAND_COMMENTS_LOOKUP_SQL);
        Table dwd_demand_relation = tEnv.sqlQuery(DWD_DEMAND_RELATION_LOOKUP_SQL);
        //Table dwd_demand_approval_history = tEnv.sqlQuery(DWD_DEMAND_APPROVAL_LOOKUP_SQL);
        Table dwd_demand_tag_story_relation_view = tEnv.sqlQuery(DWD_DEMAND_TAG_LOOKUP_SQL);

        // table to changelog stream
        DataStream<Row> dwsDmdBaseInfoStream = tEnv.toChangelogStream(dws_dmd_base_info);
        DataStream<Row> dwdDmdCommentsStream = tEnv.toChangelogStream(dwd_demand_comments);
        DataStream<Row> dwdDemandRelationStream = tEnv.toChangelogStream(dwd_demand_relation);
        //DataStream<Row> dwdDemandApprovalStream = tEnv.toChangelogStream(dwd_demand_approval_history);
        DataStream<Row> dwdDemandTagRelationStream = tEnv.toChangelogStream(dwd_demand_tag_story_relation_view);

        // format data to standard json
        DataStream<Tuple2<Long, String>> dataStream =
                dwsDmdBaseInfoStream
                        .union(dwdDmdCommentsStream)
                        .union(dwdDemandRelationStream)
                        //.union(dwdDemandApprovalStream)
                        .union(dwdDemandTagRelationStream)
                        .filter(new FilterFunction<Row>() {
                            @Override
                            public boolean filter(Row row) throws Exception {
                                return isIdNotNullOrNotDefault(row,0);
                            }
                        })
                        .process(new JsonFormatProcessFun());

        //es链接配置
        String esHost = rainbowUtils.getStringValue("sink.database.elasticsearch.ansearch", "host");
        String esPort = rainbowUtils.getStringValue("sink.database.elasticsearch.ansearch", "port");
        String esUserName = rainbowUtils.getStringValue("sink.database.elasticsearch.ansearch", "username");
        String esPassword = rainbowUtils.getStringValue("sink.database.elasticsearch.ansearch", "password");

        // sink to es
        Elasticsearch7SinkBuilder<Tuple2<Long, String>> esSinkBuilder = new Elasticsearch7SinkBuilder<>();
        esSinkBuilder.setEmitter(
                (ElasticsearchEmitter<Tuple2<Long, String>>) (row, context, indexer) ->
                        indexer.add(createUpdateIndexRequest(row)));

        esSinkBuilder.setConnectionUsername(esUserName)
                .setConnectionPassword(esPassword)
                .setHosts(new HttpHost(esHost, Integer.parseInt(esPort)))
                // 这里启用了一个指数退避重试策略，初始延迟为 1000 毫秒且最大重试次数为 10
                .setBulkFlushBackoffStrategy(FlushBackoffType.EXPONENTIAL, 10, 1000)
                .setBulkFlushInterval(6000) //批量写入的时间间隔
                .setBulkFlushMaxSizeMb(50) //批量写入时的最大数据量
                .setBulkFlushMaxActions(1000) //批量写入时的最大写入条数
                .setConnectionTimeout(600000)
                .setSocketTimeout(1000 * 60 * 10)
                .setConnectionRequestTimeout(1000 * 60 * 10);

        dataStream.sinkTo(esSinkBuilder.build()).name("sink-es");
    }

    private static UpdateRequest createUpdateIndexRequest(Tuple2<Long, String> row) {
        String idx = "andon_copilot-dmd-multi";
        IndexRequest indexRequest = new IndexRequest(idx);
        indexRequest.source(row.f1, XContentType.JSON);
        return new UpdateRequest(idx, String.valueOf(row.f0))
                .doc(indexRequest)
                .docAsUpsert(true)
                .retryOnConflict(5);
    }
    public static boolean isIdNotNullOrNotDefault(Row row, int fieldIndex) {
        Long fieldValue = row.<Long>getFieldAs(fieldIndex); // 获取字段值，可能为 null
        return fieldValue == null ? false : fieldValue != 0L;
    }
    private static class JsonFormatProcessFun extends ProcessFunction<Row, Tuple2<Long, String>> {

        @Override
        public void processElement(Row row, ProcessFunction<Row, Tuple2<Long, String>>.Context ctx,
                Collector<Tuple2<Long, String>> out) {
            long id = row.<Long>getFieldAs(0);
            String jsonString = row.<String>getFieldAs(1);

            JSONObject jsonObject = new JSONObject(jsonString);

            // 获取第一个有效的键
            Optional<String> firstKey = jsonObject.keySet().stream()
                    .filter(VALID_KEYS::contains)
                    .findFirst();

            // 检查键是否不是 "fields"
            if (firstKey.isPresent()) {
                String key = firstKey.get();
                String value = jsonObject.getString(key);
                jsonObject.put(key, new JSONArray(value));
                out.collect(new Tuple2<>(id, jsonObject.toString()));
                String comments = concatComments(firstKey.get(), jsonObject);
                // 将评论内容拼接在一起，下发给下个算子
                if (StringUtils.isNotEmpty(comments)) {
                    out.collect(new Tuple2<>(id, comments));
                }
            }

            // firstKey == "fields"
            out.collect(new Tuple2<>(id, jsonObject.toString()));
        }
    }

    private static String concatComments(String firstKey, JSONObject jsonObject) {
        // 拼接评论
        JSONObject concatComments = new JSONObject();
        StringBuilder commentContent = new StringBuilder();
        if (firstKey.equals("ancomment")) {
            JSONArray comments = jsonObject.getJSONArray("ancomment");
            for (int i = 0; i < comments.length(); i++) {
                JSONObject comment = comments.getJSONObject(i);
                commentContent.append(comment.getString("comment_text")).append("\n");
            }
        }
        concatComments.put("fields", new JSONObject().put("comments", commentContent.toString()));
        return StringUtils.isNotEmpty(commentContent.toString()) ? concatComments.toString() : "";
    }
}