package com.tencent.andata.etl.dwd;


import static com.tencent.andata.etl.sql.DwdFaultReportSql.deDupSQL;
import static com.tencent.andata.etl.sql.DwdFaultReportSql.insertIntoIceberg;
import static com.tencent.andata.etl.sql.DwdFaultReportSql.insertIntoPg;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

@Builder
public class DwdFaultReport {

    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {

        final String SOURCE_TABLE_NAME = "ods_fault_report";
        final String SINK_TABLE_NAME = "dwd_incident_ticket_fault_report";
        final String SINK_PG_TABLE_NAME = "dwd_fault_report";

        Properties properties = PropertyUtils.loadProperties("env.properties");

        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        final ObjectMapper mapper = new ObjectMapper();
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();

        // pg table mapping to flink sink table
        ArrayNode pgTable2FlinkTableMap = mapper.readValue(
                "[{\"rdbTable\":\"dwd_fault_report\",\"fTable\":\"pg_sink_dwd_fault_report\"}]",
                ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(pgDBConf, pgTable2FlinkTableMap, PGSQL, tEnv);

        // iceberg table mapping to flink sink table
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(
                        "[\n"
                                + "    {\n"
                                + "        \"icebergTable\": \"ods_fault_report\",\n"
                                + "        \"fTable\": \"iceberg_source_ods_fault_report\",\n"
                                + "        \"primaryKey\": \"value_of_primary_key\"\n"
                                + "    },\n"
                                + "    {\n"
                                + "        \"icebergTable\": \"dwd_incident_ticket_fault_report\",\n"
                                + "        \"fTable\": \"iceberg_sink_dwd_incident_ticket_fault_report\",\n"
                                + "        \"primaryKey\": \"value_of_primary_key\"\n"
                                + "    }\n"
                                + "]",
                        ArrayNode.class), tEnv, catalog
        );

        // create temporary view for source table
        tEnv.createTemporaryView("fault_report_view", tEnv.sqlQuery(deDupSQL));

        StatementSet stmtSet = flinkEnv.stmtSet();
        // iceberg: iceberg.ods_fault_report -> iceberg.dwd_fault_report
        stmtSet.addInsertSql(insertIntoIceberg(
                        "fault_report_view",
                        "iceberg_sink_dwd_incident_ticket_fault_report",
                        tEnv.from("iceberg_sink_dwd_incident_ticket_fault_report")))

                // pg: iceberg.ods_fault_report -> dwd_fault_report
                .addInsertSql(insertIntoPg(
                        "fault_report_view",
                        "pg_sink_dwd_fault_report",
                        tEnv.from("pg_sink_dwd_fault_report")));
    }
}