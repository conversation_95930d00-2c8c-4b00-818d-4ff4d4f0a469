package com.tencent.andata.etl.calculator.calculators.statemachine;

import java.time.LocalDateTime;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * 转单状态管理器
 * <p>
 * 负责管理转单计算过程中的状态转换，替代原来的多个boolean标志。
 * 提供状态查询、状态转换、状态历史记录等功能。
 * </p>
 * <p>
 * 主要功能：
 * 1. 状态转换管理
 * 2. 首次状态检查
 * 3. 状态历史记录
 * 4. 状态转换验证
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class TransferStateManager {

    /**
     * 当前状态映射
     * Key: 状态类型, Value: 是否已经发生过
     */
    private final EnumMap<TransferState, Boolean> stateMap;

    /**
     * 状态转换历史
     * Key: 状态类型, Value: 首次转换时间和相关数据
     */
    private final EnumMap<TransferState, StateTransition> transitionHistory;

    /**
     * 自定义状态数据
     * 用于存储与状态相关的额外数据
     */
    private final Map<String, Object> stateData;

    /**
     * 构造函数
     */
    public TransferStateManager() {
        this.stateMap = new EnumMap<>(TransferState.class);
        this.transitionHistory = new EnumMap<>(TransferState.class);
        this.stateData = new HashMap<>();
        
        // 初始化所有状态为未发生
        for (TransferState state : TransferState.values()) {
            stateMap.put(state, false);
        }
        
        log.debug("TransferStateManager initialized with {} states", TransferState.values().length);
    }

    /**
     * 检查是否是首次进入某个状态
     *
     * @param state 要检查的状态
     * @return true 如果是首次进入，false 如果已经进入过
     */
    public boolean isFirstTime(TransferState state) {
        return !stateMap.getOrDefault(state, false);
    }

    /**
     * 标记状态为已发生，并记录转换信息
     *
     * @param state 状态
     * @param operateTime 操作时间
     * @param additionalData 额外数据
     * @return true 如果是首次转换，false 如果之前已经转换过
     */
    public boolean markAsProcessed(TransferState state, String operateTime, Map<String, Object> additionalData) {
        boolean isFirstTime = isFirstTime(state);
        
        if (isFirstTime) {
            stateMap.put(state, true);
            
            StateTransition transition = new StateTransition(
                    state,
                    operateTime,
                    LocalDateTime.now(),
                    additionalData != null ? new HashMap<>(additionalData) : new HashMap<>()
            );
            
            transitionHistory.put(state, transition);
            
            log.trace("State transition: {} -> {} at {}", 
                     state.getDescription(), "PROCESSED", operateTime);
        }
        
        return isFirstTime;
    }

    /**
     * 标记状态为已发生（简化版本）
     *
     * @param state 状态
     * @param operateTime 操作时间
     * @return true 如果是首次转换，false 如果之前已经转换过
     */
    public boolean markAsProcessed(TransferState state, String operateTime) {
        return markAsProcessed(state, operateTime, null);
    }

    /**
     * 获取状态转换历史
     *
     * @param state 状态
     * @return 状态转换信息，如果没有转换过则返回空
     */
    public Optional<StateTransition> getTransitionHistory(TransferState state) {
        return Optional.ofNullable(transitionHistory.get(state));
    }

    /**
     * 获取首次转换时间
     *
     * @param state 状态
     * @return 首次转换时间，如果没有转换过则返回空
     */
    public Optional<String> getFirstTransitionTime(TransferState state) {
        return getTransitionHistory(state).map(StateTransition::getOperateTime);
    }

    /**
     * 存储自定义状态数据
     *
     * @param key 数据键
     * @param value 数据值
     */
    public void putStateData(String key, Object value) {
        stateData.put(key, value);
    }

    /**
     * 获取自定义状态数据
     *
     * @param key 数据键
     * @param clazz 数据类型
     * @return 数据值
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<T> getStateData(String key, Class<T> clazz) {
        Object value = stateData.get(key);
        if (value != null && clazz.isInstance(value)) {
            return Optional.of((T) value);
        }
        return Optional.empty();
    }

    /**
     * 重置所有状态
     */
    public void reset() {
        stateMap.clear();
        transitionHistory.clear();
        stateData.clear();
        
        // 重新初始化所有状态为未发生
        for (TransferState state : TransferState.values()) {
            stateMap.put(state, false);
        }
        
        log.debug("TransferStateManager reset completed");
    }

    /**
     * 获取当前状态摘要
     *
     * @return 状态摘要信息
     */
    public Map<TransferState, Boolean> getStateSummary() {
        return new EnumMap<>(stateMap);
    }

    /**
     * 检查多个状态是否都已发生
     *
     * @param states 要检查的状态列表
     * @return true 如果所有状态都已发生
     */
    public boolean allStatesProcessed(TransferState... states) {
        for (TransferState state : states) {
            if (isFirstTime(state)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查任意一个状态是否已发生
     *
     * @param states 要检查的状态列表
     * @return true 如果任意一个状态已发生
     */
    public boolean anyStateProcessed(TransferState... states) {
        for (TransferState state : states) {
            if (!isFirstTime(state)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 状态转换信息
     */
    public static class StateTransition {
        private final TransferState state;
        private final String operateTime;
        private final LocalDateTime transitionTime;
        private final Map<String, Object> additionalData;

        public StateTransition(TransferState state, String operateTime, LocalDateTime transitionTime, Map<String, Object> additionalData) {
            this.state = state;
            this.operateTime = operateTime;
            this.transitionTime = transitionTime;
            this.additionalData = additionalData;
        }

        public TransferState getState() {
            return state;
        }

        public String getOperateTime() {
            return operateTime;
        }

        public LocalDateTime getTransitionTime() {
            return transitionTime;
        }

        public Map<String, Object> getAdditionalData() {
            return additionalData;
        }

        @Override
        public String toString() {
            return String.format("StateTransition{state=%s, operateTime='%s', transitionTime=%s}", 
                               state, operateTime, transitionTime);
        }
    }
}
