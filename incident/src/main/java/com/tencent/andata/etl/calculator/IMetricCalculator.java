package com.tencent.andata.etl.calculator;

import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;

/**
 * 指标计算器接口
 * <p>
 * 定义了所有具体指标计算器需要遵循的契约。
 * 每个计算器负责计算一组相关的指标。
 *
 * @param <T> 计算上下文的类型，提供了计算所需的所有数据和状态。
 */
public interface IMetricCalculator<T extends CalculationContext> {

    /**
     * 在处理单个工单的操作流水之前调用，用于初始化计算器内部的状态。
     *
     * @param context 计算上下文，包含工单的全部信息。
     */
    void initialize(T context);

    /**
     * 遍历操作流水时，为每一条操作记录调用此方法。
     *
     * @param context       计算上下文。
     * @param currentOp     当前正在处理的操作记录。
     * @param previousOp    前一条操作记录，如果当前是第一条，则为 null。
     * @param nextOp        后一条操作记录，如果当前是最后一条，则为 null。
     */
    void processOperation(T context, Operation currentOp, Operation previousOp, Operation nextOp);

    /**
     * 在遍历完所有操作流水后调用，用于执行任何最终的计算或数据整理。
     *
     * @param context 计算上下文。
     */
    void calculate(T context);

    /**
     * 将此计算器负责的指标结果填充到最终的指标POJO中。
     *
     * @param metrics 最终的指标结果对象。
     * @param context 计算上下文。
     */
    void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, T context);
}