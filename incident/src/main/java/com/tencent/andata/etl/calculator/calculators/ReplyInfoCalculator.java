package com.tencent.andata.etl.calculator.calculators;

import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.calculator.IMetricCalculator;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import org.apache.commons.lang3.StringUtils;

/**
 * 回复信息提取计算器
 * <p>
 * 负责提取首次回复和首次供应商回复的内容。
 * 对应原 DwmIncidentTicketStatisticTransform.TicketReplyInfoMapFunction 中的逻辑。
 */
public class ReplyInfoCalculator implements IMetricCalculator<CalculationContext> {

    private String firstReplyContent;
    private String firstAgentReplyContent;

    @Override
    public void initialize(CalculationContext context) {
        this.firstReplyContent = "";
        this.firstAgentReplyContent = "";
    }

    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        // 如果两个内容都已经找到，则无需继续处理
        if (StringUtils.isNotEmpty(firstReplyContent) && StringUtils.isNotEmpty(firstAgentReplyContent)) {
            return;
        }

        // 只关心有外部回复的记录
        if (StringUtils.isEmpty(currentOp.getExternReply())) {
            return;
        }

        // 排除建单时的自动回复
        if(OperationType.of(currentOp.getOperationType()) == OperationType.CREATE){
            return;
        }

        OperatorType operatorType = OperatorType.fromType(currentOp.getOperatorType());
        if (operatorType == OperatorType.CUSTOMER_SERVICE) {
            // 记录客服的首次回复
            if (StringUtils.isEmpty(firstReplyContent)) {
                this.firstReplyContent = currentOp.getExternReply();
            }

            // 记录供应商的首次回复
            if (StringUtils.isEmpty(firstAgentReplyContent) && OperatorPost.of(currentOp.getPost()) == OperatorPost.SUPPLIER) {
                this.firstAgentReplyContent = currentOp.getExternReply();
            }
        }
    }

    @Override
    public void calculate(CalculationContext context) {
        // 无需在遍历后计算
    }

    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        metrics.setFirstReplyContent(this.firstReplyContent);
        metrics.setFirstAgentReplyContent(this.firstAgentReplyContent);
    }
}