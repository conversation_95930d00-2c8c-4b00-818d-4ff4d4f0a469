package com.tencent.andata.etl.calculator;

import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.entity.TicketProfile;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 计算上下文
 * <p>
 * 一个用于在指标计算的整个生命周期中存储和传递状态的对象。
 * 它封装了输入数据、中间计算结果以及最终输出，简化了方法签名，并使状态管理更加清晰。
 * 每个 TicketProfile 将拥有一个独立的 CalculationContext 实例。
 */
@Getter
@Setter
@RequiredArgsConstructor
public class CalculationContext {

    /**
     * 输入的工单资料，包含基础信息和完整的操作流水列表。
     */
    private final TicketProfile ticketProfile;

    /**
     * 预处理过的操作流水，按操作类型(operation_type)分组，
     * 以便快速查找特定类型的操作记录，避免在计算过程中重复过滤。
     * Key: operation_type_code, Value: List of Operations
     * 注意：这里没有使用Map<OperationType, List<Operation>>，而是使用Integer作为key
     * 是因为OperationType枚举的映射是上游业务系统提供的，枚举值可能随时会新增或变化，数仓这边可能感知不到，导致部分流水无法被正确分组
     */
    private Map<Integer, List<Operation>> operationsByType;

    /**
     * 预处理过的操作流水，按操作员岗位(post)分组，
     * 用于快速查找特定岗位执行的操作。
     * Key: post_code, Value: List of Operations
     * 注意：这里没有使用Map<Post, List<Operation>>，而是使用Integer作为key，理由同上
     */
    private Map<Integer, List<Operation>> operationsByPost;

    /**
     * 最终计算出的指标集合。
     * 各个计算器策略会将结果填充到这个对象中。
     */
    private DwmIncidentTicketStatisticMetrics metrics = new DwmIncidentTicketStatisticMetrics();

    /**
     * 初始化上下文，进行必要的预计算和数据分组。
     * 此方法在主流程遍历操作流水之前调用。
     */
    public void initialize() {
        // 使用Java Stream API进行分组
        this.operationsByType = this.ticketProfile.getOperations().stream().collect(Collectors.groupingBy(Operation::getOperationType));

        this.operationsByPost = this.ticketProfile.getOperations().stream().collect(Collectors.groupingBy(Operation::getPost));
    }
}