package com.tencent.andata.etl.calculator.calculators;

import static com.tencent.andata.etl.entity.Operation.isValidContent;
import static com.tencent.andata.utils.DateFormatUtils.timeDelta;
import static com.tencent.andata.utils.TimeUtil.formatDateTime;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.calculator.IMetricCalculator;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import com.tencent.andata.etl.enums.TicketServiceChannel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 转单指标计算器
 * <p>
 * 负责计算与工单流转相关的复杂指标，包括：
 * - 转单次数和类型统计
 * - 处理人跟踪和分析
 * - 队列和时间跟踪
 * - 热线转单处理
 * </p>
 * <p>
 * 采用面向对象设计，将复杂逻辑分解为多个内部类，提高代码可维护性和扩展性。
 * 使用函数式编程和Stream API优化性能，减少内存占用。
 * </p>
 * 对应原 DwmIncidentTicketStatisticTransform.TicketTransferInfoMapFunction 中的逻辑。
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class TransferCalculator implements IMetricCalculator<CalculationContext> {

    // ============================== 常量定义 ==============================
    private static final String HOTLINE_SERVICE_CHANNEL = String.valueOf(TicketServiceChannel.CALLCENTER.getId());
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String DEFAULT_TIME = "1970-01-01 00:00:00";


    // 后端岗位常量集合
    private static final List<OperatorPost> BACKEND_POSTS = Arrays.asList(
            OperatorPost.OPERATION, // 运维
            OperatorPost.PRODUCTION_RESEARCH, // 产研
            OperatorPost.P_VERTICAL_PRODUCTION_RESEARCH // 垂直产研
    );

    // 公司ID常量集合分别对应
    // 忆享云、安畅、云腾未来-腰部、安畅-腰部、安畅专项、私有云安畅、安畅网络、腾云悦智、云腾未来、腾云悦智_私有云、云腾未来_私有云、联想、金道
    private static final List<Integer> COMPANY_IDS = Arrays.asList(
            1, 2, 4, 5, 9, 23, 38, 169, 322, 163, 321, 335, 336
    );

    // 一线操作类型集合
    private static final List<OperationType> FIRST_LINE_OPERATION_TYPES = Arrays.asList(
            OperationType.RETRIEVE,
            OperationType.TRANSFER,
            OperationType.REPLY,
            OperationType.TRANSFER_RESPONSIBLE,
            OperationType.WAIT_CUSTOMER_ADD_INFO,
            OperationType.CLOSE_APPLICATION,
            OperationType.WAIT_CUSTOMER_CLOSE,
            OperationType.ADD_INFO_APPLICATION,
            OperationType.TO_CONFIRM_RESTORE,
            OperationType.RESET_STATUS,
            OperationType.INSURE_ARCHIVE
    );

    // 1.5线操作类型集合
    private static final List<OperationType> SECOND_LINE_OPERATION_TYPES = Arrays.asList(
            OperationType.RETRIEVE,
            OperationType.PULL,
            OperationType.TRANSFER,
            OperationType.REPLY,
            OperationType.TRANSFER_RESPONSIBLE,
            OperationType.WAIT_CUSTOMER_ADD_INFO,
            OperationType.CLOSE_APPLICATION,
            OperationType.AGREE_CLOSE_APPLICATION,
            OperationType.DISAGREE_CLOSE_APPLICATION,
            OperationType.WAIT_CUSTOMER_CLOSE,
            OperationType.CLOSE,
            OperationType.ADD_INFO_APPLICATION,
            OperationType.SET_DELAY_DISPATCH,
            OperationType.PULL_RESPONSIBLE,
            OperationType.TO_CONFIRM_RESTORE,
            OperationType.AGREE_ADD_INFO_APPLICATION,
            OperationType.DISAGREE_ADD_INFO_APPLICATION,
            OperationType.PULL_INCIDENT_MANAGER,
            OperationType.INSURE_ARCHIVE,
            OperationType.BUILD_GROUP_CHAT
    );

    // TEG队列ID常量
    private static final List<Integer> TGW_QUEUE_IDS = Arrays.asList(
            6210, //专项KA-网络TGW队列-CLB/EIP转发问题
            6209 // 专项-网络TGW队列-CLB/EIP转发问题
    );

    // ============================== 内部数据结构 ==============================
    private TransferMetrics transferMetrics;
    private OperatorTracker operatorTracker;

    // ============================== 计算器状态字段 ==============================
    // 辅助状态变量
    private boolean isFirstAssignFlag = true;
    private boolean isFirstCheckFlag = false;
    private boolean isFirstTransferOperationFlag = true;
    private boolean isFirstTransferSecondLineFlag = true;
    private boolean isFirstTransferProductionResearchFlag = true;
    private boolean isFirstTransferVerticalProductionResearchFlag = true;
    private boolean isFirstTransferComplaintQueueTimeFlag = true;
    private boolean isFirstSecondLineOperatorFlag1 = true;
    private boolean isFirstSecondLineOperatorFlag2 = true;
    private boolean isFirstSecondLineOperatorFlag3 = true;
    private boolean isFirstSecondLineFactFlag = true;

    // 其他状态
    private String serviceChannel = "";
    private String closeTime = "";
    private String ticketSolvedTime = "";
    private boolean isCloseFlag = false;

    @Override
    public void initialize(CalculationContext context) {
        // 初始化所有数据结构
        this.transferMetrics = new TransferMetrics();
        this.operatorTracker = new OperatorTracker();

        // 重置所有状态标志
        resetAllFlags();

        // 获取基础信息
        this.closeTime = context.getMetrics().getCloseTime();
        this.ticketSolvedTime = context.getMetrics().getTicketSolvedTime();
        this.serviceChannel = Optional.of(context.getTicketProfile().getServiceChannel()).map(channel -> String.valueOf(channel)).orElse("");
        this.isCloseFlag = context.getTicketProfile().getOperations().stream().anyMatch(op -> op.getOperationType() == OperationType.CLOSE.getCode());
    }

    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        OperatorType operatorType = OperatorType.fromType(currentOp.getOperatorType());
        OperatorPost operatorPost = OperatorPost.of(currentOp.getOperatorPost());
        OperationType opType = OperationType.of(currentOp.getOperationType());
        OperatorPost targetPost = OperatorPost.of(currentOp.getTargetPost());

        final String operateTime = formatDateTime(currentOp.getOperateTime());
        final int index = context.getTicketProfile().getOperations().indexOf(currentOp);

        // 检查是否应该处理此操作
        if (!shouldProcessOperation(operateTime)) {
            return;
        }

        // 1. 处理转单链和转单类型
        processTransferChain(currentOp, operatorPost, targetPost);

        // 2. 处理各种转单逻辑
        processTransferOperations(currentOp, opType, operatorType, targetPost, operateTime, previousOp);

        // 3. 处理操作人跟踪
        processOperatorTracking(currentOp, operatorType, operatorPost, operateTime, index);

        // 5. 处理特殊队列和标记
        processSpecialQueuesAndFlags(currentOp, operatorPost, operateTime, index);

        // 6. 处理热线转单逻辑
        processHotlineTransfer(currentOp, opType, operateTime);
    }

    // ============================== 接口实现方法 ==============================

    @Override
    public void calculate(CalculationContext context) {
        // 后处理逻辑

        // 1. 处理1.5线操作人相关计算
        finalizeSecondLineOperators();

        // 2. 计算最后处理人信息
        calculateLastOperators(context);

        // 3. 计算各种时长
        calculateDurations();

        // 4. 生成转单类型字符串
        transferMetrics.transferType = String.join("->", transferMetrics.transferPostList);
    }

    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        // 转单次数相关
        metrics.setTransferToOutTimes(transferMetrics.transferToOutTimes);
        metrics.setTransferToInTimes(transferMetrics.transferToInTimes);
        metrics.setFirstTransferPost(transferMetrics.firstTransferPost);
        metrics.setTransferOperationTimes(transferMetrics.transferOperationTimes);
        metrics.setTransferFirstLineTimes(transferMetrics.transferFirstLineTimes);
        metrics.setTransferSecondLineTimes(transferMetrics.transferSecondLineTimes);
        metrics.setTransferProductionResearchTimes(transferMetrics.transferProductionResearchTimes);
        metrics.setTransferVerticalProductionResearchTimes(transferMetrics.transferVerticalProductionResearchTimes);

        // 转单状态标记
        metrics.setIsTransferFirstLine(transferMetrics.isTransferFirstLine ? 1 : 0);
        metrics.setIsTransferTgw(transferMetrics.isTransferTgw ? 1 : 0);

        // 转单链
        try {
            metrics.setTransferChain(OBJECT_MAPPER.writeValueAsString(transferMetrics.transferChain));
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize transfer chain for ticketId: {}", context.getTicketProfile().getTicketId(), e);
            metrics.setTransferChain("[]");
        }

        // 转单时间信息
        metrics.setTransferFirstLineTime(transferMetrics.transferFirstLineTime);
        metrics.setFirstTransferSecondLineTime(transferMetrics.firstTransferSecondLineTime);
        metrics.setFirstTransferOperationTime(transferMetrics.firstTransferOperationTime);
        metrics.setFirstTransferProductionResearchTime(transferMetrics.firstTransferProductionResearchTime);
        metrics.setFirstTransferComplaintQueueTime(transferMetrics.firstTransferComplaintQueueTime);

        // 队列信息
        metrics.setTransferFirstLineQueue(transferMetrics.transferFirstLineQueue);
        metrics.setFirstSecondLineAssign(transferMetrics.firstSecondLineAssign);
        metrics.setFirstSecondLineFactAssign(transferMetrics.firstSecondLineFactAssign);
        metrics.setCurrentSecondLineFactAssign(transferMetrics.currentSecondLineFactAssign);
        metrics.setLastOperationFactAssign(transferMetrics.lastOperationFactAssign);
        metrics.setLastProductionResearchFactAssign(transferMetrics.lastProductionResearchFactAssign);

        // 热线转单相关
        metrics.setHotlineFirstTransferLineTime(transferMetrics.hotlineFirstTransferTime);
        metrics.setHotlineFirstTransferResponseTime(transferMetrics.hotlineFirstTransferResponseTime);
        metrics.setHotlineFirstTransferDealDuration(transferMetrics.hotlineFirstTransferDealDuration);
        metrics.setHotlineFirstTransferResponseDuration(transferMetrics.hotlineFirstTransferResponseDuration);

        // 操作人信息
        metrics.setOperationHandler(operatorTracker.operationHandler);
        metrics.setLastCustomerOperator(operatorTracker.lastCustomerOperator);
        metrics.setCurrentFirstLineOperator(operatorTracker.currentFirstLineOperator);
        metrics.setCurrentSecondLineOperator(operatorTracker.currentSecondLineOperator);
        metrics.setFirstLineLastOperator(operatorTracker.firstLineLastOperator);
        metrics.setLastOfcOperator(operatorTracker.lastOfcOperator);
        metrics.setProductionResearchHandler(operatorTracker.productionResearchHandler);

        metrics.setTransferFirstLineStaff(operatorTracker.transferFirstLineStaff);
        metrics.setFirstTransferOperationStaff(operatorTracker.firstTransferOperationStaff);
        metrics.setFirstTransferSecondLineStaff(operatorTracker.firstTransferSecondLineStaff);

        metrics.setOperationHandlerCompanyId(operatorTracker.operationHandlerCompanyId);
        metrics.setCurrentSecondLineOperatorCompanyId(operatorTracker.currentSecondLineOperatorCompanyId);
        metrics.setFirstSecondLineOperatorCompanyId(operatorTracker.firstSecondLineOperatorCompanyId);
        metrics.setFirstLineLastOperatorCompanyId(operatorTracker.firstLineLastOperatorCompanyId);

        metrics.setFirstTransferOperationStaffPost(operatorTracker.firstTransferOperationStaffPost);
        metrics.setFirstTransferProductionResearchStaffPost(operatorTracker.firstTransferProductionResearchStaffPost);
        metrics.setFirstTransferVertProductionResearchStaffPost(operatorTracker.firstTransferVertProductionResearchStaffPost);
        metrics.setLastOfcOperatorPost(operatorTracker.lastOfcOperatorPost);
        metrics.setFirstLineLastOperatorFactAssign(operatorTracker.firstLineLastOperatorFactAssign);

        metrics.setFirstSecondLineOperator(operatorTracker.firstSecondLineOperator);
        metrics.setFirstSecondLineFactOperator(operatorTracker.firstSecondLineFactOperator);
        metrics.setSecondLineLastCompanyOperator(operatorTracker.secondLineLastCompanyOperator);
        metrics.setSecondLineLastCompanyOperatorGroupName(operatorTracker.secondLineLastCompanyOperatorGroupName);
        metrics.setSecondLineLastCompanyOperatorCompanyId(operatorTracker.secondLineLastCompanyOperatorCompanyId);

        try {
            metrics.setSecondLineOperators(OBJECT_MAPPER.writeValueAsString(operatorTracker.secondLineOperators));
            metrics.setSecondLineOperatorCompanyIds(OBJECT_MAPPER.writeValueAsString(operatorTracker.secondLineOperatorCompanyIds));
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize second line operators for ticketId: {}", context.getTicketProfile().getTicketId(), e);
            metrics.setSecondLineOperators("[]");
            metrics.setSecondLineOperatorCompanyIds("{}");
        }

        metrics.setFirstSecondLineServiceTime(operatorTracker.firstSecondLineServiceTime);
        metrics.setFirstSecondLineResponseDuration(operatorTracker.firstSecondLineResponseDuration);
        metrics.setSecondLineSolvedDuration(operatorTracker.secondLineSolvedDuration);
        metrics.setLastSecondLineOperatorDuration(operatorTracker.lastSecondLineOperatorDuration);

        metrics.setIsTransferCompanyInSecondLine(operatorTracker.isTransferCompanyInSecondLine ? 1 : 0);
        metrics.setIsTransferInnerStaffInSecondLine(operatorTracker.isTransferInnerStaffInSecondLine ? 1 : 0);

        // 其他
        metrics.setMeasures(transferMetrics.measures);
        metrics.setTransferType(transferMetrics.transferType);
    }

    /**
     * 重置所有状态标志
     */
    private void resetAllFlags() {
        isFirstAssignFlag = true;
        isFirstCheckFlag = false;
        isFirstSecondLineFactFlag = true;
        isFirstTransferOperationFlag = true;
        isFirstTransferSecondLineFlag = true;
        isFirstSecondLineOperatorFlag1 = true;
        isFirstSecondLineOperatorFlag2 = true;
        isFirstSecondLineOperatorFlag3 = true;
        isFirstTransferComplaintQueueTimeFlag = true;
        isFirstTransferProductionResearchFlag = true;
        isFirstTransferVerticalProductionResearchFlag = true;
    }

    /**
     * 判断是否应该处理当前操作
     */
    private boolean shouldProcessOperation(String operateTime) {
        // 如果是结单工单且操作时间超过结单时间，则跳过
        return !isCloseFlag || !isValidContent(closeTime, DEFAULT_TIME) || operateTime.compareTo(closeTime) <= 0;
    }

    // ============================== 私有辅助方法 ==============================

    /**
     * 处理转单链和转单类型
     */
    private void processTransferChain(Operation currentOp, OperatorPost fromPost, OperatorPost targetPost) {
        OperationType opType = OperationType.of(currentOp.getOperationType());

        if (opType == OperationType.TRANSFER) {
            // 计算转入转出次数
            boolean fromIsBackend = BACKEND_POSTS.contains(fromPost);
            boolean toIsBackend = BACKEND_POSTS.contains(targetPost);

            if (fromIsBackend && !toIsBackend) {
                transferMetrics.transferToOutTimes++;
            }

            if (!fromIsBackend && toIsBackend) {
                transferMetrics.transferToInTimes++;
            }
        }

        // 记录转单岗位
        String postStr = String.valueOf(currentOp.getPost());
        if (!transferMetrics.transferPostList.contains(postStr)) {
            transferMetrics.transferPostList.add(postStr);
        }

        // 构建转单链
        transferMetrics.transferChain.add(fromPost.getName() + "->" + targetPost.getName());
    }

    /**
     * 处理各种转单操作
     */
    private void processTransferOperations(Operation currentOp, OperationType opType, OperatorType operatorType, OperatorPost targetPost, String operateTime, Operation previousOp) {

        // 处理转单操作
        if (opType == OperationType.TRANSFER) {
            processTransferByTargetPost(currentOp, targetPost, operateTime, operatorType);
        }

        // 处理认领操作
        if (opType == OperationType.PULL) {
            processPullOperation(currentOp, targetPost, operateTime, operatorType, previousOp);
        }

        // 处理派单操作
        if (opType == OperationType.DISPATCH) {
            processDispatchOperation(currentOp, operatorType, targetPost, operateTime);
        }
    }

    /**
     * 根据目标岗位处理转单
     */
    private void processTransferByTargetPost(Operation currentOp, OperatorPost targetPost, String operateTime, OperatorType operatorType) {
        switch (targetPost) {
            case FIRST_LINE:
                handleTransferToFirstLine(currentOp, operateTime, operatorType);
                break;
            case SECOND_LINE:
                handleTransferToSecondLine(currentOp, operateTime, operatorType);
                break;
            case OPERATION:
                handleTransferToOperation(currentOp, operateTime);
                break;
            case PRODUCTION_RESEARCH:
                handleTransferToProductionResearch(currentOp, operateTime);
                break;
            case P_VERTICAL_PRODUCTION_RESEARCH:
                handleTransferToVerticalProductionResearch(currentOp);
                break;
            default:
                break;
        }
    }

    /**
     * 处理转一线
     */
    private void handleTransferToFirstLine(Operation currentOp, String operateTime, OperatorType operatorType) {
        transferMetrics.transferFirstLineTimes++;
        transferMetrics.isTransferFirstLine = true;
        transferMetrics.transferFirstLineTime = operateTime;
        transferMetrics.transferFirstLineQueue = currentOp.getFactAssign();

        if (operatorType == OperatorType.CUSTOMER_SERVICE && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
            operatorTracker.transferFirstLineStaff = currentOp.getOperatorName();
        }
    }

    /**
     * 处理转1.5线
     */
    private void handleTransferToSecondLine(Operation currentOp, String operateTime, OperatorType operatorType) {
        transferMetrics.transferSecondLineTimes++;

        if (isValidContent(transferMetrics.firstTransferSecondLineTime, DEFAULT_TIME)) {
            transferMetrics.firstTransferSecondLineTime = operateTime;
        }

        if (transferMetrics.firstSecondLineAssign < 0) {
            transferMetrics.firstSecondLineAssign = currentOp.getNextAssign();
        }

        if (isFirstSecondLineOperatorFlag1 && isValidContent(currentOp.getNextOperatorName(), "SYSTEM")) {
            updateFirstSecondLineOperator(currentOp, operateTime);
        }

        if (isFirstTransferSecondLineFlag && operatorType == OperatorType.CUSTOMER_SERVICE && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
            isFirstTransferSecondLineFlag = false;
            operatorTracker.firstTransferSecondLineStaff = currentOp.getOperatorName();
        }
    }

    /**
     * 处理转运维
     */
    private void handleTransferToOperation(Operation currentOp, String operateTime) {
        transferMetrics.transferOperationTimes++;

        if (isFirstTransferOperationFlag && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
            isFirstTransferOperationFlag = false;
            transferMetrics.firstTransferOperationTime = operateTime;
            operatorTracker.firstTransferOperationStaffPost = currentOp.getPost();
            operatorTracker.firstTransferOperationStaff = currentOp.getOperatorName();
        }
    }

    /**
     * 处理转产研
     */
    private void handleTransferToProductionResearch(Operation currentOp, String operateTime) {
        transferMetrics.transferProductionResearchTimes++;

        if (isFirstTransferProductionResearchFlag) {
            isFirstTransferProductionResearchFlag = false;
            operatorTracker.firstTransferProductionResearchStaffPost = currentOp.getPost();
            transferMetrics.firstTransferProductionResearchTime = operateTime;
        }
    }

    /**
     * 处理转垂直产研
     */
    private void handleTransferToVerticalProductionResearch(Operation currentOp) {
        transferMetrics.transferVerticalProductionResearchTimes++;

        if (isFirstTransferVerticalProductionResearchFlag) {
            isFirstTransferVerticalProductionResearchFlag = false;
            operatorTracker.firstTransferVertProductionResearchStaffPost = currentOp.getPost();
        }
    }

    /**
     * 处理认领操作
     */
    private void processPullOperation(Operation currentOp, OperatorPost targetPost, String operateTime, OperatorType operatorType, Operation previousOp) {
        if (targetPost == OperatorPost.SECOND_LINE) {
            if (previousOp != null && isFirstTransferSecondLineFlag && operatorType == OperatorType.CUSTOMER_SERVICE) {
                isFirstTransferSecondLineFlag = false;
                String preOperator = previousOp.getOperatorName();
                String preNextOperator = previousOp.getNextOperatorName();
                operatorTracker.firstTransferSecondLineStaff =
                        "SYSTEM".equalsIgnoreCase(preOperator) ? preOperator : preNextOperator;
            }

            if (StringUtils.isEmpty(operatorTracker.firstSecondLineServiceTime)) {
                // 这里应该是secondLineFirstProcessingTime，简化处理
                operatorTracker.firstSecondLineServiceTime = operateTime;
            }

            if (isFirstSecondLineOperatorFlag2 && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
                updateFirstSecondLineOperator(currentOp, operateTime);
                isFirstSecondLineOperatorFlag2 = false;
            }
        } else if (targetPost == OperatorPost.OPERATION) {
            transferMetrics.transferOperationTimes++;
            if (isFirstTransferOperationFlag) {
                isFirstTransferOperationFlag = false;
                transferMetrics.firstTransferOperationTime = operateTime;
            }
        }
    }

    /**
     * 处理派单操作
     */
    private void processDispatchOperation(Operation currentOp, OperatorType operatorType, OperatorPost targetPost, String operateTime) {
        if (operatorType == OperatorType.SYSTEM && targetPost == OperatorPost.SECOND_LINE) {
            if (!isFirstSecondLineOperatorFlag3 && isFirstSecondLineOperatorFlag1) {
                updateFirstSecondLineOperator(currentOp, operateTime);
                isFirstSecondLineOperatorFlag1 = false;
            }
        }

        // 808 -- 专项-需求队列
        if (currentOp.getFactAssign() == 808 && transferMetrics.firstSecondLineAssign < 0) {
            transferMetrics.firstSecondLineAssign = currentOp.getFactAssign();
        }
    }

    /**
     * 更新首次1.5线操作人信息
     */
    private void updateFirstSecondLineOperator(Operation currentOp, String operateTime) {
        operatorTracker.operatorTreeMap.put(operateTime, new HashMap<String, Integer>() {{
            put(currentOp.getNextOperatorName(), currentOp.getNextOperatorCompanyId());
        }});

        if (isFirstSecondLineOperatorFlag3) {
            isFirstSecondLineOperatorFlag3 = false;
        }
    }

    /**
     * 处理操作人跟踪
     */
    private void processOperatorTracking(Operation currentOp, OperatorType operatorType, OperatorPost operatorPost, String operateTime, int index) {

        // 跟踪当前处理人
        if (currentOp.getPost() == OperatorPost.SECOND_LINE.getCode()) {
            transferMetrics.currentSecondLineFactAssign = currentOp.getFactAssign();
        } else if (operatorPost == OperatorPost.FIRST_LINE && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
            operatorTracker.currentFirstLineOperator = currentOp.getOperatorName();
        }

        // 跟踪运维处理人
        if (currentOp.getPost() == OperatorPost.OPERATION.getCode() && operatorPost == OperatorPost.OPERATION) {
            operatorTracker.operationHandler = currentOp.getOperatorName();
            operatorTracker.operationHandlerCompanyId = currentOp.getOperatorCompanyId();
        }

        // 跟踪产研处理人
        if (currentOp.getPost() == OperatorPost.PRODUCTION_RESEARCH.getCode() && operatorPost == OperatorPost.PRODUCTION_RESEARCH
                && operateTime.compareTo(ticketSolvedTime) <= 0 && !ticketSolvedTime.equals(DEFAULT_TIME)) {
            operatorTracker.productionResearchHandler = currentOp.getOperatorName();
        }

        // 跟踪客服处理人
        if (operatorType == OperatorType.CUSTOMER_SERVICE) {
            updateCustomerServiceOperators(currentOp, operateTime);
        }

        // 更新队列信息
        updateQueueInfo(currentOp, operatorPost);
    }

    /**
     * 更新客服操作人信息
     */
    private void updateCustomerServiceOperators(Operation currentOp, String operateTime) {
        boolean passesFilter = currentOp.getOperatorPost() > -1
                && isValidContent(currentOp.getOperatorName(), "SYSTEM")
                && (!isCloseFlag || (isValidContent(closeTime, DEFAULT_TIME) && operateTime.compareTo(closeTime) <= 0));

        if (passesFilter) {
            operatorTracker.lastCustomerOperator = currentOp.getOperatorName();

            // 跟踪1.5线操作人
            if (currentOp.getOperatorPost() == OperatorPost.SECOND_LINE.getCode()) {
                operatorTracker.secondLineOperators.add(currentOp.getOperatorName());
                operatorTracker.secondLineOperatorCompanyIds.put(currentOp.getOperatorName(), currentOp.getOperatorCompanyId());

                // 跟踪供应商最终处理人
                updateSecondLineCompanyOperator(currentOp);
            }
        }
    }

    /**
     * 更新1.5线供应商处理人
     */
    private void updateSecondLineCompanyOperator(Operation currentOp) {
        int[] validOperationTypes = {5, 40, 13, 12, 28, 30, 43, 33, 3, 48, 50, 2, 16, 8, 35, 15, 11, 14, 41, 10};
        boolean isValidOperation = Arrays.stream(validOperationTypes).anyMatch(x -> x == currentOp.getOperationType());

        if (currentOp.getPost() == OperatorPost.SECOND_LINE.getCode()
                && COMPANY_IDS.contains(currentOp.getOperatorCompanyId())
                && isValidOperation) {
            operatorTracker.secondLineLastCompanyOperator = currentOp.getOperatorName();
            operatorTracker.secondLineLastCompanyOperatorCompanyId = currentOp.getOperatorCompanyId();
            operatorTracker.secondLineLastCompanyOperatorGroupName = currentOp.getOperatorGroupName();
        }
    }

    /**
     * 更新队列信息
     */
    private void updateQueueInfo(Operation currentOp, OperatorPost operatorPost) {
        if (operatorPost == OperatorPost.OPERATION && currentOp.getOperationType() != OperationType.CLOSE.getCode()) {
            transferMetrics.lastOperationFactAssign = currentOp.getFactAssign();
        } else if ((operatorPost == OperatorPost.PRODUCTION_RESEARCH || operatorPost == OperatorPost.UNKNOWN)
                && currentOp.getOperationType() != OperationType.CLOSE.getCode()) {
            transferMetrics.lastProductionResearchFactAssign = currentOp.getFactAssign();
        }
    }

    /**
     * 处理特殊队列和标记
     */
    private void processSpecialQueuesAndFlags(Operation currentOp, OperatorPost operatorPost, String operateTime, int index) {
        // 检查TGW队列
        if (TGW_QUEUE_IDS.contains(currentOp.getFactAssign())) {
            transferMetrics.isTransferTgw = true;
        }

        // 首次转投诉队列时间
        if (currentOp.getFactAssign() == 4220 && isFirstTransferComplaintQueueTimeFlag && index > 1) {
            isFirstTransferComplaintQueueTimeFlag = false;
            // 获取上一个操作的时间，这里简化处理
            transferMetrics.firstTransferComplaintQueueTime = operateTime;
        }

        // 处理结果提取
        if (OperatorType.fromType(currentOp.getOperatorType()) == OperatorType.CUSTOMER_SERVICE
                && isValidContent(currentOp.getInnerReply(), "NULL")) {
            if (currentOp.getFactAssign() == 4220 || "38".equals(serviceChannel)) {
                transferMetrics.measures = currentOp.getInnerReply();
            }
        }
    }

    /**
     * 处理热线转单逻辑
     */
    private void processHotlineTransfer(Operation currentOp, OperationType opType, String operateTime) {
        if (!HOTLINE_SERVICE_CHANNEL.equals(serviceChannel)) {
            return;
        }

        // 计算热线转单相关时长
        if (isFirstAssignFlag && (opType == OperationType.TRANSFER || opType == OperationType.DISPATCH)) {
            transferMetrics.hotlineFirstTransferTime = operateTime;
            isFirstAssignFlag = false;
            isFirstCheckFlag = true;
        } else if (isFirstCheckFlag && opType == OperationType.RETRIEVE) {
            transferMetrics.hotlineFirstTransferResponseTime = operateTime;
            isFirstCheckFlag = false;
        }
    }

    /**
     * 完成1.5线操作人相关计算
     */
    private void finalizeSecondLineOperators() {
        // 处理1.5线操作人公司转换逻辑
        if (!operatorTracker.secondLineOperatorCompanyIds.isEmpty()) {
            // 检查是否转内部员工
            if (operatorTracker.secondLineOperatorCompanyIds.values().stream()
                    .anyMatch(id -> id == 0 || id == 217)) {
                operatorTracker.isTransferInnerStaffInSecondLine = true;
            }

            // 检查是否转多个供应商
            long distinctCompanyCount = operatorTracker.secondLineOperatorCompanyIds.values().stream()
                    .map(id -> id == 217 ? 0 : id)
                    .filter(id -> id != 0)
                    .distinct()
                    .count();

            if (distinctCompanyCount > 1) {
                operatorTracker.isTransferCompanyInSecondLine = true;
            }
        }

        // 设置首次1.5线操作人
        if (!operatorTracker.operatorTreeMap.isEmpty()) {
            String firstKey = operatorTracker.operatorTreeMap.firstKey();
            Map<String, Integer> firstEntry = operatorTracker.operatorTreeMap.get(firstKey);

            operatorTracker.firstSecondLineOperator = firstEntry.keySet().stream()
                    .findFirst().orElse("");
            operatorTracker.firstSecondLineOperatorCompanyId = firstEntry.values().stream()
                    .findFirst().orElse(-1);
        }
    }

    /**
     * 计算最后处理人信息
     */
    private void calculateLastOperators(CalculationContext context) {
        List<Operation> operations = context.getTicketProfile().getOperations();

        // 计算一线最后处理人
        operations.stream()
                .filter(op -> FIRST_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType()))
                        && op.getOperatorPost() == OperatorPost.FIRST_LINE.getCode()
                        && OperatorType.fromType(op.getOperatorType()) == OperatorType.CUSTOMER_SERVICE)
                .filter(op -> shouldProcessOperation(formatDateTime(op.getOperateTime())))
                .max(Operation::compareTo)
                .ifPresent(lastOp -> {
                    operatorTracker.firstLineLastOperator = lastOp.getOperatorName();
                    operatorTracker.firstLineLastOperatorCompanyId = lastOp.getOperatorCompanyId();
                    operatorTracker.firstLineLastOperatorFactAssign = lastOp.getFactAssign();
                });

        // 计算1.5线当前处理人
        operations.stream()
                .filter(op -> SECOND_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType()))
                        && op.getOperatorPost() == OperatorPost.SECOND_LINE.getCode()
                        && OperatorType.fromType(op.getOperatorType()) == OperatorType.CUSTOMER_SERVICE)
                .filter(op -> shouldProcessOperation(formatDateTime(op.getOperateTime())))
                .max(Operation::compareTo)
                .ifPresent(lastOp -> {
                    operatorTracker.currentSecondLineOperator = lastOp.getOperatorName();
                    operatorTracker.currentSecondLineOperatorCompanyId = lastOp.getOperatorCompanyId();
                });

        // 计算最后处理人
        operations.stream()
                .filter(op -> (op.getOperatorPost() == OperatorPost.FIRST_LINE.getCode() && FIRST_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType())))
                        || (op.getOperatorPost() == OperatorPost.SECOND_LINE.getCode() && SECOND_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType()))))
                .filter(op -> shouldProcessOperation(formatDateTime(op.getOperateTime())))
                .max(Operation::compareTo)
                .ifPresent(lastOp -> {
                    operatorTracker.lastOfcOperator = lastOp.getOperatorName();
                    operatorTracker.lastOfcOperatorPost = lastOp.getOperatorPost();
                });
    }

    /**
     * 计算各种时长
     */
    private void calculateDurations() {
        // 计算1.5线解决时长
        if (!ticketSolvedTime.equals(DEFAULT_TIME) && isValidContent(operatorTracker.firstSecondLineServiceTime, DEFAULT_TIME)) {
            try {
                operatorTracker.secondLineSolvedDuration = (int) timeDelta(operatorTracker.firstSecondLineServiceTime, ticketSolvedTime);
            } catch (Exception e) {
                log.error("Failed to calculate second line solved duration", e);
            }
        }

        // 计算热线转单处理时长
        if (!transferMetrics.hotlineFirstTransferTime.equals(DEFAULT_TIME) && !closeTime.equals(DEFAULT_TIME)) {
            try {
                transferMetrics.hotlineFirstTransferDealDuration = (int) timeDelta(closeTime, transferMetrics.hotlineFirstTransferTime);
            } catch (Exception e) {
                log.error("Failed to calculate hotline transfer deal duration", e);
            }
        }

        // 计算热线转单响应时长
        if (!transferMetrics.hotlineFirstTransferResponseTime.equals(DEFAULT_TIME)
                && !transferMetrics.hotlineFirstTransferTime.equals(DEFAULT_TIME)) {
            try {
                transferMetrics.hotlineFirstTransferResponseDuration = (int) timeDelta(
                        transferMetrics.hotlineFirstTransferResponseTime, transferMetrics.hotlineFirstTransferTime);
            } catch (Exception e) {
                log.error("Failed to calculate hotline transfer response duration", e);
            }
        }
    }

    /**
     * 转单统计指标数据结构
     * 使用Builder模式构建复杂的转单指标
     */
    private static class TransferMetrics {

        // 转单次数统计
        private int transferToOutTimes = 0;
        private int transferToInTimes = 0;
        private int transferOperationTimes = 0;
        private int transferFirstLineTimes = 0;
        private int transferSecondLineTimes = 0;
        private int transferProductionResearchTimes = 0;
        private int transferVerticalProductionResearchTimes = 0;

        // 转单状态标记
        private boolean isTransferTgw = false;
        private boolean isTransferFirstLine = false;

        // 首次转单信息
        private int firstTransferPost = 0;
        private ArrayList<String> transferChain = new ArrayList<>();
        private ArrayList<String> transferPostList = new ArrayList<>();

        // 转单时间信息
        private String transferFirstLineTime = DEFAULT_TIME;
        private String firstTransferOperationTime = DEFAULT_TIME;
        private String firstTransferSecondLineTime = DEFAULT_TIME;
        private String firstTransferComplaintQueueTime = DEFAULT_TIME;
        private String firstTransferProductionResearchTime = DEFAULT_TIME;


        // 队列信息
        private int firstSecondLineAssign = -1;
        private int transferFirstLineQueue = -1;
        private int lastOperationFactAssign = -1;
        private int firstSecondLineFactAssign = -1;
        private int currentSecondLineFactAssign = -1;
        private int lastProductionResearchFactAssign = -1;

        // 热线转单相关
        private int hotlineFirstTransferDealDuration = 0;
        private int hotlineFirstTransferResponseDuration = 0;
        private String hotlineFirstTransferTime = DEFAULT_TIME;
        private String hotlineFirstTransferResponseTime = DEFAULT_TIME;

        // 其他统计
        private String measures = "";
        private String transferType = "";
    }

    /**
     * 操作人跟踪器
     * 负责跟踪各种操作人信息
     */
    private static class OperatorTracker {

        // 处理人信息
        private String operationHandler = "";
        private String lastCustomerOperator = "";
        private String currentFirstLineOperator = "";
        private String currentSecondLineOperator = "";
        private String firstLineLastOperator = "";
        private String lastOfcOperator = "";
        private String productionResearchHandler = "";

        // 转单操作人
        private String transferFirstLineStaff = "";
        private String firstTransferOperationStaff = "";
        private String firstTransferSecondLineStaff = "";

        // 公司ID
        private int operationHandlerCompanyId = -1;
        private int currentSecondLineOperatorCompanyId = -1;
        private int firstSecondLineOperatorCompanyId = -1;
        private int firstLineLastOperatorCompanyId = -1;

        // 岗位信息
        private int firstTransferOperationStaffPost = -1;
        private int firstTransferProductionResearchStaffPost = -1;
        private int firstTransferVertProductionResearchStaffPost = -1;
        private int lastOfcOperatorPost = -1;
        private int firstLineLastOperatorFactAssign = -1;

        // 1.5线相关操作人
        private String firstSecondLineOperator = "";
        private String firstSecondLineFactOperator = "";
        private String secondLineLastCompanyOperator = "";
        private String secondLineLastCompanyOperatorGroupName = "";
        private int secondLineLastCompanyOperatorCompanyId = -1;

        // 1.5线集合
        private Set<String> secondLineOperators = new HashSet<>();
        private Map<String, Integer> secondLineOperatorCompanyIds = new HashMap<>();

        // 时间跟踪
        private String firstSecondLineServiceTime = DEFAULT_TIME;
        private int firstSecondLineResponseDuration = 0;
        private int secondLineSolvedDuration = 0;
        private int lastSecondLineOperatorDuration = 0;

        // 转供应商相关
        private boolean isTransferCompanyInSecondLine = false;
        private boolean isTransferInnerStaffInSecondLine = false;

        // 辅助变量
        private TreeMap<String, Map<String, Integer>> operatorTreeMap = new TreeMap<>();
    }
}