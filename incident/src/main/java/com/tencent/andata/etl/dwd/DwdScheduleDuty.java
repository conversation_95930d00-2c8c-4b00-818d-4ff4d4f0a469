package com.tencent.andata.etl.dwd;


import static com.tencent.andata.etl.sql.DwdScheduleDutySql.GET_CUSTOMER_SERVICE_SCHEDULE;
import static com.tencent.andata.etl.sql.DwdScheduleDutySql.GET_COMPANY_SCHEDULE;
import static com.tencent.andata.etl.tablemap.DwdScheduleDutyMapping.icebergTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdScheduleDutyMapping.mysqlTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdScheduleDutyMapping.pgsqlTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdScheduleDutyMapping.starRocksTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class DwdScheduleDuty {

    private static final Logger logger = LoggerFactory.getLogger(DwdScheduleDuty.class);
    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        // 出库starrocks表注册
        DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(mysqlTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, MYSQL, tEnv);

        // pgsql table mapping to flink table
        ArrayNode pgsqlTable2FlinkTableMap = mapper.readValue(pgsqlTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(pgsqlDBConf, pgsqlTable2FlinkTableMap, PGSQL, tEnv);

        // starrocks table mapping to flink table
        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(starRocksTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(rocksDbConf, starRocksTable2FlinkTableMap, ROCKS, tEnv);

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );

        Map<String, String> tblMap = getStringMap();

        StatementSet stmtSet = flinkEnv.stmtSet();

        for (Entry<String, String> entry : tblMap.entrySet()) {
            stmtSet.addInsertSql(insertIntoSql(
                    entry.getKey(),
                    entry.getValue(),
                    tEnv.from(entry.getValue()),
                    ICEBERG
            ));
        }

        // join 后得到的客服排班信息表
        Table tbl = tEnv.sqlQuery(GET_CUSTOMER_SERVICE_SCHEDULE);

        // join 后得到的排班信息表
        Table companyTb = tEnv.sqlQuery(GET_COMPANY_SCHEDULE);

        tEnv.createTemporaryView("customer_service_schedule_view", tbl);
        tEnv.createTemporaryView("company_schedule_view", companyTb);

        stmtSet.addInsertSql(insertIntoSql(
                        "customer_service_schedule_view",
                        "iceberg_sink_customer_service_schedule", // 客服排班信息表写入iceberg
                        tbl,
                        ICEBERG
                ))
                .addInsertSql(insertIntoSql(
                        "customer_service_schedule_view",
                        "pgsql_sink_customer_service_schedule", // 客服排班信息表写入postgresql
                        tEnv.from("pgsql_sink_customer_service_schedule"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "company_schedule_view",
                        "iceberg_sink_company_service_schedule",
                        companyTb,
                        ICEBERG
                ))
                .addInsertSql(insertIntoSql(
                        "company_schedule_view",
                        "pgsql_sink_company_service_schedule",
                        tEnv.from("pgsql_sink_company_service_schedule"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "customer_service_schedule_view",
                        "flink_starrocks_dwm_customer_service_schedule",
                        tEnv.from("flink_starrocks_dwm_customer_service_schedule"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "company_schedule_view",
                        "flink_starrocks_dwm_company_service_schedule",
                        tEnv.from("flink_starrocks_dwm_company_service_schedule"),
                        ROCKS
                ));
    }

    @NotNull
    private static Map<String, String> getStringMap() {
        Map<String, String> tblMap = new HashMap<>();
        tblMap.put("mysql_source_t103_schedule", "iceberg_sink_dwd_customer_service_schedule");
        tblMap.put("mysql_source_t101_work_shift", "iceberg_sink_dwd_customer_service_work_shift");
        tblMap.put("mysql_source_t104_schedule_duty_map", "iceberg_sink_dwd_customer_service_schedule_duty_map");

        tblMap.put("mysql_source_t907_company_schedule", "iceberg_sink_company_schedule");
        tblMap.put("mysql_source_t906_company_work", "iceberg_sink_company_work");
        tblMap.put("mysql_source_t908_company_schedule_duties", "iceberg_sink_company_schedule_duties");
        return tblMap;
    }
}
