package com.tencent.andata.etl.dwd;


import static com.tencent.andata.etl.sql.DwdTIncidentTicketSql.FULL_JOIN_TICKET_SQL;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketMapping.icebergTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketMapping.mysqlTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketMapping.pgsqlTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketMapping.starRocksTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class DwdIncidentTicket {

    private static final Logger logger = LoggerFactory.getLogger(DwdIncidentTicket.class);
    private final String icebergDbName;
    private final String pgDbName;

    @NotNull
    private static Map<String, String> getStringMap() {
        Map<String, String> tblMap = new HashMap<>();
        tblMap.put("mysql_source_t102_duty", "iceberg_sink_dim_incident_duty");
        tblMap.put("mysql_source_t201_ticket", "iceberg_sink_dwd_incident_ticket");
        tblMap.put("mysql_source_t201_ticket_extra", "iceberg_sink_dwd_incident_ticket_extra");
        tblMap.put("mysql_source_t202_ticket_operation", "iceberg_sink_dwd_incident_ticket_operation");
        tblMap.put("mysql_source_t201_ticket_tce_insales", "iceberg_sink_dwd_incident_ticket_tce_insales");
        tblMap.put("mysql_source_t208_ticket_custom_field", "iceberg_sink_dwd_incident_ticket_custom_field");
        tblMap.put("mysql_source_t107_company_duty_config", "iceberg_sink_dim_incident_company_duty_config");
        tblMap.put("mysql_source_t204_ticket_operation_extra", "iceberg_sink_dwd_incident_ticket_operation_extra");
        return tblMap;
    }

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        final KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        final DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        final DatabaseConf s360Conf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", "s360"))
                .build();

        final DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        final ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(mysqlTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, MYSQL, tEnv);

        // pgsql table mapping to flink table
        ArrayNode pgsqlTable2FlinkTableMap = mapper.readValue(pgsqlTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(pgsqlDBConf, pgsqlTable2FlinkTableMap, PGSQL, tEnv);

        // starrocks table mapping to flink table
        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(starRocksTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(rocksDbConf, starRocksTable2FlinkTableMap, ROCKS, tEnv);

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );
        // 注册s360pg表
        tEnv.executeSql(getSinkDwmS360TicketStatisticDDL(s360Conf));

        Map<String, String> tblMap = getStringMap();

        StatementSet stmtSet = flinkEnv.stmtSet();
        for (Entry<String, String> entry : tblMap.entrySet()) {
            stmtSet.addInsertSql(insertIntoSql(
                    entry.getKey(),
                    entry.getValue(),
                    tEnv.from(entry.getValue()),
                    ICEBERG
            ));
        }

        // full join 后得到的事件单主宽表
        Table tbl = tEnv.sqlQuery(FULL_JOIN_TICKET_SQL);

        tEnv.createTemporaryView("incident_ticket_base_info_view", tbl);


        stmtSet.addInsertSql(insertIntoSql(
                        "incident_ticket_base_info_view",
                        "iceberg_sink_dwd_incident_ticket_base_info", // 事件基础信息宽表写入iceberg
                        tbl,
                        ICEBERG
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t102_duty",
                        "pgsql_sink_dim_incident_duty", // 队列/职责维表写入postgresql
                        tEnv.from("pgsql_sink_dim_incident_duty"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t107_company_duty_config",
                        "pgsql_sink_dim_incident_company_duty_config", // 供应商队列配置维表写入postgresql
                        tEnv.from("pgsql_sink_dim_incident_company_duty_config"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t201_ticket",
                        "flink_starrocks_dwd_incident_ticket", // 工单明细数据写入starrocks
                        tEnv.from("flink_starrocks_dwd_incident_ticket"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t202_ticket_operation",
                        "flink_starrocks_dwd_incident_ticket_operation", // 工单流水数据写入starrocks
                        tEnv.from("flink_starrocks_dwd_incident_ticket_operation"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t204_ticket_operation_extra",
                        "flink_starrocks_dwd_incident_ticket_operation_extra", // 工单附加数据写入starrocks
                        tEnv.from("flink_starrocks_dwd_incident_ticket_operation_extra"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t208_ticket_custom_field",
                        "flink_starrocks_dwd_incident_ticket_custom_field", // 工单自定义字段数据写入starrocks
                        tEnv.from("flink_starrocks_dwd_incident_ticket_custom_field"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "mysql_source_t102_duty",
                        "flink_starrocks_dim_incident_duty", // 队列/职责维表写入starrocks
                        tEnv.from("flink_starrocks_dim_incident_duty"),
                        ROCKS
                ))
                .addInsertSql("INSERT INTO pg_dwm_s360_ticket_statistic\n"
                        + "SELECT\n"
                        + "  CAST(ticket_id AS BIGINT) AS ticket_id,\n"
                        + "  CAST(ticket_id AS BIGINT) AS value_of_primary_key,\n"
                        + "  CAST(CASE WHEN "
                        + "     service_scene_checked > 0 THEN service_scene_checked ELSE service_scene"
                        + "  END AS BIGINT) AS service_scene_checked \n"
                        + "FROM mysql_source_t201_ticket");
    }

    @NotNull
    private static String getSinkDwmS360TicketStatisticDDL(DatabaseConf pgDBConf) {
        String url = String.format(
                "***************************************************************************"
                        + "&serverTimezone=Asia/Shanghai", pgDBConf.dbHost, pgDBConf.dbPort, pgDBConf.dbName);
        return
                "CREATE TABLE pg_dwm_s360_ticket_statistic (\n"
                        + "  `ticket_id` BIGINT,\n"
                        + "  `value_of_primary_key` BIGINT,\n"
                        + "  `service_scene_checked` BIGINT,\n"
                        + "  PRIMARY KEY (ticket_id) NOT ENFORCED\n"
                        + ") WITH (\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'url' = '" + url + "',\n"
                        + "  'username' = '" + pgDBConf.userName + "',\n"
                        + "  'password' = '" + pgDBConf.password + "',\n"
                        + "  'table-name' = 'dwm_s360_ticket_statistic'\n"
                        + ")";
    }
}