package com.tencent.andata.etl.calculator.calculators.transfer;

import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;

/**
 * 转单子计算器接口
 * <p>
 * 定义了转单相关子计算器的通用契约，每个子计算器负责特定的转单指标计算。
 * 采用组合模式，将复杂的转单计算逻辑分解为多个独立的子计算器。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ITransferSubCalculator {

    /**
     * 初始化子计算器
     * 在处理工单操作流水之前调用，用于初始化计算器内部状态
     *
     * @param context 计算上下文，包含工单的全部信息
     */
    void initialize(CalculationContext context);

    /**
     * 处理单个操作记录
     * 遍历操作流水时，为每一条操作记录调用此方法
     *
     * @param context    计算上下文
     * @param currentOp  当前正在处理的操作记录
     * @param previousOp 前一条操作记录，如果当前是第一条，则为 null
     * @param nextOp     后一条操作记录，如果当前是最后一条，则为 null
     */
    void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp);

    /**
     * 执行最终计算
     * 在遍历完所有操作流水后调用，用于执行任何最终的计算或数据整理
     *
     * @param context 计算上下文
     */
    void calculate(CalculationContext context);

    /**
     * 填充指标结果
     * 将计算结果填充到指标对象中
     *
     * @param metrics 指标对象
     * @param context 计算上下文
     */
    void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context);

    /**
     * 获取子计算器名称
     * 用于日志记录和调试
     *
     * @return 子计算器名称
     */
    default String getName() {
        return this.getClass().getSimpleName();
    }
}
