package com.tencent.andata.etl.calculators;

import static com.tencent.andata.etl.entity.Operation.isValidContent;
import static com.tencent.andata.etl.enums.OperationType.PULL;
import static com.tencent.andata.etl.enums.OperationType.TRANSFER;
import static com.tencent.andata.etl.enums.OperatorPost.FIRST_LINE;
import static com.tencent.andata.etl.enums.OperatorPost.SECOND_LINE;
import static com.tencent.andata.etl.enums.OperatorType.CUSTOMER_SERVICE;
import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;

import com.tencent.andata.etl.entity.Operation;
import io.vavr.collection.List;
import io.vavr.control.Option;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

public class TransferMetricsCalculator {

    private static final LocalDateTime DEFAULT_TIME = LocalDateTime.parse("1970-01-01T00:00:00");

    public static StatAccumulator calculateStatistics(java.util.List<Operation> operations) {
        StatAccumulator initAcc = StatAccumulator.builder()
                .isTransferFirstLine(0)
                .transferFirstLineTime(DEFAULT_TIME)
                .transferFirstLineTimes(0)
                .transferOperationTimes(0)
                .transferOperationTimes(0)
                .transferFirstLineQueue(0)
                .transferFirstLineStaff("")
                .transferSecondLineTimes(0)
                .transferProductionResearchTimes(0)
                .firstTransferOperationStaffPost(0)
                .firstTransferOperationStaff("")
                .transferVerticalProductionResearchTimes(0)
                .firstTransferProductionResearchStaffPost(0)
                .firstTransferVertProductionResearchStaffPost(0)
                .firstTransferOperationTime(DEFAULT_TIME)
                .firstTransferSecondLineStaff("")
                .firstTransferComplaintQueueTime(DEFAULT_TIME)
                .firstTransferProductionResearchTime(DEFAULT_TIME)
                .isTransferCompanyInSecondLine(0)
                .isTransferInnerStaffInSecondLine(0)
                .isTransferHotlineFirstLine(0)
                .hotlineFirstTransferDealDuration(0)
                .hotlineFirstTransferLineTime(DEFAULT_TIME)
                .hotlineFirstTransferResponseTime(DEFAULT_TIME)
                .hotlineFirstTransferResponseDuration(0)
                .build();

        // 计算转单相关的指标
        StatAccumulator finalAcc = List.ofAll(operations).foldLeft(initAcc, (acc, op) ->
                Match(op).of(
                        // 匹配targetPost
                        Case($(o -> o.getTargetPost() == FIRST_LINE.getCode()), // 一线
                                o -> getTransferOperation(o)
                                        .peek(x -> acc.setTransferOperationTimes(acc.transferOperationTimes + 1))
                                        .peek(x -> acc.setFirstTransferOperationStaffPost(o.getOperatorPost()))
                                        .peek(x -> acc.setFirstTransferOperationStaff(o.getOperatorName()))
                                        .peek(x -> acc.setFirstTransferOperationTime(o.getOperateTime()))
                                        .map(x -> acc)
                                        .get()
                        ),

                        // 二线
                        Case($(o -> o.getTargetPost() == SECOND_LINE.getCode()),
                                o -> {
                                    int transferSecondLineTimes = getTransferOperation(o).map(x -> 1).getOrElse(0);
                                    acc.setFirstTransferSecondLineStaff(getFirstTransferSecondLineOperatorName(op, acc));
                                    acc.setTransferSecondLineTimes(acc.transferSecondLineTimes + transferSecondLineTimes);
                                    return acc;
                                }
                        )
                ));

        return finalAcc;
    }

    // 匹配首次转一线操作
    private static String getFirstTransferSecondLineOperatorName(Operation op, StatAccumulator acc) {
        return Option.of(op)
                .filter(x -> x == getTransferOperation(op) || x == getPullOperation(op))
                .filter(x -> StringUtils.isEmpty(acc.firstTransferSecondLineStaff))
                .filter(x -> isValidContent(x.getOperatorName(), "SYSTEM"))
                .filter(x -> x.getOperatorType() == CUSTOMER_SERVICE.getType())
                .map(Operation::getOperatorName)
                .getOrElse("");

    }

    // 匹配转单操作
    private static Option<Operation> getTransferOperation(Operation op) {
        return Option.of(op)
                .filter(x -> x.getOperationType() == TRANSFER.getCode());
    }

    private static Option<Operation> getPullOperation(Operation op) {
        return Option.of(op)
                .filter(x -> x.getOperationType() == PULL.getCode());
    }

    @Data
    @Builder
    @ToString
    public static class StatAccumulator {

        private int isTransferFirstLine;
        private LocalDateTime transferFirstLineTime;
        private int transferFirstLineTimes;
        private int transferOperationTimes;
        private int transferFirstLineQueue;
        private String transferFirstLineStaff;
        private int transferSecondLineTimes;
        private LocalDateTime firstTransferSecondLineTime;
        private int transferProductionResearchTimes;
        private int firstTransferOperationStaffPost;
        private String firstTransferOperationStaff;
        private int transferVerticalProductionResearchTimes;
        private int firstTransferProductionResearchStaffPost;
        private int firstTransferVertProductionResearchStaffPost;
        private LocalDateTime firstTransferOperationTime;
        private String firstTransferSecondLineStaff;
        private LocalDateTime firstTransferComplaintQueueTime;
        private LocalDateTime firstTransferProductionResearchTime;
        private int isTransferCompanyInSecondLine;
        private int isTransferInnerStaffInSecondLine;
        private int isTransferHotlineFirstLine;
        private int hotlineFirstTransferDealDuration;
        private LocalDateTime hotlineFirstTransferLineTime;
        private LocalDateTime hotlineFirstTransferResponseTime;
        private int hotlineFirstTransferResponseDuration;
    }
}