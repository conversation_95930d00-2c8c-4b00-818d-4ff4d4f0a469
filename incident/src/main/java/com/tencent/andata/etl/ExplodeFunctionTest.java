package com.tencent.andata.etl;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonToRowDataConverters;
import org.apache.flink.formats.json.JsonToRowDataConverters.JsonToRowDataConverter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude.Include;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.catalog.ResolvedSchema;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.conversion.RowRowConverter;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;

public class ExplodeFunctionTest {

    /**
     * 测试数组展开udtf函数
     *
     * @param args 参数列表
     * @throws Exception 异常
     */
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().enableObjectReuse();

        EnvironmentSettings settings = EnvironmentSettings
                .newInstance()
                .inStreamingMode()
                .build();

        StreamTableEnvironment tEnv = StreamTableEnvironment.create(env, settings);

        String str = "{\"id\": 1, \"name\": \"aliya\", \"operate_time\": \"2023-01-20T06:50:56.083Z\"}";

        tEnv.executeSql(
                "create table test_tbl (id int, name string, operate_time timestamp) with ('connector' = 'datagen')");

        Table tbl = tEnv.sqlQuery("select * from test_tbl");

        tbl.printSchema();

        final ResolvedSchema sourceSchema = tbl.getResolvedSchema();
        String[] fieldNames = sourceSchema.getColumnNames().toArray(new String[0]);
        TypeInformation<?>[] fieldTypeInfos = sourceSchema.getColumnDataTypes()
                .stream()
                .map(x -> InternalTypeInfo.of(x.getLogicalType()))
                .toArray(TypeInformation[]::new);

        LogicalType[] logicalTypes = sourceSchema.getColumnDataTypes()
                .stream()
                .map(DataType::getLogicalType)
                .toArray(LogicalType[]::new);

        DataType dataType = InternalTypeInfo.ofFields(logicalTypes, fieldNames).getDataType();

        // 创建JsonNode到RowData的转换器
        JsonToRowDataConverter runtimeConverter =
                new JsonToRowDataConverters(false, true, TimestampFormat.SQL)
                        .createRowConverter(RowType.of(logicalTypes, fieldNames));

        // 创建RowData到Row的转换器
        RowRowConverter rowRowConverter = RowRowConverter.create(dataType);

        ObjectMapper mapper = new ObjectMapper().setSerializationInclusion(Include.ALWAYS);

        JsonNode message = mapper.readValue(str, JsonNode.class);

        RowData rowData = (RowData) runtimeConverter.convert(message);

        System.out.println(rowRowConverter.toExternal(rowData));
        /*
        *

        Table t =
                tEnv.fromDataStream(
                        env.fromCollection(
                                Lists.newArrayList(
                                        Row.of(1, 2, 3),
                                        Row.of(2, 3, 3),
                                        Row.of(null, 3, 31241234))),
                        $("id1"),
                        $("id2"),
                        $("id"),
                        $("proctime").proctime());

        tEnv.createTemporaryView("t", t);
        tEnv.executeSql("select id1, id2, ARRAY[CAST(id1 AS BIGINT), CAST(id2 AS BIGINT)] as ids from t").print();
        tEnv.createFunction("explode", ExplodeFunction.class);
        tEnv.executeSql(
                        "select id1,id2, exploded_id from t, lateral table(explode(ARRAY[CAST(id1 AS BIGINT), CAST(id2"
                                + " AS BIGINT)])) AS exploded(exploded_id)")
                .print();
         */

        env.execute("test");
    }
}
