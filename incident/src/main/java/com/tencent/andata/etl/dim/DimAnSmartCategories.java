package com.tencent.andata.etl.dim;


import static com.tencent.andata.etl.sql.DimAnSmartCategoriesSql.querySql;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

@Builder
public class DimAnSmartCategories {


    private final String icebergDbName;
    private final String pgDbName;


    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {

        final String SOURCE_TABLE_NAME = "ansmart_categories";
        final String SINK_TABLE_NAME = "dim_ansmart_categories";
        final String SINK_PG_TABLE_NAME = "dim_ansmart_categories";

        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "ansmart"))
                .build();

        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        final ObjectMapper mapper = new ObjectMapper();
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();
        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(
                "[{\"rdbTable\":\"ansmart_categories\",\"fTable\":\"mysql_source_ansmart_categories\"}]",
                ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, MYSQL, tEnv);

        // pg table mapping to flink sink table
        ArrayNode pgTable2FlinkTableMap = mapper.readValue(
                "[{\"rdbTable\":\"dim_ansmart_categories\",\"fTable\":\"pg_sink_dim_ansmart_categories\"}]",
                ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(pgsqlDBConf, pgTable2FlinkTableMap, PGSQL, tEnv);

        // iceberg table mapping to flink sink table
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(
                        "[{\"icebergTable\":\"dim_ansmart_categories\",\"fTable\":\"iceberg_sink_dim_ansmart_categories\",\"primaryKey\":\"id\"}]",
                        ArrayNode.class), tEnv, catalog
        );

        // 创建flink临时表
        tEnv.createTemporaryView(
                "ansmart_categories_view",
                flinkEnv.streamTEnv().sqlQuery(querySql));

        StatementSet stmtSet = flinkEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                        "ansmart_categories_view",
                        "pg_sink_dim_ansmart_categories",
                        tEnv.from("pg_sink_dim_ansmart_categories"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "ansmart_categories_view",
                        "iceberg_sink_dim_ansmart_categories",
                        tEnv.from("iceberg_sink_dim_ansmart_categories"),
                        ICEBERG
                ));
    }
}