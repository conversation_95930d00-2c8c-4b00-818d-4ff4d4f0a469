package com.tencent.andata.etl.calculator.calculators;

import static com.tencent.andata.etl.entity.Operation.isValidContent;
import static com.tencent.andata.utils.DateFormatUtils.timeDelta;
import static com.tencent.andata.utils.TimeUtil.formatDateTime;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.calculator.IMetricCalculator;
import com.tencent.andata.etl.calculator.calculators.statemachine.TransferState;
import com.tencent.andata.etl.calculator.calculators.statemachine.TransferStateManager;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import com.tencent.andata.etl.enums.TicketServiceChannel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import lombok.extern.slf4j.Slf4j;

/**
 * 基于状态机的转单指标计算器（方案二：状态机优化）
 * <p>
 * 使用状态机模式替代原来的多个boolean标志，提供更清晰的状态管理。
 * 主要改进：
 * 1. 使用TransferStateManager管理状态转换
 * 2. 用枚举状态替代boolean标志
 * 3. 提供状态转换历史记录
 * 4. 简化状态相关的逻辑判断
 * </p>
 * <p>
 * 对应原 TransferCalculator 的状态机重构版本。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class TransferCalculatorStateMachine implements IMetricCalculator<CalculationContext> {

    // ============================== 常量定义 ==============================
    private static final String HOTLINE_SERVICE_CHANNEL = String.valueOf(TicketServiceChannel.CALLCENTER.getId());
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String DEFAULT_TIME = "1970-01-01 00:00:00";

    // 后端岗位常量集合
    private static final List<OperatorPost> BACKEND_POSTS = Arrays.asList(
            OperatorPost.OPERATION, // 运维
            OperatorPost.PRODUCTION_RESEARCH, // 产研
            OperatorPost.P_VERTICAL_PRODUCTION_RESEARCH // 垂直产研
    );

    // 公司ID常量集合
    private static final List<Integer> COMPANY_IDS = Arrays.asList(
            1, 2, 4, 5, 9, 23, 38, 169, 322, 163, 321, 335, 336
    );

    // TGW队列ID常量
    private static final List<Integer> TGW_QUEUE_IDS = Arrays.asList(
            6210, // 专项KA-网络TGW队列-CLB/EIP转发问题
            6209  // 专项-网络TGW队列-CLB/EIP转发问题
    );

    // 一线操作类型集合
    private static final List<OperationType> FIRST_LINE_OPERATION_TYPES = Arrays.asList(
            OperationType.RETRIEVE, OperationType.TRANSFER, OperationType.REPLY,
            OperationType.TRANSFER_RESPONSIBLE, OperationType.WAIT_CUSTOMER_ADD_INFO,
            OperationType.CLOSE_APPLICATION, OperationType.WAIT_CUSTOMER_CLOSE,
            OperationType.ADD_INFO_APPLICATION, OperationType.TO_CONFIRM_RESTORE,
            OperationType.RESET_STATUS, OperationType.INSURE_ARCHIVE
    );

    // 1.5线操作类型集合
    private static final List<OperationType> SECOND_LINE_OPERATION_TYPES = Arrays.asList(
            OperationType.RETRIEVE, OperationType.PULL, OperationType.TRANSFER,
            OperationType.REPLY, OperationType.TRANSFER_RESPONSIBLE, OperationType.WAIT_CUSTOMER_ADD_INFO,
            OperationType.CLOSE_APPLICATION, OperationType.AGREE_CLOSE_APPLICATION,
            OperationType.DISAGREE_CLOSE_APPLICATION, OperationType.WAIT_CUSTOMER_CLOSE,
            OperationType.CLOSE, OperationType.ADD_INFO_APPLICATION, OperationType.SET_DELAY_DISPATCH,
            OperationType.PULL_RESPONSIBLE, OperationType.TO_CONFIRM_RESTORE,
            OperationType.AGREE_ADD_INFO_APPLICATION, OperationType.DISAGREE_ADD_INFO_APPLICATION,
            OperationType.PULL_INCIDENT_MANAGER, OperationType.INSURE_ARCHIVE, OperationType.BUILD_GROUP_CHAT
    );

    // ============================== 核心组件 ==============================
    /**
     * 状态管理器 - 替代原来的多个boolean标志
     */
    private TransferStateManager stateManager;

    /**
     * 转单指标数据
     */
    private TransferMetrics transferMetrics;

    /**
     * 操作人跟踪数据
     */
    private OperatorTracker operatorTracker;

    // ============================== 工单基础信息 ==============================
    private String serviceChannel = "";
    private String closeTime = "";
    private String ticketSolvedTime = "";
    private boolean isCloseFlag = false;

    @Override
    public void initialize(CalculationContext context) {
        // 初始化状态管理器
        this.stateManager = new TransferStateManager();
        
        // 初始化数据结构
        this.transferMetrics = new TransferMetrics();
        this.operatorTracker = new OperatorTracker();

        // 获取基础信息
        this.closeTime = context.getMetrics().getCloseTime();
        this.ticketSolvedTime = context.getMetrics().getTicketSolvedTime();
        this.serviceChannel = Optional.of(context.getTicketProfile().getServiceChannel())
                .map(channel -> String.valueOf(channel)).orElse("");
        this.isCloseFlag = context.getTicketProfile().getOperations().stream()
                .anyMatch(op -> op.getOperationType() == OperationType.CLOSE.getCode());

        log.debug("TransferCalculatorStateMachine initialized for ticket: {}", context.getTicketProfile().getTicketId());
    }

    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        OperatorType operatorType = OperatorType.fromType(currentOp.getOperatorType());
        OperatorPost operatorPost = OperatorPost.of(currentOp.getOperatorPost());
        OperationType opType = OperationType.of(currentOp.getOperationType());
        OperatorPost targetPost = OperatorPost.of(currentOp.getTargetPost());

        final String operateTime = formatDateTime(currentOp.getOperateTime());
        final int index = context.getTicketProfile().getOperations().indexOf(currentOp);

        // 检查是否应该处理此操作
        if (!shouldProcessOperation(operateTime)) {
            return;
        }

        // 1. 处理转单链和转单类型
        processTransferChain(currentOp, operatorPost, targetPost);

        // 2. 处理各种转单逻辑（使用状态机）
        processTransferOperationsWithStateMachine(currentOp, opType, operatorType, targetPost, operateTime, previousOp);

        // 3. 处理操作人跟踪
        processOperatorTracking(currentOp, operatorType, operatorPost, operateTime, index);

        // 4. 处理特殊队列和标记
        processSpecialQueuesAndFlags(currentOp, operatorPost, operateTime, index);

        // 5. 处理热线转单逻辑（使用状态机）
        processHotlineTransferWithStateMachine(currentOp, opType, operateTime);
    }

    @Override
    public void calculate(CalculationContext context) {
        // 1. 处理1.5线操作人相关计算
        finalizeSecondLineOperators();

        // 2. 计算最后处理人信息
        calculateLastOperators(context);

        // 3. 计算各种时长
        calculateDurations();

        // 4. 生成转单类型字符串
        transferMetrics.transferType = String.join("->", transferMetrics.transferPostList);

        log.debug("TransferCalculatorStateMachine calculation completed");
    }

    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        // 转单次数相关
        metrics.setTransferToOutTimes(transferMetrics.transferToOutTimes);
        metrics.setTransferToInTimes(transferMetrics.transferToInTimes);
        metrics.setFirstTransferPost(transferMetrics.firstTransferPost);
        metrics.setTransferOperationTimes(transferMetrics.transferOperationTimes);
        metrics.setTransferFirstLineTimes(transferMetrics.transferFirstLineTimes);
        metrics.setTransferSecondLineTimes(transferMetrics.transferSecondLineTimes);
        metrics.setTransferProductionResearchTimes(transferMetrics.transferProductionResearchTimes);
        metrics.setTransferVerticalProductionResearchTimes(transferMetrics.transferVerticalProductionResearchTimes);

        // 转单状态标记
        metrics.setIsTransferFirstLine(transferMetrics.isTransferFirstLine ? 1 : 0);
        metrics.setIsTransferTgw(transferMetrics.isTransferTgw ? 1 : 0);

        // 转单链
        try {
            metrics.setTransferChain(OBJECT_MAPPER.writeValueAsString(transferMetrics.transferChain));
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize transfer chain for ticketId: {}", context.getTicketProfile().getTicketId(), e);
            metrics.setTransferChain("[]");
        }

        // 转单时间信息 - 从状态管理器获取
        stateManager.getFirstTransitionTime(TransferState.FIRST_TRANSFER_OPERATION)
                .ifPresent(time -> metrics.setFirstTransferOperationTime(time));
        stateManager.getFirstTransitionTime(TransferState.FIRST_TRANSFER_PRODUCTION_RESEARCH)
                .ifPresent(time -> metrics.setFirstTransferProductionResearchTime(time));
        stateManager.getFirstTransitionTime(TransferState.FIRST_TRANSFER_COMPLAINT_QUEUE)
                .ifPresent(time -> metrics.setFirstTransferComplaintQueueTime(time));

        metrics.setTransferFirstLineTime(transferMetrics.transferFirstLineTime);
        metrics.setFirstTransferSecondLineTime(transferMetrics.firstTransferSecondLineTime);

        // 队列信息
        metrics.setTransferFirstLineQueue(transferMetrics.transferFirstLineQueue);
        metrics.setFirstSecondLineAssign(transferMetrics.firstSecondLineAssign);
        metrics.setFirstSecondLineFactAssign(transferMetrics.firstSecondLineFactAssign);
        metrics.setCurrentSecondLineFactAssign(transferMetrics.currentSecondLineFactAssign);
        metrics.setLastOperationFactAssign(transferMetrics.lastOperationFactAssign);
        metrics.setLastProductionResearchFactAssign(transferMetrics.lastProductionResearchFactAssign);

        // 热线转单相关 - 从状态管理器获取
        stateManager.getFirstTransitionTime(TransferState.HOTLINE_FIRST_ASSIGN)
                .ifPresent(time -> metrics.setHotlineFirstTransferLineTime(time));
        stateManager.getFirstTransitionTime(TransferState.HOTLINE_FIRST_CHECK)
                .ifPresent(time -> metrics.setHotlineFirstTransferResponseTime(time));

        metrics.setHotlineFirstTransferDealDuration(transferMetrics.hotlineFirstTransferDealDuration);
        metrics.setHotlineFirstTransferResponseDuration(transferMetrics.hotlineFirstTransferResponseDuration);

        // 操作人信息
        populateOperatorMetrics(metrics, context);

        // 其他
        metrics.setMeasures(transferMetrics.measures);
        metrics.setTransferType(transferMetrics.transferType);

        log.debug("Metrics populated for ticket: {}", context.getTicketProfile().getTicketId());
    }

    // ============================== 私有方法 ==============================

    /**
     * 判断是否应该处理当前操作
     */
    private boolean shouldProcessOperation(String operateTime) {
        return !isCloseFlag || !isValidContent(closeTime, DEFAULT_TIME) || operateTime.compareTo(closeTime) <= 0;
    }

    /**
     * 处理转单链和转单类型
     */
    private void processTransferChain(Operation currentOp, OperatorPost fromPost, OperatorPost targetPost) {
        OperationType opType = OperationType.of(currentOp.getOperationType());

        if (opType == OperationType.TRANSFER) {
            // 计算转入转出次数
            boolean fromIsBackend = BACKEND_POSTS.contains(fromPost);
            boolean toIsBackend = BACKEND_POSTS.contains(targetPost);

            if (fromIsBackend && !toIsBackend) {
                transferMetrics.transferToOutTimes++;
            }

            if (!fromIsBackend && toIsBackend) {
                transferMetrics.transferToInTimes++;
            }
        }

        // 记录转单岗位
        String postStr = String.valueOf(currentOp.getPost());
        if (!transferMetrics.transferPostList.contains(postStr)) {
            transferMetrics.transferPostList.add(postStr);
        }

        // 构建转单链
        transferMetrics.transferChain.add(fromPost.getName() + "->" + targetPost.getName());
    }

    /**
     * 使用状态机处理各种转单操作
     */
    private void processTransferOperationsWithStateMachine(Operation currentOp, OperationType opType, 
                                                          OperatorType operatorType, OperatorPost targetPost, 
                                                          String operateTime, Operation previousOp) {
        // 处理转单操作
        if (opType == OperationType.TRANSFER) {
            processTransferByTargetPostWithStateMachine(currentOp, targetPost, operateTime, operatorType);
        }

        // 处理认领操作
        if (opType == OperationType.PULL) {
            processPullOperationWithStateMachine(currentOp, targetPost, operateTime, operatorType, previousOp);
        }

        // 处理派单操作
        if (opType == OperationType.DISPATCH) {
            processDispatchOperationWithStateMachine(currentOp, operatorType, targetPost, operateTime);
        }
    }

    /**
     * 使用状态机根据目标岗位处理转单
     */
    private void processTransferByTargetPostWithStateMachine(Operation currentOp, OperatorPost targetPost, 
                                                            String operateTime, OperatorType operatorType) {
        switch (targetPost) {
            case FIRST_LINE:
                handleTransferToFirstLine(currentOp, operateTime, operatorType);
                break;
            case SECOND_LINE:
                handleTransferToSecondLineWithStateMachine(currentOp, operateTime, operatorType);
                break;
            case OPERATION:
                handleTransferToOperationWithStateMachine(currentOp, operateTime);
                break;
            case PRODUCTION_RESEARCH:
                handleTransferToProductionResearchWithStateMachine(currentOp, operateTime);
                break;
            case P_VERTICAL_PRODUCTION_RESEARCH:
                handleTransferToVerticalProductionResearchWithStateMachine(currentOp);
                break;
            default:
                break;
        }
    }

    /**
     * 处理转一线
     */
    private void handleTransferToFirstLine(Operation currentOp, String operateTime, OperatorType operatorType) {
        transferMetrics.transferFirstLineTimes++;
        transferMetrics.isTransferFirstLine = true;
        transferMetrics.transferFirstLineTime = operateTime;
        transferMetrics.transferFirstLineQueue = currentOp.getFactAssign();

        if (operatorType == OperatorType.CUSTOMER_SERVICE && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
            operatorTracker.transferFirstLineStaff = currentOp.getOperatorName();
        }
    }

    /**
     * 使用状态机处理转1.5线
     */
    private void handleTransferToSecondLineWithStateMachine(Operation currentOp, String operateTime, OperatorType operatorType) {
        transferMetrics.transferSecondLineTimes++;

        if (isValidContent(transferMetrics.firstTransferSecondLineTime, DEFAULT_TIME)) {
            transferMetrics.firstTransferSecondLineTime = operateTime;
        }

        if (transferMetrics.firstSecondLineAssign < 0) {
            transferMetrics.firstSecondLineAssign = currentOp.getNextAssign();
        }

        // 使用状态机管理首次1.5线操作人状态
        if (stateManager.isFirstTime(TransferState.FIRST_SECOND_LINE_OPERATOR_1) 
            && isValidContent(currentOp.getNextOperatorName(), "SYSTEM")) {
            updateFirstSecondLineOperator(currentOp, operateTime);
            stateManager.markAsProcessed(TransferState.FIRST_SECOND_LINE_OPERATOR_1, operateTime);
        }

        // 使用状态机管理首次转1.5线员工状态
        if (stateManager.isFirstTime(TransferState.FIRST_TRANSFER_SECOND_LINE) 
            && operatorType == OperatorType.CUSTOMER_SERVICE 
            && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
            stateManager.markAsProcessed(TransferState.FIRST_TRANSFER_SECOND_LINE, operateTime);
            operatorTracker.firstTransferSecondLineStaff = currentOp.getOperatorName();
        }
    }

    /**
     * 使用状态机处理转运维
     */
    private void handleTransferToOperationWithStateMachine(Operation currentOp, String operateTime) {
        transferMetrics.transferOperationTimes++;

        // 使用状态机管理首次转运维状态
        if (stateManager.isFirstTime(TransferState.FIRST_TRANSFER_OPERATION) 
            && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
            
            Map<String, Object> additionalData = new HashMap<>();
            additionalData.put("operatorName", currentOp.getOperatorName());
            additionalData.put("post", currentOp.getPost());
            
            stateManager.markAsProcessed(TransferState.FIRST_TRANSFER_OPERATION, operateTime, additionalData);
            operatorTracker.firstTransferOperationStaffPost = currentOp.getPost();
            operatorTracker.firstTransferOperationStaff = currentOp.getOperatorName();
        }
    }

    /**
     * 使用状态机处理转产研
     */
    private void handleTransferToProductionResearchWithStateMachine(Operation currentOp, String operateTime) {
        transferMetrics.transferProductionResearchTimes++;

        // 使用状态机管理首次转产研状态
        if (stateManager.isFirstTime(TransferState.FIRST_TRANSFER_PRODUCTION_RESEARCH)) {
            Map<String, Object> additionalData = new HashMap<>();
            additionalData.put("post", currentOp.getPost());
            
            stateManager.markAsProcessed(TransferState.FIRST_TRANSFER_PRODUCTION_RESEARCH, operateTime, additionalData);
            operatorTracker.firstTransferProductionResearchStaffPost = currentOp.getPost();
        }
    }

    /**
     * 使用状态机处理转垂直产研
     */
    private void handleTransferToVerticalProductionResearchWithStateMachine(Operation currentOp) {
        transferMetrics.transferVerticalProductionResearchTimes++;

        // 使用状态机管理首次转垂直产研状态
        if (stateManager.isFirstTime(TransferState.FIRST_TRANSFER_VERTICAL_PRODUCTION_RESEARCH)) {
            Map<String, Object> additionalData = new HashMap<>();
            additionalData.put("post", currentOp.getPost());
            
            stateManager.markAsProcessed(TransferState.FIRST_TRANSFER_VERTICAL_PRODUCTION_RESEARCH, 
                                       formatDateTime(currentOp.getOperateTime()), additionalData);
            operatorTracker.firstTransferVertProductionResearchStaffPost = currentOp.getPost();
        }
    }

    /**
     * 使用状态机处理认领操作
     */
    private void processPullOperationWithStateMachine(Operation currentOp, OperatorPost targetPost, 
                                                     String operateTime, OperatorType operatorType, Operation previousOp) {
        if (targetPost == OperatorPost.SECOND_LINE) {
            // 使用状态机处理首次转1.5线员工逻辑
            if (previousOp != null && stateManager.isFirstTime(TransferState.FIRST_TRANSFER_SECOND_LINE) 
                && operatorType == OperatorType.CUSTOMER_SERVICE) {
                
                stateManager.markAsProcessed(TransferState.FIRST_TRANSFER_SECOND_LINE, operateTime);
                String preOperator = previousOp.getOperatorName();
                String preNextOperator = previousOp.getNextOperatorName();
                operatorTracker.firstTransferSecondLineStaff = "SYSTEM".equalsIgnoreCase(preOperator) ? preOperator : preNextOperator;
            }

            if (operatorTracker.firstSecondLineServiceTime.equals(DEFAULT_TIME)) {
                operatorTracker.firstSecondLineServiceTime = operateTime;
            }

            // 使用状态机处理第二种1.5线操作人状态
            if (stateManager.isFirstTime(TransferState.FIRST_SECOND_LINE_OPERATOR_2) 
                && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
                updateFirstSecondLineOperator(currentOp, operateTime);
                stateManager.markAsProcessed(TransferState.FIRST_SECOND_LINE_OPERATOR_2, operateTime);
            }
        } else if (targetPost == OperatorPost.OPERATION) {
            transferMetrics.transferOperationTimes++;
            
            // 使用状态机处理首次转运维状态
            if (stateManager.isFirstTime(TransferState.FIRST_TRANSFER_OPERATION)) {
                stateManager.markAsProcessed(TransferState.FIRST_TRANSFER_OPERATION, operateTime);
            }
        }
    }

    /**
     * 使用状态机处理派单操作
     */
    private void processDispatchOperationWithStateMachine(Operation currentOp, OperatorType operatorType, 
                                                         OperatorPost targetPost, String operateTime) {
        if (operatorType == OperatorType.SYSTEM && targetPost == OperatorPost.SECOND_LINE) {
            // 使用状态机处理复杂的1.5线操作人状态逻辑
            if (!stateManager.isFirstTime(TransferState.FIRST_SECOND_LINE_OPERATOR_3) 
                && stateManager.isFirstTime(TransferState.FIRST_SECOND_LINE_OPERATOR_1)) {
                updateFirstSecondLineOperator(currentOp, operateTime);
                stateManager.markAsProcessed(TransferState.FIRST_SECOND_LINE_OPERATOR_1, operateTime);
            }
        }

        // 808 -- 专项-需求队列
        if (currentOp.getFactAssign() == 808 && transferMetrics.firstSecondLineAssign < 0) {
            transferMetrics.firstSecondLineAssign = currentOp.getFactAssign();
        }
    }

    /**
     * 使用状态机处理热线转单逻辑
     */
    private void processHotlineTransferWithStateMachine(Operation currentOp, OperationType opType, String operateTime) {
        if (!HOTLINE_SERVICE_CHANNEL.equals(serviceChannel)) {
            return;
        }

        // 使用状态机计算热线转单相关时长
        if (stateManager.isFirstTime(TransferState.HOTLINE_FIRST_ASSIGN) 
            && (opType == OperationType.TRANSFER || opType == OperationType.DISPATCH)) {
            
            stateManager.markAsProcessed(TransferState.HOTLINE_FIRST_ASSIGN, operateTime);
            transferMetrics.hotlineFirstTransferTime = operateTime;
            
        } else if (stateManager.isFirstTime(TransferState.HOTLINE_FIRST_CHECK) 
                   && !stateManager.isFirstTime(TransferState.HOTLINE_FIRST_ASSIGN)
                   && opType == OperationType.RETRIEVE) {
            
            stateManager.markAsProcessed(TransferState.HOTLINE_FIRST_CHECK, operateTime);
            transferMetrics.hotlineFirstTransferResponseTime = operateTime;
        }
    }

    // 其他方法保持不变，这里省略以控制文件长度...
    // 包括：processOperatorTracking, processSpecialQueuesAndFlags, updateFirstSecondLineOperator,
    // finalizeSecondLineOperators, calculateLastOperators, calculateDurations, populateOperatorMetrics 等

    /**
     * 更新首次1.5线操作人信息
     */
    private void updateFirstSecondLineOperator(Operation currentOp, String operateTime) {
        operatorTracker.operatorTreeMap.put(operateTime, new HashMap<String, Integer>() {{
            put(currentOp.getNextOperatorName(), currentOp.getNextOperatorCompanyId());
        }});

        // 使用状态机标记第三种1.5线操作人状态
        if (stateManager.isFirstTime(TransferState.FIRST_SECOND_LINE_OPERATOR_3)) {
            stateManager.markAsProcessed(TransferState.FIRST_SECOND_LINE_OPERATOR_3, operateTime);
        }
    }

    // 为了控制文件长度，其他方法将在后续添加...
    // 这里先提供核心的状态机逻辑实现

    /**
     * 处理操作人跟踪（简化版本）
     */
    private void processOperatorTracking(Operation currentOp, OperatorType operatorType, 
                                       OperatorPost operatorPost, String operateTime, int index) {
        // 基本的操作人跟踪逻辑
        if (currentOp.getPost() == OperatorPost.SECOND_LINE.getCode()) {
            transferMetrics.currentSecondLineFactAssign = currentOp.getFactAssign();
        } else if (operatorPost == OperatorPost.FIRST_LINE && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
            operatorTracker.currentFirstLineOperator = currentOp.getOperatorName();
        }

        // 跟踪运维处理人
        if (currentOp.getPost() == OperatorPost.OPERATION.getCode() && operatorPost == OperatorPost.OPERATION) {
            operatorTracker.operationHandler = currentOp.getOperatorName();
            operatorTracker.operationHandlerCompanyId = currentOp.getOperatorCompanyId();
        }

        // 跟踪产研处理人
        if (currentOp.getPost() == OperatorPost.PRODUCTION_RESEARCH.getCode() && operatorPost == OperatorPost.PRODUCTION_RESEARCH
                && operateTime.compareTo(ticketSolvedTime) <= 0 && !ticketSolvedTime.equals(DEFAULT_TIME)) {
            operatorTracker.productionResearchHandler = currentOp.getOperatorName();
        }

        // 跟踪客服处理人
        if (operatorType == OperatorType.CUSTOMER_SERVICE) {
            updateCustomerServiceOperators(currentOp, operateTime);
        }
    }

    /**
     * 更新客服操作人信息
     */
    private void updateCustomerServiceOperators(Operation currentOp, String operateTime) {
        boolean passesFilter = currentOp.getOperatorPost() > -1
                && isValidContent(currentOp.getOperatorName(), "SYSTEM")
                && (!isCloseFlag || (isValidContent(closeTime, DEFAULT_TIME) && operateTime.compareTo(closeTime) <= 0));

        if (passesFilter) {
            operatorTracker.lastCustomerOperator = currentOp.getOperatorName();

            // 跟踪1.5线操作人
            if (currentOp.getOperatorPost() == OperatorPost.SECOND_LINE.getCode()) {
                operatorTracker.secondLineOperators.add(currentOp.getOperatorName());
                operatorTracker.secondLineOperatorCompanyIds.put(currentOp.getOperatorName(), currentOp.getOperatorCompanyId());

                // 跟踪供应商最终处理人
                updateSecondLineCompanyOperator(currentOp);
            }
        }
    }

    /**
     * 更新1.5线供应商处理人
     */
    private void updateSecondLineCompanyOperator(Operation currentOp) {
        int[] validOperationTypes = {5, 40, 13, 12, 28, 30, 43, 33, 3, 48, 50, 2, 16, 8, 35, 15, 11, 14, 41, 10};
        boolean isValidOperation = Arrays.stream(validOperationTypes).anyMatch(x -> x == currentOp.getOperationType());

        if (currentOp.getPost() == OperatorPost.SECOND_LINE.getCode()
                && COMPANY_IDS.contains(currentOp.getOperatorCompanyId())
                && isValidOperation) {
            operatorTracker.secondLineLastCompanyOperator = currentOp.getOperatorName();
            operatorTracker.secondLineLastCompanyOperatorCompanyId = currentOp.getOperatorCompanyId();
            operatorTracker.secondLineLastCompanyOperatorGroupName = currentOp.getOperatorGroupName();
        }
    }

    /**
     * 处理特殊队列和标记
     */
    private void processSpecialQueuesAndFlags(Operation currentOp, OperatorPost operatorPost, String operateTime, int index) {
        // 检查TGW队列
        if (TGW_QUEUE_IDS.contains(currentOp.getFactAssign())) {
            transferMetrics.isTransferTgw = true;
        }

        // 使用状态机处理首次转投诉队列时间
        if (currentOp.getFactAssign() == 4220 
            && stateManager.isFirstTime(TransferState.FIRST_TRANSFER_COMPLAINT_QUEUE) 
            && index > 1) {
            stateManager.markAsProcessed(TransferState.FIRST_TRANSFER_COMPLAINT_QUEUE, operateTime);
        }

        // 处理结果提取
        if (OperatorType.fromType(currentOp.getOperatorType()) == OperatorType.CUSTOMER_SERVICE
                && isValidContent(currentOp.getInnerReply(), "NULL")) {
            if (currentOp.getFactAssign() == 4220 || "38".equals(serviceChannel)) {
                transferMetrics.measures = currentOp.getInnerReply();
            }
        }

        // 更新队列信息
        if (operatorPost == OperatorPost.OPERATION && currentOp.getOperationType() != OperationType.CLOSE.getCode()) {
            transferMetrics.lastOperationFactAssign = currentOp.getFactAssign();
        } else if ((operatorPost == OperatorPost.PRODUCTION_RESEARCH || operatorPost == OperatorPost.UNKNOWN)
                && currentOp.getOperationType() != OperationType.CLOSE.getCode()) {
            transferMetrics.lastProductionResearchFactAssign = currentOp.getFactAssign();
        }
    }

    /**
     * 完成1.5线操作人相关计算
     */
    private void finalizeSecondLineOperators() {
        // 处理1.5线操作人公司转换逻辑
        if (!operatorTracker.secondLineOperatorCompanyIds.isEmpty()) {
            // 检查是否转内部员工
            if (operatorTracker.secondLineOperatorCompanyIds.values().stream()
                    .anyMatch(id -> id == 0 || id == 217)) {
                operatorTracker.isTransferInnerStaffInSecondLine = true;
            }

            // 检查是否转多个供应商
            long distinctCompanyCount = operatorTracker.secondLineOperatorCompanyIds.values().stream()
                    .map(id -> id == 217 ? 0 : id)
                    .filter(id -> id != 0)
                    .distinct()
                    .count();

            if (distinctCompanyCount > 1) {
                operatorTracker.isTransferCompanyInSecondLine = true;
            }
        }

        // 设置首次1.5线操作人
        if (!operatorTracker.operatorTreeMap.isEmpty()) {
            String firstKey = operatorTracker.operatorTreeMap.firstKey();
            Map<String, Integer> firstEntry = operatorTracker.operatorTreeMap.get(firstKey);

            operatorTracker.firstSecondLineOperator = firstEntry.keySet().stream()
                    .findFirst().orElse("");
            operatorTracker.firstSecondLineOperatorCompanyId = firstEntry.values().stream()
                    .findFirst().orElse(-1);
        }
    }

    /**
     * 计算最后处理人信息
     */
    private void calculateLastOperators(CalculationContext context) {
        List<Operation> operations = context.getTicketProfile().getOperations();

        // 计算一线最后处理人
        operations.stream()
                .filter(op -> FIRST_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType()))
                        && op.getOperatorPost() == OperatorPost.FIRST_LINE.getCode()
                        && OperatorType.fromType(op.getOperatorType()) == OperatorType.CUSTOMER_SERVICE)
                .filter(op -> shouldProcessOperation(formatDateTime(op.getOperateTime())))
                .max(Operation::compareTo)
                .ifPresent(lastOp -> {
                    operatorTracker.firstLineLastOperator = lastOp.getOperatorName();
                    operatorTracker.firstLineLastOperatorCompanyId = lastOp.getOperatorCompanyId();
                    operatorTracker.firstLineLastOperatorFactAssign = lastOp.getFactAssign();
                });

        // 计算1.5线当前处理人
        operations.stream()
                .filter(op -> SECOND_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType()))
                        && op.getOperatorPost() == OperatorPost.SECOND_LINE.getCode()
                        && OperatorType.fromType(op.getOperatorType()) == OperatorType.CUSTOMER_SERVICE)
                .filter(op -> shouldProcessOperation(formatDateTime(op.getOperateTime())))
                .max(Operation::compareTo)
                .ifPresent(lastOp -> {
                    operatorTracker.currentSecondLineOperator = lastOp.getOperatorName();
                    operatorTracker.currentSecondLineOperatorCompanyId = lastOp.getOperatorCompanyId();
                });

        // 计算最后处理人
        operations.stream()
                .filter(op -> (op.getOperatorPost() == OperatorPost.FIRST_LINE.getCode() && FIRST_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType())))
                        || (op.getOperatorPost() == OperatorPost.SECOND_LINE.getCode() && SECOND_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType()))))
                .filter(op -> shouldProcessOperation(formatDateTime(op.getOperateTime())))
                .max(Operation::compareTo)
                .ifPresent(lastOp -> {
                    operatorTracker.lastOfcOperator = lastOp.getOperatorName();
                    operatorTracker.lastOfcOperatorPost = lastOp.getOperatorPost();
                });
    }

    /**
     * 计算各种时长
     */
    private void calculateDurations() {
        // 计算1.5线解决时长
        if (!ticketSolvedTime.equals(DEFAULT_TIME) && isValidContent(operatorTracker.firstSecondLineServiceTime, DEFAULT_TIME)) {
            try {
                operatorTracker.secondLineSolvedDuration = (int) timeDelta(operatorTracker.firstSecondLineServiceTime, ticketSolvedTime);
            } catch (Exception e) {
                log.error("Failed to calculate second line solved duration", e);
            }
        }

        // 计算热线转单处理时长
        if (!transferMetrics.hotlineFirstTransferTime.equals(DEFAULT_TIME) && !closeTime.equals(DEFAULT_TIME)) {
            try {
                transferMetrics.hotlineFirstTransferDealDuration = (int) timeDelta(closeTime, transferMetrics.hotlineFirstTransferTime);
            } catch (Exception e) {
                log.error("Failed to calculate hotline transfer deal duration", e);
            }
        }

        // 计算热线转单响应时长
        if (!transferMetrics.hotlineFirstTransferResponseTime.equals(DEFAULT_TIME)
                && !transferMetrics.hotlineFirstTransferTime.equals(DEFAULT_TIME)) {
            try {
                transferMetrics.hotlineFirstTransferResponseDuration = (int) timeDelta(
                        transferMetrics.hotlineFirstTransferResponseTime, transferMetrics.hotlineFirstTransferTime);
            } catch (Exception e) {
                log.error("Failed to calculate hotline transfer response duration", e);
            }
        }
    }

    /**
     * 填充操作人相关指标
     */
    private void populateOperatorMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        // 操作人信息
        metrics.setOperationHandler(operatorTracker.operationHandler);
        metrics.setLastCustomerOperator(operatorTracker.lastCustomerOperator);
        metrics.setCurrentFirstLineOperator(operatorTracker.currentFirstLineOperator);
        metrics.setCurrentSecondLineOperator(operatorTracker.currentSecondLineOperator);
        metrics.setFirstLineLastOperator(operatorTracker.firstLineLastOperator);
        metrics.setLastOfcOperator(operatorTracker.lastOfcOperator);
        metrics.setProductionResearchHandler(operatorTracker.productionResearchHandler);

        metrics.setTransferFirstLineStaff(operatorTracker.transferFirstLineStaff);
        metrics.setFirstTransferOperationStaff(operatorTracker.firstTransferOperationStaff);
        metrics.setFirstTransferSecondLineStaff(operatorTracker.firstTransferSecondLineStaff);

        metrics.setOperationHandlerCompanyId(operatorTracker.operationHandlerCompanyId);
        metrics.setCurrentSecondLineOperatorCompanyId(operatorTracker.currentSecondLineOperatorCompanyId);
        metrics.setFirstSecondLineOperatorCompanyId(operatorTracker.firstSecondLineOperatorCompanyId);
        metrics.setFirstLineLastOperatorCompanyId(operatorTracker.firstLineLastOperatorCompanyId);

        metrics.setFirstTransferOperationStaffPost(operatorTracker.firstTransferOperationStaffPost);
        metrics.setFirstTransferProductionResearchStaffPost(operatorTracker.firstTransferProductionResearchStaffPost);
        metrics.setFirstTransferVertProductionResearchStaffPost(operatorTracker.firstTransferVertProductionResearchStaffPost);
        metrics.setLastOfcOperatorPost(operatorTracker.lastOfcOperatorPost);
        metrics.setFirstLineLastOperatorFactAssign(operatorTracker.firstLineLastOperatorFactAssign);

        metrics.setFirstSecondLineOperator(operatorTracker.firstSecondLineOperator);
        metrics.setFirstSecondLineFactOperator(operatorTracker.firstSecondLineFactOperator);
        metrics.setSecondLineLastCompanyOperator(operatorTracker.secondLineLastCompanyOperator);
        metrics.setSecondLineLastCompanyOperatorGroupName(operatorTracker.secondLineLastCompanyOperatorGroupName);
        metrics.setSecondLineLastCompanyOperatorCompanyId(operatorTracker.secondLineLastCompanyOperatorCompanyId);

        try {
            metrics.setSecondLineOperators(OBJECT_MAPPER.writeValueAsString(operatorTracker.secondLineOperators));
            metrics.setSecondLineOperatorCompanyIds(OBJECT_MAPPER.writeValueAsString(operatorTracker.secondLineOperatorCompanyIds));
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize second line operators for ticketId: {}", context.getTicketProfile().getTicketId(), e);
            metrics.setSecondLineOperators("[]");
            metrics.setSecondLineOperatorCompanyIds("{}");
        }

        metrics.setFirstSecondLineServiceTime(operatorTracker.firstSecondLineServiceTime);
        metrics.setFirstSecondLineResponseDuration(operatorTracker.firstSecondLineResponseDuration);
        metrics.setSecondLineSolvedDuration(operatorTracker.secondLineSolvedDuration);
        metrics.setLastSecondLineOperatorDuration(operatorTracker.lastSecondLineOperatorDuration);

        metrics.setIsTransferCompanyInSecondLine(operatorTracker.isTransferCompanyInSecondLine ? 1 : 0);
        metrics.setIsTransferInnerStaffInSecondLine(operatorTracker.isTransferInnerStaffInSecondLine ? 1 : 0);
    }

    // ============================== 内部数据结构 ==============================
    /**
     * 转单统计指标数据结构
     */
    private static class TransferMetrics {
        // 转单次数统计
        private int transferToOutTimes = 0;
        private int transferToInTimes = 0;
        private int transferOperationTimes = 0;
        private int transferFirstLineTimes = 0;
        private int transferSecondLineTimes = 0;
        private int transferProductionResearchTimes = 0;
        private int transferVerticalProductionResearchTimes = 0;

        // 转单状态标记
        private boolean isTransferTgw = false;
        private boolean isTransferFirstLine = false;

        // 首次转单信息
        private int firstTransferPost = 0;
        private ArrayList<String> transferChain = new ArrayList<>();
        private ArrayList<String> transferPostList = new ArrayList<>();

        // 转单时间信息
        private String transferFirstLineTime = DEFAULT_TIME;
        private String firstTransferSecondLineTime = DEFAULT_TIME;

        // 队列信息
        private int firstSecondLineAssign = -1;
        private int transferFirstLineQueue = -1;
        private int lastOperationFactAssign = -1;
        private int firstSecondLineFactAssign = -1;
        private int currentSecondLineFactAssign = -1;
        private int lastProductionResearchFactAssign = -1;

        // 热线转单相关
        private int hotlineFirstTransferDealDuration = 0;
        private int hotlineFirstTransferResponseDuration = 0;
        private String hotlineFirstTransferTime = DEFAULT_TIME;
        private String hotlineFirstTransferResponseTime = DEFAULT_TIME;

        // 其他统计
        private String measures = "";
        private String transferType = "";
    }

    /**
     * 操作人跟踪器
     */
    private static class OperatorTracker {
        // 处理人信息
        private String operationHandler = "";
        private String lastCustomerOperator = "";
        private String currentFirstLineOperator = "";
        private String currentSecondLineOperator = "";
        private String firstLineLastOperator = "";
        private String lastOfcOperator = "";
        private String productionResearchHandler = "";

        // 转单操作人
        private String transferFirstLineStaff = "";
        private String firstTransferOperationStaff = "";
        private String firstTransferSecondLineStaff = "";

        // 公司ID
        private int operationHandlerCompanyId = -1;
        private int currentSecondLineOperatorCompanyId = -1;
        private int firstSecondLineOperatorCompanyId = -1;
        private int firstLineLastOperatorCompanyId = -1;

        // 岗位信息
        private int firstTransferOperationStaffPost = -1;
        private int firstTransferProductionResearchStaffPost = -1;
        private int firstTransferVertProductionResearchStaffPost = -1;
        private int lastOfcOperatorPost = -1;
        private int firstLineLastOperatorFactAssign = -1;

        // 1.5线相关操作人
        private String firstSecondLineOperator = "";
        private String firstSecondLineFactOperator = "";
        private String secondLineLastCompanyOperator = "";
        private String secondLineLastCompanyOperatorGroupName = "";
        private int secondLineLastCompanyOperatorCompanyId = -1;

        // 1.5线集合
        private Set<String> secondLineOperators = new HashSet<>();
        private Map<String, Integer> secondLineOperatorCompanyIds = new HashMap<>();

        // 时间跟踪
        private String firstSecondLineServiceTime = DEFAULT_TIME;
        private int firstSecondLineResponseDuration = 0;
        private int secondLineSolvedDuration = 0;
        private int lastSecondLineOperatorDuration = 0;

        // 转供应商相关
        private boolean isTransferCompanyInSecondLine = false;
        private boolean isTransferInnerStaffInSecondLine = false;

        // 辅助变量
        private TreeMap<String, Map<String, Integer>> operatorTreeMap = new TreeMap<>();
    }
}
