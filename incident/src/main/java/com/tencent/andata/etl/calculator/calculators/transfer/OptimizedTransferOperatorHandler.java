package com.tencent.andata.etl.calculator.calculators.transfer;

import static com.tencent.andata.etl.entity.Operation.isValidContent;

import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 优化的转单操作人处理器
 * <p>
 * 专门处理转单相关的操作人跟踪逻辑，采用以下优化策略：
 * 1. 策略模式：根据操作类型分发处理
 * 2. 函数式编程：使用Predicate和Consumer简化条件判断
 * 3. 提前返回：减少嵌套层级
 * 4. 职责分离：每个方法只处理一种场景
 * 5. 配置化：将复杂的条件判断抽取为可配置的规则
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class OptimizedTransferOperatorHandler {

    private static final String DEFAULT_TIME = "1970-01-01 00:00:00";

    // 操作人跟踪数据
    private final OperatorTracker operatorTracker;

    // 状态标志
    private boolean isFirstTransferSecondLineFlag = true;
    private boolean isFirstSecondLineOperatorFlag1 = true;
    private boolean isFirstSecondLineOperatorFlag2 = true;
    private boolean isFirstSecondLineOperatorFlag3 = true;

    // 操作处理策略映射
    private final Map<OperationType, BiConsumer<Operation, TransferContext>> operationHandlers;

    public OptimizedTransferOperatorHandler(OperatorTracker operatorTracker) {
        this.operatorTracker = operatorTracker;
        this.operationHandlers = initializeOperationHandlers();
    }

    /**
     * 处理转单相关的操作人跟踪（优化版本）
     */
    public void processTransferOperatorTracking(Operation currentOp, OperationType opType,
            OperatorType operatorType, Operation previousOp, String operateTime) {
        // 只处理1.5线相关的操作
        if (!isSecondLineOperation(currentOp)) {
            return;
        }

        // 创建转单上下文
        TransferContext context = TransferContext.builder()
                .currentOp(currentOp)
                .operatorType(operatorType)
                .previousOp(previousOp)
                .operateTime(operateTime)
                .build();

        // 使用策略模式处理
        BiConsumer<Operation, TransferContext> handler = operationHandlers.get(opType);
        if (handler != null) {
            try {
                handler.accept(currentOp, context);
                log.trace("Processed {} operation for second line tracking", opType);
            } catch (Exception e) {
                log.error("Failed to process {} operation for second line tracking", opType, e);
            }
        }
    }

    /**
     * 初始化操作处理策略
     */
    private Map<OperationType, BiConsumer<Operation, TransferContext>> initializeOperationHandlers() {
        Map<OperationType, BiConsumer<Operation, TransferContext>> handlers = new HashMap<>();

        handlers.put(OperationType.PULL, this::handlePullOperation);
        handlers.put(OperationType.TRANSFER, this::handleTransferOperation);
        handlers.put(OperationType.DISPATCH, this::handleDispatchOperation);

        return handlers;
    }

    /**
     * 处理转单操作
     */
    private void handleTransferOperation(Operation currentOp, TransferContext context) {
        // 条件检查：操作人有效性
        if (!isValidOperator(currentOp.getOperatorName())) {
            return;
        }

        // 处理首次1.5线操作人
        processFirstSecondLineOperator(currentOp, context,
                () -> isFirstSecondLineOperatorFlag1,
                () -> { /* 无需额外状态更新 */ });

        // 处理首次转1.5线员工
        processFirstTransferSecondLineStaff(currentOp,
                () -> isFirstTransferSecondLineFlag && context.operatorType == OperatorType.CUSTOMER_SERVICE,
                () -> isFirstTransferSecondLineFlag = false);
    }

    /**
     * 处理认领操作
     */
    private void handlePullOperation(Operation currentOp, TransferContext context) {
        // 处理基于前一个操作的首次转1.5线员工
        processFirstTransferSecondLineStaffFromPrevious(context);

        // 记录首次1.5线服务时间
        recordFirstSecondLineServiceTime(context.operateTime);

        // 处理首次1.5线操作人（条件2）
        if (isFirstSecondLineOperatorFlag2 && isValidOperator(currentOp.getOperatorName())) {
            updateFirstSecondLineOperator(currentOp, context.operateTime);
            isFirstSecondLineOperatorFlag2 = false;
        }
    }

    /**
     * 处理派单操作
     */
    private void handleDispatchOperation(Operation currentOp, TransferContext context) {
        // 处理复杂的1.5线操作人状态逻辑
        if (context.operatorType == OperatorType.SYSTEM
                && !isFirstSecondLineOperatorFlag3
                && isFirstSecondLineOperatorFlag1) {
            updateFirstSecondLineOperator(currentOp, context.operateTime);
            isFirstSecondLineOperatorFlag1 = false;
        }
    }

    /**
     * 处理首次1.5线操作人（通用方法）
     */
    private void processFirstSecondLineOperator(Operation currentOp, TransferContext context, Supplier<Boolean> condition, Runnable stateUpdater) {
        if (condition.get()) {
            updateFirstSecondLineOperator(currentOp, context.operateTime);
            stateUpdater.run();
        }
    }

    /**
     * 处理首次转1.5线员工（通用方法）
     */
    private void processFirstTransferSecondLineStaff(Operation currentOp, Supplier<Boolean> condition, Runnable stateUpdater) {
        if (condition.get()) {
            operatorTracker.firstTransferSecondLineStaff = currentOp.getOperatorName();
            stateUpdater.run();
        }
    }

    /**
     * 处理基于前一个操作的首次转1.5线员工
     */
    private void processFirstTransferSecondLineStaffFromPrevious(TransferContext context) {
        if (context.previousOp != null
                && isFirstTransferSecondLineFlag
                && context.operatorType == OperatorType.CUSTOMER_SERVICE) {

            isFirstTransferSecondLineFlag = false;
            operatorTracker.firstTransferSecondLineStaff = determineTransferStaffFromPrevious(context.previousOp);
        }
    }

    /**
     * 记录首次1.5线服务时间
     */
    private void recordFirstSecondLineServiceTime(String operateTime) {
        if (StringUtils.isEmpty(operatorTracker.firstSecondLineServiceTime)) {
            operatorTracker.firstSecondLineServiceTime = operateTime;
        }
    }

    /**
     * 更新首次1.5线操作人信息
     */
    private void updateFirstSecondLineOperator(Operation currentOp, String operateTime) {
        operatorTracker.operatorTreeMap.put(operateTime, new HashMap<String, Integer>() {{
            put(currentOp.getNextOperatorName(), currentOp.getNextOperatorCompanyId());
        }});

        if (isFirstSecondLineOperatorFlag3) {
            isFirstSecondLineOperatorFlag3 = false;
        }
    }

    /**
     * 从前一个操作确定转单员工
     */
    private String determineTransferStaffFromPrevious(Operation previousOp) {
        String preOperator = previousOp.getOperatorName();
        String preNextOperator = previousOp.getNextOperatorName();
        return "SYSTEM".equalsIgnoreCase(preOperator) ? preOperator : preNextOperator;
    }

    /**
     * 检查是否为1.5线操作
     */
    private boolean isSecondLineOperation(Operation operation) {
        return OperatorPost.of(operation.getTargetPost()) == OperatorPost.SECOND_LINE;
    }

    /**
     * 检查操作人是否有效
     */
    private boolean isValidOperator(String operatorName) {
        return isValidContent(operatorName, "SYSTEM");
    }

    /**
     * 重置所有状态标志
     */
    public void resetFlags() {
        isFirstTransferSecondLineFlag = true;
        isFirstSecondLineOperatorFlag1 = true;
        isFirstSecondLineOperatorFlag2 = true;
        isFirstSecondLineOperatorFlag3 = true;
    }

    /**
     * 转单上下文数据结构
     */
    public static class TransferContext {

        public final OperatorType operatorType;
        public final Operation currentOp;
        public final Operation previousOp;
        public final String operateTime;

        private TransferContext(Operation currentOp, OperatorType operatorType, Operation previousOp, String operateTime) {
            this.currentOp = currentOp;
            this.previousOp = previousOp;
            this.operateTime = operateTime;
            this.operatorType = operatorType;
        }

        public static TransferContextBuilder builder() {
            return new TransferContextBuilder();
        }

        public static class TransferContextBuilder {

            private OperatorType operatorType;
            private Operation previousOp;
            private Operation currentOp;
            private String operateTime;

            public TransferContextBuilder currentOp(Operation currentOp) {
                this.currentOp = currentOp;
                return this;
            }

            public TransferContextBuilder operatorType(OperatorType operatorType) {
                this.operatorType = operatorType;
                return this;
            }

            public TransferContextBuilder previousOp(Operation previousOp) {
                this.previousOp = previousOp;
                return this;
            }

            public TransferContextBuilder operateTime(String operateTime) {
                this.operateTime = operateTime;
                return this;
            }

            public TransferContext build() {
                return new TransferContext(currentOp, operatorType, previousOp, operateTime);
            }
        }
    }

    /**
     * 操作人跟踪器数据结构（简化版本）
     */
    public static class OperatorTracker {

        public String firstTransferSecondLineStaff = "";
        public String firstSecondLineServiceTime = DEFAULT_TIME;
        public TreeMap<String, Map<String, Integer>> operatorTreeMap = new TreeMap<>();
    }
}