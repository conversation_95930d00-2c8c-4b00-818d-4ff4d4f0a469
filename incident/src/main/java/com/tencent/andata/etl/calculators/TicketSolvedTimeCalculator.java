//package com.tencent.andata.etl.calculators;
//
//import static com.tencent.andata.etl.entity.Operation.findLastOperation;
//import static com.tencent.andata.etl.enums.OperatorType.CUSTOMER;
//import static com.tencent.andata.etl.enums.OperatorType.CUSTOMER_SERVICE;
//import static com.tencent.andata.etl.enums.OperatorType.SYSTEM;
//import static com.tencent.andata.etl.enums.OperatorType.fromType;
//
//import com.tencent.andata.etl.entity.Operation;
//import com.tencent.andata.etl.enums.OperationType;
//import com.tencent.andata.etl.enums.OperatorType;
//import com.tencent.andata.etl.enums.TargetStatus;
//import io.vavr.collection.HashMap;
//import io.vavr.collection.List;
//import io.vavr.collection.Map;
//import io.vavr.control.Option;
//import java.io.IOException;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.Objects;
//import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
//import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
//
//public class TicketSolvedTimeCalculator {
//
//    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//    private final Map<OperatorType, ClosingTimeStrategy> STRATEGIES = HashMap.of(
//            SYSTEM, TicketSolvedTimeCalculator::calcSystemClosingTime, // 系统结单
//            CUSTOMER, TicketSolvedTimeCalculator::calcCustomerClosingTime, // 客户结单
//            CUSTOMER_SERVICE, TicketSolvedTimeCalculator::calcCustomerServiceClosingTime // 客服结单
//    );
//    private Map<Integer, List<Operation>> recordsByType;
//
//    public TicketSolvedTimeCalculator(java.util.List<Operation> records) {
//        this.recordsByType = List.ofAll(records).groupBy(Operation::getOperationType);
//    }
//
//    private static LocalDateTime calcSystemClosingTime(Map<Integer, List<Operation>> recordsByType,
//            Operation closingRecord) {
//
//        Option<Operation> lastConfirmCloseOperation = recordsByType
//                .get(OperationType.WAIT_CUSTOMER_CLOSE.getCode()) // 待客户确认结单
//                .getOrElse(List.of(closingRecord))
//                .filter(x -> x.getOperationId() < closingRecord.getOperationId())
//                .maxBy(Operation::getOperationId);
//
//        return lastConfirmCloseOperation.isDefined()
//                ? getSolvedTime(recordsByType, closingRecord, lastConfirmCloseOperation)
//                : findAlternativeCloseTime(recordsByType, closingRecord);
//    }
//
//    private static LocalDateTime getSolvedTime(Map<Integer, List<Operation>> recordsByType, Operation closingRecord,
//            Option<Operation> lastConfirmCloseOperation) {
//
//        return lastConfirmCloseOperation
//                .map(x -> {
//                    switch (fromType(x.getOperatorType())) {
//                        case SYSTEM: // 最后一次“待客户确认结单”是“系统”操作的
//                            return findClosestSystemTime(recordsByType, lastConfirmCloseOperation.get());
//                        case CUSTOMER_SERVICE: // 如果最后一次“待客户确认结单”是“客服”操作的
//                            return findClosestCustomerServiceTime(recordsByType, lastConfirmCloseOperation.get());
//                        default: // “客服”结单
//                            return null;
//                    }
//                }).getOrElse(closingRecord.getOperateTime());
//    }
//
//    private static LocalDateTime calcCustomerServiceClosingTime(Map<Integer, List<Operation>> recordsByType,
//            Operation closingRecord) {
//        return closingRecord.getOperateTime();
//    }
//
//    private static LocalDateTime calcCustomerClosingTime(Map<Integer, List<Operation>> recordsByType,
//            Operation closingRecord) {
//
//        Option<Operation> lastConfirmCloseOperation = recordsByType
//                .get(OperationType.WAIT_CUSTOMER_CLOSE.getCode()) // 待客户确认结单
//                .getOrElse(List.of(closingRecord))
//                .filter(x -> x.getOperationId() < closingRecord.getOperationId())
//                .maxBy(Operation::getOperationId);
//
//        return getSolvedTime(recordsByType, closingRecord, lastConfirmCloseOperation);
//    }
//
//    private static LocalDateTime defaultStrategy(Map<Integer, List<Operation>> recordsByType, Operation closingRecord) {
//        return closingRecord.getOperateTime();
//    }
//
//    private static LocalDateTime findClosestSystemTime(Map<Integer, List<Operation>> recordsByType,
//            Operation lastConfirmCloseOperation) {
//
//        // 操作类型：“待客户补充”
//        Option<Operation> lastSupplement =
//                findLastOperation(recordsByType, OperationType.WAIT_CUSTOMER_ADD_INFO, lastConfirmCloseOperation);
//
//        // 操作类型：“待确认业务恢复”
//        Option<Operation> lastBusinessRestore =
//                findLastOperation(recordsByType, OperationType.TO_CONFIRM_RESTORE, lastConfirmCloseOperation);
//        // 操作类型：“同意申请结单”且targetStatus：“待补充”
//        Option<Operation> lastAgreeClose =
//                findLastOperation(recordsByType, OperationType.AGREE_CLOSE_APPLICATION, lastConfirmCloseOperation,
//                        TargetStatus.TO_BE_ADDED_BY_CUSTOMER);
//
//        return List.of(lastSupplement, lastBusinessRestore, lastAgreeClose)
//                .filter(Option::isDefined)
//                .map(Option::get)
//                .maxBy(Operation::getOperateTime)
//                .getOrElse(lastConfirmCloseOperation)
//                .getOperateTime();
//
//    }
//
//    private static LocalDateTime findClosestCustomerServiceTime(Map<Integer, List<Operation>> recordsByType,
//            Operation lastConfirmCloseOperation) {
//
//        // 操作类型：“待客户补充”
//        Option<Operation> lastSupplement =
//                findLastOperation(recordsByType, OperationType.WAIT_CUSTOMER_ADD_INFO, lastConfirmCloseOperation);
//
//        // 操作类型：“待确认业务恢复”
//        Option<Operation> lastBusinessRestore =
//                findLastOperation(recordsByType, OperationType.TO_CONFIRM_RESTORE, lastConfirmCloseOperation);
//        // 操作类型：“同意申请结单”且targetStatus：“待补充”
//        Option<Operation> lastAgreeClose =
//                findLastOperation(recordsByType, OperationType.AGREE_CLOSE_APPLICATION, lastConfirmCloseOperation,
//                        TargetStatus.TO_BE_ADDED_BY_CUSTOMER);
//
//        if (!lastSupplement.isDefined() && !lastBusinessRestore.isDefined() && !lastAgreeClose.isDefined()) {
//            return lastConfirmCloseOperation.getOperateTime();
//        }
//
//        java.util.HashMap<LocalDateTime, String> nearestTimeMap = new java.util.HashMap<>();
//        lastSupplement.peek(record -> nearestTimeMap.put(record.getOperateTime(), "lastSupplement"));
//        lastAgreeClose.peek(record -> nearestTimeMap.put(record.getOperateTime(), "lastAgreeClose"));
//        lastBusinessRestore.peek(record -> nearestTimeMap.put(record.getOperateTime(), "lastBusinessRestore"));
//
//        String nearestType = HashMap.ofAll(nearestTimeMap).max().getOrNull()._2;
//
//        switch (nearestType) {
//            case "lastBusinessRestore": // “待确认业务恢复”
//                return findBusinessRestoreTime(recordsByType, lastConfirmCloseOperation, lastBusinessRestore.get());
//            case "lastSupplement": // “待客户补充”
//                return findSupplementTime(recordsByType, lastConfirmCloseOperation, lastSupplement.get());
//            case "lastAgreeClose": // “同意申请结单”且targetStatus为“待补充”
//            default:
//                return lastConfirmCloseOperation.getOperateTime();
//        }
//    }
//
//    private static LocalDateTime findBusinessRestoreTime(Map<Integer, List<Operation>> recordsByType,
//            Operation lastConfirmCloseOperation, Operation lastBusinessRestore) {
//
//
//
//        /*
//            Option<Operation> restoreAnalysis = recordsByType
//                .get(OperationType.RESTORED.getCode()) // 已恢复分析根因
//                .getOrElse(List.empty())
//                .maxBy(Operation::getOperateTime);
//
//            a) 如果流水中存在操作类型为“已恢复分析根因”，就取操作“待确认业务恢复”的时间为结单时间点；
//            b) 如果流水中存在操作类型为“未恢复”，就取操作“待客户确认结单”的时间为结单时间点；
//            c) 否则，取操作“待确认业务恢复”的时间为结单时间点；
//            这里将条件进行了合并，只需要判断条件b，条件 a 和 c 合并到一起 统一 返回 “待确认业务恢复”的时间
//         */
//        Option<Operation> notRestored = recordsByType
//                .get(OperationType.NOT_RESTORED.getCode()) // 未恢复
//                .getOrElse(List.empty())
//                .maxBy(Operation::getOperateTime);
//
//        return notRestored.isDefined() ? lastConfirmCloseOperation.getOperateTime()
//                : lastBusinessRestore.getOperateTime();
//    }
//
//    private static LocalDateTime findSupplementTime(Map<Integer, List<Operation>> recordsByType,
//            Operation lastConfirmCloseOperation, Operation lastSupplement) {
//        Option<Operation> lastClaim = recordsByType
//                .get(OperationType.PULL.getCode()) //认领
//                .getOrElse(List.of(lastConfirmCloseOperation))
//                .filter(x -> x.getOperationId() < lastConfirmCloseOperation.getOperationId())
//                .filter(x -> x.getRemark().contains("认领原因:工单回访"))
//                .maxBy(Operation::getOperateTime);
//
//        return lastClaim.isDefined() ? lastSupplement.getOperateTime() : lastConfirmCloseOperation.getOperateTime();
//    }
//
//    private static LocalDateTime findAlternativeCloseTime(Map<Integer, List<Operation>> recordsByType,
//            Operation closingRecord) {
//
//        // 撤销
//        Option<Operation> cancel = findLastOperation(recordsByType, OperationType.CANCEL, closingRecord);
//
//        // 待确认业务恢复
//        Option<Operation> toConfirmRestore = findLastOperation(recordsByType, OperationType.TO_CONFIRM_RESTORE,
//                closingRecord);
//
//        // “同意申请结单”且“targetStatus”为“待确认结单“
//        Option<Operation> lastAgreeClose = findLastOperation(recordsByType, OperationType.AGREE_CLOSE_APPLICATION,
//                closingRecord, TargetStatus.CLOSE_CONFIRMATION_PENDING);
//
//        // “故障单批量回复”且“targetStatus“为“待确认结单”
//        Option<Operation> fallBack = findLastOperation(recordsByType, OperationType.BATCH_REPLY_FAILURE, closingRecord,
//                TargetStatus.CLOSE_CONFIRMATION_PENDING);
//
//        // “申请结单”
//        Option<Operation> applyClose =
//                findLastOperation(recordsByType, OperationType.CLOSE_APPLICATION,
//                        lastAgreeClose.getOrElse(closingRecord));
//
//        // “待客户补充”
//        Option<Operation> lastSupplement =
//                findLastOperation(recordsByType, OperationType.WAIT_CUSTOMER_ADD_INFO, closingRecord);
//
//        return List.of(lastAgreeClose, fallBack, cancel, toConfirmRestore, lastSupplement)
//                .filter(Option::isDefined)
//                .map(Option::get)
//                .maxBy(Operation::getOperateTime)
//                // 如果是“同意申请结单”且“targetStatus”为“待确认结单“距离较近，则：用最后一次“申请结单”作为统计截止时间点
//                // 如果是“故障单批量回复”且“targetStatus”为"待确认结单"距离较近，则：用最后一次“故障单批量回复”作为统计截止时间点
//                // 如果是“撤销 ”距离较近，则：用最后一次“撤销”作为统计截止时间点
//                // 如果是“待客户补充”距离较近，则：	用最后一次“待客户补充”作为结单时间点 (2024.10更新)
//                .map(x -> x == lastAgreeClose.getOrNull() ? applyClose.get().getOperateTime() : x.getOperateTime())
//                .getOrElse(closingRecord.getOperateTime());
//    }
//
//    public static void main(String[] args) throws IOException {
//
//        String filePath = "incident/src/main/resources/operation.txt"; // 替换为你的文件路径
//        String ticketOperationStr = new String(Files.readAllBytes(Paths.get(filePath)));
//        ObjectMapper objectMapper = new ObjectMapper();
//        java.util.List<Operation> opts =
//                objectMapper.readValue(ticketOperationStr, new TypeReference<java.util.List<Operation>>() {
//                });
//
//        // System.out.println(objectMapper.writeValueAsString(opts));
//        // calculate
//        Long ticketId = opts.get(0).getTicketId();
//        TicketSolvedTimeCalculator calculator = new TicketSolvedTimeCalculator(opts);
//        LocalDateTime closingTime = calculator.calcClosingTime(ticketId, opts);
//        System.out.println("工单 id: " + ticketId + " 解决时间: " + closingTime.format(formatter));
//    }
//
//    /**
//     * 计算工单的解决时间
//     *
//     * @param ticketId 工单id
//     * @param operations 工单流水
//     * @return 工单解决时间
//     */
//    public LocalDateTime calcClosingTime(Long ticketId, java.util.List<Operation> operations) {
//        this.recordsByType = List
//                .ofAll(operations)
//                .groupBy(Operation::getOperationType);
//
//        Option<Operation> closingRecord = this.recordsByType
//                .get(OperationType.CLOSE.getCode()) //结单
//                .getOrElse(List.empty())
//                .filter(x -> !(!Objects.equals(x.getOperatorType(), SYSTEM.getType())
//                        && x.getOperator().equalsIgnoreCase("SYSTEM")))
//                .maxBy(Operation::getOperationId);
//
//        // 如果不存在“结单”流水，制造一条operationId为Long的上限；operatorType为“客户”
//        // 操作时间为“1970-01-01 00:00:00”的流水，用于计算工单未“结单”时的解决时间。
//        closingRecord = closingRecord.isDefined() ? closingRecord :
//                Option.of(
//                        Operation.builder()
//                                .operatorType(CUSTOMER.getType())
//                                .operateTime(LocalDateTime.parse("1970-01-01T00:00:00"))
//                                .operationId(Long.MAX_VALUE).build()
//                );
//
//        // 释放内存
//        operations = null;
//
//        System.out.println(
//                "工单 id: " + ticketId + " 结单时间：" + closingRecord.get().getOperateTime().format(formatter));
//        return closingRecord.map(x -> STRATEGIES
//                        .getOrElse(fromType(x.getOperatorType()), TicketSolvedTimeCalculator::defaultStrategy)
//                        .calcClosingTime(recordsByType, x))
//                .getOrElse(closingRecord.get().getOperateTime());
//        //.getOrElse(LocalDateTime.parse("1970-01-01T00:00:00"));
//    }
//
//    /**********************************************已结单工单解决时间计算逻辑如下********************************************
//     * 首先、先寻找结单类型：看operationType==16的流水中：
//     *   1、如果operatorType==1 则是“客户”结单；
//     *   2、如果operatorType==2 则是“客服”结单；
//     *   3、如果operatorType==3 则是“系统”结单；
//     * <p>
//     * 一、如果是“系统”结单
//     *   1、看距离“结单”时间最近的几个操作类型，除“评价“和“评论”和“转需求单”和“投诉”和“拆单”和“编辑”和“转TAPD”和“转TAPD Bug”和“有效外呼”
//     *      和“无效外呼”和“交接”和“确认归档”之外：如果是“待客户确认结单”距离较近，则
//     *     1.1、如果最后一次“待客户确认结单”是“系统”操作的，需要找距离这次“待客户确认结单”更近的“待客户补充” 或 “待确认业务恢复” 或
//     *          （“同意申请结单”且“target_status”为“待补充”）操作行为，以此操作时间为结单时间点。
//     *     1.2、如果最后一次“待客户确认结单”是“客服”操作的，需要找距离这次“待客户确认结单”更近的“待客户补充” 或 “待确认业务恢复” 或
//     *          （“同意申请结单”且“target_status”为“待补充”）:
//     *       1.2.1、如果既没有“待客户补充”，也没有“待确认业务恢复”，也没有“同意申请结单“且“target_status”为“待补充”，就取最后一次“客服”
//     *           操作“待客户确认结单”时间作为结单时间。
//     *       1.2.2、如果是“待确认业务恢复”更近：
//     *          1.2.2.1、如果流水中存在操作类型为“已恢复分析根因”，就取操作“待确认业务恢复”的时间为结单时间点；
//     *          1.2.2.2、如果流水中存在操作类型为“未恢复”，就取操作“待客户确认结单”的时间为结单时间点；
//     *          1.2.2.3、否则，取操作“待确认业务恢复”的时间为结单时间点；
//     *       1.2.3、如果是“待客户补充”更近：
//     *          1.2.3.1、如果“待客户确认结单”操作的前一个操作为“认领”且此次“认领”操作的备注内容包含“认领原因:工单回访”就取最后“待客户补充”
//     *            时间作为结单时间；
//     *          1.2.3.2、否则取最后一次客服操作“待确认结单时间”作为结单时间；
//     *       1.2.4、如果是“同意申请结单”且“target_status”为”待补充”更近：
//     *          1.2.4.1、取最后一次客服操作“待客户确认结单”时间作为结单时间。
//     *     2、如果是“同意申请结单”且“target_status”为“待确认结单“距离较近，则：
//     *         2.1、取最后一次“申请结单”作为结单时间点；
//     *     3、如果是“故障单批量回复”且“target_status“为“待确认结单”距离较近，则：
//     *         3.1、取最后一次“故障单批量回复”作为结单时间点。
//     *     4、如果是“待客户补充”距离较近，则：
//     *         4.1、取最后一次“待客户补充”作为结单时间点 (2024.10更新)
//     * </p>
//     * <p>
//     * 二、如果是“客户“结单
//     *   1、看距离“结单”时间最近的几个操作类型，除“评价“和“评论”和“转需求单”和“投诉”和“拆单”和“编辑”和“转TAPD”和“转TAPD Bug”和“有效外呼”
//     *      和“无效外呼”和“交接”和“确认归档”之外：如果是“待客户确认结单”距离较近，则
//     *     1.1、如果最后一次“待客户确认结单”是“系统”操作的，需要找距离这次“待客户确认结单”更近的“待客户补充” 或 “待确认业务恢复” 或
//     *         （“同意申请结单”且“target_status”为“待补充”）操作行为，以此操作时间为结单时间点。
//     *     1.2、如果最后一次“待客户确认结单”是“客服”操作的，需要找距离这次“待客户确认结单”更近的“待客户补充” 或 “待确认业务恢复” 或
//     *         （“同意申请结单”且“target_status”为“待补充”）:
//     *       1.2.1、如果既没有“待客户补充”也没有“待确认业务恢复”也没有“同意申请结单“且“target_status”为“待补充”，就取最后一次“客服”
//     *              操作“待客户确认结单”时间作为结单时间。
//     *       1.2.2、如果是“待确认业务恢复”更近：
//     *          1.2.2.1、如果流水中存在操作类型为“已恢复分析根因”，就取操作“待确认业务恢复”的时间为结单时间点；
//     *          1.2.2.2、如果流水中存在操作类型为“未恢复”，就取操作“待客户确认结单”的时间为结单时间点；
//     *          1.2.2.3、否则，就取操作“待确认业务恢复”的时间为结单时间点；
//     *       1.2.3、如果是“待客户补充”更近：
//     *          1.2.3.1、如果“待客户确认结单”操作的前一个操作为“认领”，且此次“认领”操作的备注内容包含“认领原因:工单回访”，取最后“待客户补
//     *                   充”时间作为结单时间；
//     *          1.2.3.2、否则取最后一次客服操作“待确认结单时间”作为结单时间；
//     *       1.2.4、如果是“同意申请结单”且“target_status”为”待补充”更近：
//     *          1.2.4.1、取最后一次客服操作“待客户确认结单”时间作为结单时间。
//     *   2、如果是“同意申请结单”且“target_status”为“待确认结单“距离较近，则：
//     *     2.1、取最后一次“申请结单”作为结单时间点；
//     *     -- 3、如果是“待客户补充“距离较近，则：
//     *     --     3.1、取最后一次“待客户补充”作为结单时间点；
//     *   4、如果是“待确认业务恢复“距离较近，则：
//     *         4.1、取最后一次“待确认业务恢复”作为结单时间点；
//     *   5、如果是“已恢复分析根因“距离较近，则：
//     *         5.1、取最后一次“待确认业务恢复”作为结单时间点；
//     *   6、如果是“故障单批量回复”且“target_status“为“待确认结单”距离较近，则：
//     *         6.1、取最后一次“故障单批量回复”作为结单时间点。
//     *   7、如果不是以上六种类型则：
//     *     7.1、用最后一次“结单”时间作为结单时间点
//     * </p>
//     * <p>
//     * 三、如果是“客服“结单：用“客服“结单时间作为结单时间点
//     **********************************************未结单工单解决时间计算逻辑如下********************************************
//     *   1、看距离“结单”时间最近的几个操作类型，除“评价“和“评论”和“转需求单”和“投诉”和“拆单”和“编辑”和“转TAPD”和“转TAPD Bug”和“有效外呼”
//     *      和“无效外呼”和“交接”和“确认归档”之外：如果是“待客户确认结单”距离较近，则
//     *     1.1、如果最后一次“待客户确认结单”是“系统”操作的，需要找距离这次“待客户确认结单”更近的“待客户补充” 或 “待确认业务恢复” 或
//     *         （“同意申请结单”且“target_status”为“待补充”）操作行为，以此操作时间为结单时间点。
//     *     1.2、如果最后一次“待客户确认结单”是“客服”操作的，需要找距离这次“待客户确认结单”更近的“待客户补充” 或 “待确认业务恢复” 或
//     *          （“同意申请结单”且“target_status”为“待补充”）:
//     *       1.2.1、如果既没有“待客户补充”，也没有“待确认业务恢复”，也没有“同意申请结单“且“target_status”为“待补充”，就取最后一次“客服”
//     *           操作“待客户确认结单”时间作为结单时间。
//     *       1.2.2、如果是“待确认业务恢复”更近：
//     *          1.2.2.1、如果流水中存在操作类型为“已恢复分析根因”，就取操作“待确认业务恢复”的时间为结单时间点；
//     *          1.2.2.2、如果流水中存在操作类型为“未恢复”，就取操作“待客户确认结单”的时间为结单时间点；
//     *          1.2.2.3、否则，取操作“待确认业务恢复”的时间为结单时间点；
//     *       1.2.3、如果是“待客户补充”更近：
//     *          1.2.3.1、如果“待客户确认结单”操作的前一个操作为“认领”且此次“认领”操作的备注内容包含“认领原因:工单回访”就取最后“待客户补充”
//     *            时间作为结单时间；
//     *          1.2.3.2、否则取最后一次客服操作“待确认结单时间”作为结单时间；
//     *       1.2.4、如果是“同意申请结单”且“target_status”为”待补充”更近：
//     *          1.2.4.1、取最后一次客服操作“待客户确认结单”时间作为结单时间。
//     *   2、如果是“同意申请结单”且“target_status”为“待确认结单“距离较近，则：
//     *     2.1、取最后一次“申请结单”作为结单时间点；
//     *     -- 3、如果是“待客户补充“距离较近，则：
//     *     --     3.1、取最后一次“待客户补充”作为结单时间点；
//     *   4、如果是“待确认业务恢复“距离较近，则：
//     *         4.1、取最后一次“待确认业务恢复”作为结单时间点；
//     *   5、如果是“已恢复分析根因“距离较近，则：
//     *         5.1、取最后一次“待确认业务恢复”作为结单时间点；
//     *   6、如果是“故障单批量回复”且“target_status“为“待确认结单”距离较近，则：
//     *         6.1、取最后一次“故障单批量回复”作为结单时间点。
//     *   7、如果是“撤销”距离较近，则：
//     *         7.1、取最后一次“撤销”作为统计截止时间点。
//     *   7、如果不是以上类型则：
//     *     7.1、用最后一次“当前时间节点”时间作为统计截止时间点
//     * 四、其他结单
//     *      1、若不是以上类型但是status = '已结单'	用首次status = '已结单' 或 target_status = '已结单'时间作为结单时间点
//     * </p>
//     ******************************************************************************************************************/
//    interface ClosingTimeStrategy {
//
//        LocalDateTime calcClosingTime(Map<Integer, List<Operation>> recordsByType, Operation closingRecord);
//    }
//}