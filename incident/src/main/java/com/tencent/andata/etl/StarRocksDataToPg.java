package com.tencent.andata.etl;

import static com.tencent.andata.utils.TableUtils.getTableTypeInformation;

import com.starrocks.connector.flink.StarRocksSource;
import com.starrocks.connector.flink.table.source.StarRocksSourceOptions;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.Properties;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.TableResult;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.data.RowData;
import org.jetbrains.annotations.NotNull;

public class StarRocksDataToPg {

    /**
     * starRocks数据写入pg
     */
    public static void main(String[] args) throws Exception {
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        String pgDbName = parameterTool.get("pgDbName");

        // 设置Flink的执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 设置Flink Table的执行环境
        final EnvironmentSettings settings = EnvironmentSettings
                .newInstance()
                .inStreamingMode()
                .build();

        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env, settings);

        Properties properties = PropertyUtils.loadProperties("env.properties");

        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", pgDbName))
                .build();

        String pgTableDDL = getSinkTableDDL(pgDBConf);
        TableResult sinkTable = tableEnv.executeSql(pgTableDDL); // 注册PG表


        DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "andata_rt"))
                .build();

        // 读取StarRocks数据
        TableSchema tableSchema = TableSchema.builder()
                .field("ticket_id", DataTypes.BIGINT())
                .field("last_customer_operator", DataTypes.STRING())
                .field("second_line_last_company_operator", DataTypes.STRING())
                .build();

        StarRocksSourceOptions options = getStarRocksSourceOptions(rocksDbConf);
        SingleOutputStreamOperator<RowData> source = env.addSource(StarRocksSource.source(tableSchema, options))
                .returns(getTableTypeInformation(tableEnv.from("pg_sink_dwm_ticket_statistic")));

        // Stream 转成 view
        tableEnv.createTemporaryView("source_starrocks_view", source);

        tableEnv.executeSql(getInsertStatement());

        // 开启ck
        setCheckpointConf(env.getCheckpointConfig());

        // 执行
        env.execute("StarRocks-Sink-PG");
    }

    @NotNull
    private static StarRocksSourceOptions getStarRocksSourceOptions(DatabaseConf rocksDbConf) {
        String scanUrl = String.format("%s:%d", rocksDbConf.dbHost, 8080);
        String jdbcUrl = String.format("jdbc:mysql://%s:%d", rocksDbConf.dbHost, rocksDbConf.dbPort);
        return StarRocksSourceOptions.builder()
                .withProperty("jdbc-url", jdbcUrl)
                .withProperty("scan-url", scanUrl)
                .withProperty("scan.max-retries", "100")
                .withProperty("username", rocksDbConf.userName)
                .withProperty("password", rocksDbConf.password)
                .withProperty("database-name", rocksDbConf.dbName)
                .withProperty("scan.params.keep-alive-min", "1440")
                .withProperty("scan.connect.timeout-ms", "2592000")
                .withProperty("scan.params.query-timeout-s", "2592000")
                .withProperty("scan.params.mem-limit-byte", "4294967296")
                .withProperty("table-name", "dwm_incident_ticket_statistic")
                .withProperty("scan.columns", "ticket_id,last_customer_operator,second_line_last_company_operator")
                .build();
    }

    @NotNull
    private static String getSinkTableDDL(DatabaseConf pgDBConf) {
        String url = String.format(
                "***************************************************************************"
                        + "&serverTimezone=Asia/Shanghai", pgDBConf.dbHost, pgDBConf.dbPort, pgDBConf.dbName);
        return
                "CREATE TABLE pg_sink_dwm_ticket_statistic (\n"
                        + "  `ticket_id` BIGINT,\n"
                        + "  `last_customer_operator` STRING,\n"
                        + "  `second_line_last_company_operator` STRING,\n"
                        + "  PRIMARY KEY (ticket_id) NOT ENFORCED\n"
                        + ") WITH (\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'url' = '" + url + "',\n"
                        + "  'username' = '" + pgDBConf.userName + "',\n"
                        + "  'password' = '" + pgDBConf.password + "',\n"
                        + "  'table-name' = 'dwm_ticket_statistic'\n"
                        + ")";
    }

    private static String getInsertStatement() {
        return "INSERT INTO pg_sink_dwm_ticket_statistic SELECT * FROM source_starrocks_view";
    }

    private static void setCheckpointConf(CheckpointConfig checkpointConfig) {
        checkpointConfig.setMaxConcurrentCheckpoints(1);
        checkpointConfig.setCheckpointInterval(20 * 1000L);
        checkpointConfig.setTolerableCheckpointFailureNumber(10);
        checkpointConfig.setMinPauseBetweenCheckpoints(30 * 1000L);
        checkpointConfig.setCheckpointTimeout(4 * 60 * 60 * 1000L);
    }
}
