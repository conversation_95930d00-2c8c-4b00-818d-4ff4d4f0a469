package com.tencent.andata.etl.calculator;

import com.tencent.andata.etl.calculator.calculators.DenyCalculator;
import com.tencent.andata.etl.calculator.calculators.DurationCalculator;
import com.tencent.andata.etl.calculator.calculators.InteractionCalculator;
import com.tencent.andata.etl.calculator.calculators.ReplyInfoCalculator;
import com.tencent.andata.etl.calculator.calculators.SolvedTimeCalculator;
import com.tencent.andata.etl.calculator.calculators.TransferCalculator;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.entity.TicketProfile;
import io.vavr.Tuple3;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.Set;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

/**
 * 指标计算主编排器
 * <p>
 * 这是重构后计算逻辑的核心入口。它取代了原有的长串MapFunction调用链。
 * 其核心职责是：
 * 1. 管理一组具体的指标计算器 (IMetricCalculator)。
 * 2. 对每一个输入的TicketProfile，执行一次完整的计算流程。
 * 3. 实现"一次遍历"，在单次循环中调用所有计算器来处理每个操作记录。
 * 4. 最终聚合并返回一个包含所有指标的强类型POJO。
 */
public class IncidentMetricsOrchestrator extends RichMapFunction<TicketProfile, DwmIncidentTicketStatisticMetrics> {


    private transient List<IMetricCalculator<CalculationContext>> calculators;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 在这里实例化并注册所有具体的指标计算器
        // 这种方式将注册逻辑与执行逻辑分离，符合SOLID原则。
        // 使用LinkedHashSet保持插入顺序，以便于后续的计算器依赖关系
        Set<IMetricCalculator<CalculationContext>> orderedCalculators = new LinkedHashSet<>();
        orderedCalculators.add(new InteractionCalculator());
        orderedCalculators.add(new SolvedTimeCalculator());
        orderedCalculators.add(new DenyCalculator());
        orderedCalculators.add(new DurationCalculator());
        orderedCalculators.add(new TransferCalculator());
        orderedCalculators.add(new ReplyInfoCalculator());

        // 转换为不可变列表确保顺序不被修改
        this.calculators = Collections.unmodifiableList(new ArrayList<>(orderedCalculators));
    }

    @Override
    public DwmIncidentTicketStatisticMetrics map(TicketProfile ticketProfile) throws Exception {
        // 1. 为每个工单创建一个独立的计算上下文
        final CalculationContext context = new CalculationContext(ticketProfile);
        context.initialize(); // 执行预计算，如数据分组

        // 2. 初始化所有计算器
        for (IMetricCalculator<CalculationContext> calculator : calculators) {
            calculator.initialize(context);
        }

        // 3. 创建操作记录的三元组视图 (prev, current, next)
        // 这使得在处理当前记录时能方便地访问其上下文，是许多状态判断的关键
        List<Tuple3<Operation, Operation, Operation>> operationTriplets = Operation.createTriplets(ticketProfile.getOperations());

        // 4. 执行核心的"一次遍历"
        for (Tuple3<Operation, Operation, Operation> triplet : operationTriplets) {
            Operation previousOp = triplet._1;
            Operation currentOp = triplet._2;
            Operation nextOp = triplet._3;

            // 将当前操作记录分发给所有计算器处理
            for (IMetricCalculator<CalculationContext> calculator : calculators) {
                calculator.processOperation(context, currentOp, previousOp, nextOp);
            }
        }

        // 5. 执行遍历后的最终计算
        for (IMetricCalculator<CalculationContext> calculator : calculators) {
            calculator.calculate(context);
        }

        // 6. 收集结果
        for (IMetricCalculator<CalculationContext> calculator : calculators) {
            calculator.populateMetrics(context.getMetrics(), context);
        }

        // 填充来自TicketProfile的基础信息
        populateBaseMetrics(context.getMetrics(), ticketProfile);

        return context.getMetrics();
    }

    private void populateBaseMetrics(DwmIncidentTicketStatisticMetrics metrics, TicketProfile profile) {
        metrics.setTicketId(profile.getTicketId());
        metrics.setCreateTime(profile.getCreateTime());
        metrics.setUpdateTime(profile.getUpdateTime());
        metrics.setServiceChannel(profile.getServiceChannel());
    }
}