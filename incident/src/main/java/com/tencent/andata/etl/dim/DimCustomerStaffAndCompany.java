package com.tencent.andata.etl.dim;

import static com.tencent.andata.etl.sql.DimCustomerStaffSql.CLEAN_STAFF_SQL;
import static com.tencent.andata.etl.sql.DimCustomerStaffSql.QUERY_COMPANY_SQL;
import static com.tencent.andata.etl.tablemap.DimCustomerStaffAndCompanyInfoMapping.icebergTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DimCustomerStaffAndCompanyInfoMapping.mysqlAccountTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DimCustomerStaffAndCompanyInfoMapping.mysqlWorkTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DimCustomerStaffAndCompanyInfoMapping.pgsqlTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DimCustomerStaffAndCompanyInfoMapping.starRocksTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

@Builder
public class DimCustomerStaffAndCompany {

    private final String icebergDbName;
    private final String pgDbName;


    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {

        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();

        // get config from property file
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlWorkDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        DatabaseConf mysqlAccountDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "account"))
                .build();

        DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlWorkTable2FlinkTableMap = mapper.readValue(mysqlWorkTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlWorkDBConf, mysqlWorkTable2FlinkTableMap, MYSQL, tEnv);

        // mysql table mapping to flink table
        ArrayNode mysqlAccountTable2FlinkTableMap = mapper.readValue(mysqlAccountTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(mysqlAccountDBConf, mysqlAccountTable2FlinkTableMap, MYSQL, tEnv);

        // pgsql table mapping to flink table
        ArrayNode pgsqlTable2FlinkTableMap = mapper.readValue(pgsqlTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(pgsqlDBConf, pgsqlTable2FlinkTableMap, PGSQL, tEnv);

        // starrocks table mapping to flink table
        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(starRocksTable2FlinkTable, ArrayNode.class);
        TableUtils.rdbTable2FlinkTable(rocksDbConf, starRocksTable2FlinkTableMap, ROCKS, tEnv);

        // icebergTable mapping to flinkTable
        ArrayNode icebergTable2FlinkTableMap = mapper.readValue(icebergTable2FlinkTable, ArrayNode.class);
        TableUtils.icebergTable2FlinkTable(this.icebergDbName, icebergTable2FlinkTableMap, tEnv, catalog);

        tEnv.createTemporaryView("dim_customer_staff_info_view", tEnv.sqlQuery(CLEAN_STAFF_SQL));

        StatementSet stmtSet = flinkEnv.stmtSet();

        //mysql -> ods iceberg
        HashMap<String, String> tblMap = new HashMap<String, String>() {
            {
                put("mysql_source_t_users", "iceberg_sink_ods_t_users");
                put("mysql_source_t006_staff", "iceberg_sink_ods_t006_staff");
                put("mysql_source_t903_company_user", "iceberg_sink_ods_t903_company_user");
            }
        };
        for (Map.Entry<String, String> entry : tblMap.entrySet()) {
            stmtSet.addInsertSql(insertIntoSql(
                    entry.getKey(),
                    entry.getValue(),
                    tEnv.from(entry.getValue()),
                    ICEBERG
            ));
        }

        // company -> pg
        tEnv.createTemporaryView("dim_company_info_view", tEnv.sqlQuery(QUERY_COMPANY_SQL));

        stmtSet.addInsertSql(insertIntoSql(
                        "dim_company_info_view",
                        "pgsql_sink_dim_company_info", // 公司维表写入pgsql
                        tEnv.from("pgsql_sink_dim_company_info"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "dim_company_info_view",
                        "iceberg_sink_dim_company_info", // 公司维表写入iceberg
                        tEnv.from("iceberg_sink_dim_company_info"),
                        ICEBERG
                ))

                .addInsertSql(insertIntoSql(
                        "dim_company_info_view",
                        "flink_starrocks_dim_company_info", // 公司维表写入starrocks
                        tEnv.from("flink_starrocks_dim_company_info"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dim_customer_staff_info_view",
                        "pgsql_sink_dim_customer_staff_info", // 客服维表写入pgsql
                        tEnv.from("pgsql_sink_dim_customer_staff_info"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "dim_customer_staff_info_view",
                        "flink_starrocks_dim_customer_staff_info", // 客服维表写入starrocks
                        tEnv.from("flink_starrocks_dim_customer_staff_info"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "dim_customer_staff_info_view",
                        "iceberg_sink_dim_customer_staff_info", // 客服维表写入iceberg
                        tEnv.from("iceberg_sink_dim_customer_staff_info"),
                        ICEBERG
                ));
    }
}
