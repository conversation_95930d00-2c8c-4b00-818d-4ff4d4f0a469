package com.tencent.andata.etl.calculator.calculators;

import static com.tencent.andata.etl.entity.Operation.isValidContent;
import static com.tencent.andata.utils.DateFormatUtils.timeDelta;
import static com.tencent.andata.utils.TimeUtil.formatDateTime;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.calculator.IMetricCalculator;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import com.tencent.andata.etl.enums.TargetStatus;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 交互类指标计算器
 * <p>
 * 负责计算与工单基本交互相关的核心指标，例如结单、回复、转单、首次响应等。
 * 对应原 DwmIncidentTicketStatisticTransform.TicketCloseAndInteractionMapFunction 中的逻辑。
 */
@Slf4j
public class InteractionCalculator implements IMetricCalculator<CalculationContext> {

    // 使用 ObjectMapper 来处理 JSON 序列化
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 工单关闭时间（格式：yyyy-MM-dd HH:mm:ss）
    private String closedTime;
    // 关闭类型（0-未知, 1-客服关闭, 2-客户关闭）
    private int closureType;
    // 关闭操作人姓名
    private String closedOperator;
    // 关闭操作人岗位类型
    private int closedOperatorPost;

    // 满意度状态（"满意"/"不满意"/"未评价"）
    private String isSatisfied;

    // 总回复次数
    private int replyCount;
    // 一线回复次数
    private int firstLineReplyCount;
    // 二线回复次数
    private int secondLineReplyCount;
    // 供应商回复次数
    private int agentReplyCount;
    // 客户回复次数
    private int customerReplyCount;

    // 转单次数
    private int transferCount;

    // 是否上报（0-未上报, 1-已上报）
    private int isReported;
    // 最后上报人姓名
    private String lastReporter;
    // 最后上报时间（格式：yyyy-MM-dd HH:mm:ss）
    private String lastReportedTime;

    // 是否投诉（0-无投诉, 1-有投诉）
    private int isComplaint;
    // 是否内部建单（-1-未知, 0-外部建单, 1-内部建单）
    private int isInternallyCreated;
    // 内部建单人姓名
    private String internalCreator;
    // 请求来源
    private String requestSource;

    // 互动次数
    private int interactionCount;
    // 外呼次数
    private int outboundCallCount;
    // 是否电话回访（0-否, 1-是）
    private int isPhoneCallback;

    // 首次响应人姓名
    private String firstResponder;
    // 首次响应时间（格式：yyyy-MM-dd HH:mm:ss）
    private String firstResponseTime;
    // 首次响应时长（单位：秒）
    private long firstResponseDuration;

    // 经手人集合（存储所有操作涉及的操作人姓名）
    private Set<String> operatorNames;
    // 首次运维接单人姓名
    private String firstOperationReceiver;


    @Override
    public void initialize(CalculationContext context) {
        // 初始化所有状态变量，确保每个工单的计算都是从干净的状态开始
        this.closedTime = "1970-01-01 00:00:00";
        this.lastReportedTime = "1970-01-01 00:00:00";
        this.firstResponseTime = "1970-01-01 00:00:00";
        this.closureType = 0;
        this.closedOperator = "";
        this.closedOperatorPost = 0;
        this.isSatisfied = "未评价";
        this.replyCount = 0;
        this.firstLineReplyCount = 0;
        this.secondLineReplyCount = 0;
        this.agentReplyCount = 0;
        this.customerReplyCount = 0;
        this.transferCount = 0;
        this.isReported = 0;
        this.lastReporter = "";
        this.isComplaint = 0;
        this.isInternallyCreated = -1;
        this.internalCreator = "";
        this.requestSource = "";
        this.interactionCount = 0;
        this.outboundCallCount = 0;
        this.isPhoneCallback = 0;
        this.firstResponder = "";
        this.firstResponseDuration = 0L;
        this.operatorNames = new HashSet<>();
        this.firstOperationReceiver = "";
    }

    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        // 使用更具可读性的枚举进行判断
        OperationType opType = OperationType.of(currentOp.getOperationType());
        OperatorType operatorType = OperatorType.fromType(currentOp.getOperatorType());
        OperatorPost operatorPost = OperatorPost.of(currentOp.getOperatorPost());

        // 1. 计算经手人 (Operators)
        addOperator(currentOp);

        // 2. 计算首次运维接单人
        findFirstOperationReceiver(currentOp);

        // 3. 计算外呼次数
        calcOutboundCallCount(opType);

        // 4. 计算回复次数
        calcReplyCount(currentOp, operatorType, operatorPost);

        // 5. 计算首次响应信息
        calcFirstResponse(currentOp, opType, operatorType);

        // 6. 查找结单、待客户确认、有效外呼等关键操作，用于后续判断
        // (这部分逻辑移到 calculate 方法中，利用预分组的map进行，效率更高)

        // 7. 计算结单信息
        calcCloseInfo(currentOp, opType, operatorType);

        // 8. 计算转单次数
        if (opType == OperationType.TRANSFER) {
            this.transferCount++;
        }

        // 9. 计算上报信息
        calcReportInfo(currentOp, opType);

        // 10. 计算是否投诉
        if (opType == OperationType.COMPLAINT) {
            this.isComplaint = 1;
        }

        // 11. 计算内部建单信息
        calcInnerCreateInfo(currentOp, opType, operatorType);

        // 12. 计算互动次数 (移到 calculate 方法中，避免 O(N^2) 复杂度)
    }

    @Override
    public void calculate(CalculationContext context) {
        // 在遍历结束后进行需要全局信息的计算

        // 0. 对工单经手人进行去重、去除空白、去除系统操作
        this.operatorNames = this.operatorNames.stream()
                .filter(StringUtils::isNotBlank)
                .filter(op -> !"SYSTEM".equalsIgnoreCase(op))
                .map(String::trim)
                .collect(Collectors.toSet());

        // 1. 计算满意度
        calcSatisfaction(context.getTicketProfile().getServiceRate(), context.getTicketProfile().getUnsatisfyReason());

        // 2. 计算电话回访状态 (isPhoneCallback)
        // 逻辑：如果存在“有效外呼”，且其发生时间晚于最后一次“待客户确认结单”或“结单”操作，则视为电话回访
        List<Operation> closeOps = context.getOperationsByType().getOrDefault(OperationType.CLOSE.getCode(), Collections.emptyList());
        List<Operation> effectiveCallOps = context.getOperationsByType().getOrDefault(OperationType.EFFECTIVE_CALL.getCode(), Collections.emptyList());
        List<Operation> waitCloseOps = context.getOperationsByType().getOrDefault(OperationType.WAIT_CUSTOMER_CLOSE.getCode(), Collections.emptyList());

        if (!effectiveCallOps.isEmpty()) {
            effectiveCallOps.stream()
                    .max(Operation::compareTo)
                    .ifPresent(lastCall -> {
                        boolean afterWait = waitCloseOps.stream()
                                .max(Operation::compareTo)
                                .map(lastWait -> lastCall.getOperateTime().isAfter(lastWait.getOperateTime()))
                                .orElse(false);

                        boolean afterClose = closeOps.stream()
                                .max(Operation::compareTo)
                                .map(lastClose -> lastCall.getOperateTime().isAfter(lastClose.getOperateTime()))
                                .orElse(false);

                        if (afterWait || afterClose) {
                            this.isPhoneCallback = 1;
                        }
                    });
        }

        // 3. 计算首次响应时长
        if (StringUtils.isNotBlank(this.firstResponseTime) && !this.firstResponseTime.equals("1970-01-01 00:00:00")) {
            try {
                this.firstResponseDuration = timeDelta(context.getTicketProfile().getCreateTime(), this.firstResponseTime);
            } catch (Exception e) {
                log.error("Failed to calculate firstResponseDuration for ticketId: {}", context.getTicketProfile().getTicketId(), e);
                this.firstResponseDuration = 0L;
            }
        }

        // 4. 计算互动次数 (优化 O(N^2) -> O(N))
        calcInteractionTimes(context.getTicketProfile().getOperations());
    }


    private String serializeOperatorsToJson() {
        try {
            return objectMapper.writeValueAsString(this.operatorNames);
        } catch (JsonProcessingException e) {
            return "[]"; // 出错时提供默认值
        }
    }

    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        // 将本计算器计算出的所有指标填充到最终的POJO中
        metrics.setCloseTime(this.closedTime);
        metrics.setCloseType(this.closureType);
        metrics.setIsSatisfy(this.isSatisfied);
        metrics.setReplyTimes(this.replyCount);
        metrics.setIsReported(this.isReported);
        metrics.setIsComplaint(this.isComplaint);
        metrics.setLastReporter(this.lastReporter);
        metrics.setOutTimes(this.outboundCallCount);
        metrics.setTransferTimes(this.transferCount);
        metrics.setRequestSource(this.requestSource);
        metrics.setInnerCreator(this.internalCreator);
        metrics.setCloseOperator(this.closedOperator);
        metrics.setAgentReplyTimes(this.agentReplyCount);
        metrics.setOperators(serializeOperatorsToJson());
        metrics.setIsPhoneCallback(this.isPhoneCallback);
        metrics.setStuffCounts(this.operatorNames.size());
        metrics.setInteractionTimes(this.interactionCount);
        metrics.setFirstResponseStuff(this.firstResponder);
        metrics.setLastReportedTime(this.lastReportedTime);
        metrics.setIsInnerCreated(this.isInternallyCreated);
        metrics.setFirstResponseTime(this.firstResponseTime);
        metrics.setCloseOperatorPost(this.closedOperatorPost);
        metrics.setCustomerReplyTimes(this.customerReplyCount);
        metrics.setFirstLineReplyTimes(this.firstLineReplyCount);
        metrics.setSecondLineReplyTimes(this.secondLineReplyCount);
        metrics.setFirstResponseDuration(this.firstResponseDuration);
        metrics.setFirstOperationsReceiveOrderStuff(this.firstOperationReceiver);
    }

    // ============================== 私有辅助方法 ==============================

    private void addOperator(Operation op) {
        // 使用HashSet，自动处理重复值
        this.operatorNames.add(op.getResponsibleName());
        this.operatorNames.add(op.getNextOperatorName());
        this.operatorNames.add(op.getCurrentOperatorName());
        this.operatorNames.add(op.getNextResponsibleName());
    }

    private void findFirstOperationReceiver(Operation op) {
        // 当首次运维接单人未设置且操作岗位是运维时，设置接单人
        if (StringUtils.isEmpty(this.firstOperationReceiver) &&
                (op.getOperatorPost() == OperatorPost.OPERATION.getCode() ||
                        op.getPost() == OperatorPost.OPERATION.getCode())) {
            this.firstOperationReceiver = op.getOperator();
        }
    }

    private void calcOutboundCallCount(OperationType opType) {
        if (opType == OperationType.EFFECTIVE_CALL ||
                opType == OperationType.NONEFFECTIVE_CALL) {
            this.outboundCallCount++;
        }
    }

    private void calcReplyCount(Operation op, OperatorType operatorType, OperatorPost operatorPost) {
        // 只处理有外部回复内容的记录
        if (!isValidContent(op.getExternReply(), "NULL")) {
            return;
        }

        if (operatorType == OperatorType.CUSTOMER_SERVICE && OperationType.of(op.getOperationType()) != OperationType.CREATE) {
            this.replyCount++;
            // 根据岗位细分回复次数
            switch (operatorPost) {
                case FIRST_LINE:
                case OPERATION: // 运维被视为一线
                case PRODUCTION_RESEARCH: // 产研被视为一线
                    this.firstLineReplyCount++;
                    break;
                case SUPPLIER:
                    this.agentReplyCount++;
                    break;
                case SECOND_LINE:
                    this.secondLineReplyCount++;
                    break;
                default:
                    // 其他岗位暂不计数
                    break;
            }
        } else if (OperationType.of(op.getOperationType()) == OperationType.CUSTOMER_REPLY) {
            this.customerReplyCount++;
        }
    }

    private void calcFirstResponse(Operation op, OperationType opType, OperatorType operatorType) {
        // 如果已经找到首次响应，则直接返回
        if (StringUtils.isNotBlank(this.firstResponder)) {
            return;
        }

        boolean isCustomerService = operatorType == OperatorType.CUSTOMER_SERVICE;
        boolean isNotSystem = !"SYSTEM".equalsIgnoreCase(op.getOperatorName());
        boolean hasReply = isValidContent(op.getExternReply(), "NULL");
        boolean isEffectiveCall = opType == OperationType.EFFECTIVE_CALL;
        boolean isNotCreate = opType != OperationType.CREATE;

        if (isCustomerService && isNotSystem && ((hasReply && isNotCreate) || isEffectiveCall)) {
            this.firstResponseTime = formatDateTime(op.getOperateTime());
            this.firstResponder = op.getOperatorName();
        }
    }


    private void calcCloseInfo(Operation op, OperationType opType, OperatorType operatorType) {
        if (opType == OperationType.CLOSE || (opType == OperationType.CREATE && op.getTargetStatus() == TargetStatus.CLOSED.getCode())) {
            this.closedOperator = op.getOperator();
            this.closureType = operatorType.getType();
            this.closedOperatorPost = op.getOperatorPost();
            this.closedTime = formatDateTime(op.getOperateTime());
        }
    }

    private void calcReportInfo(Operation op, OperationType opType) {
        if (opType == OperationType.REPORT_FAILURE) {
            this.isReported = 1;
            this.lastReporter = op.getOperator();
            this.lastReportedTime = formatDateTime(op.getOperateTime());
        }
    }

    private void calcInnerCreateInfo(Operation op, OperationType opType, OperatorType operatorType) {
        // 只在第一次建单操作时执行
        if (opType == OperationType.CREATE && this.isInternallyCreated == -1) {
            if (!"SYSTEM".equalsIgnoreCase(op.getOperatorName())) {
                this.requestSource = op.getRequestSource();
                if (operatorType == OperatorType.CUSTOMER_SERVICE) {
                    this.isInternallyCreated = 1;
                    this.internalCreator = op.getOperatorName();
                } else if (operatorType == OperatorType.CUSTOMER) {
                    this.isInternallyCreated = 0;
                }
            }
        }
    }

    private void calcSatisfaction(long serviceRate, long unsatisfyReason) {
        // 将serviceRate转换为int，因为switch不支持long
        int rate = (int) serviceRate;
        switch (rate) {
            case 5:
                this.isSatisfied = "满意";
                break;
            case 0:
                this.isSatisfied = (unsatisfyReason == 0) ? "未评价" : "不满意";
                break;
            default:
                // 包括了 serviceRate 1-4 和 unsatisfyReason 1-6 的情况
                this.isSatisfied = (unsatisfyReason == 7) ? "满意" : "不满意";
        }
    }

    private void calcInteractionTimes(List<Operation> operations) {
        AtomicInteger count = new AtomicInteger(0);
        AtomicReference<String> lastCustomerServiceReplyTime = new AtomicReference<>("1970-01-01 00:00:00");

        operations.forEach(op -> {
            OperatorType operatorType = OperatorType.fromType(op.getOperatorType());
            String operateTime = formatDateTime(op.getOperateTime());

            // 如果是客户的操作，并且发生在我方客服上次回复之后
            if (operatorType == OperatorType.CUSTOMER && operateTime.compareTo(lastCustomerServiceReplyTime.get()) > 0) {
                // 查找在此客户操作之后，我方客服的首次回复
                operations.stream()
                        .filter(nextOp -> OperatorType.fromType(nextOp.getOperatorType()) == OperatorType.CUSTOMER_SERVICE)
                        .filter(nextOp -> isValidContent(nextOp.getExternReply(), "NULL"))
                        .filter(nextOp -> nextOp.getOperationId() > op.getOperationId())
                        .min(Operation::compareTo)
                        .ifPresent(firstReplyAfterCustomer -> {
                            count.getAndIncrement();
                            // 更新我方客服的最后回复时间，避免重复计数
                            lastCustomerServiceReplyTime.set(formatDateTime(firstReplyAfterCustomer.getOperateTime()));
                        });
            }
        });

        this.interactionCount = count.get();
    }
}