package com.tencent.andata.etl;

import static com.tencent.andata.etl.sql.RiskInfo2SMPKafkaSql.QUERY_RISK_INFO_SQL;
import static com.tencent.andata.etl.tablemap.RiskInfoTableMapping.smartyPgTable2FlinkTable;
import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;
import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static java.util.concurrent.TimeUnit.MINUTES;
import static org.apache.flink.api.common.restartstrategy.RestartStrategies.failureRateRestart;

import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.CDCTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import com.tencent.andata.utils.struct.DatabaseEnum;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class RiskInfoToKafka {

    /**
     * 风控数据写入smp的kafka
     */
    public static void main(String[] args) throws Exception {
        FlinkEnv fEnv = FlinkEnvUtils.getStreamTableEnv(args);
        // 允许对象重用
        fEnv.env()
                .getConfig()
                .enableObjectReuse();

        fEnv.env()
                .setRestartStrategy(failureRateRestart(50,
                        Time.of(1, MINUTES), Time.of(10, MINUTES)));

        // table env config
        setTabEnvConf(fEnv.streamTEnv().getConfig().getConfiguration());

        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        final KVConfBuilder<DatabaseConf> kvConfBuilder =
                new KVConfBuilder<>(DatabaseConf.class).setRainbowUtils(rainbowUtils);
        ParameterTool paraTool = ParameterTool.fromArgs(args);
        String pgDbName = paraTool.get("smartyDbName");
        final DatabaseConf smartyConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", pgDbName))
                .build();

        final DatabaseConf dataWareConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "pgsql", "dataware_r"))
                .build();

        final ObjectMapper mapper = new ObjectMapper();
        StreamTableEnvironment tEnv = fEnv.streamTEnv();
        ArrayNode pgTable2fTableMap = mapper.readValue(smartyPgTable2FlinkTable, ArrayNode.class);


        // 风控相关的表注册成flink table
        buildSourceView(smartyConf, fEnv, pgTable2fTableMap, DatabaseEnum.PGSQL, paraTool);
        StreamSupport.stream(pgTable2fTableMap.spliterator(), false)
                .map(x -> x.get("rdbTable").asText())
                .forEach(table -> tEnv.createTemporaryView(table + "_time_view", tEnv.sqlQuery("SELECT *,PROCTIME() AS process_time FROM " + table + "_view")));
        // 注册成flink table
        TableUtils.registerTable(tEnv, dwmWebimBaseViewDDL(dataWareConf));
        TableUtils.registerTable(tEnv, kafkaTableDDL(paraTool.get("topic"), paraTool.get("serverIp")));

        //JDBCTableDDLFactory pgTableFactory = new JDBCTableDDLFactory("kafka_output_table", PGSQL, smartyConf);
        //TableUtils.registerTable(tEnv, pgTableFactory.createTableDDL("pg_kafka_output_table"));

        Table imOpinion = tEnv.sqlQuery(QUERY_RISK_INFO_SQL);

        tEnv.createTemporaryView("input_view", imOpinion);

        StatementSet stmtSet = fEnv.stmtSet();
        stmtSet.addInsertSql(""
                + "INSERT INTO kafka_output_table "
                + "SELECT id, conversation_id, ticket_id, `statement`, risk_type, risk_level, is_valid, "
                + "is_created_chat FROM input_view WHERE is_annotated = 1");

        stmtSet.execute();

        fEnv.env().execute("Risk Info Sink To SMP");
    }

    private static void setTabEnvConf(Configuration conf) {
        // 限制 taskName 的长度
        conf.setString("pipeline.task-name-length", "25");
        //开启微批模式
        conf.setString("table.exec.mini-batch.size", "5000");
        conf.setString("table.exec.mini-batch.enabled", "true");
        conf.setString("table.exec.mini-batch.allow-latency", "5 s");
        // 状态保留1天
        conf.setString("table.exec.state.ttl", "86400000");
        conf.setString("execution.runtime-mode", "streaming");
        conf.setString("execution.checkpointing.interval", "30s");
        conf.setString("table.exec.sink.not-null-enforcer", "DROP");
        conf.setString("table.exec.sink.upsert-materialize", "NONE");
        conf.setString("table.exec.legacy-cast-behaviour", "enabled");
    }

    private static String dwmWebimBaseViewDDL(DatabaseConf db) {
        return String.format(
                "CREATE TEMPORARY TABLE dwm_webim_base_view\n"
                        + " (\n"
                        + "  `conversation_id` STRING,\n"
                        + "  `conversation_ticket_ids` STRING\n"
                        + ")\n"
                        + "  WITH (\n"
                        + "  'password' = '%s',\n"
                        + "  'connector' = 'jdbc',\n"
                        + "  'table-name' = 'dwm_im_online_customer_service_backend_data',\n"
                        + "  'url' = 'jdbc:postgresql://%s:%s/%s?"
                        + "reWriteBatchedInserts=true&?currentSchema=public&serverTimezone=Asia/Shanghai',\n"
                        + "  'username' = '%s',\n"
                        + "  'lookup.cache.max-rows' = '20000',\n"
                        + "  'lookup.cache.ttl' = '30 minute'\n"
                        + ")",
                db.password,
                db.dbHost,
                db.dbPort,
                db.dbName,
                db.userName);
    }

    private static String kafkaTableDDL(String topic, String serverIp) {
        // -- 创建输出表 - Kafka
        return String.format("CREATE TABLE kafka_output_table (\n"
                + "  `Id`               BIGINT,\n"
                + "  `ConversationId`   STRING,\n"
                + "  `TicketId`         BIGINT,\n"
                + "  `RiskStatement`    STRING,\n"
                + "  `RiskTypes`        STRING,\n"
                + "  `RiskLevel`        STRING,\n"
                + "  `IsEffect`         INT,\n"
                + "  `IsPushGroup`      INT,\n"
                + "  PRIMARY KEY (Id) NOT ENFORCED\n"
                + ") WITH (\n"
                + "  'connector' = 'upsert-kafka',\n"
                + "  'topic' = '%s',\n"
                + "  'properties.bootstrap.servers' = '%s:9092',\n"
                + "  'key.format' = 'json',\n"
                + "  'value.format' = 'json',\n"
                + "  'key.json.ignore-parse-errors' = 'true',\n"
                + "  'value.json.fail-on-missing-field' = 'false'\n"
                + ")\n", topic, serverIp);
    }
}
