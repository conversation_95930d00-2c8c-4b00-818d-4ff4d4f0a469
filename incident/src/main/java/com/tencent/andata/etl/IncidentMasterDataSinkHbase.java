package com.tencent.andata.etl;

import static com.tencent.andata.utils.TableUtils.row2Json;
import static com.tencent.andata.utils.TableUtils.sinkToHbase;
import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;

import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.udf.ToJson;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

@Slf4j

public class IncidentMasterDataSinkHbase {

    private static final String kafkaSinkSql = "CREATE TABLE kafka_sink_tb (\n" +
            "%s" +
            ",\nPRIMARY KEY (%s) NOT ENFORCED\n" +
            ") WITH (\n" +
            "  'connector' = 'upsert-kafka',\n" +
            "  'topic' = '%s',\n" +
            "  'properties.bootstrap.servers' = '%s:9092',\n" +
            "  'properties.max.request.size' = '4194304',\n" +
            "  'properties.batch.size' = '16384',\n" +
            "  'properties.buffer.memory' = '31457280',\n" +
            "  'properties.linger.ms' = '20',\n" +
            "  'key.format' = 'json',\n" +
            "  'value.format' = 'json',\n" +
            "  'value.json.fail-on-missing-field' = 'false'" +
            ");";

    private static final String insertKafkaSql = "INSERT INTO kafka_sink_tb (\n" +
            "SELECT\n" +
            "%s" +
            "\nFROM t202_ticket_operation_filter_view)";

    private static Tuple2<String, String> getMqTblDDLAndInsSQL(String topic, String serverIp, Table tbl, String pk) {
        List<String> columns = tbl.getResolvedSchema().getColumnNames();
        List<String> columnDataTypes = tbl
                .getResolvedSchema()
                .getColumnDataTypes()
                .stream()
                .map(c -> c.getLogicalType().copy(true).toString())
                .collect(Collectors.toList());

        StringBuilder ddlBuilder = new StringBuilder();
        StringBuilder insertBuilder = new StringBuilder();

        for (int i = 0; i < columns.size(); i++) {
            String columnName = columns.get(i);
            String columnType = columnDataTypes.get(i);
            ddlBuilder.append(columnName);
            ddlBuilder.append(" ");
            ddlBuilder.append(columnType);
            ddlBuilder.append(",\n");

            insertBuilder.append(columnName);
            insertBuilder.append(",\n");

        }

        return new Tuple2<>(
                String.format(kafkaSinkSql, StringUtils.stripEnd(ddlBuilder.toString(), ",\n"), pk, topic, serverIp),
                String.format(insertKafkaSql, StringUtils.stripEnd(insertBuilder.toString(), ",\n"))
        );
    }

    /**
     * 工单主数据入 hbase
     */
    public static void main(String[] args) throws Exception {
        Properties properties = PropertyUtils.loadProperties("env.properties");
        FlinkEnv fEnv = FlinkEnvUtils.getStreamTableEnv(args);

        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        final KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(java.lang.String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(""
                        + "["
                        + "{\"rdbTable\":\"t201_ticket\",\"fTable\":\"mysql_source_t201_ticket\"},"
                        + "{\"rdbTable\":\"t202_ticket_operation\",\"fTable\":\"mysql_source_t202_ticket_operation\"}"
                        + "]\n",
                ArrayNode.class);

        ParameterTool parTool = ParameterTool.fromArgs(args);
        buildSourceView(mysqlDBConf, fEnv, mysqlTable2FlinkTableMap, MYSQL, parTool);

        StreamTableEnvironment tEnv = fEnv.streamTEnv();
        StreamExecutionEnvironment env = fEnv.env();

        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                1, // max failures per interval
                Time.of(10, TimeUnit.SECONDS), //time interval for measuring failure rate
                Time.of(3, TimeUnit.SECONDS) // delay
        ));

        // 注册hive udf
        fEnv.hiveModuleV2().registryHiveUDF("to_json", ToJson.class.getName());

        // 工单基础表
        Table t201TicketTable = tEnv.sqlQuery(
                "select *,"
                        + "  'https://andon.woa.com/ticket/detail/?id=' "
                        + "   || CAST(`ticket_id` AS STRING) "
                        + "   || '&sign=' "
                        + "   || MD5(MD5(CAST(`ticket_id` AS STRING) || 'andontcs')) AS url,\n"
                        + "   'https://andon.cloud.tencent.com/ticket/nologin/redirect?id=' "
                        + "   || CAST(`ticket_id` AS STRING) "
                        + "   || '&sign=' "
                        + "   || MD5(MD5(CAST(`ticket_id` AS STRING) || 'andontcs')) AS short_url\n"
                        + " from t201_ticket_view where create_time > '2025-02-10'");

        tEnv.createTemporaryView("t201_ticket_with_url_view", t201TicketTable);

        Table ticketBaseInfoTable = tEnv.sqlQuery(
                row2Json(t201TicketTable, "CAST(`ticket_id` AS STRING)", "t201_ticket_with_url_view"));

        // 工单流水表
        Table t202TicketTable = tEnv.sqlQuery(
                "select * from t202_ticket_operation_view where operate_time > '2025-02-10'");

        tEnv.createTemporaryView("t202_ticket_operation_filter_view", t202TicketTable);
        Table ticketOperationTable = tEnv.sqlQuery(
                row2Json(t202TicketTable,
                        "CAST(`ticket_id` AS STRING) || '-' || CAST(`operation_id` AS STRING)",
                        "t202_ticket_operation_filter_view"));

        // get hbase table name from rainbow
        String ticketBaseInfo = rainbowUtils.getStringValue("cdc.database.hbase", "TICKET_BASE_INFO");
        String ticketOperation = rainbowUtils.getStringValue("cdc.database.hbase", "TICKET_OPERATION");

        // hbase table mapping to flinkTable
        TableUtils.hbaseTable2FlinkTable("h_ticket_base_info", ticketBaseInfo, "cf", tEnv);
        TableUtils.hbaseTable2FlinkTable("h_ticket_operation", ticketOperation, "cf", tEnv);

        Tuple2<String, String> tuple2 =
                getMqTblDDLAndInsSQL(parTool.get("topic"), parTool.get("serverIp"), t202TicketTable, "operation_id");

        log.info("kafkaTableDDL: " + tuple2.f0);
        log.info("kafkaTableInsert: " + tuple2.f1);
        // 注册kafka table
        TableUtils.registerTable(tEnv, tuple2.f0);

        tEnv.createTemporaryView("ticket_base_info_view", ticketBaseInfoTable);
        tEnv.createTemporaryView("ticket_operation_view", ticketOperationTable);
        StatementSet stmtSet = fEnv.stmtSet();
        stmtSet.addInsertSql(sinkToHbase("ticket_base_info_view", "h_ticket_base_info"))
               .addInsertSql(sinkToHbase("ticket_operation_view", "h_ticket_operation"))
               .addInsertSql(tuple2.f1);

        stmtSet.execute();

        // 配置
        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS)));

        env.getConfig().enableObjectReuse();

        Configuration configuration = tEnv.getConfig().getConfiguration();
        configuration.setString("pipeline.task-name-length", "25");
        configuration.setString("table.exec.state.ttl", "259200000");
        configuration.setString("table.exec.sink.upsert-materialize", "NONE");
        configuration.setString("table.optimizer.reuse-source-enabled", "true");
        configuration.setString("table.optimizer.reuse-sub-plan-enabled", "true");
        configuration.setString("table.optimizer.multiple-input-enabled", "true");
        configuration.setString("table.exec.simplify-operator-name-enabled", "true");
        configuration.setString("pipeline.name", "Incident Master Data Sink To Hbase");
        configuration.setString("table.optimizer.source.aggregate-pushdown-enabled", "true");
        configuration.setString("table.optimizer.source.predicate-pushdown-enabled", "true");

        env.execute("Incident Master Data Sink To Hbase");
    }
}