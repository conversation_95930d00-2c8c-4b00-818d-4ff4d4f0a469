package com.tencent.andata.etl;

import static com.tencent.andata.utils.ExceptionWrapperUtil.consumer;

import com.tencent.andata.etl.factory.ApplicationStrategyFactory.ApplicationStrategy;
import com.tencent.andata.etl.factory.ApplicationStrategyFactory.SubApplication;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.FlinkTableConf;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.NoBlockCacheRocksDbOptionsFactory;
import com.tencent.andata.utils.udf.SimpleStringTransform;
import com.tencent.andata.utils.udf.ToJson;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.restartstrategy.RestartStrategies.FailureRateRestartStrategyConfiguration;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class Application {

    private static IcebergCatalogReader catalog = new IcebergCatalogReader();
    private static FailureRateRestartStrategyConfiguration failureRateRestart = RestartStrategies
            .failureRateRestart(
                    50, // max failures per interval
                    Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                    Time.of(30, TimeUnit.SECONDS)); // delay

    /**
     * 在一个main方法中运行多个flink etl 任务
     *
     * @param args args[0] = iceberg db name, args[1] = pg db name
     */
    public static void main(String[] args) throws Exception {
        EmbeddedRocksDBStateBackend stateBackend = new EmbeddedRocksDBStateBackend(true);
        stateBackend.setRocksDBOptions(new NoBlockCacheRocksDbOptionsFactory());

        FlinkEnv fEnv = FlinkEnvUtils.getStreamTableEnv(args);
        final StreamTableEnvironment tEnv = fEnv.streamTEnv();
        fEnv.env().setRestartStrategy(failureRateRestart);
        fEnv.env().setStateBackend(stateBackend);

        // tEnvConf
        Configuration configuration = new FlinkTableConf()
                .setEnv(tEnv)
                .setConf("pipeline.task-name-length", "25")
                .build();

        // get iceberg db name and pg db name from args
        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        String pgDbName = parameterTool.get("pgDbName");
        String icebergDbName = parameterTool.get("icebergDbName");
        String subApplicationStr = parameterTool.get("subApplication");
        List<SubApplication> subAppList = Arrays.stream(subApplicationStr.split(","))
                .map(SubApplication::valueOf)
                .collect(Collectors.toList());
        // 注册hive udf
        fEnv.hiveModuleV2().registryHiveUDF("to_json", ToJson.class.getName());
        // 注册自定义udf
        fEnv.streamTEnv().createTemporaryFunction("simple_string_trans", SimpleStringTransform.class);

        // instantiate the ETL

        List<Object> appList = new ArrayList<>();
        for (SubApplication subApp : subAppList) {
            ApplicationStrategy applicationStrategy = subApp.getStrategy();
            applicationStrategy.setup(configuration, appList, icebergDbName, pgDbName);
            configuration.setString("pipeline.name", applicationStrategy.getPipelineName());

        }

        // 遍历appList，调用run方法
        appList.forEach(consumer(app -> MethodUtils.invokeMethod(app, "run", fEnv, catalog)));

        // execute the SQL statements
        fEnv.stmtSet().execute();
        fEnv.env().execute("Flink ETL Application");
    }
}