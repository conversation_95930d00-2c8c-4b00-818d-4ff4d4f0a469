package com.tencent.andata.etl.calculator.calculators;

import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.calculator.IMetricCalculator;
import com.tencent.andata.etl.calculator.calculators.transfer.HotlineTransferCalculator;
import com.tencent.andata.etl.calculator.calculators.transfer.ITransferSubCalculator;
import com.tencent.andata.etl.calculator.calculators.transfer.OperatorTrackingCalculator;
import com.tencent.andata.etl.calculator.calculators.transfer.TransferMetricsCalculator;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 重构后的转单指标计算器（方案一：模块化重构）
 * <p>
 * 采用组合模式，将原来庞大的TransferCalculator拆分为多个专门的子计算器：
 * - TransferMetricsCalculator：转单次数和类型计算
 * - OperatorTrackingCalculator：操作人跟踪计算
 * - HotlineTransferCalculator：热线转单特殊逻辑
 * </p>
 * <p>
 * 优势：
 * 1. 符合单一职责原则，每个子计算器职责明确
 * 2. 便于单元测试和维护
 * 3. 支持并行开发和扩展
 * 4. 降低代码复杂度
 * </p>
 * <p>
 * 对应原 TransferCalculator 的重构版本。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class TransferCalculatorRefactored implements IMetricCalculator<CalculationContext> {

    /**
     * 子计算器列表
     * 按照依赖关系和执行顺序排列
     */
    private final List<ITransferSubCalculator> subCalculators;

    /**
     * 构造函数，初始化所有子计算器
     */
    public TransferCalculatorRefactored() {
        this.subCalculators = Arrays.asList(
                new TransferMetricsCalculator(),    // 转单指标计算
                new OperatorTrackingCalculator(),   // 操作人跟踪计算
                new HotlineTransferCalculator()     // 热线转单计算
        );
        
        log.info("TransferCalculatorRefactored initialized with {} sub-calculators", subCalculators.size());
    }

    @Override
    public void initialize(CalculationContext context) {
        log.debug("Initializing TransferCalculatorRefactored for ticket: {}", context.getTicketProfile().getTicketId());
        
        // 初始化所有子计算器
        for (ITransferSubCalculator subCalculator : subCalculators) {
            try {
                subCalculator.initialize(context);
                log.trace("Initialized sub-calculator: {}", subCalculator.getName());
            } catch (Exception e) {
                log.error("Failed to initialize sub-calculator: {}", subCalculator.getName(), e);
                throw new RuntimeException("Failed to initialize sub-calculator: " + subCalculator.getName(), e);
            }
        }
    }

    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        log.trace("Processing operation {} for ticket: {}", currentOp.getOperationId(), context.getTicketProfile().getTicketId());
        
        // 让所有子计算器处理当前操作
        for (ITransferSubCalculator subCalculator : subCalculators) {
            try {
                subCalculator.processOperation(context, currentOp, previousOp, nextOp);
            } catch (Exception e) {
                log.error("Sub-calculator {} failed to process operation {}", 
                         subCalculator.getName(), currentOp.getOperationId(), e);
                // 继续处理其他子计算器，不中断整个流程
            }
        }
    }

    @Override
    public void calculate(CalculationContext context) {
        log.debug("Executing final calculations for ticket: {}", context.getTicketProfile().getTicketId());
        
        // 让所有子计算器执行最终计算
        for (ITransferSubCalculator subCalculator : subCalculators) {
            try {
                subCalculator.calculate(context);
                log.trace("Completed final calculation for sub-calculator: {}", subCalculator.getName());
            } catch (Exception e) {
                log.error("Sub-calculator {} failed to execute final calculation", subCalculator.getName(), e);
                // 继续处理其他子计算器，不中断整个流程
            }
        }
    }

    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        log.debug("Populating metrics for ticket: {}", context.getTicketProfile().getTicketId());
        
        // 让所有子计算器填充指标
        for (ITransferSubCalculator subCalculator : subCalculators) {
            try {
                subCalculator.populateMetrics(metrics, context);
                log.trace("Populated metrics for sub-calculator: {}", subCalculator.getName());
            } catch (Exception e) {
                log.error("Sub-calculator {} failed to populate metrics", subCalculator.getName(), e);
                // 继续处理其他子计算器，不中断整个流程
            }
        }
        
        log.debug("Completed metrics population for ticket: {}", context.getTicketProfile().getTicketId());
    }

    /**
     * 获取子计算器列表（用于测试和调试）
     */
    public List<ITransferSubCalculator> getSubCalculators() {
        return subCalculators;
    }

    /**
     * 获取指定类型的子计算器（用于测试和调试）
     */
    @SuppressWarnings("unchecked")
    public <T extends ITransferSubCalculator> T getSubCalculator(Class<T> calculatorClass) {
        return subCalculators.stream()
                .filter(calc -> calculatorClass.isInstance(calc))
                .map(calc -> (T) calc)
                .findFirst()
                .orElse(null);
    }
}
