package com.tencent.andata.etl.calculator.calculators;


import static com.tencent.andata.etl.enums.OperationType.AGREE_CLOSE_APPLICATION;
import static com.tencent.andata.etl.enums.OperationType.BATCH_REPLY_FAILURE;
import static com.tencent.andata.etl.enums.OperationType.CANCEL;
import static com.tencent.andata.etl.enums.OperationType.CLOSE;
import static com.tencent.andata.etl.enums.OperationType.CLOSE_APPLICATION;
import static com.tencent.andata.etl.enums.OperationType.NOT_RESTORED;
import static com.tencent.andata.etl.enums.OperationType.PULL;
import static com.tencent.andata.etl.enums.OperationType.TO_CONFIRM_RESTORE;
import static com.tencent.andata.etl.enums.OperationType.WAIT_CUSTOMER_ADD_INFO;
import static com.tencent.andata.etl.enums.OperationType.WAIT_CUSTOMER_CLOSE;
import static com.tencent.andata.etl.enums.OperationType.of;

import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.calculator.IMetricCalculator;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorType;
import com.tencent.andata.etl.enums.TargetStatus;
import com.tencent.andata.utils.TimeUtil;
import io.vavr.Function1;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;


/**
 * 工单解决时长计算器
 * <p>
 * 根据工单操作记录计算解决时间，支持已关闭和未关闭工单的时间计算。
 * 计算逻辑基于结单操作类型(客户/客服/系统)和相关操作序列确定最终解决时间点。
 *
 * <h3>计算规则</h3>
 * <h4>结单类型识别</h4>
 * <ul>
 * <li>客户结单(operatorType=1): 客户主动关闭工单</li>
 * <li>客服结单(operatorType=2): 客服主动关闭工单</li>
 * <li>系统结单(operatorType=3): 系统自动关闭工单</li>
 * </ul>
 *
 * <h4>已关闭工单解决时间计算</h4>
 * <ul>
 * <li><b>客服结单:</b> 直接取结单操作时间</li>
 * <li><b>系统/客户结单:</b> 优先查找"待客户确认结单"操作，根据该操作的执行者类型进行分支处理：
 *   <ul>
 *   <li>系统执行: 取最近的"待客户补充/待确认业务恢复/同意申请结单"操作时间</li>
 *   <li>客服执行: 根据最近操作类型决定：
 *     <ul>
 *     <li>待确认业务恢复: 检查是否存在"未恢复"状态，决定取业务恢复时间或确认结单时间</li>
 *     <li>待客户补充: 检查前序是否有"工单回访"认领，决定取补充时间或确认结单时间</li>
 *     <li>同意申请结单: 取待确认结单时间</li>
 *     </ul>
 *   </li>
 *   </ul>
 * </li>
 * <li><b>兜底处理:</b> 若无"待客户确认结单"操作，则查找替代操作(同意申请结单/批量回复/撤销/业务恢复/客户补充)取最新时间</li>
 * </ul>
 *
 * <h4>未关闭工单解决时间计算</h4>
 * <ul>
 * <li>应用与已关闭工单相同的"待客户确认结单"处理逻辑</li>
 * <li>兜底策略: 查找撤销操作或取当前统计时间点</li>
 * </ul>
 *
 * <h4>特殊规则</h4>
 * <ul>
 * <li>排除非核心操作: 评价、评论、转需求单等操作不参与计算</li>
 * <li>同意申请结单(待确认)时取对应的申请结单操作时间</li>
 * <li>统计"待客户确认结单"操作次数用于指标计算</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @see OperationType 操作类型枚举
 * @see TargetStatus 目标状态枚举
 * @see CalculationContext 计算上下文
 * @since 2025.07
 */
public class SolvedTimeCalculator implements IMetricCalculator<CalculationContext> {

    /**
     * 工单解决时间，格式为"yyyy-MM-dd HH:mm:ss"
     */
    private String ticketSolvedTime;
    /**
     * 待客户确认结单次数统计
     */
    private int confirmCloseCount;

    /**
     * 初始化计算器状态
     *
     * @param context 计算上下文对象
     */
    @Override
    public void initialize(CalculationContext context) {
        this.ticketSolvedTime = "1970-01-01 00:00:00";
        this.confirmCloseCount = 0;
    }

    /**
     * 处理单个操作记录，统计待确认结单次数
     *
     * @param context 计算上下文
     * @param currentOp 当前操作记录
     * @param previousOp 前一个操作记录(未使用)
     * @param nextOp 下一个操作记录(未使用)
     */
    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        OperationType opType = of(currentOp.getOperationType());
        OperatorType operatorType = OperatorType.fromType(currentOp.getOperatorType());

        if (operatorType == OperatorType.CUSTOMER_SERVICE && opType == WAIT_CUSTOMER_CLOSE) {
            // 客服待客户确认结单，待确认结单次数+1
            this.confirmCloseCount++;
        }

        if (operatorType == OperatorType.CUSTOMER_SERVICE && opType == AGREE_CLOSE_APPLICATION
                && currentOp.getTargetStatus() == TargetStatus.CLOSE_CONFIRMATION_PENDING.getCode()) {
            // 客服同意申请结单，待确认结单次数+1
            this.confirmCloseCount++;
        }
    }

    /**
     * 计算工单解决时间
     * <p>
     * 主要逻辑：优先基于结单操作计算已关闭工单时间，否则计算未关闭工单时间
     *
     * @param context 计算上下文，包含分类的操作记录
     */
    @Override
    public void calculate(CalculationContext context) {
        Map<Integer, List<Operation>> opsByType = context.getOperationsByType();

        Optional<Operation> finalCloseOp = opsByType.getOrDefault(CLOSE.getCode(), Collections.emptyList())
                .stream()
                .filter(x -> !(x.getOperatorType() != OperatorType.SYSTEM.getType() && Objects.equals(x.getOperator(), "SYSTEM")))
                .max(Operation::compareTo);

        LocalDateTime solvedTime;

        solvedTime = finalCloseOp
                .map(event -> calculateClosedTicketTime(opsByType, event))
                .orElseGet(() -> {
                    Operation virtualCloseOp = Operation.builder()
                            .operatorType(OperatorType.CUSTOMER.getType())
                            .operateTime(LocalDateTime.parse("1970-01-01T00:00:00"))
                            .operationId(Long.MAX_VALUE)
                            .build();
                    return calculateOpenTicketTime(opsByType, virtualCloseOp);
                });

        this.ticketSolvedTime = TimeUtil.formatDateTime(solvedTime);
    }

    /**
     * 计算已关闭工单的解决时间
     * <p>
     * 根据结单操作的执行者类型(系统/客户/客服)采用不同的计算策略
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param closeOp 结单操作记录
     * @return 计算得出的解决时间
     */
    private LocalDateTime calculateClosedTicketTime(Map<Integer, List<Operation>> opsByType, Operation closeOp) {
        switch (OperatorType.fromType(closeOp.getOperatorType())) {
            case SYSTEM:
                return calculateSystemCloseTime(opsByType, closeOp);
            case CUSTOMER:
                return calculateCustomerCloseTime(opsByType, closeOp);
            case CUSTOMER_SERVICE:
            default:
                return closeOp.getOperateTime();
        }
    }

    /**
     * 计算未关闭工单的解决时间
     * <p>
     * 使用虚拟结单操作应用替代时间计算逻辑
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param virtualCloseOp 虚拟结单操作(用于边界处理)
     * @return 计算得出的解决时间
     */
    private LocalDateTime calculateOpenTicketTime(Map<Integer, List<Operation>> opsByType, Operation virtualCloseOp) {
        return calculateFallbackSolvedTime(opsByType, virtualCloseOp);
    }

    /**
     * 计算系统结单的解决时间
     * <p>
     * 优先查找"待客户确认结单"操作，若存在则基于该操作计算时间，否则使用替代逻辑
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param closeOp 系统结单操作记录
     * @return 计算得出的解决时间
     */
    private LocalDateTime calculateSystemCloseTime(Map<Integer, List<Operation>> opsByType, Operation closeOp) {
        return calculateConfirmBasedCloseTime(opsByType, closeOp, true);
    }

    /**
     * 计算客户结单的解决时间
     * <p>
     * 基于"待客户确认结单"操作计算时间
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param closeOp 客户结单操作记录
     * @return 计算得出的解决时间
     */
    private LocalDateTime calculateCustomerCloseTime(Map<Integer, List<Operation>> opsByType, Operation closeOp) {
        return calculateConfirmBasedCloseTime(opsByType, closeOp, false);
    }

    /**
     * 基于确认结单操作计算解决时间
     * <p>
     * 统一处理系统结单和客户结单的逻辑，减少代码重复
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param closeOp 结单操作记录
     * @param useFallbackMethod 是否在无确认操作时使用替代逻辑
     * @return 计算得出的解决时间
     */
    private LocalDateTime calculateConfirmBasedCloseTime(Map<Integer, List<Operation>> opsByType, Operation closeOp, boolean useFallbackMethod) {
        return findOpBefore(opsByType, WAIT_CUSTOMER_CLOSE, closeOp)
                .map(op -> calculateTimeFromLastConfirm(opsByType, closeOp, op))
                .orElseGet(() -> useFallbackMethod ? calculateFallbackSolvedTime(opsByType, closeOp) : closeOp.getOperateTime());
    }

    /**
     * 基于最后确认结单操作计算解决时间
     * <p>
     * 根据确认结单操作的执行者类型(系统/客服)采用不同的时间计算策略
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param closeOp 结单操作记录
     * @param confirmOp 最后一次"待客户确认结单"操作，可为null
     * @return 计算得出的解决时间
     */
    private LocalDateTime calculateTimeFromLastConfirm(Map<Integer, List<Operation>> opsByType, Operation closeOp, Operation confirmOp) {
        return Optional.ofNullable(confirmOp).map(x -> {
            OperatorType operatorType = OperatorType.fromType(x.getOperatorType());
            switch (operatorType) {
                case SYSTEM:
                    return calculateSystemTime(opsByType, confirmOp);
                case CUSTOMER_SERVICE:
                    return calculateServiceTime(opsByType, confirmOp);
                default:
                    return null;
            }
        }).orElse(closeOp.getOperateTime());
    }

    /**
     * 计算系统操作的解决时间
     * <p>
     * 查找最近的候选操作(待客户补充/待确认业务恢复/同意申请结单)，取最新时间作为解决时间
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param confirmOp 最后一次"待客户确认结单"操作
     * @return 计算得出的解决时间
     */
    private LocalDateTime calculateSystemTime(Map<Integer, List<Operation>> opsByType, Operation confirmOp) {
        CandidateOps candidates = findCandidates(opsByType, confirmOp);
        return Stream.of(candidates.addInfo, candidates.restore, candidates.agreeClose)
                .filter(Objects::nonNull)
                .max(Operation::compareTo)
                .orElse(confirmOp)
                .getOperateTime();
    }

    /**
     * 计算客服操作的解决时间
     * <p>
     * 根据最近操作类型(业务恢复/补充信息/同意申请结单)采用不同的时间计算策略
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param confirmOp 最后一次"待客户确认结单"操作
     * @return 计算得出的解决时间
     */
    private LocalDateTime calculateServiceTime(Map<Integer, List<Operation>> opsByType, Operation confirmOp) {
        CandidateOps candidates = findCandidates(opsByType, confirmOp);

        if (candidates.isEmpty()) {
            return confirmOp.getOperateTime();
        }

        // 根据最新操作类型直接计算时间
        switch (candidates.getLatestType()) {
            case "restore":
                // 检查是否存在"未恢复"状态
                boolean hasNotRestored = !opsByType.getOrDefault(NOT_RESTORED.getCode(), Collections.emptyList()).isEmpty();
                return hasNotRestored ? confirmOp.getOperateTime() : candidates.restore.getOperateTime();

            case "addInfo":
                // 检查前序是否有"工单回访"认领
                boolean hasClaimVisit = opsByType.getOrDefault(PULL.getCode(), Collections.emptyList()).stream()
                        .anyMatch(op -> op.getOperationId() < confirmOp.getOperationId()
                                && StringUtils.contains(op.getRemark(), "认领原因:工单回访"));
                return hasClaimVisit ? candidates.addInfo.getOperateTime() : confirmOp.getOperateTime();

            default:
                return confirmOp.getOperateTime();
        }
    }

    /**
     * 计算兜底解决时间
     * <p>
     * 用于无"待客户确认结单"操作或未关闭工单的兜底时间计算，查找候选操作取最新时间
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param closeOp 参考操作记录(结单操作或虚拟操作)
     * @return 计算得出的解决时间
     */
    private LocalDateTime calculateFallbackSolvedTime(Map<Integer, List<Operation>> opsByType, Operation closeOp) {
        // 查找基础候选操作
        FallbackCandidates candidates = findFallbackCandidates(opsByType, closeOp);

        // 从候选操作中选择最新的作为解决时间
        return selectLatestSolvedTime(candidates, closeOp);
    }

    /**
     * 查找兜底候选操作
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param closeOp 参考操作记录
     * @return 候选操作集合
     */
    private FallbackCandidates findFallbackCandidates(Map<Integer, List<Operation>> opsByType, Operation closeOp) {
        // 基础操作：撤销、业务恢复、补充信息
        Operation lastCancelOp = findOpBefore(opsByType, CANCEL, closeOp).orElse(null);
        Operation lastRestoreOp = findOpBefore(opsByType, TO_CONFIRM_RESTORE, closeOp).orElse(null);
        Operation lastAddInfoOp = findOpBefore(opsByType, WAIT_CUSTOMER_ADD_INFO, closeOp).orElse(null);

        // 特殊状态操作：同意申请结单、故障单批量回复
        Operation lastAgreeCloseOp = findSpecialStatusOp(
                opsByType, AGREE_CLOSE_APPLICATION, closeOp, TargetStatus.CLOSE_CONFIRMATION_PENDING.getCode()).orElse(null);

        Operation lastBatchReplyOp = findSpecialStatusOp(
                opsByType, BATCH_REPLY_FAILURE, closeOp, TargetStatus.CLOSE_CONFIRMATION_PENDING.getCode()).orElse(null);

        // 对应的申请结单操作
        Operation correspondingApplyCloseOp = findLastOp(
                opsByType.getOrDefault(CLOSE_APPLICATION.getCode(), Collections.emptyList()),
                CLOSE_APPLICATION,
                op -> op.getOperationId() < (lastAgreeCloseOp != null ? lastAgreeCloseOp.getOperationId() : closeOp.getOperationId())).orElse(null);

        return new FallbackCandidates(lastCancelOp, lastRestoreOp, lastAddInfoOp,
                lastAgreeCloseOp, lastBatchReplyOp, correspondingApplyCloseOp);
    }

    /**
     * 查找具有特定状态的操作
     */
    private Optional<Operation> findSpecialStatusOp(Map<Integer, List<Operation>> opsByType, OperationType operationType, Operation closeOp, Integer targetStatus) {
        return findLastOp(opsByType.getOrDefault(operationType.getCode(), Collections.emptyList()), operationType,
                op -> op.getOperationId() < closeOp.getOperationId() && op.getTargetStatus() == targetStatus);
    }

    /**
     * 从候选操作中选择最新的解决时间
     */
    private LocalDateTime selectLatestSolvedTime(FallbackCandidates candidates, Operation closeOp) {
        List<Operation> validCandidates = candidates.getAllValidOperations();

        return validCandidates.stream()
                .max(Operation::compareTo)
                .map(latestOp -> getResolvedTime(latestOp, candidates))
                .orElse(closeOp.getOperateTime());
    }

    /**
     * 获取操作的实际解决时间（对同意申请结单有特殊处理）
     */
    private LocalDateTime getResolvedTime(Operation operation, FallbackCandidates candidates) {
        // 如果是同意申请结单操作，优先使用对应的申请结单时间
        if (candidates.lastAgreeCloseOp != null &&
                operation.getOperationId() == candidates.lastAgreeCloseOp.getOperationId()) {
            return candidates.correspondingApplyCloseOp != null
                    ? candidates.correspondingApplyCloseOp.getOperateTime()
                    : operation.getOperateTime();
        }
        return operation.getOperateTime();
    }

    /**
     * 查找指定类型的最后操作
     * <p>
     * 过滤掉排除的操作类型，按指定类型和条件查找最后的操作记录
     *
     * @param operations 待查找的操作列表
     * @param type 目标操作类型
     * @param predicate 过滤条件谓词
     * @return 符合条件的最后操作，可能为空
     */
    private Optional<Operation> findLastOp(List<Operation> operations, OperationType type, Function1<Operation, Boolean> predicate) {
        return operations.stream()
                .filter(x -> !Operation.EXCLUDE_OPERATION_TYPES.contains(of(x.getOperationType())))
                .filter(x -> of(x.getOperationType()) == type)
                .filter(predicate::apply)
                .max(Operation::compareTo);
    }

    /**
     * 查找指定类型在参考操作之前的最后操作
     * <p>
     * 简化重复的操作查找逻辑
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param type 目标操作类型
     * @param refOp 参考操作(查找在此操作之前的记录)
     * @return 符合条件的最后操作，可能为空
     */
    private Optional<Operation> findOpBefore(Map<Integer, List<Operation>> opsByType, OperationType type, Operation refOp) {
        return findLastOp(opsByType.getOrDefault(type.getCode(), Collections.emptyList()),
                type, op -> op.getOperationId() < refOp.getOperationId());
    }

    /**
     * 查找候选操作详情(用于客服操作时间计算)
     *
     * @param opsByType 按操作类型分类的操作记录映射
     * @param refOp 参考操作(用于时间比较)
     * @return 候选操作详情对象
     */
    private CandidateOps findCandidates(Map<Integer, List<Operation>> opsByType, Operation refOp) {
        Optional<Operation> addInfo = findOpBefore(opsByType, WAIT_CUSTOMER_ADD_INFO, refOp);
        Optional<Operation> restore = findOpBefore(opsByType, TO_CONFIRM_RESTORE, refOp);

        Optional<Operation> agreeClose = findLastOp(opsByType.getOrDefault(AGREE_CLOSE_APPLICATION.getCode(), Collections.emptyList()),
                AGREE_CLOSE_APPLICATION, op -> op.getOperationId() < refOp.getOperationId()
                        && op.getTargetStatus() == TargetStatus.TO_BE_ADDED_BY_CUSTOMER.getCode());

        return new CandidateOps(addInfo.orElse(null), restore.orElse(null), agreeClose.orElse(null));
    }

    /**
     * 填充计算结果到指标对象
     *
     * @param metrics 工单统计指标对象
     * @param context 计算上下文(未使用)
     */
    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        metrics.setTicketSolvedTime(this.ticketSolvedTime);
        metrics.setToBeConfirmCloseTimes(this.confirmCloseCount);
    }

    /**
     * 兜底候选操作封装类
     */
    private static class FallbackCandidates {

        final Operation lastCancelOp;
        final Operation lastRestoreOp;
        final Operation lastAddInfoOp;
        final Operation lastAgreeCloseOp;
        final Operation lastBatchReplyOp;
        final Operation correspondingApplyCloseOp;

        FallbackCandidates(Operation lastCancelOp,
                Operation lastRestoreOp,
                Operation lastAddInfoOp,
                Operation lastAgreeCloseOp,
                Operation lastBatchReplyOp,
                Operation correspondingApplyCloseOp) {
            this.lastCancelOp = lastCancelOp;
            this.lastRestoreOp = lastRestoreOp;
            this.lastAddInfoOp = lastAddInfoOp;
            this.lastAgreeCloseOp = lastAgreeCloseOp;
            this.lastBatchReplyOp = lastBatchReplyOp;
            this.correspondingApplyCloseOp = correspondingApplyCloseOp;
        }

        List<Operation> getAllValidOperations() {
            return Stream.of(lastAgreeCloseOp, lastBatchReplyOp, lastCancelOp, lastRestoreOp, lastAddInfoOp)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 候选操作封装类
     */
    private static class CandidateOps {

        final Operation addInfo;
        final Operation restore;
        final Operation agreeClose;

        CandidateOps(Operation addInfo, Operation restore, Operation agreeClose) {
            this.addInfo = addInfo;
            this.restore = restore;
            this.agreeClose = agreeClose;
        }

        boolean isEmpty() {
            return addInfo == null && restore == null && agreeClose == null;
        }

        String getLatestType() {
            Operation latest = Stream.of(addInfo, restore, agreeClose)
                    .filter(Objects::nonNull)
                    .max(Operation::compareTo)
                    .orElse(null);

            if (latest == addInfo) {return "addInfo";}
            if (latest == restore) {return "restore";}
            if (latest == agreeClose) {return "agreeClose";}
            return "";
        }
    }
}