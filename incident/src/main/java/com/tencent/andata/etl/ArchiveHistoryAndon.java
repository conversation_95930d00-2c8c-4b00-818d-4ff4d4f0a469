package com.tencent.andata.etl;

import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.MYSQL;

import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.FlinkTableConf;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

// 归档andon数据
public class ArchiveHistoryAndon {

    public static void main(String[] args) throws Exception {
        FlinkEnv fEnv = FlinkEnvUtils.getStreamTableEnv(args);
        final StreamTableEnvironment tEnv = fEnv.streamTEnv();

        fEnv.env().setRestartStrategy(RestartStrategies.failureRateRestart(
                50, // max failures per interval
                Time.of(2, TimeUnit.MINUTES), //time interval for measuring failure rate
                Time.of(10, TimeUnit.SECONDS) // delay
        ));

        fEnv.env().getConfig().disableForceKryo();
        fEnv.env().getConfig().enableObjectReuse();
        final IcebergCatalogReader catalog = new IcebergCatalogReader();

        // tEnvConf
        Configuration configuration = new FlinkTableConf()
                .setEnv(tEnv)
                .setConf("pipeline.task-name-length", "25")
                .build();

        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "message"))
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(
                "["
                        + "{\"rdbTable\":\"records\",\"fTable\":\"mysql_source_records\"}"
                        + "]",
                ArrayNode.class);

        // Properties cdcProperties = new Properties();
        // 需要过滤的 oplog 操作。操作包括 c 表示插入，u 表示更新，d 表示删除。默认情况下，不跳过任何操作，以逗号分隔
        // cdcProperties.put("debezium.skipped.operations", "d");
        // TableUtils.rdbTable2FlinkTable(mysqlDBConf, mysqlTable2FlinkTableMap, DatabaseEnum.MYSQL, tEnv, 
        // cdcProperties);

        ParameterTool parTool = ParameterTool.fromArgs(args);
        buildSourceView(mysqlDBConf, fEnv, mysqlTable2FlinkTableMap, MYSQL, parTool);

        // iceberg table mapping to flink sink table
        TableUtils.icebergTable2FlinkTable(
                "andata_rt",
                mapper.readValue(
                        "[\n"
                                + "{\n"
                                + "\"icebergTable\": \"dwd_notification_records\",\n"
                                + "\"fTable\": \"iceberg_sink_dwd_notification_records\",\n"
                                + "\"primaryKey\":\"id,create_time\"\n"
                                + "}"
                                + "]", ArrayNode.class), tEnv, catalog);


        StatementSet stmtSet = fEnv.stmtSet();
        stmtSet.addInsertSql(insertIntoSql(
                "records_view",
               "iceberg_sink_dwd_notification_records",
                tEnv.from("iceberg_sink_dwd_notification_records"),
                ICEBERG));

        fEnv.stmtSet().execute();
        fEnv.env().execute("messageRecord-archive");
    }
}