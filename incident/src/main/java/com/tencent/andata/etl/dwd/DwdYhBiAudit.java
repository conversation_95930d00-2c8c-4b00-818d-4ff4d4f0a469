package com.tencent.andata.etl.dwd;


import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;

import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.ddl.FlinkTableDDL;
import com.tencent.andata.utils.ddl.strategy.IcebergTableBuilderStrategy;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class DwdYhBiAudit {

    private static final Logger logger = LoggerFactory.getLogger(DwdYhBiAudit.class);
    private final String icebergDbName;
    private final String pgDbName;

    /**
     * run the DWD ETL
     *
     * @param flinkEnv flink运行环境工具类
     * @param catalog  iceberg catalog
     */
    public void run(FlinkEnv flinkEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = flinkEnv.streamTEnv();

        ObjectMapper mapper = new ObjectMapper();

        Properties properties = PropertyUtils.loadProperties("env.properties");

        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        DatabaseConf pgDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "postgresql", this.pgDbName))
                .build();

        String pgTableDDL = "CREATE TABLE pg_source_gactions (\n"
                + "  `id` BIGINT,\n"
                + "  `GUSER` STRING,\n"
                + "  `IP` STRING,\n"
                + "  `GROUP` STRING,\n"
                + "  `ACTIONTIME` TIMESTAMP,\n"
                + "  `ACTIONOBJECTTYPE` STRING,\n"
                + "  `ACTIONOBJECT` STRING,\n"
                + "  `ACTIONTYPE` STRING,\n"
                + "  `DETAILS` STRING,\n"
                + "  `VISIBLE` BOOLEAN,\n"
                + "  PRIMARY KEY (id) NOT ENFORCED\n"
                + ") WITH (\n"
                + "  'connector' = 'postgres-cdc',\n"
                + "  'hostname' = '" + pgDBConf.dbHost + "',\n"
                + "  'port' = '" + pgDBConf.dbPort + "',\n"
                + "  'username' = '" + pgDBConf.userName + "',\n"
                + "  'password' = '" + pgDBConf.password + "',\n"
                + "  'database-name' = 'yh_monitor_data',\n"
                + "  'schema-name' = 'public',\n"
                + "  'table-name' = 'GACTIONS'\n"
                + ")";

        tEnv.executeSql(pgTableDDL);


        // 注册Iceberg Table
        org.apache.iceberg.Table yhbiIcebergTable = catalog.getTableInstance(
                this.icebergDbName, "dwd_yh_bi_audit");

        IcebergTableBuilderStrategy yhbiIcebergTableBuilderStrategy = new IcebergTableBuilderStrategy(
                yhbiIcebergTable
        ).primaryKeyName("id").writeUpsertEnabled("true");

        String yhbiTableDDL = FlinkTableDDL.builder()
                .flinkTableName("iceberg_sink_dwd_yh_bi_audit")
                .tableBuilderStrategy(yhbiIcebergTableBuilderStrategy)
                .build();

        TableUtils.registerTable(tEnv, yhbiTableDDL);

        StatementSet stmtSet = flinkEnv.stmtSet();

        String queryPgTableSQL = "SELECT\n"
                + "    `id` AS id,\n"
                + "    `GUSER` AS user_id,\n"
                + "    `IP` AS ip,\n"
                + "    `GROUP` AS `group`,\n"
                + "    `ACTIONTIME` AS action_time,\n"
                + "    `ACTIONOBJECTTYPE` AS action_object_type,\n"
                + "    `ACTIONOBJECT` AS action_object,\n"
                + "    `ACTIONTYPE` AS action_type,\n"
                + "    `DETAILS` AS details,\n"
                + "    `VISIBLE` AS visible\n"
                + "FROM pg_source_gactions";

        Table tbl = tEnv.sqlQuery(queryPgTableSQL);

        tEnv.toChangelogStream(tbl)
                .print("test")
                .setParallelism(1);

        tEnv.createTemporaryView("gactions_view", tbl);

        stmtSet.addInsertSql(insertIntoSql(
                "gactions_view",
                "iceberg_sink_dwd_yh_bi_audit", // 永洪bi行为数据写入iceberg
                tEnv.from("iceberg_sink_dwd_yh_bi_audit"),
                ICEBERG
        ));
    }
}
