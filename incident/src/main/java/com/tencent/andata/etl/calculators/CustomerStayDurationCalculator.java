package com.tencent.andata.etl.calculators;


import static com.tencent.andata.etl.enums.OperationType.AGREE_CLOSE_APPLICATION;
import static com.tencent.andata.etl.enums.OperationType.CUSTOMER_REPLY;
import static com.tencent.andata.etl.enums.OperationType.NOT_RESTORED;
import static com.tencent.andata.etl.enums.OperationType.PULL;
import static com.tencent.andata.etl.enums.OperationType.REPLY;
import static com.tencent.andata.etl.enums.OperationType.RESTORED;
import static com.tencent.andata.etl.enums.OperationType.TO_CONFIRM_RESTORE;
import static com.tencent.andata.etl.enums.OperationType.URGE;
import static com.tencent.andata.etl.enums.OperationType.WAIT_CUSTOMER_ADD_INFO;
import static com.tencent.andata.etl.enums.OperationType.WAIT_CUSTOMER_CLOSE;
import static com.tencent.andata.etl.enums.OperationType.of;
import static com.tencent.andata.etl.enums.OperatorType.CUSTOMER;
import static com.tencent.andata.etl.enums.OperatorType.CUSTOMER_SERVICE;
import static com.tencent.andata.etl.enums.OperatorType.fromType;
import static com.tencent.andata.etl.enums.TargetStatus.CLOSE_CONFIRMATION_PENDING;
import static com.tencent.andata.etl.enums.TargetStatus.fromCode;
import static com.tencent.andata.etl.enums.TicketServiceChannel.KA;
import static com.tencent.andata.etl.enums.TicketServiceChannel.fromId;
import static io.vavr.API.$;
import static io.vavr.API.Case;

import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import io.vavr.API;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.collection.HashMap;
import io.vavr.collection.List;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class CustomerStayDurationCalculator {

    public static void main(String[] args) throws IOException {
        String filePath = "incident/src/main/resources/operation.txt"; // 替换为你的文件路径
        String ticketOperationStr = new String(Files.readAllBytes(Paths.get(filePath)));
        ObjectMapper objectMapper = new ObjectMapper();

        // calculate
        java.util.List<Operation> opts = objectMapper
                .readValue(ticketOperationStr, new TypeReference<java.util.List<Operation>>() {})
                .stream()
                .sorted()
                .collect(Collectors.toList());

        String solvedTime = "2024-07-15 11:24:16.0";

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");

        // 解析字符串为LocalDateTime
        LocalDateTime ticketSolvedTime = LocalDateTime.parse(solvedTime, formatter);

        // 计算客户在各岗位的停留时长
        Map<OperatorPost, Long> rst = calculateStayingDurations(opts, ticketSolvedTime);
        System.out.println("Customer Total duration: " + rst.values().sum() + " seconds");
        rst.forEach((post, duration) ->
                System.out.println("Customer On Post: " + post + ", Duration: " + duration + " seconds"));
    }

    /**
     * 计算客户在各岗位的停留时长：
     * 遍历工单流水，operator_type为“客服”或“客户”：
     * <p>
     * 1、operation_type为“待客户补充”时，取operate_time作为time1，再在此操作之后的operation_type为“认领”、“催单”、
     * “客户回复“、“待客户确认结单”、operator_type为“客服”且service_channel为“大客户群”且operation_type为“回复”时，取此操
     * 作的operate_time作为time2；
     * </p>
     * <p>
     * 2、operation_type为“待确认业务恢复”时，取operate_time作为time1，再在此操作之后的operation_type为“认领”、“催单”、
     * “客户回复“、“待客户确认结单”、operator_type为“客服”且service_channel为“大客户群”且operation_type为“回复”、“已恢复”、
     * “未恢复”时，取此操作的operate_time作为time2；
     * </p>
     * <p>
     * 3、operation_type为“待客户确认结单”时看post，取operate_time作为time1，再在此操作之后的operation_type为“认领”、“催单”、
     * “客户回复“、“待客户确认结单”、operator_type为“客服”且service_channel为“大客户群”且operation_type为“回复”时，取此操
     * 作的operate_time作为time2；
     * </p>
     * <p>
     * 4、operation_type为“同意申请结单”且“target_status为“待确认结单“时看post，取operate_time作为time1，再在此操作之后的
     * operation_type为“认领”、“催单”、“客户回复“、operator_type为“客服”且service_channel为“大客户群”且operation_type为
     * “回复”时，取此操作的operate_time作为time2；
     * </p>
     * 若time2在ticket_solved_time之后则以ticket_solved_time作为对应的time2，按各岗位累加time1之，time2之前(包含time2)的工单
     * 流水中的duration作为各岗位对应的的客户停留时长。
     *
     * @param opts 工单操作流水
     * @param endTime 工单解决时间
     * @return Map<Integer, Duration>
     */
    public static Map<OperatorPost, Long> calculateStayingDurations(java.util.List<Operation> opts,
            LocalDateTime endTime) {
        Tuple2<Map<OperatorPost, Operation>, Map<OperatorPost, Long>> result = List
                .ofAll(opts)
                .filter(op -> (!op.getOperateTime().isAfter(endTime)))
                .foldLeft(Tuple.of(HashMap.empty(), HashMap.empty()), (acc, op) -> process(acc, op, endTime));

        // 释放内存
        opts = null;

        return result._2;
    }

    private static Tuple2<Map<OperatorPost, Operation>, Map<OperatorPost, Long>> process(
            Tuple2<Map<OperatorPost, Operation>, Map<OperatorPost, Long>> acc,
            Operation op,
            LocalDateTime ticketSolvedTime) {

        Map<OperatorPost, Operation> lastOps = acc._1;
        Map<OperatorPost, Long> durations = acc._2;

        // 临时可变的Map
        Map<OperatorPost, Operation> finalLastOps = lastOps;
        java.util.Map<OperatorPost, Long> mutableDurations = durations.toJavaMap();
        java.util.Map<OperatorPost, Operation> mutableLastOps = lastOps.toJavaMap();

        Option.of(op)
                .filter(o -> isValidOperator(o) & !finalLastOps.values().isEmpty())
                .filter(o -> o.getOperateTime().isAfter(finalLastOps.values().max().get().getOperateTime()))
                .peek(o -> mutableDurations.merge(OperatorPost.of(o.getPost()), o.getDuration(), Long::sum))
                .filter(o -> finalLastOps.get(OperatorPost.of(o.getPost())).isDefined())
                .filter(o -> getValidOp2Predicate(finalLastOps.get(OperatorPost.of(o.getPost())).get()).test(o))
                .peek(x -> mutableLastOps.clear());

        if (isValidOp1(op) && isValidOperator(op)) {
            mutableLastOps.put(OperatorPost.of(op.getPost()), op);
        }

        // 将临时可变的Map转换回不可变的Vavr Map
        durations = HashMap.ofAll(mutableDurations);
        lastOps = HashMap.ofAll(mutableLastOps);

        return Tuple.of(lastOps, durations);
    }

    private static boolean isValidOp1(Operation op) {
        return API.Match(of(op.getOperationType()))
                .of(
                        // 待客户补充
                        Case($(WAIT_CUSTOMER_ADD_INFO), () -> true),
                        // 待确认业务恢复
                        Case($(TO_CONFIRM_RESTORE), () -> true),
                        // 待客户确认结单
                        Case($(WAIT_CUSTOMER_CLOSE), () -> true),
                        // 同意申请结单且 “targetStatus“为“待确认结单”
                        Case($(AGREE_CLOSE_APPLICATION),
                                () -> CLOSE_CONFIRMATION_PENDING == fromCode(op.getTargetStatus())),
                        Case($(), false)
                );
    }

    private static Predicate<Operation> getValidOp2Predicate(Operation lastOp) {
        int lastOpType = lastOp.getOperationType();
        int lastOpTargetStatus = lastOp.getTargetStatus();

        return op -> {
            switch (of(lastOpType)) {
                case WAIT_CUSTOMER_ADD_INFO: // 待客户补充
                    return isCommonValidOp(op) || isSpecialValidOp(op);
                case TO_CONFIRM_RESTORE: // 待确认业务恢复
                    return isCommonValidOp(op)
                            || isSpecialValidOp(op)
                            || isIn(RESTORED, NOT_RESTORED).test(of(op.getOperationType()));
                case WAIT_CUSTOMER_CLOSE: // 待客户确认结单
                    return isCommonValidOp(op) || isSpecialValidOp(op);
                case DISAGREE_CLOSE_APPLICATION: // 同意申请结单
                    return CLOSE_CONFIRMATION_PENDING == fromCode(lastOpTargetStatus)
                            && (isSpecialValidOp(op) || isCommonValidOp(op));
                default:
                    return false;
            }
        };
    }

    private static boolean isCommonValidOp(Operation op) {
        return isIn(PULL, URGE, CUSTOMER_REPLY, WAIT_CUSTOMER_CLOSE).test(of(op.getOperationType()));
    }

    private static boolean isSpecialValidOp(Operation op) {
        // operator_type为“客服”，service_channel为“大客户群”，operation_type为“回复”
        return REPLY == of(op.getOperationType())
                && fromType(op.getOperatorType()) == CUSTOMER_SERVICE
                && KA == fromId(op.getServiceChannel());
    }

    private static Predicate<OperationType> isIn(OperationType... values) {
        Set<OperationType> valueSet = Stream.of(values).collect(Collectors.toSet());
        return valueSet::contains;
    }

    private static boolean isValidOperator(Operation op) {
        return List.of(CUSTOMER, CUSTOMER_SERVICE)
                .contains(fromType(op.getOperatorType()));
    }

    private static long calculateInvalidDuration(LocalDateTime time1, LocalDateTime time2, Operation op) {
        return Option.of(op)
                .filter(o -> o.getOperateTime().isAfter(time1) && o.getOperateTime().isBefore(time2))
                .filter(o -> !isValidOperator(o))
                .map(Operation::getDuration)
                .getOrElse(0L);
    }
}