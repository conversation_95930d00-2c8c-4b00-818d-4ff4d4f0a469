package com.tencent.andata.etl.dwd;

import static com.tencent.andata.etl.sql.DwdTIncidentTicketAlarmRecordSql.TICKET_ALARM_RECORD_PROCESS_SQL;
import static com.tencent.andata.etl.sql.DwdTIncidentTicketSql.QUERY_T226_TICKET_ACCESS_RECORD_SQL;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketMappingV2.icebergTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketMappingV2.mysqlTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketMappingV2.pgsqlTable2FlinkTable;
import static com.tencent.andata.etl.tablemap.DwdIncidentTicketMappingV2.starRocksTable2FlinkTable;
import static com.tencent.andata.utils.TableUtils.insertIntoSql;
import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.ICEBERG;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;
import static com.tencent.andata.utils.struct.DatabaseEnum.ROCKS;

import com.tencent.andata.utils.BuildFtables;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.IcebergCatalogReader;
import com.tencent.andata.utils.KVConfBuilder;
import com.tencent.andata.utils.PropertyUtils;
import com.tencent.andata.utils.RainbowUtils;
import com.tencent.andata.utils.TableUtils;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import com.tencent.andata.utils.struct.DbPurposeEnum;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import lombok.Builder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Builder
public class DwdIncidentTicketV2 {

    private static final Logger logger = LoggerFactory.getLogger(DwdIncidentTicket.class);
    private final String icebergDbName;
    private final String pgDbName;

    @NotNull
    private static Map<String, String> getTblMap() {
        Map<String, String> tblMap = new HashMap<>();
        tblMap.put("t256_ka_keyissues_view", "iceberg_sink_dwd_incident_ka_keyissues");
        tblMap.put("t246_ticket_review_view", "iceberg_sink_dwd_incident_ticket_review");
        tblMap.put("t211_ticket_comment_view", "iceberg_sink_dwd_incident_ticket_comment");
        tblMap.put("t235_ticket_calling_view", "iceberg_sink_dwd_incident_ticket_calling");
        tblMap.put("t201_ticket_preview_view", "iceberg_sink_dwd_incident_ticket_preview");
        tblMap.put("t1003_ticket_favorites_view", "iceberg_sink_dwd_incident_ticket_favorites");
        tblMap.put("t251_ticket_quality_category_view", "iceberg_sink_ods_t251_ticket_quality_category");
        tblMap.put("t1004_ticket_favorites_marks_view", "iceberg_sink_dwd_incident_ticket_favorites_marks");
        tblMap.put("t252_ticket_quality_operation_view", "iceberg_sink_dwd_incident_ticket_quality_operation");
        return tblMap;
    }

    /**
     * run the DWD ETL
     *
     * @param fEnv flink运行环境工具类
     * @param catalog iceberg catalog
     */
    public void run(FlinkEnv fEnv, IcebergCatalogReader catalog) throws Exception {
        StreamTableEnvironment tEnv = fEnv.streamTEnv();
        Properties properties = PropertyUtils.loadProperties("env.properties");
        RainbowUtils rainbowUtils = new RainbowUtils(properties);

        final KVConfBuilder<DatabaseConf> kvConfBuilder = new KVConfBuilder<>(DatabaseConf.class)
                .setRainbowUtils(rainbowUtils);

        final DatabaseConf mysqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "mysql", "work"))
                .build();

        final DatabaseConf pgsqlDBConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "sink.database", "pgsql", this.pgDbName))
                .build();

        final DatabaseConf rocksDbConf = kvConfBuilder
                .setGroupName(String.format("%s.%s.%s", "cdc.database", "starrocks", "dataware"))
                .build();

        final ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap = mapper.readValue(mysqlTable2FlinkTable, ArrayNode.class);
        buildSourceView(mysqlDBConf, fEnv, mysqlTable2FlinkTableMap, DatabaseEnum.MYSQL, null);

        // pgsql table mapping to flink table
        ArrayNode pgsqlTable2FlinkTableMap = mapper.readValue(pgsqlTable2FlinkTable, ArrayNode.class);
        BuildFtables.rdbTable2FlinkTable(pgsqlDBConf, pgsqlTable2FlinkTableMap, PGSQL, tEnv, DbPurposeEnum.SINK);

        // starrocks table mapping to flink table
        ArrayNode starRocksTable2FlinkTableMap = mapper.readValue(starRocksTable2FlinkTable, ArrayNode.class);
        BuildFtables.rdbTable2FlinkTable(rocksDbConf, starRocksTable2FlinkTableMap, ROCKS, tEnv, DbPurposeEnum.SINK);

        // icebergTable mapping to flinkTable
        TableUtils.icebergTable2FlinkTable(
                this.icebergDbName,
                mapper.readValue(icebergTable2FlinkTable, ArrayNode.class), tEnv, catalog
        );

        Map<String, String> tblMap = getTblMap();

        StatementSet stmtSet = fEnv.stmtSet();
        for (Entry<String, String> entry : tblMap.entrySet()) {
            stmtSet.addInsertSql(insertIntoSql(
                    entry.getKey(),
                    entry.getValue(),
                    tEnv.from(entry.getValue()),
                    ICEBERG
            ));

            // t246_ticket_review 需要写入pg
            if (entry.getKey().equals("t246_ticket_review_view")) {
                stmtSet.addInsertSql(insertIntoSql(
                        entry.getKey(),
                        "pgsql_sink_dwd_incident_ticket_review",
                        tEnv.from(entry.getValue()),
                        PGSQL
                ));
            }
        }

        // 工单ola告警记录
        tEnv.createTemporaryView("ticket_alarm_record_view", tEnv.sqlQuery(TICKET_ALARM_RECORD_PROCESS_SQL));

        // 内部客户工单访问记录
        tEnv.createTemporaryView("ticket_access_record_view", tEnv.sqlQuery(QUERY_T226_TICKET_ACCESS_RECORD_SQL));

        // s360访问数据
        tEnv.createTemporaryView("s360_customer_dynamic_view", tEnv.sqlQuery("SELECT * FROM "
                + "iceberg_source_ods_s360_customer_dynamic /*+ OPTIONS('streaming'='true', 'monitor-interval'='1s', "
                + "'starting-strategy'='TABLE_SCAN_THEN_INCREMENTAL')*/ WHERE record_update_time > '2024-12-12'"));

        stmtSet
                .addInsertSql(insertIntoSql(
                        "t211_ticket_comment_view",
                        "pgsql_sink_dwd_incident_ticket_comment", // 工单评价表写入postgresql
                        tEnv.from("pgsql_sink_dwd_incident_ticket_comment"),
                        PGSQL
                ))
                .addInsertSql(insertIntoSql(
                        "t211_ticket_comment_view",
                        "flink_starrocks_dwd_incident_ticket_comment", // 工单评价表写入starrocks
                        tEnv.from("flink_starrocks_dwd_incident_ticket_comment"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "ticket_access_record_view",
                        "iceberg_sink_dwd_incident_ticket_access_record",
                        tEnv.from("iceberg_sink_dwd_incident_ticket_access_record"),
                        ICEBERG
                ))
                .addInsertSql(insertIntoSql(
                        "ticket_alarm_record_view",
                        "iceberg_sink_dwd_incident_ticket_alarm_record",
                        tEnv.from("iceberg_sink_dwd_incident_ticket_alarm_record"),
                        ICEBERG
                ))
                .addInsertSql(insertIntoSql(
                        "ticket_alarm_record_view",
                        "flink_starrocks_dwd_incident_ticket_alarm_record",
                        tEnv.from("flink_starrocks_dwd_incident_ticket_alarm_record"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "s360_customer_dynamic_view",
                        "flink_starrocks_ods_s360_customer_dynamic",
                        tEnv.from("flink_starrocks_ods_s360_customer_dynamic"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "t933_ticket_public_opinion_view",
                        "flink_starrocks_dwd_incident_ticket_public_opinion", // 工单舆情数据写入starrocks
                        tEnv.from("flink_starrocks_dwd_incident_ticket_public_opinion"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "t256_ka_keyissues_view",
                        "flink_starrocks_dwd_incident_ka_keyissues",
                        tEnv.from("flink_starrocks_dwd_incident_ka_keyissues"),
                        ROCKS
                ))
                .addInsertSql(insertIntoSql(
                        "ticket_access_record_view",
                        "flink_starrocks_dwd_incident_ticket_access_record",
                        tEnv.from("flink_starrocks_dwd_incident_ticket_access_record"),
                        ROCKS
                ));
    }
}