package com.tencent.andata.etl.calculator.calculators;

import static com.tencent.andata.etl.entity.Operation.isValidContent;
import static com.tencent.andata.utils.TimeUtil.formatDateTime;

import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.calculator.IMetricCalculator;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import com.tencent.andata.etl.enums.TargetStatus;
import com.tencent.andata.utils.DateFormatUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.EnumSet;
import java.util.Set;

/**
 * 时长类指标计算器
 * <p>
 * 负责计算工单生命周期中的各类处理时长，包括各岗位处理时长、客户停留时长、工单总时长等。
 * </p>
 * <p>
 * 计算逻辑基于工单的操作流水，通过分析每个操作的类型、操作人岗位、目标状态等信息，
 * 精确计算不同维度的时长指标。
 * </p>
 * <p>
 * 对应原 DwmIncidentTicketStatisticTransform.TicketDurationMapFunction 中的逻辑。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class DurationCalculator implements IMetricCalculator<CalculationContext> {

    // 定义需要增加时长的目标岗位集合
    private static final Set<OperatorPost> TRANSFERABLE_TARGET_POSTS = EnumSet.of(
            OperatorPost.FIRST_LINE,
            OperatorPost.SECOND_LINE,
            OperatorPost.SUPPLIER
    );

    // 定义有效的客户停留计算操作人类型
    private static final Set<OperatorType> CUSTOMER_STAY_VALID_TYPES = EnumSet.of(
            OperatorType.CUSTOMER_SERVICE,
            OperatorType.CUSTOMER
    );

    // ============================== 基本时长字段 ==============================

    /**
     * 供应商处理时长（秒）
     */
    private int supplierDealDuration = 0;

    /**
     * 其他角色处理时长（秒）
     */
    private int otherRoleDealDuration = 0;

    /**
     * 客户停留总时长（秒）
     */
    private int customerStayTotalDuration = 0;

    /**
     * 一线处理时长（秒）
     */
    private int firstLineDealDuration = 0;

    /**
     * 运维处理时长（秒）
     */
    private int operationDealDuration = 0;

    /**
     * 1.5线处理时长（秒）
     */
    private int secondLineDealDuration = 0;

    /**
     * 工单总时长（秒）：从创建到解决的总时间
     */
    private int ticketLifecycleDuration = 0;

    /**
     * 客户在供应商停留时长（秒）
     */
    private int customerStayAtSupplierDuration = 0;

    /**
     * 产研处理时长（秒）
     */
    private int productResearchDealDuration = 0;

    /**
     * 客户在其他角色停留时长（秒）
     */
    private int customerStayAtOtherRoleDuration = 0;

    /**
     * 客户在一线停留时长（秒）
     */
    private int customerStayAtFirstLineDuration = 0;

    /**
     * 客户在1.5线停留时长（秒）
     */
    private int customerStayAtSecondLineDuration = 0;

    /**
     * 垂直产研处理时长（秒）
     */
    private int verticalProductResearchDealDuration = 0;

    /**
     * TEG队列处理时长（秒）
     */
    private int tegQueueDealDuration = 0;

    // ============================== 辅助状态字段 ==============================

    /**
     * 客户停留时长计算标志：1表示开始计算，0表示停止计算
     */
    private int isCustomerStayCalculationActive = 0;

    /**
     * 工单解决时间，用于过滤解决后的操作
     */
    private String ticketResolvedTime;

    /**
     * 初始化计算器
     * <p>
     * 重置所有时长计数器和辅助状态字段，准备开始新的计算
     * </p>
     *
     * @param context 计算上下文，包含工单信息和指标对象
     */
    @Override
    public void initialize(CalculationContext context) {
        // 重置所有时长计数器
        this.supplierDealDuration = 0;
        this.otherRoleDealDuration = 0;
        this.customerStayTotalDuration = 0;
        this.tegQueueDealDuration = 0;
        this.firstLineDealDuration = 0;
        this.operationDealDuration = 0;
        this.secondLineDealDuration = 0;
        this.ticketLifecycleDuration = 0;
        this.customerStayAtSupplierDuration = 0;
        this.productResearchDealDuration = 0;
        this.customerStayAtOtherRoleDuration = 0;
        this.customerStayAtFirstLineDuration = 0;
        this.customerStayAtSecondLineDuration = 0;
        this.verticalProductResearchDealDuration = 0;

        this.isCustomerStayCalculationActive = 0;
        this.ticketResolvedTime = context.getMetrics().getTicketSolvedTime();
    }

    /**
     * 处理单个操作记录
     * <p>
     * 根据操作类型、操作人岗位、目标状态等信息，计算各类时长指标
     * </p>
     *
     * @param context 计算上下文
     * @param currentOp 当前操作
     * @param previousOp 前一个操作（可能为null）
     * @param nextOp 下一个操作（可能为null）
     */
    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        // 使用枚举提高代码可读性
        OperatorPost operatorPost = OperatorPost.of(currentOp.getPost());
        OperatorPost targetPost = OperatorPost.of(currentOp.getTargetPost());
        OperationType operationType = OperationType.of(currentOp.getOperationType());
        OperatorType operatorType = OperatorType.fromType(currentOp.getOperatorType());
        TargetStatus targetStatus = TargetStatus.fromCode(currentOp.getTargetStatus());

        final long duration = currentOp.getDuration();
        final String operateTime = formatDateTime(currentOp.getOperateTime());

        // 如果操作时间超过了工单解决时间，则跳过处理
        if (isOperationAfterSolvedTime(operateTime)) {
            return;
        }

        // 1. 计算TEG队列时长
        calcTegQueueDuration(currentOp, operatorType, duration);

        // 2. 计算各岗位处理时长
        calcPostDuration(currentOp, operatorType, operatorPost, duration);

        // 3. 更新各岗位时长（处理转交场景）
        updateAllRoleDuration(operationType, targetPost, operatorPost, duration);

        // 4. 计算客户停留时长
        calcCustomerStayDuration(operatorType, operatorPost, targetStatus, duration);
    }

    /**
     * 计算工单总时长
     * <p>
     * 基于工单创建时间和解决时间计算工单生命周期总时长
     * </p>
     *
     * @param context 计算上下文
     */
    @Override
    public void calculate(CalculationContext context) {
        // 计算工单总时长
        String createTime = context.getTicketProfile().getCreateTime();
        this.ticketLifecycleDuration = (int) DateFormatUtils.timeDelta(createTime, this.ticketResolvedTime);
    }

    /**
     * 填充计算结果到指标对象
     * <p>
     * 将所有计算得到的时长指标设置到metrics对象中，并计算衍生字段
     * </p>
     *
     * @param metrics 指标对象
     * @param context 计算上下文
     */
    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        // 设置所有时长字段到metrics对象
        metrics.setAgentDuration(this.supplierDealDuration);
        metrics.setUnknownDuration(this.otherRoleDealDuration);
        metrics.setCustomerDuration(this.customerStayTotalDuration);
        metrics.setTegQueueDuration(this.tegQueueDealDuration);
        metrics.setOperationDuration(this.operationDealDuration);
        metrics.setFirstLineDuration(this.firstLineDealDuration);
        metrics.setSecondLineDuration(this.secondLineDealDuration);
        metrics.setTicketDealDuration(this.ticketLifecycleDuration);
        metrics.setAgentCustomerDuration(this.customerStayAtSupplierDuration);
        metrics.setProductResearchDuration(this.productResearchDealDuration);
        metrics.setUnknownCustomerDuration(this.customerStayAtOtherRoleDuration);
        metrics.setFirstLineCustomerDuration(this.customerStayAtFirstLineDuration);
        metrics.setSecondLineCustomerDuration(this.customerStayAtSecondLineDuration);
        metrics.setVerticalProductResearchDuration(this.verticalProductResearchDealDuration);

        // 计算衍生字段：工程师实际处理时长 = 岗位总时长 - 客户停留时长
        metrics.setAgentEngineerDuration(this.supplierDealDuration - this.customerStayAtSupplierDuration);
        metrics.setTicketStaffDealDuration(this.ticketLifecycleDuration - this.customerStayTotalDuration);
        metrics.setUnknownEngineerDuration(this.otherRoleDealDuration - this.customerStayAtOtherRoleDuration);
        metrics.setFirstLineEngineerDuration(this.firstLineDealDuration - this.customerStayAtFirstLineDuration);
        metrics.setSecondLineEngineerDuration(this.secondLineDealDuration - this.customerStayAtSecondLineDuration);
    }

    // ============================== 私有辅助方法 ==============================

    /**
     * 检查操作时间是否超过工单解决时间
     * <p>
     * 用于过滤工单解决后的操作，避免重复计算
     * </p>
     *
     * @param operateTime 操作时间，格式为"yyyy-MM-dd HH:mm:ss"
     * @return 如果操作时间在解决时间之后返回true，否则返回false
     */
    private boolean isOperationAfterSolvedTime(String operateTime) {
        return isValidContent(ticketResolvedTime, "1970-01-01 00:00:00") && operateTime.compareTo(ticketResolvedTime) > 0;
    }

    /**
     * 计算TEG队列处理时长
     * <p>
     * 当操作人为客服且实际指派给TEG-T2000产研或运维时，累加TEG队列时长
     * </p>
     *
     * @param currentOp 当前操作
     * @param operatorType 操作人类型
     * @param duration 操作时长（秒）
     */
    private void calcTegQueueDuration(Operation currentOp, OperatorType operatorType, long duration) {
        if (operatorType == OperatorType.CUSTOMER_SERVICE) {
            int factAssign = currentOp.getFactAssign();
            // 4218: TEG-T2000产研、4217: TEG-T2000运维
            if (factAssign == 4218 || factAssign == 4217) {
                tegQueueDealDuration += (int) duration;
            }
        }
    }

    /**
     * 计算各岗位处理时长
     * <p>
     * 根据操作类型和操作人岗位，计算对应岗位的处理时长
     * </p>
     *
     * @param currentOp 当前操作
     * @param operatorType 操作人类型
     * @param operatorPost 操作人岗位
     * @param duration 操作时长（秒）
     */
    private void calcPostDuration(Operation currentOp, OperatorType operatorType, OperatorPost operatorPost, long duration) {
        OperationType operationType = OperationType.of(currentOp.getOperationType());

        // 判断是否为有效的处理操作
        boolean isValidProcessingOperation = isValidProcessingOperator(operatorType) || operationType == OperationType.WAIT_CUSTOMER_CLOSE;

        if (isValidProcessingOperation) {
            // 计算基本岗位时长（供应商、一线、1.5线、其他）
            calcBasicPostDuration(operatorPost, duration);

            // 计算特殊岗位时长（运维、产研、垂直产研）
            if (isValidProcessingOperator(operatorType)) {
                calcSpecialPostDuration(operatorPost, duration);
            }
        }
    }

    /**
     * 判断是否为有效的处理操作人类型
     * <p>
     * 有效的处理操作人包括：客户、客服、事件经理
     * </p>
     *
     * @param operatorType 操作人类型
     * @return 如果是有效处理操作人返回true，否则返回false
     */
    private boolean isValidProcessingOperator(OperatorType operatorType) {
        return operatorType == OperatorType.CUSTOMER || // 客户
                operatorType == OperatorType.CUSTOMER_SERVICE || // 客服
                operatorType == OperatorType.INCIDENT_MANAGER; // 事件经理
    }

    /**
     * 计算基本岗位时长
     * <p>
     * 计算供应商、一线、1.5线、其他等基础岗位的处理时长
     * </p>
     *
     * @param operatorPost 操作人岗位
     * @param duration 操作时长（秒）
     */
    private void calcBasicPostDuration(OperatorPost operatorPost, long duration) {
        switch (operatorPost) {
            case SUPPLIER:
                supplierDealDuration += (int) duration;
                break;
            case FIRST_LINE:
                firstLineDealDuration += (int) duration;
                break;
            case SECOND_LINE:
                secondLineDealDuration += (int) duration;
                break;
            case UNKNOWN:
                otherRoleDealDuration += (int) duration;
                break;
            default:
                // 其他岗位在这里不处理
                break;
        }
    }

    /**
     * 计算特殊岗位时长
     * <p>
     * 计算运维、产研、垂直产研等特殊岗位的处理时长
     * </p>
     *
     * @param operatorPost 操作人岗位
     * @param duration 操作时长（秒）
     */
    private void calcSpecialPostDuration(OperatorPost operatorPost, long duration) {
        switch (operatorPost) {
            case OPERATION:
                operationDealDuration += (int) duration;
                break;
            case PRODUCTION_RESEARCH:
                productResearchDealDuration += (int) duration;
                break;
            case P_VERTICAL_PRODUCTION_RESEARCH:
                verticalProductResearchDealDuration += (int) duration;
                break;
            default:
                // 其他岗位在这里不处理
                break;
        }
    }

    /**
     * 更新所有角色的时长
     * <p>
     * 处理工单转交场景，从源岗位减去时长，加到目标岗位
     * </p>
     *
     * @param targetPost 目标岗位
     * @param post 源岗位
     * @param duration 操作时长（秒）
     */
    private void updateAllRoleDuration(OperationType operationtype, OperatorPost targetPost, OperatorPost post, long duration) {
        if (!(operationtype == OperationType.WAIT_CUSTOMER_ADD_INFO && targetPost != post)) {
            return;
        }

        // 从源岗位减去时长
        adjustDurationByPost(post, -(int) duration);

        // 使用集合操作判断目标岗位是否在指定集合中
        if (TRANSFERABLE_TARGET_POSTS.contains(targetPost)) {
            adjustDurationByPost(targetPost, (int) duration);
        }
    }

    /**
     * 根据岗位调整时长
     * <p>
     * 统一处理所有岗位的时长调整，避免重复代码
     * </p>
     *
     * @param post 岗位
     * @param delta 时长变化量（正数增加，负数减少）
     */
    private void adjustDurationByPost(OperatorPost post, int delta) {
        switch (post) {
            case SUPPLIER:
                supplierDealDuration += delta;
                break;
            case FIRST_LINE:
                firstLineDealDuration += delta;
                break;
            case SECOND_LINE:
                secondLineDealDuration += delta;
                break;
            case OPERATION:
                operationDealDuration += delta;
                break;
            case PRODUCTION_RESEARCH:
                productResearchDealDuration += delta;
                break;
            case P_VERTICAL_PRODUCTION_RESEARCH:
                verticalProductResearchDealDuration += delta;
                break;
            default:
                break;
        }
    }

    /**
     * 计算客户停留时长
     * <p>
     * 根据操作人、岗位和目标状态，计算客户在各岗位的停留时长
     * </p>
     *
     * @param operatorType 操作人类型
     * @param operatorPost 操作人岗位
     * @param targetStatus 目标状态
     * @param duration 操作时长（秒）
     */
    private void calcCustomerStayDuration(OperatorType operatorType, OperatorPost operatorPost, TargetStatus targetStatus, long duration) {
        // 只处理客服和系统操作（使用静态常量避免重复创建对象）
        if (!CUSTOMER_STAY_VALID_TYPES.contains(operatorType)) {
            return;
        }

        // 更新客户停留标志
        updateCustomerDurationFlag(targetStatus);

        // 如果客户停留标志激活，则累加各岗位的客户停留时长
        if (isCustomerStayCalculationActive > 0) {
            // 检查是否应该停止计算客户停留时长
            if (!isCustomerStayStatus(targetStatus)) {
                isCustomerStayCalculationActive = 0;
            }

            // 累加总客户停留时长
            customerStayTotalDuration += (int) duration;

            // 根据岗位累加细分的客户停留时长
            calcCustomerStayDurationByPost(operatorPost, duration);
        }
    }

    /**
     * 更新客户停留标志
     * <p>
     * 根据目标状态决定是否开始计算客户停留时长
     * </p>
     *
     * @param targetStatus 目标状态
     */
    private void updateCustomerDurationFlag(TargetStatus targetStatus) {
        if (isCustomerStayStatus(targetStatus)) {
            isCustomerStayCalculationActive = 1;
        }
    }

    /**
     * 判断是否为客户停留状态
     * <p>
     * 客户停留状态包括：待客户补充信息、待客户确认恢复、待客户确认关闭
     * </p>
     *
     * @param targetStatus 目标状态
     * @return 如果是客户停留状态返回true，否则返回false
     */
    private boolean isCustomerStayStatus(TargetStatus targetStatus) {
        return targetStatus == TargetStatus.TO_CONFIRM_RESTORE ||
                targetStatus == TargetStatus.TO_BE_ADDED_BY_CUSTOMER ||
                targetStatus == TargetStatus.CLOSE_CONFIRMATION_PENDING;
    }

    /**
     * 根据岗位累加客户停留时长
     * <p>
     * 将客户停留时长累加到对应的岗位细分指标中
     * </p>
     *
     * @param operatorPost 操作人岗位
     * @param duration 操作时长（秒）
     */
    private void calcCustomerStayDurationByPost(OperatorPost operatorPost, long duration) {
        switch (operatorPost) {
            case SUPPLIER:
                customerStayAtSupplierDuration += (int) duration;
                break;
            case FIRST_LINE:
                customerStayAtFirstLineDuration += (int) duration;
                break;
            case SECOND_LINE:
                customerStayAtSecondLineDuration += (int) duration;
                break;
            case UNKNOWN:
                customerStayAtOtherRoleDuration += (int) duration;
                break;
            default:
                // 其他岗位在这里不处理
                break;
        }
    }
}