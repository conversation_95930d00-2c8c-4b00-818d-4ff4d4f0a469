package com.tencent.andata.etl.calculators;

import static com.tencent.andata.etl.entity.Operation.isValidContent;
import static com.tencent.andata.etl.enums.OperatorType.CUSTOMER;
import static com.tencent.andata.etl.enums.OperatorType.CUSTOMER_SERVICE;
import static io.vavr.API.$;
import static io.vavr.API.Case;

import com.tencent.andata.etl.entity.Operation;
import io.vavr.API;
import io.vavr.collection.List;
import io.vavr.control.Option;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.Getter;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

public class InteractionMetricsCalculator {

    public static void main(String[] args) throws IOException {
        String filePath = "incident/src/main/resources/operation.txt"; // 替换为你的文件路径
        String ticketOperationStr = new String(Files.readAllBytes(Paths.get(filePath)));
        ObjectMapper objectMapper = new ObjectMapper();

        // calculate
        java.util.List<Operation> opts = objectMapper
                .readValue(ticketOperationStr, new TypeReference<java.util.List<Operation>>() {})
                .stream()
                .sorted()
                .collect(Collectors.toList());

        System.out.println("ticket_id: " + opts.get(0).getTicketId()
                + " InteractionCount: " + calculateInteractionMetrics(opts).getInteractionCount());
    }

    /**
     * 交互次数计算逻辑：
     * 按时间排序，取流水表中第一条“operator_type为”为“客户”的流水，在排序流水中找到第一条“operator_type为”为“客服”，且“extern_reply”
     * 不为空的流水则交互次数记为1，并返回该流水的“operate_time”记为time1，若未能找到该流水，则交互次数仍为0。在time1时间后继续在其余流水
     * 中匹配这种模式并累加。成对计算
     *
     * @param records 工单操作流水
     * @return InteractionMetrics
     */
    public static InteractionMetrics calculateInteractionMetrics(java.util.List<Operation> records) {
        @Data
        class Accumulator {

            final int interactionCount;
            final List<LocalDateTime> interactionTimes;
            final boolean lookingForCustomer;
            final Option<LocalDateTime> lastCustomerTime;
        }

        Accumulator initialAccumulator = new Accumulator(0, List.empty(), true, Option.none());

        Accumulator result = List.ofAll(records).foldLeft(initialAccumulator, (acc, record) ->
                API.Match(acc).of(
                        Case($(a -> a.lookingForCustomer && record.getOperatorType() == CUSTOMER.getType()),
                                new Accumulator(acc.interactionCount, acc.interactionTimes, false,
                                        Option.of(record.getOperateTime()))
                        ),
                        Case($(a -> !a.lookingForCustomer && record.getOperatorType() == CUSTOMER_SERVICE.getType()
                                        && isValidContent(record.getExternReply(), "null")),
                                new Accumulator(acc.interactionCount + 1,
                                        acc.interactionTimes.append(record.getOperateTime()), true, Option.none())
                        ),
                        Case($(), acc)
                )
        );

        // 释放内存
        records = null;

        return new InteractionMetrics(result.interactionCount, result.interactionTimes);
    }

    @Getter
    public static class InteractionMetrics {

        private final int interactionCount;
        private final List<LocalDateTime> interactionTimes;

        public InteractionMetrics(int interactionCount, List<LocalDateTime> interactionTimes) {
            this.interactionCount = interactionCount;
            this.interactionTimes = interactionTimes;
        }
    }
}