package com.tencent.andata.etl.calculator.calculators.transfer;

import static com.tencent.andata.etl.entity.Operation.isValidContent;
import static com.tencent.andata.utils.TimeUtil.formatDateTime;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 转单指标计算器
 * <p>
 * 负责计算转单次数、转单类型、转单链等核心转单指标。
 * 包括转入转出次数统计、各岗位转单次数统计、转单链构建等功能。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class TransferMetricsCalculator implements ITransferSubCalculator {

    private static final String DEFAULT_TIME = "1970-01-01 00:00:00";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // 后端岗位常量集合
    private static final List<OperatorPost> BACKEND_POSTS = Arrays.asList(
            OperatorPost.OPERATION, // 运维
            OperatorPost.PRODUCTION_RESEARCH, // 产研
            OperatorPost.P_VERTICAL_PRODUCTION_RESEARCH // 垂直产研
    );

    // TGW队列ID常量
    private static final List<Integer> TGW_QUEUE_IDS = Arrays.asList(
            6210, // 专项KA-网络TGW队列-CLB/EIP转发问题
            6209  // 专项-网络TGW队列-CLB/EIP转发问题
    );

    // 转单指标数据
    private TransferMetrics transferMetrics;

    // 状态标志
    private boolean isFirstTransferOperationFlag = true;
    private boolean isFirstTransferProductionResearchFlag = true;
    private boolean isFirstTransferVerticalProductionResearchFlag = true;
    private boolean isFirstTransferComplaintQueueTimeFlag = true;

    // 工单基础信息
    private String closeTime = "";
    private boolean isCloseFlag = false;

    @Override
    public void initialize(CalculationContext context) {
        this.transferMetrics = new TransferMetrics();
        
        // 重置状态标志
        this.isFirstTransferOperationFlag = true;
        this.isFirstTransferProductionResearchFlag = true;
        this.isFirstTransferVerticalProductionResearchFlag = true;
        this.isFirstTransferComplaintQueueTimeFlag = true;

        // 获取基础信息
        this.closeTime = context.getMetrics().getCloseTime();
        this.isCloseFlag = context.getTicketProfile().getOperations().stream()
                .anyMatch(op -> op.getOperationType() == OperationType.CLOSE.getCode());
    }

    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        OperatorPost operatorPost = OperatorPost.of(currentOp.getOperatorPost());
        OperationType opType = OperationType.of(currentOp.getOperationType());
        OperatorPost targetPost = OperatorPost.of(currentOp.getTargetPost());
        OperatorType operatorType = OperatorType.fromType(currentOp.getOperatorType());

        final String operateTime = formatDateTime(currentOp.getOperateTime());
        final int index = context.getTicketProfile().getOperations().indexOf(currentOp);

        // 检查是否应该处理此操作
        if (!shouldProcessOperation(operateTime)) {
            return;
        }

        // 1. 处理转单链和转单类型
        processTransferChain(currentOp, operatorPost, targetPost);

        // 2. 处理转单操作
        if (opType == OperationType.TRANSFER) {
            processTransferByTargetPost(currentOp, targetPost, operateTime, operatorType);
        }

        // 3. 处理认领操作
        if (opType == OperationType.PULL && targetPost == OperatorPost.OPERATION) {
            transferMetrics.transferOperationTimes++;
            if (isFirstTransferOperationFlag) {
                isFirstTransferOperationFlag = false;
                transferMetrics.firstTransferOperationTime = operateTime;
            }
        }

        // 4. 处理特殊队列标记
        processSpecialQueues(currentOp, operateTime, index);
    }

    @Override
    public void calculate(CalculationContext context) {
        // 生成转单类型字符串
        transferMetrics.transferType = String.join("->", transferMetrics.transferPostList);
    }

    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        // 转单次数相关
        metrics.setTransferToOutTimes(transferMetrics.transferToOutTimes);
        metrics.setTransferToInTimes(transferMetrics.transferToInTimes);
        metrics.setFirstTransferPost(transferMetrics.firstTransferPost);
        metrics.setTransferOperationTimes(transferMetrics.transferOperationTimes);
        metrics.setTransferFirstLineTimes(transferMetrics.transferFirstLineTimes);
        metrics.setTransferSecondLineTimes(transferMetrics.transferSecondLineTimes);
        metrics.setTransferProductionResearchTimes(transferMetrics.transferProductionResearchTimes);
        metrics.setTransferVerticalProductionResearchTimes(transferMetrics.transferVerticalProductionResearchTimes);

        // 转单状态标记
        metrics.setIsTransferFirstLine(transferMetrics.isTransferFirstLine ? 1 : 0);
        metrics.setIsTransferTgw(transferMetrics.isTransferTgw ? 1 : 0);

        // 转单链
        try {
            metrics.setTransferChain(OBJECT_MAPPER.writeValueAsString(transferMetrics.transferChain));
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize transfer chain for ticketId: {}", context.getTicketProfile().getTicketId(), e);
            metrics.setTransferChain("[]");
        }

        // 转单时间信息
        metrics.setTransferFirstLineTime(transferMetrics.transferFirstLineTime);
        metrics.setFirstTransferOperationTime(transferMetrics.firstTransferOperationTime);
        metrics.setFirstTransferProductionResearchTime(transferMetrics.firstTransferProductionResearchTime);
        metrics.setFirstTransferComplaintQueueTime(transferMetrics.firstTransferComplaintQueueTime);

        // 队列信息
        metrics.setTransferFirstLineQueue(transferMetrics.transferFirstLineQueue);
        metrics.setLastOperationFactAssign(transferMetrics.lastOperationFactAssign);
        metrics.setLastProductionResearchFactAssign(transferMetrics.lastProductionResearchFactAssign);

        // 其他
        metrics.setMeasures(transferMetrics.measures);
        metrics.setTransferType(transferMetrics.transferType);
    }

    /**
     * 判断是否应该处理当前操作
     */
    private boolean shouldProcessOperation(String operateTime) {
        return !isCloseFlag || !isValidContent(closeTime, DEFAULT_TIME) || operateTime.compareTo(closeTime) <= 0;
    }

    /**
     * 处理转单链和转单类型
     */
    private void processTransferChain(Operation currentOp, OperatorPost fromPost, OperatorPost targetPost) {
        OperationType opType = OperationType.of(currentOp.getOperationType());

        if (opType == OperationType.TRANSFER) {
            // 计算转入转出次数
            boolean fromIsBackend = BACKEND_POSTS.contains(fromPost);
            boolean toIsBackend = BACKEND_POSTS.contains(targetPost);

            if (fromIsBackend && !toIsBackend) {
                transferMetrics.transferToOutTimes++;
            }

            if (!fromIsBackend && toIsBackend) {
                transferMetrics.transferToInTimes++;
            }
        }

        // 记录转单岗位
        String postStr = String.valueOf(currentOp.getPost());
        if (!transferMetrics.transferPostList.contains(postStr)) {
            transferMetrics.transferPostList.add(postStr);
        }

        // 构建转单链
        transferMetrics.transferChain.add(fromPost.getName() + "->" + targetPost.getName());
    }

    /**
     * 根据目标岗位处理转单
     */
    private void processTransferByTargetPost(Operation currentOp, OperatorPost targetPost, String operateTime, OperatorType operatorType) {
        switch (targetPost) {
            case FIRST_LINE:
                handleTransferToFirstLine(currentOp, operateTime);
                break;
            case SECOND_LINE:
                handleTransferToSecondLine();
                break;
            case OPERATION:
                handleTransferToOperation(operateTime);
                break;
            case PRODUCTION_RESEARCH:
                handleTransferToProductionResearch(operateTime);
                break;
            case P_VERTICAL_PRODUCTION_RESEARCH:
                handleTransferToVerticalProductionResearch();
                break;
            default:
                break;
        }
    }

    /**
     * 处理转一线
     */
    private void handleTransferToFirstLine(Operation currentOp, String operateTime) {
        transferMetrics.transferFirstLineTimes++;
        transferMetrics.isTransferFirstLine = true;
        transferMetrics.transferFirstLineTime = operateTime;
        transferMetrics.transferFirstLineQueue = currentOp.getFactAssign();
    }

    /**
     * 处理转1.5线
     */
    private void handleTransferToSecondLine() {
        transferMetrics.transferSecondLineTimes++;
    }

    /**
     * 处理转运维
     */
    private void handleTransferToOperation(String operateTime) {
        transferMetrics.transferOperationTimes++;

        if (isFirstTransferOperationFlag) {
            isFirstTransferOperationFlag = false;
            transferMetrics.firstTransferOperationTime = operateTime;
        }
    }

    /**
     * 处理转产研
     */
    private void handleTransferToProductionResearch(String operateTime) {
        transferMetrics.transferProductionResearchTimes++;

        if (isFirstTransferProductionResearchFlag) {
            isFirstTransferProductionResearchFlag = false;
            transferMetrics.firstTransferProductionResearchTime = operateTime;
        }
    }

    /**
     * 处理转垂直产研
     */
    private void handleTransferToVerticalProductionResearch() {
        transferMetrics.transferVerticalProductionResearchTimes++;
    }

    /**
     * 处理特殊队列
     */
    private void processSpecialQueues(Operation currentOp, String operateTime, int index) {
        // 检查TGW队列
        if (TGW_QUEUE_IDS.contains(currentOp.getFactAssign())) {
            transferMetrics.isTransferTgw = true;
        }

        // 首次转投诉队列时间
        if (currentOp.getFactAssign() == 4220 && isFirstTransferComplaintQueueTimeFlag && index > 1) {
            isFirstTransferComplaintQueueTimeFlag = false;
            transferMetrics.firstTransferComplaintQueueTime = operateTime;
        }

        // 更新队列信息
        OperatorPost operatorPost = OperatorPost.of(currentOp.getOperatorPost());
        if (operatorPost == OperatorPost.OPERATION && currentOp.getOperationType() != OperationType.CLOSE.getCode()) {
            transferMetrics.lastOperationFactAssign = currentOp.getFactAssign();
        } else if ((operatorPost == OperatorPost.PRODUCTION_RESEARCH || operatorPost == OperatorPost.UNKNOWN)
                && currentOp.getOperationType() != OperationType.CLOSE.getCode()) {
            transferMetrics.lastProductionResearchFactAssign = currentOp.getFactAssign();
        }
    }

    /**
     * 转单统计指标数据结构
     */
    private static class TransferMetrics {
        // 转单次数统计
        private int transferToOutTimes = 0;
        private int transferToInTimes = 0;
        private int transferOperationTimes = 0;
        private int transferFirstLineTimes = 0;
        private int transferSecondLineTimes = 0;
        private int transferProductionResearchTimes = 0;
        private int transferVerticalProductionResearchTimes = 0;

        // 转单状态标记
        private boolean isTransferTgw = false;
        private boolean isTransferFirstLine = false;

        // 首次转单信息
        private int firstTransferPost = 0;
        private ArrayList<String> transferChain = new ArrayList<>();
        private ArrayList<String> transferPostList = new ArrayList<>();

        // 转单时间信息
        private String transferFirstLineTime = DEFAULT_TIME;
        private String firstTransferOperationTime = DEFAULT_TIME;
        private String firstTransferProductionResearchTime = DEFAULT_TIME;
        private String firstTransferComplaintQueueTime = DEFAULT_TIME;

        // 队列信息
        private int transferFirstLineQueue = -1;
        private int lastOperationFactAssign = -1;
        private int lastProductionResearchFactAssign = -1;

        // 其他统计
        private String measures = "";
        private String transferType = "";
    }
}
