package com.tencent.andata.etl.dwm.util;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Random;

/**
 * 数据倾斜解决工具类
 * 通过加盐和两阶段分发来打散热点数据，解决数据倾斜问题
 */
public class DataSkewResolver {
    
    private static final Logger LOG = LoggerFactory.getLogger(DataSkewResolver.class);
    
    /**
     * 参考：https://www.jianshu.com/p/4ae20202e06d
     * 通过加盐和两阶段分发来打散热点数据，解决数据倾斜问题。
     *
     * 警告：此操作会破坏原始key的分组语义。
     * 在此之后返回的数据流中，不能再假设拥有相同`ticket_id`的所有记录都在同一个物理任务中。
     * 如果需要对`ticket_id`进行分组聚合，必须先按原始`ticket_id`进行第二次keyBy。
     *
     * @param stream 原始数据流
     * @param keySelector 用于提取key的函数
     * @param saltFactor 加盐因子，通常等于下游算子的并行度
     * @return 经过负载均衡处理后的数据流
     */
    public static <T> DataStream<T> resolveSkew(DataStream<T> stream, KeySelector<T, String> keySelector,
            int saltFactor) {
        LOG.info("应用数据倾斜修复，通过对key加盐处理。加盐因子: {}", saltFactor);
        TypeInformation<T> typeInfo = stream.getType();

        DataStream<Tuple2<String, T>> saltedStream = stream.map(
                new SaltingMapFunction<>(keySelector, saltFactor))
                .name("Add-Salt-To-Key")
                .returns(Types.TUPLE(Types.STRING, typeInfo));

        // 按加盐的Key进行分发，然后去掉盐，恢复原始数据流
        return saltedStream
                .keyBy(tuple -> tuple.f0)
                .map(tuple -> tuple.f1)
                .returns(typeInfo)
                .name("Remove-Salt-And-Distribute");
    }
    
    /**
     * 加盐映射函数
     */
    private static class SaltingMapFunction<T> extends RichMapFunction<T, Tuple2<String, T>> {
        private final KeySelector<T, String> keySelector;
        private final int saltFactor;
        private transient Random random;

        public SaltingMapFunction(KeySelector<T, String> keySelector, int saltFactor) {
            this.keySelector = keySelector;
            this.saltFactor = saltFactor;
        }

        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            this.random = new Random();
        }

        @Override
        public Tuple2<String, T> map(T element) throws Exception {
            String originalKey = keySelector.getKey(element);
            String nonNullKey = (originalKey == null) ? "" : originalKey;

            // 创建带盐的Key
            String saltedKey = nonNullKey + "_" + random.nextInt(saltFactor);
            return Tuple2.of(saltedKey, element);
        }
    }
} 