package com.tencent.andata.etl.calculator.calculators.statemachine;

/**
 * 转单状态枚举
 * <p>
 * 定义了转单计算过程中的各种状态，用于替代原来的多个boolean标志。
 * 采用状态机模式，使状态管理更加清晰和可维护。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum TransferState {
    
    // ============================== 初始状态 ==============================
    /**
     * 初始状态 - 工单刚开始处理
     */
    INITIAL("INITIAL", "初始状态"),

    // ============================== 转单相关状态 ==============================
    /**
     * 首次转运维状态
     */
    FIRST_TRANSFER_OPERATION("FIRST_TRANSFER_OPERATION", "首次转运维"),

    /**
     * 首次转1.5线状态
     */
    FIRST_TRANSFER_SECOND_LINE("FIRST_TRANSFER_SECOND_LINE", "首次转1.5线"),

    /**
     * 首次转产研状态
     */
    FIRST_TRANSFER_PRODUCTION_RESEARCH("FIRST_TRANSFER_PRODUCTION_RESEARCH", "首次转产研"),

    /**
     * 首次转垂直产研状态
     */
    FIRST_TRANSFER_VERTICAL_PRODUCTION_RESEARCH("FIRST_TRANSFER_VERTICAL_PRODUCTION_RESEARCH", "首次转垂直产研"),

    /**
     * 首次转投诉队列状态
     */
    FIRST_TRANSFER_COMPLAINT_QUEUE("FIRST_TRANSFER_COMPLAINT_QUEUE", "首次转投诉队列"),

    // ============================== 操作人相关状态 ==============================
    /**
     * 首次1.5线操作人状态1
     */
    FIRST_SECOND_LINE_OPERATOR_1("FIRST_SECOND_LINE_OPERATOR_1", "首次1.5线操作人状态1"),

    /**
     * 首次1.5线操作人状态2
     */
    FIRST_SECOND_LINE_OPERATOR_2("FIRST_SECOND_LINE_OPERATOR_2", "首次1.5线操作人状态2"),

    /**
     * 首次1.5线操作人状态3
     */
    FIRST_SECOND_LINE_OPERATOR_3("FIRST_SECOND_LINE_OPERATOR_3", "首次1.5线操作人状态3"),

    /**
     * 首次1.5线事实分配状态
     */
    FIRST_SECOND_LINE_FACT("FIRST_SECOND_LINE_FACT", "首次1.5线事实分配"),

    // ============================== 热线相关状态 ==============================
    /**
     * 热线首次分配状态
     */
    HOTLINE_FIRST_ASSIGN("HOTLINE_FIRST_ASSIGN", "热线首次分配"),

    /**
     * 热线首次检查状态
     */
    HOTLINE_FIRST_CHECK("HOTLINE_FIRST_CHECK", "热线首次检查"),

    // ============================== 完成状态 ==============================
    /**
     * 处理完成状态
     */
    COMPLETED("COMPLETED", "处理完成");

    private final String code;
    private final String description;

    TransferState(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取状态
     */
    public static TransferState fromCode(String code) {
        for (TransferState state : values()) {
            if (state.code.equals(code)) {
                return state;
            }
        }
        throw new IllegalArgumentException("Unknown transfer state code: " + code);
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", description, code);
    }
}
