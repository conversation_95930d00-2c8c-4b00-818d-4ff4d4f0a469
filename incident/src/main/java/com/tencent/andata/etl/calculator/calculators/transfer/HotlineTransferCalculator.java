package com.tencent.andata.etl.calculator.calculators.transfer;

import static com.tencent.andata.utils.DateFormatUtils.timeDelta;
import static com.tencent.andata.utils.TimeUtil.formatDateTime;

import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.TicketServiceChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * 热线转单计算器
 * <p>
 * 专门处理热线渠道（CallCenter）的转单逻辑，包括：
 * - 热线首次转单时间计算
 * - 热线转单响应时间计算
 * - 热线转单处理时长计算
 * </p>
 * <p>
 * 热线转单有特殊的业务逻辑，需要单独处理。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class HotlineTransferCalculator implements ITransferSubCalculator {

    private static final String DEFAULT_TIME = "1970-01-01 00:00:00";
    private static final String HOTLINE_SERVICE_CHANNEL = String.valueOf(TicketServiceChannel.CALLCENTER.getId());

    // 热线转单指标数据
    private HotlineTransferMetrics hotlineMetrics;

    // 状态标志
    private boolean isFirstAssignFlag = true;
    private boolean isFirstCheckFlag = false;

    // 工单基础信息
    private String serviceChannel = "";
    private String closeTime = "";

    @Override
    public void initialize(CalculationContext context) {
        this.hotlineMetrics = new HotlineTransferMetrics();
        
        // 重置状态标志
        this.isFirstAssignFlag = true;
        this.isFirstCheckFlag = false;

        // 获取基础信息
        this.serviceChannel = String.valueOf(context.getTicketProfile().getServiceChannel());
        this.closeTime = context.getMetrics().getCloseTime();
    }

    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        // 只处理热线渠道的工单
        if (!HOTLINE_SERVICE_CHANNEL.equals(serviceChannel)) {
            return;
        }

        OperationType opType = OperationType.of(currentOp.getOperationType());
        final String operateTime = formatDateTime(currentOp.getOperateTime());

        // 处理热线转单逻辑
        processHotlineTransfer(opType, operateTime);
    }

    @Override
    public void calculate(CalculationContext context) {
        // 计算热线转单相关时长
        calculateHotlineTransferDurations();
    }

    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        // 热线转单相关指标
        metrics.setHotlineFirstTransferLineTime(hotlineMetrics.hotlineFirstTransferTime);
        metrics.setHotlineFirstTransferResponseTime(hotlineMetrics.hotlineFirstTransferResponseTime);
        metrics.setHotlineFirstTransferDealDuration(hotlineMetrics.hotlineFirstTransferDealDuration);
        metrics.setHotlineFirstTransferResponseDuration(hotlineMetrics.hotlineFirstTransferResponseDuration);
    }

    /**
     * 处理热线转单逻辑
     */
    private void processHotlineTransfer(OperationType opType, String operateTime) {
        // 计算热线转单相关时长
        if (isFirstAssignFlag && (opType == OperationType.TRANSFER || opType == OperationType.DISPATCH)) {
            hotlineMetrics.hotlineFirstTransferTime = operateTime;
            isFirstAssignFlag = false;
            isFirstCheckFlag = true;
        } else if (isFirstCheckFlag && opType == OperationType.RETRIEVE) {
            hotlineMetrics.hotlineFirstTransferResponseTime = operateTime;
            isFirstCheckFlag = false;
        }
    }

    /**
     * 计算热线转单时长
     */
    private void calculateHotlineTransferDurations() {
        // 计算热线转单处理时长
        if (!hotlineMetrics.hotlineFirstTransferTime.equals(DEFAULT_TIME) && !closeTime.equals(DEFAULT_TIME)) {
            try {
                hotlineMetrics.hotlineFirstTransferDealDuration = (int) timeDelta(closeTime, hotlineMetrics.hotlineFirstTransferTime);
            } catch (Exception e) {
                log.error("Failed to calculate hotline transfer deal duration", e);
            }
        }

        // 计算热线转单响应时长
        if (!hotlineMetrics.hotlineFirstTransferResponseTime.equals(DEFAULT_TIME)
                && !hotlineMetrics.hotlineFirstTransferTime.equals(DEFAULT_TIME)) {
            try {
                hotlineMetrics.hotlineFirstTransferResponseDuration = (int) timeDelta(
                        hotlineMetrics.hotlineFirstTransferResponseTime, hotlineMetrics.hotlineFirstTransferTime);
            } catch (Exception e) {
                log.error("Failed to calculate hotline transfer response duration", e);
            }
        }
    }

    /**
     * 热线转单指标数据结构
     */
    private static class HotlineTransferMetrics {
        // 热线转单相关时间
        private String hotlineFirstTransferTime = DEFAULT_TIME;
        private String hotlineFirstTransferResponseTime = DEFAULT_TIME;
        
        // 热线转单相关时长
        private int hotlineFirstTransferDealDuration = 0;
        private int hotlineFirstTransferResponseDuration = 0;
    }
}
