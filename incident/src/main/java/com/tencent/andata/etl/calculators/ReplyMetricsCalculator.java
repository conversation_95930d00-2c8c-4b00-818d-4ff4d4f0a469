package com.tencent.andata.etl.calculators;

import static com.tencent.andata.etl.entity.Operation.isValidContent;
import static com.tencent.andata.etl.enums.OperationType.CREATE;
import static com.tencent.andata.etl.enums.OperationType.CUSTOMER_REPLY;
import static com.tencent.andata.etl.enums.OperatorPost.FIRST_LINE;
import static com.tencent.andata.etl.enums.OperatorPost.OPERATION;
import static com.tencent.andata.etl.enums.OperatorPost.PRODUCTION_RESEARCH;
import static com.tencent.andata.etl.enums.OperatorPost.SECOND_LINE;
import static com.tencent.andata.etl.enums.OperatorPost.SUPPLIER;
import static com.tencent.andata.etl.enums.OperatorPost.UNKNOWN;
import static com.tencent.andata.etl.enums.OperatorType.CUSTOMER;
import static com.tencent.andata.etl.enums.OperatorType.CUSTOMER_SERVICE;
import static io.vavr.API.$;
import static io.vavr.API.Case;
import static io.vavr.API.Match;
import static io.vavr.API.Option;

import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperatorPost;
import io.vavr.collection.List;
import io.vavr.control.Option;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.IdentityHashMap;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;


/*
 * 工单回复时长相关指标计算
 */
public class ReplyMetricsCalculator {

    private static final LocalDateTime START_TIME = LocalDateTime.parse("1970-01-01T00:00:00");

    public static void main(String[] args) throws Exception {
        String filePath = "incident/src/main/resources/operation.txt"; // 替换为你的文件路径
        String ticketOperationStr = new String(Files.readAllBytes(Paths.get(filePath)));
        ObjectMapper objectMapper = new ObjectMapper();

        // calculate
        java.util.List<Operation> opts = objectMapper
                .readValue(ticketOperationStr, new TypeReference<java.util.List<Operation>>() {})
                .stream()
                .sorted()
                .collect(Collectors.toList());

        // 计算回复时长相关指标
        ReplayStatistics statistics = calculateStatistics(opts);
        System.out.println("TicketId: " + opts.get(0).getTicketId() + " Statistics: " + statistics);
    }

    public static ReplayStatistics calculateStatistics(java.util.List<Operation> operations) {

        StatAccumulator initAcc = StatAccumulator.builder()
                .operatorReplyMap(new IdentityHashMap<>())
                .customerAskedTime(START_TIME)
                .secondLineReplyTimes(0)
                .firstLineReplyTimes(0)
                .customerReplyTimes(0)
                .customerAsked(false)
                .agentReplyTimes(0)
                .replyTimes(0)
                .build();

        StatAccumulator finalAcc = List.ofAll(operations).foldLeft(initAcc, (acc, op) ->
                Match(op).of(
                        // operator_type为“客服”， extern_reply”不为空且operation_type不为“建单”
                        Case($(o -> o.getOperatorType() == CUSTOMER_SERVICE.getType()
                                        && o.getOperationType() != CREATE.getCode()
                                        && isValidContent(o.getExternReply(), "NULL")),
                                o -> {
                                    int firstLineReplyTimes = Match(OperatorPost.of(o.getOperatorPost())).of(
                                            Case($(UNKNOWN),
                                                    Match(OperatorPost.of(o.getPost())).of(
                                                            Case($(FIRST_LINE), 1), // 一线
                                                            Case($(OPERATION), 1), // 运维
                                                            Case($(PRODUCTION_RESEARCH), 1), // 产研
                                                            Case($(), 0))),
                                            Case($(FIRST_LINE), 1), // 一线
                                            Case($(), 0)
                                    );

                                    int secondLineReplyTimes = Match(OperatorPost.of(o.getOperatorPost())).of(
                                            Case($(UNKNOWN),
                                                    Match(OperatorPost.of(o.getPost())).of(
                                                            Case($(SECOND_LINE), 1), // 1.5线
                                                            Case($(), 0))),
                                            Case($(SECOND_LINE), 1), // 1.5线
                                            Case($(), 0)
                                    );

                                    int agentReplyTimes = Match(OperatorPost.of(o.getOperatorPost())).of(
                                            Case($(UNKNOWN),
                                                    Match(OperatorPost.of(o.getPost())).of(
                                                            Case($(SUPPLIER), 1), // 供应商
                                                            Case($(), 0))),
                                            Case($(SUPPLIER), 1), // 供应商
                                            Case($(), 0)
                                    );

                                    // operation_type为customer_reply，且“extern_reply”内容不为空
                                    int customerReplyTimes = Match(o.getOperationType()).of(
                                            Case($(CUSTOMER_REPLY.getCode()), 1), // operation_type为“客户回复”
                                            Case($(), 0)
                                    );

                                    long replyDuration = Match(acc).of(
                                            Case($(c -> c.customerAsked && c.customerAskedTime.isAfter(START_TIME)),
                                                    getDuration(acc.customerAskedTime, o.getOperateTime())),
                                            Case($(c -> !c.customerAsked), 0L),
                                            Case($(), 0L)
                                    );

                                    Option(replyDuration)
                                            .filter(x -> x > 0)
                                            .peek(x -> acc.operatorReplyMap.put(o.getOperatorName(), x));

                                    return StatAccumulator.builder()
                                            .customerAsked(false)
                                            .replyTimes(acc.replyTimes + 1)
                                            .operatorReplyMap(acc.operatorReplyMap)
                                            .customerAskedTime(acc.customerAskedTime)
                                            .agentReplyTimes(acc.agentReplyTimes + agentReplyTimes)
                                            .customerReplyTimes(acc.customerReplyTimes + customerReplyTimes)
                                            .firstLineReplyTimes(acc.firstLineReplyTimes + firstLineReplyTimes)
                                            .secondLineReplyTimes(acc.secondLineReplyTimes + secondLineReplyTimes)
                                            .build();
                                }),

                        // operator_type为“客户”，或 operation_type为建单作为客户发言的流水
                        Case($(o -> o.getOperatorType() == CUSTOMER.getType()
                                        || o.getOperationType() == CREATE.getCode()),
                                o -> {
                                    int customerReplyTimes = Match(o).of(
                                            Case($(x -> x.getOperationType() == CUSTOMER_REPLY.getCode()
                                                            && isValidContent(o.getExternReply(), "NULL")),
                                                    1),
                                            Case($(), 0)
                                    );

                                    boolean isAsked = !acc.customerAsked;
                                    LocalDateTime askTime = isAsked ? o.getOperateTime() : acc.customerAskedTime;

                                    return StatAccumulator.builder()
                                            .customerAsked(isAsked)
                                            .customerAskedTime(askTime)
                                            .replyTimes(acc.replyTimes)
                                            .agentReplyTimes(acc.agentReplyTimes)
                                            .operatorReplyMap(acc.operatorReplyMap)
                                            .firstLineReplyTimes(acc.firstLineReplyTimes)
                                            .secondLineReplyTimes(acc.secondLineReplyTimes)
                                            .customerReplyTimes(acc.customerReplyTimes + customerReplyTimes)
                                            .build();
                                }),
                        Case($(), acc)
                ));

        int averageReplyDuration = Option.of(finalAcc.operatorReplyMap)
                .filter(x -> !x.isEmpty())
                .map(x -> x.values().stream().mapToInt(Long::intValue).sum() / x.size())
                .getOrElse(0);

        long longestReplyDuration = finalAcc.operatorReplyMap
                .values()
                .stream()
                .max(Long::compareTo)
                .orElse(0L);

        String longestReplyStaff = finalAcc.operatorReplyMap
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue() == longestReplyDuration)
                .map(Map.Entry::getKey)
                .findFirst()
                .orElseGet(""::toString);

        // 释放内存
        operations = null;

        return ReplayStatistics.builder()
                .firstLineReplyTimes(finalAcc.firstLineReplyTimes)
                .secondLineReplyTimes(finalAcc.secondLineReplyTimes)
                .agentReplyTimes(finalAcc.agentReplyTimes)
                .replyTimes(finalAcc.replyTimes)
                .customerReplyTimes(finalAcc.customerReplyTimes)
                .averageReplyDuration(averageReplyDuration)
                .longestReplyDuration(longestReplyDuration)
                .longestReplyStaff(longestReplyStaff)
                .build();
    }

    @Data
    @Builder
    @ToString
    public static class ReplayStatistics {

        private final int firstLineReplyTimes;
        private final int secondLineReplyTimes;
        private final int agentReplyTimes;
        private final int replyTimes;
        private final int customerReplyTimes;
        private final int averageReplyDuration;
        private final long longestReplyDuration;
        private final String longestReplyStaff;
    }

    @Data
    @Builder
    @ToString
    private static class StatAccumulator {

        private final int firstLineReplyTimes;
        private final int secondLineReplyTimes;
        private final int agentReplyTimes;
        private final int replyTimes;
        private final int customerReplyTimes;
        private final boolean customerAsked;
        private final LocalDateTime customerAskedTime;
        private final IdentityHashMap<String, Long> operatorReplyMap;
    }

    private static long getDuration(LocalDateTime startTime, LocalDateTime endTime) {
        return Duration.between(startTime, endTime).getSeconds();
    }
}