package com.tencent.andata.etl.calculator.calculators.transfer;

import static com.tencent.andata.etl.entity.Operation.isValidContent;
import static com.tencent.andata.utils.TimeUtil.formatDateTime;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 操作人跟踪计算器
 * <p>
 * 负责跟踪和计算工单处理过程中的各种操作人信息，包括：
 * - 各岗位处理人跟踪
 * - 1.5线操作人特殊处理逻辑
 * - 供应商转换逻辑
 * - 操作人公司信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class OperatorTrackingCalculator implements ITransferSubCalculator {

    private static final String DEFAULT_TIME = "1970-01-01 00:00:00";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // 公司ID常量集合
    private static final List<Integer> COMPANY_IDS = Arrays.asList(
            1, 2, 4, 5, 9, 23, 38, 169, 322, 163, 321, 335, 336
    );

    // 一线操作类型集合
    private static final List<OperationType> FIRST_LINE_OPERATION_TYPES = Arrays.asList(
            OperationType.RETRIEVE, OperationType.TRANSFER, OperationType.REPLY,
            OperationType.TRANSFER_RESPONSIBLE, OperationType.WAIT_CUSTOMER_ADD_INFO,
            OperationType.CLOSE_APPLICATION, OperationType.WAIT_CUSTOMER_CLOSE,
            OperationType.ADD_INFO_APPLICATION, OperationType.TO_CONFIRM_RESTORE,
            OperationType.RESET_STATUS, OperationType.INSURE_ARCHIVE
    );

    // 1.5线操作类型集合
    private static final List<OperationType> SECOND_LINE_OPERATION_TYPES = Arrays.asList(
            OperationType.RETRIEVE, OperationType.PULL, OperationType.TRANSFER,
            OperationType.REPLY, OperationType.TRANSFER_RESPONSIBLE, OperationType.WAIT_CUSTOMER_ADD_INFO,
            OperationType.CLOSE_APPLICATION, OperationType.AGREE_CLOSE_APPLICATION,
            OperationType.DISAGREE_CLOSE_APPLICATION, OperationType.WAIT_CUSTOMER_CLOSE,
            OperationType.CLOSE, OperationType.ADD_INFO_APPLICATION, OperationType.SET_DELAY_DISPATCH,
            OperationType.PULL_RESPONSIBLE, OperationType.TO_CONFIRM_RESTORE,
            OperationType.AGREE_ADD_INFO_APPLICATION, OperationType.DISAGREE_ADD_INFO_APPLICATION,
            OperationType.PULL_INCIDENT_MANAGER, OperationType.INSURE_ARCHIVE, OperationType.BUILD_GROUP_CHAT
    );

    // 操作人跟踪数据
    private OperatorTracker operatorTracker;

    // 状态标志
    private boolean isFirstTransferSecondLineFlag = true;
    private boolean isFirstSecondLineOperatorFlag1 = true;
    private boolean isFirstSecondLineOperatorFlag2 = true;
    private boolean isFirstSecondLineOperatorFlag3 = true;

    // 工单基础信息
    private String closeTime = "";
    private String ticketSolvedTime = "";
    private boolean isCloseFlag = false;

    @Override
    public void initialize(CalculationContext context) {
        this.operatorTracker = new OperatorTracker();
        
        // 重置状态标志
        this.isFirstTransferSecondLineFlag = true;
        this.isFirstSecondLineOperatorFlag1 = true;
        this.isFirstSecondLineOperatorFlag2 = true;
        this.isFirstSecondLineOperatorFlag3 = true;

        // 获取基础信息
        this.closeTime = context.getMetrics().getCloseTime();
        this.ticketSolvedTime = context.getMetrics().getTicketSolvedTime();
        this.isCloseFlag = context.getTicketProfile().getOperations().stream()
                .anyMatch(op -> op.getOperationType() == OperationType.CLOSE.getCode());
    }

    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        OperatorType operatorType = OperatorType.fromType(currentOp.getOperatorType());
        OperatorPost operatorPost = OperatorPost.of(currentOp.getOperatorPost());
        OperationType opType = OperationType.of(currentOp.getOperationType());

        final String operateTime = formatDateTime(currentOp.getOperateTime());

        // 检查是否应该处理此操作
        if (!shouldProcessOperation(operateTime)) {
            return;
        }

        // 1. 处理转单相关的操作人跟踪
        processTransferOperatorTracking(currentOp, opType, operatorType, previousOp, operateTime);

        // 2. 处理一般操作人跟踪
        processGeneralOperatorTracking(currentOp, operatorType, operatorPost, operateTime);

        // 3. 处理客服操作人
        if (operatorType == OperatorType.CUSTOMER_SERVICE) {
            updateCustomerServiceOperators(currentOp, operateTime);
        }
    }

    @Override
    public void calculate(CalculationContext context) {
        // 1. 完成1.5线操作人相关计算
        finalizeSecondLineOperators();

        // 2. 计算最后处理人信息
        calculateLastOperators(context);
    }

    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        // 操作人信息
        metrics.setOperationHandler(operatorTracker.operationHandler);
        metrics.setLastCustomerOperator(operatorTracker.lastCustomerOperator);
        metrics.setCurrentFirstLineOperator(operatorTracker.currentFirstLineOperator);
        metrics.setCurrentSecondLineOperator(operatorTracker.currentSecondLineOperator);
        metrics.setFirstLineLastOperator(operatorTracker.firstLineLastOperator);
        metrics.setLastOfcOperator(operatorTracker.lastOfcOperator);
        metrics.setProductionResearchHandler(operatorTracker.productionResearchHandler);

        metrics.setTransferFirstLineStaff(operatorTracker.transferFirstLineStaff);
        metrics.setFirstTransferSecondLineStaff(operatorTracker.firstTransferSecondLineStaff);

        metrics.setOperationHandlerCompanyId(operatorTracker.operationHandlerCompanyId);
        metrics.setCurrentSecondLineOperatorCompanyId(operatorTracker.currentSecondLineOperatorCompanyId);
        metrics.setFirstSecondLineOperatorCompanyId(operatorTracker.firstSecondLineOperatorCompanyId);
        metrics.setFirstLineLastOperatorCompanyId(operatorTracker.firstLineLastOperatorCompanyId);

        metrics.setLastOfcOperatorPost(operatorTracker.lastOfcOperatorPost);
        metrics.setFirstLineLastOperatorFactAssign(operatorTracker.firstLineLastOperatorFactAssign);

        metrics.setFirstSecondLineOperator(operatorTracker.firstSecondLineOperator);
        metrics.setSecondLineLastCompanyOperator(operatorTracker.secondLineLastCompanyOperator);
        metrics.setSecondLineLastCompanyOperatorGroupName(operatorTracker.secondLineLastCompanyOperatorGroupName);
        metrics.setSecondLineLastCompanyOperatorCompanyId(operatorTracker.secondLineLastCompanyOperatorCompanyId);

        try {
            metrics.setSecondLineOperators(OBJECT_MAPPER.writeValueAsString(operatorTracker.secondLineOperators));
            metrics.setSecondLineOperatorCompanyIds(OBJECT_MAPPER.writeValueAsString(operatorTracker.secondLineOperatorCompanyIds));
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize second line operators for ticketId: {}", context.getTicketProfile().getTicketId(), e);
            metrics.setSecondLineOperators("[]");
            metrics.setSecondLineOperatorCompanyIds("{}");
        }

        metrics.setFirstSecondLineServiceTime(operatorTracker.firstSecondLineServiceTime);

        metrics.setIsTransferCompanyInSecondLine(operatorTracker.isTransferCompanyInSecondLine ? 1 : 0);
        metrics.setIsTransferInnerStaffInSecondLine(operatorTracker.isTransferInnerStaffInSecondLine ? 1 : 0);
    }

    /**
     * 判断是否应该处理当前操作
     */
    private boolean shouldProcessOperation(String operateTime) {
        return !isCloseFlag || !isValidContent(closeTime, DEFAULT_TIME) || operateTime.compareTo(closeTime) <= 0;
    }

    /**
     * 处理转单相关的操作人跟踪
     */
    private void processTransferOperatorTracking(Operation currentOp, OperationType opType, OperatorType operatorType, Operation previousOp, String operateTime) {
        OperatorPost targetPost = OperatorPost.of(currentOp.getTargetPost());

        // 处理转1.5线
        if (opType == OperationType.TRANSFER && targetPost == OperatorPost.SECOND_LINE) {
            if (isFirstSecondLineOperatorFlag1 && isValidContent(currentOp.getNextOperatorName(), "SYSTEM")) {
                updateFirstSecondLineOperator(currentOp, operateTime);
            }

            if (isFirstTransferSecondLineFlag && operatorType == OperatorType.CUSTOMER_SERVICE && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
                isFirstTransferSecondLineFlag = false;
                operatorTracker.firstTransferSecondLineStaff = currentOp.getOperatorName();
            }
        }

        // 处理认领1.5线
        if (opType == OperationType.PULL && targetPost == OperatorPost.SECOND_LINE) {
            if (previousOp != null && isFirstTransferSecondLineFlag && operatorType == OperatorType.CUSTOMER_SERVICE) {
                isFirstTransferSecondLineFlag = false;
                String preOperator = previousOp.getOperatorName();
                String preNextOperator = previousOp.getNextOperatorName();
                operatorTracker.firstTransferSecondLineStaff = "SYSTEM".equalsIgnoreCase(preOperator) ? preOperator : preNextOperator;
            }

            if (StringUtils.isEmpty(operatorTracker.firstSecondLineServiceTime)) {
                operatorTracker.firstSecondLineServiceTime = operateTime;
            }

            if (isFirstSecondLineOperatorFlag2 && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
                updateFirstSecondLineOperator(currentOp, operateTime);
                isFirstSecondLineOperatorFlag2 = false;
            }
        }

        // 处理派单到1.5线
        if (opType == OperationType.DISPATCH && operatorType == OperatorType.SYSTEM && targetPost == OperatorPost.SECOND_LINE) {
            if (!isFirstSecondLineOperatorFlag3 && isFirstSecondLineOperatorFlag1) {
                updateFirstSecondLineOperator(currentOp, operateTime);
                isFirstSecondLineOperatorFlag1 = false;
            }
        }
    }

    /**
     * 处理一般操作人跟踪
     */
    private void processGeneralOperatorTracking(Operation currentOp, OperatorType operatorType, OperatorPost operatorPost, String operateTime) {
        // 跟踪当前处理人
        if (operatorPost == OperatorPost.FIRST_LINE && isValidContent(currentOp.getOperatorName(), "SYSTEM")) {
            operatorTracker.currentFirstLineOperator = currentOp.getOperatorName();
        }

        // 跟踪运维处理人
        if (currentOp.getPost() == OperatorPost.OPERATION.getCode() && operatorPost == OperatorPost.OPERATION) {
            operatorTracker.operationHandler = currentOp.getOperatorName();
            operatorTracker.operationHandlerCompanyId = currentOp.getOperatorCompanyId();
        }

        // 跟踪产研处理人
        if (currentOp.getPost() == OperatorPost.PRODUCTION_RESEARCH.getCode() && operatorPost == OperatorPost.PRODUCTION_RESEARCH
                && operateTime.compareTo(ticketSolvedTime) <= 0 && !ticketSolvedTime.equals(DEFAULT_TIME)) {
            operatorTracker.productionResearchHandler = currentOp.getOperatorName();
        }
    }

    /**
     * 更新首次1.5线操作人信息
     */
    private void updateFirstSecondLineOperator(Operation currentOp, String operateTime) {
        operatorTracker.operatorTreeMap.put(operateTime, new HashMap<String, Integer>() {{
            put(currentOp.getNextOperatorName(), currentOp.getNextOperatorCompanyId());
        }});

        if (isFirstSecondLineOperatorFlag3) {
            isFirstSecondLineOperatorFlag3 = false;
        }
    }

    /**
     * 更新客服操作人信息
     */
    private void updateCustomerServiceOperators(Operation currentOp, String operateTime) {
        boolean passesFilter = currentOp.getOperatorPost() > -1
                && isValidContent(currentOp.getOperatorName(), "SYSTEM")
                && (!isCloseFlag || (isValidContent(closeTime, DEFAULT_TIME) && operateTime.compareTo(closeTime) <= 0));

        if (passesFilter) {
            operatorTracker.lastCustomerOperator = currentOp.getOperatorName();

            // 跟踪1.5线操作人
            if (currentOp.getOperatorPost() == OperatorPost.SECOND_LINE.getCode()) {
                operatorTracker.secondLineOperators.add(currentOp.getOperatorName());
                operatorTracker.secondLineOperatorCompanyIds.put(currentOp.getOperatorName(), currentOp.getOperatorCompanyId());

                // 跟踪供应商最终处理人
                updateSecondLineCompanyOperator(currentOp);
            }
        }
    }

    /**
     * 更新1.5线供应商处理人
     */
    private void updateSecondLineCompanyOperator(Operation currentOp) {
        int[] validOperationTypes = {5, 40, 13, 12, 28, 30, 43, 33, 3, 48, 50, 2, 16, 8, 35, 15, 11, 14, 41, 10};
        boolean isValidOperation = Arrays.stream(validOperationTypes).anyMatch(x -> x == currentOp.getOperationType());

        if (currentOp.getPost() == OperatorPost.SECOND_LINE.getCode()
                && COMPANY_IDS.contains(currentOp.getOperatorCompanyId())
                && isValidOperation) {
            operatorTracker.secondLineLastCompanyOperator = currentOp.getOperatorName();
            operatorTracker.secondLineLastCompanyOperatorCompanyId = currentOp.getOperatorCompanyId();
            operatorTracker.secondLineLastCompanyOperatorGroupName = currentOp.getOperatorGroupName();
        }
    }

    /**
     * 完成1.5线操作人相关计算
     */
    private void finalizeSecondLineOperators() {
        // 处理1.5线操作人公司转换逻辑
        if (!operatorTracker.secondLineOperatorCompanyIds.isEmpty()) {
            // 检查是否转内部员工
            if (operatorTracker.secondLineOperatorCompanyIds.values().stream()
                    .anyMatch(id -> id == 0 || id == 217)) {
                operatorTracker.isTransferInnerStaffInSecondLine = true;
            }

            // 检查是否转多个供应商
            long distinctCompanyCount = operatorTracker.secondLineOperatorCompanyIds.values().stream()
                    .map(id -> id == 217 ? 0 : id)
                    .filter(id -> id != 0)
                    .distinct()
                    .count();

            if (distinctCompanyCount > 1) {
                operatorTracker.isTransferCompanyInSecondLine = true;
            }
        }

        // 设置首次1.5线操作人
        if (!operatorTracker.operatorTreeMap.isEmpty()) {
            String firstKey = operatorTracker.operatorTreeMap.firstKey();
            Map<String, Integer> firstEntry = operatorTracker.operatorTreeMap.get(firstKey);

            operatorTracker.firstSecondLineOperator = firstEntry.keySet().stream()
                    .findFirst().orElse("");
            operatorTracker.firstSecondLineOperatorCompanyId = firstEntry.values().stream()
                    .findFirst().orElse(-1);
        }
    }

    /**
     * 计算最后处理人信息
     */
    private void calculateLastOperators(CalculationContext context) {
        List<Operation> operations = context.getTicketProfile().getOperations();

        // 计算一线最后处理人
        operations.stream()
                .filter(op -> FIRST_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType()))
                        && op.getOperatorPost() == OperatorPost.FIRST_LINE.getCode()
                        && OperatorType.fromType(op.getOperatorType()) == OperatorType.CUSTOMER_SERVICE)
                .filter(op -> shouldProcessOperation(formatDateTime(op.getOperateTime())))
                .max(Operation::compareTo)
                .ifPresent(lastOp -> {
                    operatorTracker.firstLineLastOperator = lastOp.getOperatorName();
                    operatorTracker.firstLineLastOperatorCompanyId = lastOp.getOperatorCompanyId();
                    operatorTracker.firstLineLastOperatorFactAssign = lastOp.getFactAssign();
                });

        // 计算1.5线当前处理人
        operations.stream()
                .filter(op -> SECOND_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType()))
                        && op.getOperatorPost() == OperatorPost.SECOND_LINE.getCode()
                        && OperatorType.fromType(op.getOperatorType()) == OperatorType.CUSTOMER_SERVICE)
                .filter(op -> shouldProcessOperation(formatDateTime(op.getOperateTime())))
                .max(Operation::compareTo)
                .ifPresent(lastOp -> {
                    operatorTracker.currentSecondLineOperator = lastOp.getOperatorName();
                    operatorTracker.currentSecondLineOperatorCompanyId = lastOp.getOperatorCompanyId();
                });

        // 计算最后处理人
        operations.stream()
                .filter(op -> (op.getOperatorPost() == OperatorPost.FIRST_LINE.getCode() && FIRST_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType())))
                        || (op.getOperatorPost() == OperatorPost.SECOND_LINE.getCode() && SECOND_LINE_OPERATION_TYPES.contains(OperationType.of(op.getOperationType()))))
                .filter(op -> shouldProcessOperation(formatDateTime(op.getOperateTime())))
                .max(Operation::compareTo)
                .ifPresent(lastOp -> {
                    operatorTracker.lastOfcOperator = lastOp.getOperatorName();
                    operatorTracker.lastOfcOperatorPost = lastOp.getOperatorPost();
                });
    }

    /**
     * 操作人跟踪器数据结构
     */
    private static class OperatorTracker {
        // 处理人信息
        private String operationHandler = "";
        private String lastCustomerOperator = "";
        private String currentFirstLineOperator = "";
        private String currentSecondLineOperator = "";
        private String firstLineLastOperator = "";
        private String lastOfcOperator = "";
        private String productionResearchHandler = "";

        // 转单操作人
        private String transferFirstLineStaff = "";
        private String firstTransferSecondLineStaff = "";

        // 公司ID
        private int operationHandlerCompanyId = -1;
        private int currentSecondLineOperatorCompanyId = -1;
        private int firstSecondLineOperatorCompanyId = -1;
        private int firstLineLastOperatorCompanyId = -1;

        // 岗位信息
        private int lastOfcOperatorPost = -1;
        private int firstLineLastOperatorFactAssign = -1;

        // 1.5线相关操作人
        private String firstSecondLineOperator = "";
        private String secondLineLastCompanyOperator = "";
        private String secondLineLastCompanyOperatorGroupName = "";
        private int secondLineLastCompanyOperatorCompanyId = -1;

        // 1.5线集合
        private Set<String> secondLineOperators = new HashSet<>();
        private Map<String, Integer> secondLineOperatorCompanyIds = new HashMap<>();

        // 时间跟踪
        private String firstSecondLineServiceTime = DEFAULT_TIME;

        // 转供应商相关
        private boolean isTransferCompanyInSecondLine = false;
        private boolean isTransferInnerStaffInSecondLine = false;

        // 辅助变量
        private TreeMap<String, Map<String, Integer>> operatorTreeMap = new TreeMap<>();
    }
}
