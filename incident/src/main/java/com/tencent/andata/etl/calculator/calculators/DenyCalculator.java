package com.tencent.andata.etl.calculator.calculators;

import static com.tencent.andata.etl.entity.Operation.isValidContent;

import com.tencent.andata.etl.calculator.CalculationContext;
import com.tencent.andata.etl.calculator.IMetricCalculator;
import com.tencent.andata.etl.entity.DwmIncidentTicketStatisticMetrics;
import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import com.tencent.andata.utils.DateFormatUtils;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 拒单相关指标计算器
 * <p>
 * 负责计算与工单被拒绝相关的指标。
 * 对应原 DwmIncidentTicketStatisticTransform.TicketDenyInfoMapFunction 中的逻辑。
 */
@Slf4j
public class DenyCalculator implements IMetricCalculator<CalculationContext> {

    // 正则表达式用于从备注中提取拒单原因
    private static final Pattern DENY_REASON_PATTERN = Pattern.compile("拒绝原因： (.*?)<br/>");
    private static final List<Integer> BACKEND_POSTS = Arrays.asList(
            OperatorPost.OPERATION.getCode(),
            OperatorPost.PRODUCTION_RESEARCH.getCode(),
            OperatorPost.P_VERTICAL_PRODUCTION_RESEARCH.getCode()
    );

    // ============================== 拒单统计指标 ==============================

    /**
     * 首次响应队列id
     */
    private int firstResponseQueue;

    /**
     * 运维拒绝原因
     */
    private String operationDenyReason;

    /**
     * 1.5线拒绝原因
     */
    private String secondLineDenyReason;

    /**
     * 被一1.5线拒绝次数
     */
    private int secondLineRefuseTimes = 0;

    /**
     * 1.5线最后拒单人
     */
    private String secondLineLastRefuser = "";

    /**
     * 后端首次拒单人
     */
    private String backendFirstDenyOperator;

    /**
     * 产研拒绝原因
     */
    private String productResearchDenyReason;

    /**
     * 是否被运维拒绝过
     */
    private boolean isDeniedByOperation = false;

    /**
     * 是否被1.5线拒绝过
     */
    private boolean isDeniedBySecondLine = false;

    /**
     * 用户等待的不耐烦时长
     */
    private long customerWaitingImpatienceDuration;

    /**
     * 垂直产研拒绝原因
     */
    private String verticalProductResearchDenyReason;

    /**
     * 是否被产研拒绝过
     */
    private boolean isDeniedByProductionResearch = false;


    // 标志位，确保这部分指标只被计算一次
    private boolean operationDenyFlag = true;
    private boolean secondLineDenyFlag = true;
    private boolean firstResponseQueueFlag = true;
    private boolean productResearchDenyFlag = true;
    private boolean verticalProductResearchDenyFlag = true;


    @Override
    public void initialize(CalculationContext context) {
        // 初始化状态
        this.firstResponseQueue = 0;
        this.operationDenyReason = "";
        this.secondLineDenyReason = "";
        this.backendFirstDenyOperator = "";
        this.productResearchDenyReason = "";
        this.verticalProductResearchDenyReason = "";
        this.customerWaitingImpatienceDuration = 0L;

        // 初始化新增的拒单统计指标
        this.secondLineRefuseTimes = 0;
        this.secondLineLastRefuser = "";
        this.isDeniedByOperation = false;
        this.isDeniedBySecondLine = false;
        this.isDeniedByProductionResearch = false;

        this.operationDenyFlag = true;
        this.secondLineDenyFlag = true;
        this.firstResponseQueueFlag = true;
        this.productResearchDenyFlag = true;
        this.verticalProductResearchDenyFlag = true;

    }

    @Override
    public void processOperation(CalculationContext context, Operation currentOp, Operation previousOp, Operation nextOp) {
        // 1. 计算首次响应队列
        calculateFirstResponseQueue(currentOp);

        // 2. 计算并提取各岗位拒单原因
        calculateDenyReasons(currentOp);

        // 3. 处理拒单统计逻辑
        processDenialStatistics(currentOp);
    }

    @Override
    public void calculate(CalculationContext context) {
        List<Operation> operations = context.getTicketProfile().getOperations();

        // 3. 计算后端首次拒单人
        // 后端岗位包括：运维、产研、垂直产研
        this.backendFirstDenyOperator = operations
                .stream()
                .filter(op -> {
                    // 必须是拒单操作
                    OperationType opType = OperationType.of(op.getOperationType());
                    boolean isDenyOp = opType == OperationType.REFUSE || opType == OperationType.REFUSE_TEG_FIRST_TIME;
                    // 操作人岗位和当前岗位都必须是后端岗位
                    boolean isBackendPost = BACKEND_POSTS.contains(op.getPost()) && BACKEND_POSTS.contains(op.getOperatorPost());
                    return isDenyOp && isBackendPost;
                })
                .min(Operation::compareTo) // 找到最早的一条
                .map(Operation::getOperatorName)
                .orElse("");

        // 4. 计算用户等待不耐烦时长
        this.customerWaitingImpatienceDuration = calculateImpatienceDuration(operations);
    }

    @Override
    public void populateMetrics(DwmIncidentTicketStatisticMetrics metrics, CalculationContext context) {
        metrics.setFirstResponseQueue(this.firstResponseQueue);
        metrics.setOperationDenyReason(this.operationDenyReason);
        metrics.setSecondLineDenyReason(this.secondLineDenyReason);
        metrics.setSecondLineRefuseTimes(this.secondLineRefuseTimes);
        metrics.setSecondLineLastRefuser(this.secondLineLastRefuser);
        metrics.setIsDeniedByOperation(this.isDeniedByOperation ? 1 : 0);
        metrics.setIsDeniedBySecondLine(this.isDeniedBySecondLine ? 1 : 0);
        metrics.setBackendFirstDenyOperator(this.backendFirstDenyOperator);
        metrics.setProductResearchDenyReason(this.productResearchDenyReason);
        metrics.setIsDeniedByProductionResearch(this.isDeniedByProductionResearch ? 1 : 0);
        metrics.setCustomerWaitingImpatienceDuration(this.customerWaitingImpatienceDuration);
        metrics.setVerticalProductResearchDenyReason(this.verticalProductResearchDenyReason);
    }


    private void calculateFirstResponseQueue(Operation op) {
        if (!firstResponseQueueFlag) {return;}

        OperatorType operatorType = OperatorType.fromType(op.getOperatorType());
        OperationType opType = OperationType.of(op.getOperationType());

        boolean hasReply = isValidContent(op.getExternReply(), "NULL");
        boolean isEffectiveCall = opType == OperationType.EFFECTIVE_CALL;
        boolean isNotCreate = opType != OperationType.CREATE;

        if (operatorType == OperatorType.CUSTOMER_SERVICE && ((hasReply && isNotCreate) || isEffectiveCall)) {
            this.firstResponseQueue = op.getFactAssign();
            this.firstResponseQueueFlag = false;
        }
    }

    private void calculateDenyReasons(Operation op) {
        OperationType opType = OperationType.of(op.getOperationType());
        if (opType != OperationType.REFUSE && opType != OperationType.REFUSE_TEG_FIRST_TIME) {
            return;
        }

        if (StringUtils.isBlank(op.getRemark())) {
            return;
        }

        Matcher matcher = DENY_REASON_PATTERN.matcher(op.getRemark());
        if (matcher.find()) {
            String reason = matcher.group(1);
            OperatorPost post = OperatorPost.of(op.getPost());

            switch (post) {
                case OPERATION:
                    if (operationDenyFlag) {
                        this.operationDenyReason = reason;
                        this.operationDenyFlag = false;
                    }
                    break;
                case SECOND_LINE:
                    if (secondLineDenyFlag) {
                        this.secondLineDenyReason = reason;
                        this.secondLineDenyFlag = false;
                    }
                    break;
                case PRODUCTION_RESEARCH:
                    if (productResearchDenyFlag) {
                        this.productResearchDenyReason = reason;
                        this.productResearchDenyFlag = false;
                    }
                    break;
                case P_VERTICAL_PRODUCTION_RESEARCH:
                    if (verticalProductResearchDenyFlag) {
                        this.verticalProductResearchDenyReason = reason;
                        this.verticalProductResearchDenyFlag = false;
                    }
                    break;
            }
        }
    }

    private long calculateImpatienceDuration(List<Operation> operations) {
        // 找到第一个客户催单
        return findUrgeOperation(operations)
                .flatMap(urgeOp -> findLastServiceReplyBefore(operations, urgeOp)
                        .flatMap(lastReply -> findFirstCustomerAskBetween(operations, lastReply, urgeOp)
                                .map(firstAsk -> calculateTimeDeltaSafe(firstAsk, urgeOp))
                        )
                )
                .orElse(0L);
    }

    /**
     * 查找工单中的第一条催单操作
     *
     * @param operations 工单操作流水列表
     * @return 包含催单操作的Optional对象，如果不存在则返回空Optional
     */
    private Optional<Operation> findUrgeOperation(List<Operation> operations) {
        return operations.stream()
                .filter(op -> OperationType.of(op.getOperationType()) == OperationType.URGE)
                .findFirst();
    }

    /**
     * 查找催单操作前的最后一次客服有效回复
     *
     * @param operations 工单操作流水列表
     * @param urgeOp 催单操作对象
     * @return 包含客服回复操作的Optional对象，如果不存在则返回空Optional
     */
    private Optional<Operation> findLastServiceReplyBefore(List<Operation> operations, Operation urgeOp) {
        return operations.stream()
                .filter(op -> op.getOperationId() < urgeOp.getOperationId())
                .filter(op -> OperatorType.fromType(op.getOperatorType()) == OperatorType.CUSTOMER_SERVICE)
                .filter(this::isValidReply)
                .max(Operation::compareTo);
    }

    /**
     * 查找客服回复和催单之间的首次客户有效提问
     *
     * @param operations 工单操作流水列表
     * @param lastReply 最后一次客服回复操作
     * @param urgeOp 催单操作对象
     * @return 包含客户提问操作的Optional对象，如果不存在则返回空Optional
     */
    private Optional<Operation> findFirstCustomerAskBetween(List<Operation> operations, Operation lastReply, Operation urgeOp) {
        return operations.stream()
                .filter(op -> op.getOperationId() > lastReply.getOperationId() && op.getOperationId() < urgeOp.getOperationId())
                .filter(op -> OperatorType.fromType(op.getOperatorType()) == OperatorType.CUSTOMER)
                .filter(this::isValidReply)
                .min(Operation::compareTo);
    }

    /**
     * 验证操作是否包含有效回复内容
     *
     * @param op 待验证的操作对象
     * @return 如果回复内容非空且不为"NULL"则返回true，否则返回false
     */
    private boolean isValidReply(Operation op) {
        return StringUtils.isNotBlank(op.getExternReply()) && !"NULL".equalsIgnoreCase(op.getExternReply());
    }

    /**
     * 安全计算两个操作之间的时间差（毫秒）
     *
     * @param firstAsk 首次客户提问操作
     * @param urgeOp 催单操作
     * @return 两个操作的时间差（毫秒），计算失败时返回0并记录错误日志
     */
    private long calculateTimeDeltaSafe(Operation firstAsk, Operation urgeOp) {
        try {
            return DateFormatUtils.timeDelta(firstAsk.getOperateTime().toString(), urgeOp.getOperateTime().toString());
        } catch (Exception e) {
            log.error("Failed to calculate impatience duration for ticketId: {}", urgeOp.getTicketId(), e);
            return 0L;
        }
    }

    /**
     * 处理拒单统计逻辑
     *
     * @param currentOp 当前操作
     */
    private void processDenialStatistics(Operation currentOp) {
        OperationType opType = OperationType.of(currentOp.getOperationType());

        // 只处理拒单操作
        if (opType != OperationType.REFUSE && opType != OperationType.REFUSE_TEG_FIRST_TIME) {
            return;
        }

        OperatorPost operatorPost = OperatorPost.of(currentOp.getPost());
        OperatorType operatorType = OperatorType.fromType(currentOp.getOperatorType());

        // 根据岗位处理拒单统计
        switch (operatorPost) {
            case SECOND_LINE:
                if (!this.isDeniedBySecondLine && operatorType != OperatorType.INCIDENT_MANAGER) {
                    this.isDeniedBySecondLine = true;
                }
                this.secondLineRefuseTimes++;
                this.secondLineLastRefuser = currentOp.getOperatorName();
                break;
            case OPERATION:
                this.isDeniedByOperation = true;
                break;
            case PRODUCTION_RESEARCH:
                this.isDeniedByProductionResearch = true;
                break;
            default:
                break;
        }
    }
}