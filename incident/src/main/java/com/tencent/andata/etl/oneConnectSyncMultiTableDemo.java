package com.tencent.andata.etl;

import static com.tencent.andata.utils.cdc.source.CDCSourceViewFactory.buildSourceView;
import static com.tencent.andata.utils.struct.DatabaseEnum.PGSQL;

import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.struct.DatabaseConf;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

@Slf4j

public class oneConnectSyncMultiTableDemo {

    public static void main(String[] args) throws Exception {
        FlinkEnv fEnv = FlinkEnvUtils.getStreamTableEnv(args);

        final DatabaseConf dbConf = DatabaseConf.builder()
                .dbHost("localhost")
                .dbPort(5432)
                .dbName("dataware")
                .userName("root")
                .password("123456")
                .build();

        ObjectMapper mapper = new ObjectMapper();

        // mysql table mapping to flink table
        ArrayNode mysqlTable2FlinkTableMap1 = mapper.readValue(
                "[\n"
                        + "    {\n"
                        + "        \"rdbTable\":\"dwd_change_var_info\",\n"
                        + "        \"fTable\":\"pg_source_dwd_change_var_info\"\n"
                        + "    }\n"
                        + "]",
                ArrayNode.class);

        ArrayNode mysqlTable2FlinkTableMap2 = mapper.readValue(
                "[\n"
                        + "    {\n"
                        + "        \"rdbTable\":\"t006_staff\",\n"
                        + "        \"fTable\":\"pg_source_t006_staff\"\n"
                        + "    }\n"
                        + "]",
                ArrayNode.class);

        ArrayNode mysqlTable2FlinkTableMap3 = mapper.readValue(
                "[\n"
                        + "    {\n"
                        + "        \"rdbTable\":\"t201_ticket_fsm\",\n"
                        + "        \"fTable\":\"pg_source_t201_ticket_fsm\"\n"
                        + "    }\n"
                        + "]",
                ArrayNode.class);

        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        buildSourceView(dbConf, fEnv, mysqlTable2FlinkTableMap1, PGSQL, parameterTool);
        buildSourceView(dbConf, fEnv, mysqlTable2FlinkTableMap2, PGSQL, parameterTool);
        buildSourceView(dbConf, fEnv, mysqlTable2FlinkTableMap3, PGSQL, parameterTool);

        StreamTableEnvironment tEnv = fEnv.streamTEnv();
        StreamExecutionEnvironment env = fEnv.env();

        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                1, // max failures per interval
                Time.of(10, TimeUnit.SECONDS), //time interval for measuring failure rate
                Time.of(3, TimeUnit.SECONDS) // delay
        ));

        // 注册hive udf
//        fEnv.hiveModuleV2().registryHiveUDF("to_json", ToJson.class.getName());

        //tEnv.sqlQuery("select * from t201_ticket_fsm_view").printSchema();
//        Table t006StaffTable = tEnv.sqlQuery("select * from t006_staff_view");
        // tEnv.toChangelogStream(tEnv.sqlQuery("select * from t201_ticket_fsm_view where id = 315")).print();
//        tEnv.createTemporaryView("t006_staff_json_view",
//                tEnv.sqlQuery(row2Json(t006StaffTable,
//                        "`user_id`",
//                        "t006_staff_view")));

        tEnv.toChangelogStream(tEnv.sqlQuery("select * from dwd_change_var_info_view")).print("1: ");
        tEnv.toChangelogStream(tEnv.sqlQuery("select * from t006_staff_view")).print("2: ");
        tEnv.toChangelogStream(tEnv.sqlQuery("select * from t201_ticket_fsm_view")).print("3: ");

        env.setParallelism(10);
        env.execute("MySQL CDC + Changelog");
    }
}
