package com.tencent.andata.etl.calculator.calculators.transfer;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.tencent.andata.etl.entity.Operation;
import com.tencent.andata.etl.enums.OperationType;
import com.tencent.andata.etl.enums.OperatorPost;
import com.tencent.andata.etl.enums.OperatorType;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * OptimizedTransferOperatorHandler 测试类
 * 验证优化后的转单操作人跟踪逻辑是否正确
 */
public class OptimizedTransferOperatorHandlerTest {

    private OptimizedTransferOperatorHandler handler;
    private OptimizedTransferOperatorHandler.OperatorTracker operatorTracker;

    @Mock
    private Operation currentOp;

    @Mock
    private Operation previousOp;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        operatorTracker = new OptimizedTransferOperatorHandler.OperatorTracker();
        handler = new OptimizedTransferOperatorHandler(operatorTracker);
    }

    @Test
    public void testProcessTransferOperatorTracking_NonSecondLineOperation() {
        // 准备测试数据
        when(currentOp.getTargetPost()).thenReturn(OperatorPost.FIRST_LINE.getCode());

        // 执行测试
        handler.processTransferOperatorTracking(
                currentOp,
                OperationType.TRANSFER,
                OperatorType.CUSTOMER_SERVICE,
                null,
                "2023-01-01 10:00:00"
        );

        // 验证：非1.5线操作应该被跳过，不会有任何处理
        assertEquals("", operatorTracker.firstTransferSecondLineStaff);
    }

    @Test
    public void testProcessTransferOperatorTracking_TransferToSecondLine() {
        // 准备测试数据
        when(currentOp.getTargetPost()).thenReturn(OperatorPost.SECOND_LINE.getCode());
        when(currentOp.getOperatorName()).thenReturn("testOperator");
        when(currentOp.getNextOperatorName()).thenReturn("nextOperator");
        when(currentOp.getNextOperatorCompanyId()).thenReturn(123);

        // 执行测试
        handler.processTransferOperatorTracking(
                currentOp,
                OperationType.TRANSFER,
                OperatorType.CUSTOMER_SERVICE,
                null,
                "2023-01-01 10:00:00"
        );

        // 验证：应该更新首次转1.5线员工
        assertEquals("testOperator", operatorTracker.firstTransferSecondLineStaff);
    }

    @Test
    public void testProcessTransferOperatorTracking_PullToSecondLine() {
        // 准备测试数据
        when(currentOp.getTargetPost()).thenReturn(OperatorPost.SECOND_LINE.getCode());
        when(currentOp.getOperatorName()).thenReturn("pullOperator");
        when(previousOp.getOperatorName()).thenReturn("prevOperator");
        when(previousOp.getNextOperatorName()).thenReturn("prevNextOperator");

        // 执行测试
        handler.processTransferOperatorTracking(
                currentOp,
                OperationType.PULL,
                OperatorType.CUSTOMER_SERVICE,
                previousOp,
                "2023-01-01 11:00:00"
        );

        // 验证：应该记录首次1.5线服务时间
        assertEquals("2023-01-01 11:00:00", operatorTracker.firstSecondLineServiceTime);
    }

    @Test
    public void testProcessTransferOperatorTracking_DispatchToSecondLine() {
        // 准备测试数据
        when(currentOp.getTargetPost()).thenReturn(OperatorPost.SECOND_LINE.getCode());
        when(currentOp.getNextOperatorName()).thenReturn("dispatchOperator");
        when(currentOp.getNextOperatorCompanyId()).thenReturn(456);

        // 执行测试
        handler.processTransferOperatorTracking(
                currentOp,
                OperationType.DISPATCH,
                OperatorType.SYSTEM,
                null,
                "2023-01-01 12:00:00"
        );

        // 验证：派单操作的特殊逻辑
        // 由于状态标志的复杂性，这里主要验证没有异常抛出
        assertNotNull(operatorTracker);
    }

    @Test
    public void testResetFlags() {
        // 执行重置
        handler.resetFlags();

        // 验证：重置后应该能够重新处理首次操作
        when(currentOp.getTargetPost()).thenReturn(OperatorPost.SECOND_LINE.getCode());
        when(currentOp.getOperatorName()).thenReturn("resetTestOperator");

        handler.processTransferOperatorTracking(
                currentOp,
                OperationType.TRANSFER,
                OperatorType.CUSTOMER_SERVICE,
                null,
                "2023-01-01 13:00:00"
        );

        assertEquals("resetTestOperator", operatorTracker.firstTransferSecondLineStaff);
    }

    @Test
    public void testTransferContextBuilder() {
        // 测试 TransferContext.Builder
        OptimizedTransferOperatorHandler.TransferContext context =
                OptimizedTransferOperatorHandler.TransferContext.builder()
                        .currentOp(currentOp)
                        .operatorType(OperatorType.CUSTOMER_SERVICE)
                        .previousOp(previousOp)
                        .operateTime("2023-01-01 14:00:00")
                        .build();

        // 验证构建的上下文对象
        assertEquals(currentOp, context.currentOp);
        assertEquals(OperatorType.CUSTOMER_SERVICE, context.operatorType);
        assertEquals(previousOp, context.previousOp);
        assertEquals("2023-01-01 14:00:00", context.operateTime);
    }

    @Test
    public void testOperatorTracker_DefaultValues() {
        // 验证 OperatorTracker 的默认值
        OptimizedTransferOperatorHandler.OperatorTracker tracker =
                new OptimizedTransferOperatorHandler.OperatorTracker();

        assertEquals("", tracker.firstTransferSecondLineStaff);
        assertEquals("1970-01-01 00:00:00", tracker.firstSecondLineServiceTime);
        assertNotNull(tracker.operatorTreeMap);
        assertTrue(tracker.operatorTreeMap.isEmpty());
    }
}