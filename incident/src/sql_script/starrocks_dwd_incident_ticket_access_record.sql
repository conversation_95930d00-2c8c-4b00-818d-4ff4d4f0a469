CREATE TABLE dwd_incident_ticket_access_record
(
    `id`                      BIGINT COMMENT '工单详情访问ID',
    `create_time`             DATETIME COMMENT '创建时间',
    `ticket_id`               BIGINT COMMENT '工单ID',
    `user_id`                 BIGINT COMMENT '访客id',
    `access_source`           BIGINT COMMENT '访客来源 1|PC端,2|小程序',
    `url`                     STRING COMMENT '来源URL',
    `params`                  STRING COMMENT '访问参数',
    `is_report`               BIGINT COMMENT '',
    `sign`                    STRING COMMENT 'sign',
    `olauuid`                 STRING COMMENT 'olauuid'

) ENGINE = OLAP
PRIMARY KEY (`id`, `create_time`)
COMMENT "内部客户工单访问记录"
PARTITION BY RANGE (`create_time`) (
START ('2016-01-01') END ('2030-01-01') EVERY (INTERVAL 1 MONTH)
)
DISTRIBUTED BY HASH (`id`,`create_time`)
PROPERTIES (
    "dynamic_partition.enable" = "true",
    "dynamic_partition.buckets" = "8",
    "dynamic_partition.time_unit" = "MONTH",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.history_partition_num" = "0",
    "replication_num" = "3",
    "in_memory" = "false",
    "bloom_filter_columns" = "ticket_id"
);